# KPI组件库快速开始指南

## 5分钟快速上手

### 1. 导入组件

```tsx
import { 
  KPITracker, 
  createKPIDataSource,
  PROJECT_KPI_CONFIG 
} from '@/components/kpi'
```

### 2. 创建数据源

```tsx
// 项目KPI数据源
const dataSource = createKPIDataSource('project')

// 或者领域指标数据源
const areaDataSource = createKPIDataSource('area')
```

### 3. 使用组件

```tsx
function ProjectPage({ projectId }: { projectId: string }) {
  return (
    <KPITracker
      parentId={projectId}
      parentType="project"
      dataSource={dataSource}
    />
  )
}
```

## 常见使用场景

### 场景1：项目详情页面

```tsx
import { KPITracker, createKPIDataSource, PROJECT_KPI_CONFIG } from '@/components/kpi'

function ProjectDetailPage({ projectId }: { projectId: string }) {
  const dataSource = createKPIDataSource('project')
  
  return (
    <div class="space-y-6">
      <h1>项目详情</h1>
      
      <KPITracker
        parentId={projectId}
        parentType="project"
        dataSource={dataSource}
        config={{
          showChart: true,
          showStatistics: true,
          allowCreate: true,
          layout: 'grid'
        }}
        kpiConfig={PROJECT_KPI_CONFIG}
      />
    </div>
  )
}
```

### 场景2：领域管理页面

```tsx
import { KPITracker, createKPIDataSource, AREA_METRIC_CONFIG } from '@/components/kpi'

function AreaDetailPage({ areaId }: { areaId: string }) {
  const dataSource = createKPIDataSource('area')
  
  return (
    <div class="space-y-6">
      <h1>领域管理</h1>
      
      <KPITracker
        parentId={areaId}
        parentType="area"
        dataSource={dataSource}
        config={{
          showChart: true,
          showHistory: true,
          allowBatchRecord: true,
          layout: 'list',
          chartType: 'line'
        }}
        kpiConfig={AREA_METRIC_CONFIG}
      />
    </div>
  )
}
```

### 场景3：仪表盘概览

```tsx
import { KPIChart, createKPIDataSource } from '@/components/kpi'
import { createSignal, onMount } from 'solid-js'

function DashboardOverview() {
  const [projectKPIs, setProjectKPIs] = createSignal([])
  const [areaMetrics, setAreaMetrics] = createSignal([])
  
  const projectDataSource = createKPIDataSource('project')
  const areaDataSource = createKPIDataSource('area')
  
  onMount(async () => {
    // 加载所有项目的KPI
    const allProjectKPIs = await Promise.all(
      projects.map(p => projectDataSource.getKPIs(p.id))
    ).then(results => results.flat())
    
    setProjectKPIs(allProjectKPIs)
    
    // 加载所有领域的指标
    const allAreaMetrics = await Promise.all(
      areas.map(a => areaDataSource.getKPIs(a.id))
    ).then(results => results.flat())
    
    setAreaMetrics(allAreaMetrics)
  })
  
  return (
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h2>项目KPI概览</h2>
        <KPIChart kpis={projectKPIs()} type="donut" />
      </div>
      
      <div>
        <h2>个人指标概览</h2>
        <KPIChart kpis={areaMetrics()} type="bar" />
      </div>
    </div>
  )
}
```

### 场景4：自定义事件处理

```tsx
import { KPITracker, createKPIDataSource } from '@/components/kpi'
import { useNotifications } from '@/hooks/useNotifications'

function ProjectWithNotifications({ projectId }: { projectId: string }) {
  const { addNotification } = useNotifications()
  const dataSource = createKPIDataSource('project')
  
  const eventHandlers = {
    onCreate: (kpi) => {
      addNotification({
        type: 'success',
        title: 'KPI创建成功',
        message: `已创建KPI: ${kpi.name}`
      })
    },
    
    onRecord: (record) => {
      addNotification({
        type: 'info',
        title: '数据记录成功',
        message: `已记录数值: ${record.value}`
      })
    },
    
    onProgressChange: (kpiId, progress) => {
      if (progress.status === 'achieved') {
        addNotification({
          type: 'success',
          title: '目标达成！',
          message: '恭喜你完成了KPI目标'
        })
      }
    },
    
    onError: (error) => {
      addNotification({
        type: 'error',
        title: '操作失败',
        message: error.message
      })
    }
  }
  
  return (
    <KPITracker
      parentId={projectId}
      parentType="project"
      dataSource={dataSource}
      eventHandlers={eventHandlers}
    />
  )
}
```

## 配置选项速查

### 显示配置

```tsx
const displayConfig = {
  showChart: true,          // 显示图表
  showQuickInput: true,     // 显示快速输入
  showHistory: false,       // 显示历史记录
  showStatistics: true,     // 显示统计信息
}
```

### 交互配置

```tsx
const interactionConfig = {
  allowCreate: true,        // 允许创建
  allowEdit: true,          // 允许编辑
  allowDelete: true,        // 允许删除
  allowBatchRecord: false,  // 允许批量记录
}
```

### 布局配置

```tsx
const layoutConfig = {
  layout: 'grid',           // 'grid' | 'list' | 'compact'
  chartType: 'bar',         // 'bar' | 'line' | 'pie' | 'donut'
}
```

### 数据配置

```tsx
const dataConfig = {
  maxRecords: 20,           // 最大记录数
  autoRefresh: false,       // 自动刷新
  refreshInterval: 30000,   // 刷新间隔(ms)
}
```

## 常用模板

### 项目KPI模板

- **开发类**: Bug修复数、代码覆盖率、响应时间
- **业务类**: 新增用户、用户满意度、销售额
- **运营类**: 页面访问量、转化率、完成率

### 领域指标模板

- **健康类**: 体重、运动时长、步数、睡眠时长
- **学习类**: 学习时长、阅读页数、练习题数
- **生活类**: 冥想时长、屏幕时间、社交时间

## 故障排除

### 常见问题

1. **数据不显示**
   - 检查数据源是否正确配置
   - 确认parentId是否有效
   - 查看控制台错误信息

2. **图表不渲染**
   - 确认KPI数据格式正确
   - 检查是否有target值
   - 验证数值类型是否为number

3. **事件不触发**
   - 确认事件处理器正确绑定
   - 检查异步操作是否正确处理
   - 查看控制台错误日志

### 调试技巧

```tsx
// 启用调试模式
const eventHandlers = {
  onCreate: (kpi) => console.log('Created:', kpi),
  onUpdate: (kpi) => console.log('Updated:', kpi),
  onError: (error, context) => console.error('Error:', error, context)
}

// 使用测试数据
const mockKPIs = [
  {
    id: '1',
    name: '测试KPI',
    value: 50,
    target: 100,
    direction: 'increase',
    // ... 其他必需字段
  }
]
```

## 下一步

- 查看 [API文档](./API.md) 了解详细接口
- 参考 [示例代码](./examples/) 学习高级用法
- 运行 [集成测试](./examples/IntegrationTest.tsx) 验证功能
