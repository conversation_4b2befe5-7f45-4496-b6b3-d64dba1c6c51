import type { Task } from '../../../types/business';

interface TasksSectionProps {
  tasks: Task[];
}

export default function TasksSection(props: TasksSectionProps) {
  const total = () => props.tasks.length;
  const completed = () => props.tasks.filter(t => t.status === 'completed').length;

  return (
    <div class="space-y-4">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="p-4 border rounded-lg">
          <div class="text-sm text-muted-foreground">任务总数</div>
          <div class="mt-1 font-medium">{total()}</div>
        </div>
        <div class="p-4 border rounded-lg">
          <div class="text-sm text-muted-foreground">已完成</div>
          <div class="mt-1 font-medium">{completed()}</div>
        </div>
      </div>
      <div class="text-sm text-muted-foreground">后续可在此接入任务列表组件...</div>
    </div>
  );
}

