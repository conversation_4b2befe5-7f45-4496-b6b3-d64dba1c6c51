#[cfg(test)]
mod tests {
    use super::*;
    use crate::domain::entities::{Task, TaskStatus, Project};
    use crate::domain::repositories::{TaskRepository, ProjectRepository};
    use crate::domain::services::TaskDomainService;
    use crate::shared::errors::{AppError, Result};
    use crate::shared::types::{Id, QueryParams, Priority, EntityStatus};
    use async_trait::async_trait;
    use std::collections::HashMap;
    use std::sync::{Arc, Mutex};

    // Mock Task Repository
    #[derive(Default)]
    struct MockTaskRepository {
        tasks: Arc<Mutex<HashMap<Id, Task>>>,
        titles: Arc<Mutex<HashMap<String, Id>>>,
    }

    impl MockTaskRepository {
        fn new() -> Self {
            Self::default()
        }

        fn add_task(&self, task: Task) {
            let mut tasks = self.tasks.lock().unwrap();
            let mut titles = self.titles.lock().unwrap();
            titles.insert(task.title.clone(), task.id.clone());
            tasks.insert(task.id.clone(), task);
        }
    }

    #[async_trait]
    impl TaskRepository for MockTaskRepository {
        async fn find_by_id(&self, id: &Id) -> Result<Option<Task>> {
            let tasks = self.tasks.lock().unwrap();
            Ok(tasks.get(id).cloned())
        }

        async fn find_by_title(&self, title: &str) -> Result<Option<Task>> {
            let titles = self.titles.lock().unwrap();
            let tasks = self.tasks.lock().unwrap();
            
            if let Some(id) = titles.get(title) {
                Ok(tasks.get(id).cloned())
            } else {
                Ok(None)
            }
        }

        async fn save(&self, task: &Task) -> Result<()> {
            self.add_task(task.clone());
            Ok(())
        }

        async fn update(&self, task: &Task) -> Result<()> {
            let mut tasks = self.tasks.lock().unwrap();
            if tasks.contains_key(&task.id) {
                tasks.insert(task.id.clone(), task.clone());
                Ok(())
            } else {
                Err(AppError::NotFound("Task not found".to_string()))
            }
        }

        async fn delete(&self, id: &Id) -> Result<()> {
            let mut tasks = self.tasks.lock().unwrap();
            if let Some(mut task) = tasks.get(id).cloned() {
                task.entity_status = EntityStatus::Deleted;
                tasks.insert(id.clone(), task);
                Ok(())
            } else {
                Err(AppError::NotFound("Task not found".to_string()))
            }
        }

        async fn find_all_active(&self) -> Result<Vec<Task>> {
            let tasks = self.tasks.lock().unwrap();
            Ok(tasks.values()
                .filter(|t| t.entity_status == EntityStatus::Active)
                .cloned()
                .collect())
        }

        async fn find_by_project_id(&self, project_id: &Id) -> Result<Vec<Task>> {
            let tasks = self.tasks.lock().unwrap();
            Ok(tasks.values()
                .filter(|t| t.project_id.as_ref() == Some(project_id) && t.entity_status != EntityStatus::Deleted)
                .cloned()
                .collect())
        }

        async fn find_by_parent_task_id(&self, parent_task_id: &Id) -> Result<Vec<Task>> {
            let tasks = self.tasks.lock().unwrap();
            Ok(tasks.values()
                .filter(|t| t.parent_task_id.as_ref() == Some(parent_task_id) && t.entity_status != EntityStatus::Deleted)
                .cloned()
                .collect())
        }

        async fn find_by_status(&self, status: &TaskStatus) -> Result<Vec<Task>> {
            let tasks = self.tasks.lock().unwrap();
            Ok(tasks.values()
                .filter(|t| t.status == *status && t.entity_status != EntityStatus::Deleted)
                .cloned()
                .collect())
        }

        async fn find_by_priority(&self, priority: &Priority) -> Result<Vec<Task>> {
            let tasks = self.tasks.lock().unwrap();
            Ok(tasks.values()
                .filter(|t| t.priority == *priority && t.entity_status != EntityStatus::Deleted)
                .cloned()
                .collect())
        }

        async fn find_due_soon(&self, days: u32) -> Result<Vec<Task>> {
            let cutoff = chrono::Utc::now() + chrono::Duration::days(days as i64);
            let tasks = self.tasks.lock().unwrap();
            Ok(tasks.values()
                .filter(|t| {
                    if let Some(due_date) = t.due_date {
                        due_date <= cutoff && t.status != TaskStatus::Completed && t.status != TaskStatus::Cancelled
                    } else {
                        false
                    }
                })
                .cloned()
                .collect())
        }

        async fn find_overdue(&self) -> Result<Vec<Task>> {
            let now = chrono::Utc::now();
            let tasks = self.tasks.lock().unwrap();
            Ok(tasks.values()
                .filter(|t| {
                    if let Some(due_date) = t.due_date {
                        due_date < now && t.status != TaskStatus::Completed && t.status != TaskStatus::Cancelled
                    } else {
                        false
                    }
                })
                .cloned()
                .collect())
        }

        async fn find_today_tasks(&self) -> Result<Vec<Task>> {
            let today_start = chrono::Utc::now().date_naive().and_hms_opt(0, 0, 0)
                .map(|dt| chrono::Utc.from_utc_datetime(&dt))
                .unwrap_or_else(|| chrono::Utc::now());
            let today_end = chrono::Utc::now().date_naive().and_hms_opt(23, 59, 59)
                .map(|dt| chrono::Utc.from_utc_datetime(&dt))
                .unwrap_or_else(|| chrono::Utc::now());

            let tasks = self.tasks.lock().unwrap();
            Ok(tasks.values()
                .filter(|t| {
                    if let Some(due_date) = t.due_date {
                        due_date >= today_start && due_date <= today_end
                    } else {
                        false
                    }
                })
                .cloned()
                .collect())
        }

        async fn find_with_pagination(&self, _params: &QueryParams) -> Result<(Vec<Task>, u64)> {
            let tasks = self.find_all_active().await?;
            let total = tasks.len() as u64;
            Ok((tasks, total))
        }

        async fn count_active_tasks(&self) -> Result<u64> {
            let tasks = self.find_all_active().await?;
            Ok(tasks.len() as u64)
        }

        async fn find_by_progress_range(&self, min_progress: f32, max_progress: f32) -> Result<Vec<Task>> {
            let tasks = self.tasks.lock().unwrap();
            Ok(tasks.values()
                .filter(|t| t.progress >= min_progress && t.progress <= max_progress && t.entity_status != EntityStatus::Deleted)
                .cloned()
                .collect())
        }

        async fn search(&self, query: &str) -> Result<Vec<Task>> {
            let tasks = self.tasks.lock().unwrap();
            Ok(tasks.values()
                .filter(|t| {
                    (t.title.contains(query) || 
                     t.description.as_ref().map_or(false, |d| d.contains(query))) &&
                    t.entity_status != EntityStatus::Deleted
                })
                .cloned()
                .collect())
        }

        async fn get_statistics(&self) -> Result<crate::domain::repositories::TaskStatistics> {
            let tasks = self.tasks.lock().unwrap();
            let total_tasks = tasks.len() as u64;
            let active_tasks = tasks.values().filter(|t| t.entity_status == EntityStatus::Active).count() as u64;
            let completed_tasks = tasks.values().filter(|t| t.status == TaskStatus::Completed).count() as u64;
            let overdue_tasks = 0; // Simplified
            let today_tasks = 0; // Simplified
            let average_progress = 0.5; // Simplified
            let completion_rate = if total_tasks > 0 { completed_tasks as f32 / total_tasks as f32 } else { 0.0 };

            Ok(crate::domain::repositories::TaskStatistics {
                total_tasks,
                active_tasks,
                completed_tasks,
                overdue_tasks,
                today_tasks,
                average_progress,
                tasks_by_status: vec![],
                tasks_by_priority: vec![],
                completion_rate,
            })
        }

        async fn get_project_completion_rate(&self, project_id: &Id) -> Result<f32> {
            let project_tasks = self.find_by_project_id(project_id).await?;
            if project_tasks.is_empty() {
                return Ok(0.0);
            }
            
            let completed_count = project_tasks.iter().filter(|t| t.status == TaskStatus::Completed).count();
            Ok(completed_count as f32 / project_tasks.len() as f32)
        }

        async fn find_root_tasks(&self) -> Result<Vec<Task>> {
            let tasks = self.tasks.lock().unwrap();
            Ok(tasks.values()
                .filter(|t| t.parent_task_id.is_none() && t.entity_status != EntityStatus::Deleted)
                .cloned()
                .collect())
        }

        async fn find_task_tree(&self, root_task_id: &Id) -> Result<Vec<Task>> {
            // Simplified implementation - just return the root task and direct children
            let mut result = Vec::new();
            
            if let Some(root_task) = self.find_by_id(root_task_id).await? {
                result.push(root_task);
                let children = self.find_by_parent_task_id(root_task_id).await?;
                result.extend(children);
            }
            
            Ok(result)
        }
    }

    // Mock Project Repository
    #[derive(Default)]
    struct MockProjectRepository {
        projects: Arc<Mutex<HashMap<Id, Project>>>,
    }

    impl MockProjectRepository {
        fn new() -> Self {
            Self::default()
        }

        fn add_project(&self, project: Project) {
            let mut projects = self.projects.lock().unwrap();
            projects.insert(project.id.clone(), project);
        }
    }

    #[async_trait]
    impl ProjectRepository for MockProjectRepository {
        async fn find_by_id(&self, id: &Id) -> Result<Option<Project>> {
            let projects = self.projects.lock().unwrap();
            Ok(projects.get(id).cloned())
        }

        // Implement other required methods with minimal functionality
        async fn find_by_name(&self, _name: &str) -> Result<Option<Project>> { Ok(None) }
        async fn save(&self, project: &Project) -> Result<()> { self.add_project(project.clone()); Ok(()) }
        async fn update(&self, _project: &Project) -> Result<()> { Ok(()) }
        async fn delete(&self, _id: &Id) -> Result<()> { Ok(()) }
        async fn find_all_active(&self) -> Result<Vec<Project>> { Ok(vec![]) }
        async fn find_by_area_id(&self, _area_id: &Id) -> Result<Vec<Project>> { Ok(vec![]) }
        async fn find_by_status(&self, _status: &crate::domain::entities::ProjectStatus) -> Result<Vec<Project>> { Ok(vec![]) }
        async fn find_by_priority(&self, _priority: &Priority) -> Result<Vec<Project>> { Ok(vec![]) }
        async fn find_due_soon(&self, _days: u32) -> Result<Vec<Project>> { Ok(vec![]) }
        async fn find_overdue(&self) -> Result<Vec<Project>> { Ok(vec![]) }
        async fn find_with_pagination(&self, _params: &QueryParams) -> Result<(Vec<Project>, u64)> { Ok((vec![], 0)) }
        async fn count_active_projects(&self) -> Result<u64> { Ok(0) }
        async fn find_by_progress_range(&self, _min_progress: f32, _max_progress: f32) -> Result<Vec<Project>> { Ok(vec![]) }
        async fn search(&self, _query: &str) -> Result<Vec<Project>> { Ok(vec![]) }
        async fn get_statistics(&self) -> Result<crate::domain::repositories::ProjectStatistics> {
            Ok(crate::domain::repositories::ProjectStatistics {
                total_projects: 0,
                active_projects: 0,
                completed_projects: 0,
                overdue_projects: 0,
                average_progress: 0.0,
                projects_by_status: vec![],
                projects_by_priority: vec![],
            })
        }
    }

    fn create_task_app_service() -> TaskAppService {
        let task_repository = Arc::new(MockTaskRepository::new());
        let project_repository = Arc::new(MockProjectRepository::new());
        let domain_service = TaskDomainService::new();
        
        TaskAppService::new(task_repository, project_repository, domain_service)
    }

    #[tokio::test]
    async fn test_create_task_success() {
        let service = create_task_app_service();
        
        let result = service.create_task(
            "Test Task".to_string(),
            Some("A test task".to_string()),
            None,
            None,
            None,
            Some(Priority::High),
        ).await;

        assert!(result.is_ok());
        let task = result.unwrap();
        assert_eq!(task.title, "Test Task");
        assert_eq!(task.description, Some("A test task".to_string()));
        assert_eq!(task.priority, Priority::High);
        assert_eq!(task.status, TaskStatus::Todo);
    }

    #[tokio::test]
    async fn test_create_task_with_project() {
        let service = create_task_app_service();
        
        // First create a project
        let project = Project::new("Test Project".to_string(), None);
        let project_id = project.id.clone();
        service.project_repository.save(&project).await.unwrap();

        let result = service.create_task(
            "Test Task".to_string(),
            None,
            Some(project_id.clone()),
            None,
            None,
            None,
        ).await;

        assert!(result.is_ok());
        let task = result.unwrap();
        assert_eq!(task.project_id, Some(project_id));
    }

    #[tokio::test]
    async fn test_update_task_status() {
        let service = create_task_app_service();
        
        let task = service.create_task(
            "Test Task".to_string(),
            None,
            None,
            None,
            None,
            None,
        ).await.unwrap();

        let result = service.update_task_status(&task.id, TaskStatus::InProgress).await;
        assert!(result.is_ok());
        
        let updated_task = result.unwrap();
        assert_eq!(updated_task.status, TaskStatus::InProgress);
    }

    #[tokio::test]
    async fn test_update_task_progress() {
        let service = create_task_app_service();
        
        let task = service.create_task(
            "Test Task".to_string(),
            None,
            None,
            None,
            None,
            None,
        ).await.unwrap();

        let result = service.update_task_progress(&task.id, 0.75).await;
        assert!(result.is_ok());
        
        let updated_task = result.unwrap();
        assert_eq!(updated_task.progress, 0.75);
        assert_eq!(updated_task.status, TaskStatus::InProgress); // Should auto-update status
    }

    #[tokio::test]
    async fn test_complete_task() {
        let service = create_task_app_service();
        
        let task = service.create_task(
            "Test Task".to_string(),
            None,
            None,
            None,
            None,
            None,
        ).await.unwrap();

        let result = service.complete_task(&task.id).await;
        assert!(result.is_ok());
        
        let completed_task = result.unwrap();
        assert_eq!(completed_task.status, TaskStatus::Completed);
        assert_eq!(completed_task.progress, 1.0);
    }

    #[tokio::test]
    async fn test_cancel_task() {
        let service = create_task_app_service();
        
        let task = service.create_task(
            "Test Task".to_string(),
            None,
            None,
            None,
            None,
            None,
        ).await.unwrap();

        let result = service.cancel_task(&task.id).await;
        assert!(result.is_ok());
        
        let cancelled_task = result.unwrap();
        assert_eq!(cancelled_task.status, TaskStatus::Cancelled);
    }

    #[tokio::test]
    async fn test_reopen_task() {
        let service = create_task_app_service();
        
        let task = service.create_task(
            "Test Task".to_string(),
            None,
            None,
            None,
            None,
            None,
        ).await.unwrap();

        // Complete the task first
        let _completed = service.complete_task(&task.id).await.unwrap();

        // Reopen the task
        let result = service.reopen_task(&task.id).await;
        assert!(result.is_ok());
        
        let reopened_task = result.unwrap();
        assert_eq!(reopened_task.status, TaskStatus::InProgress); // Should be in progress since progress was 1.0
    }

    #[tokio::test]
    async fn test_cannot_cancel_completed_task() {
        let service = create_task_app_service();
        
        let task = service.create_task(
            "Test Task".to_string(),
            None,
            None,
            None,
            None,
            None,
        ).await.unwrap();

        // Complete the task first
        let _completed = service.complete_task(&task.id).await.unwrap();

        // Try to cancel completed task
        let result = service.cancel_task(&task.id).await;
        assert!(result.is_err());
        match result.unwrap_err() {
            AppError::ValidationError(msg) => assert_eq!(msg, "Cannot cancel completed task"),
            _ => panic!("Expected ValidationError"),
        }
    }

    #[tokio::test]
    async fn test_clone_task() {
        let service = create_task_app_service();
        
        let original_task = service.create_task(
            "Original Task".to_string(),
            Some("Original description".to_string()),
            None,
            None,
            Some(chrono::Utc::now() + chrono::Duration::days(7)),
            Some(Priority::High),
        ).await.unwrap();

        let result = service.clone_task(&original_task.id, "Cloned Task".to_string()).await;
        assert!(result.is_ok());
        
        let cloned_task = result.unwrap();
        assert_eq!(cloned_task.title, "Cloned Task");
        assert_eq!(cloned_task.description, Some("Original description".to_string()));
        assert_eq!(cloned_task.priority, Priority::High);
        assert_ne!(cloned_task.id, original_task.id);
        assert_eq!(cloned_task.status, TaskStatus::Todo); // New task should start as Todo
    }

    #[tokio::test]
    async fn test_batch_update_task_status() {
        let service = create_task_app_service();
        
        // Create multiple tasks
        let task1 = service.create_task("Task 1".to_string(), None, None, None, None, None).await.unwrap();
        let task2 = service.create_task("Task 2".to_string(), None, None, None, None, None).await.unwrap();
        let task3 = service.create_task("Task 3".to_string(), None, None, None, None, None).await.unwrap();

        let task_ids = vec![task1.id, task2.id, task3.id];
        let result = service.batch_update_task_status(task_ids, TaskStatus::InProgress).await;
        
        assert!(result.is_ok());
        let updated_tasks = result.unwrap();
        assert_eq!(updated_tasks.len(), 3);
        
        for task in updated_tasks {
            assert_eq!(task.status, TaskStatus::InProgress);
        }
    }
}
