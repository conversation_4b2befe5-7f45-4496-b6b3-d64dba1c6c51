import { Component, ErrorInfo, ReactNode } from 'react'
import { Button } from '../ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  }

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)

    this.setState({
      error,
      errorInfo
    })

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }
  }

  private handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  private handleReload = () => {
    window.location.reload()
  }

  public render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default error UI
      return (
        <div className="min-h-screen flex items-center justify-center p-4">
          <Card className="w-full max-w-lg">
            <CardHeader>
              <div className="flex items-center gap-2">
                <CardTitle className="text-destructive">Something went wrong</CardTitle>
                <Badge variant="destructive">Error</Badge>
              </div>
              <CardDescription>
                An unexpected error occurred. Please try refreshing the page or contact support if
                the problem persists.
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
              {this.state.error && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Error Details:</h4>
                  <div className="bg-muted p-3 rounded-md">
                    <code className="text-sm text-destructive">{this.state.error.message}</code>
                  </div>
                </div>
              )}

              {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
                <details className="space-y-2">
                  <summary className="text-sm font-medium cursor-pointer hover:text-foreground">
                    Stack Trace (Development)
                  </summary>
                  <div className="bg-muted p-3 rounded-md overflow-auto max-h-40">
                    <pre className="text-xs">{this.state.errorInfo.componentStack}</pre>
                  </div>
                </details>
              )}
            </CardContent>

            <CardFooter className="flex gap-2">
              <Button onClick={this.handleReset} variant="outline">
                Try Again
              </Button>
              <Button onClick={this.handleReload}>Reload Page</Button>
            </CardFooter>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

// Hook-based error boundary for functional components
export function useErrorHandler() {
  return (error: Error, errorInfo?: ErrorInfo) => {
    console.error('Error caught by useErrorHandler:', error, errorInfo)

    // You can integrate with error reporting services here
    // Example: Sentry.captureException(error)
  }
}

// Simple error display component
export function ErrorDisplay({
  error,
  onRetry,
  title = 'Error',
  description = 'Something went wrong. Please try again.'
}: {
  error?: Error | string
  onRetry?: () => void
  title?: string
  description?: string
}) {
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-destructive flex items-center gap-2">
          {title}
          <Badge variant="destructive">Error</Badge>
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>

      {error && (
        <CardContent>
          <div className="bg-muted p-3 rounded-md">
            <code className="text-sm text-destructive">
              {typeof error === 'string' ? error : error.message}
            </code>
          </div>
        </CardContent>
      )}

      {onRetry && (
        <CardFooter>
          <Button onClick={onRetry} className="w-full">
            Try Again
          </Button>
        </CardFooter>
      )}
    </Card>
  )
}

// Network error component
export function NetworkError({
  onRetry,
  message = 'Network connection failed. Please check your internet connection and try again.'
}: {
  onRetry?: () => void
  message?: string
}) {
  return <ErrorDisplay title="Connection Error" description={message} onRetry={onRetry} />
}

// Not found error component
export function NotFoundError({
  resource = 'page',
  onGoBack
}: {
  resource?: string
  onGoBack?: () => void
}) {
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>404 - Not Found</CardTitle>
          <CardDescription>
            The {resource} you're looking for doesn't exist or has been moved.
          </CardDescription>
        </CardHeader>

        <CardFooter>
          <Button onClick={onGoBack || (() => window.history.back())} className="w-full">
            Go Back
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}

export default ErrorBoundary
