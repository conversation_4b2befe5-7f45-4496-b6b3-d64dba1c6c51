// ProjectDetail Page - 项目详情页面
// 重新设计的项目详情页，采用左右2:1布局
// 左侧：任务管理，右侧：KPI管理、资源关联、笔记功能

import { createSignal, createEffect, Show, For, onMount } from 'solid-js';
import { PageContainer } from '../components/layout/Layout';
import { Card, CardHeader, CardTitle, CardContent } from '../components/ui/Card';
import { Button } from '../components/ui/button';
import { cn } from '@/lib/utils';
import { Project, Task } from '../types/business';
import { getProjectById, getTasksByProject } from '../services/apiFactory';

// 导入新创建的组件
import { TaskManager } from '../components/tasks';
import { KPIManager } from '../components/kpi';
import { ResourceAssociation } from '../components/resource-association';
import { createHabitDataSource } from '../components/habit-tracker';

// 简单的参数解析函数
const getProjectIdFromPath = () => {
  const path = window.location.pathname;
  const match = path.match(/^\/projects\/([^\/]+)$/);
  return match ? match[1] : null;
};

// 简单的导航函数
const navigate = (path: string) => {
  window.history.pushState({}, '', path);
  window.dispatchEvent(new CustomEvent('routechange'));
};

export default function ProjectDetail() {
  const projectId = getProjectIdFromPath();

  const [project, setProject] = createSignal<Project | null>(null);
  const [tasks, setTasks] = createSignal<Task[]>([]);
  const [loading, setLoading] = createSignal(true);
  const [error, setError] = createSignal<string | null>(null);

  // 创建数据源
  const taskDataSource = createHabitDataSource(); // 临时使用，后续需要创建专门的任务数据源
  const kpiDataSource = createHabitDataSource(); // 临时使用，后续需要创建专门的KPI数据源
  const resourceDataSource = createHabitDataSource(); // 临时使用，后续需要创建专门的资源数据源

  // 加载项目数据
  const loadProjectData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [projectData, tasksData] = await Promise.all([
        getProjectById(projectId!),
        getTasksByProject(projectId!)
      ]);
      
      setProject(projectData);
      setTasks(tasksData);
    } catch (err) {
      console.error('Failed to load project data:', err);
      setError('加载项目数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  onMount(() => {
    if (projectId) {
      loadProjectData();
    }
  });

  // 状态显示辅助函数
  const getStatusLabel = (status: string) => {
    const statusMap: Record<string, string> = {
      'not_started': '未开始',
      'in_progress': '进行中',
      'completed': '已完成',
      'on_hold': '暂停',
      'at_risk': '有风险',
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'not_started': 'bg-gray-100 text-gray-700',
      'in_progress': 'bg-blue-100 text-blue-700',
      'completed': 'bg-green-100 text-green-700',
      'on_hold': 'bg-yellow-100 text-yellow-700',
      'at_risk': 'bg-red-100 text-red-700',
    };
    return colorMap[status] || 'bg-gray-100 text-gray-700';
  };

  const getPriorityLabel = (priority: string) => {
    const priorityMap: Record<string, string> = {
      'low': '低',
      'medium': '中',
      'high': '高',
      'critical': '紧急',
    };
    return priorityMap[priority] || priority;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const calculateRemainingDays = (dueDate?: string) => {
    if (!dueDate) return null;
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const remainingDays = () => {
    const proj = project();
    return proj ? calculateRemainingDays(proj.dueDate) : null;
  };

  // 笔记功能状态
  const [notes, setNotes] = createSignal('');
  const [isEditingNotes, setIsEditingNotes] = createSignal(false);

  // 保存笔记
  const saveNotes = () => {
    // TODO: 实现笔记保存逻辑
    console.log('Saving notes:', notes());
    setIsEditingNotes(false);
  };

  return (
    <PageContainer
      actions={
        <div class="flex items-center space-x-2">
          <Button variant="outline" onClick={() => navigate('/projects')}>
            <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            返回项目列表
          </Button>
          <Button>
            <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            编辑项目
          </Button>
        </div>
      }
    >
      <Show when={loading()}>
        <div class="flex items-center justify-center h-64">
          <div class="text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p class="text-muted-foreground">加载中...</p>
          </div>
        </div>
      </Show>

      <Show when={error()}>
        <Card>
          <CardContent class="p-6">
            <div class="text-center text-red-600">
              <p>{error()}</p>
              <Button class="mt-4" onClick={loadProjectData}>重试</Button>
            </div>
          </CardContent>
        </Card>
      </Show>

      <Show when={!loading() && !error() && project()}>
        <div class="space-y-6">
          {/* 项目标题和基本信息 */}
          <div class="space-y-4">
            <div class="flex items-start justify-between">
              <div class="space-y-2">
                <h1 class="text-3xl font-bold">{project()?.name}</h1>
                <Show when={project()?.description}>
                  <p class="text-lg text-muted-foreground">{project()?.description}</p>
                </Show>
              </div>
              <div class="flex items-center space-x-2">
                <span class={cn('px-3 py-1 rounded-full text-sm font-medium', getStatusColor(project()?.status || ''))}>
                  {getStatusLabel(project()?.status || '')}
                </span>
                <span class="px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-700">
                  {getPriorityLabel(project()?.priority || '')}
                </span>
              </div>
            </div>

            {/* 进度条 */}
            <div class="space-y-2">
              <div class="flex justify-between text-sm">
                <span class="text-muted-foreground">项目进度</span>
                <span class="font-medium">{Math.round((project()?.progress || 0) * 100)}%</span>
              </div>
              <div class="w-full bg-muted rounded-full h-3 relative overflow-hidden">
                <div
                  class="bg-primary h-full rounded-full transition-all duration-300 absolute top-0 left-0"
                  style={{
                    width: `${Math.round((project()?.progress || 0) * 100)}%`
                  }}
                />
              </div>
            </div>
          </div>

          {/* 新的左右布局 */}
          <div class="grid grid-cols-3 gap-6">
            {/* 左侧：任务管理 (2/3 宽度) */}
            <div class="col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle class="flex items-center gap-2">
                    <span>✅</span>
                    任务管理
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <TaskManager
                    projectId={projectId}
                    dataSource={taskDataSource}
                    config={{
                      allowCreate: true,
                      allowEdit: true,
                      allowDelete: true,
                      showProgress: true,
                      showPriority: true,
                      showAssignee: true,
                      layout: 'list'
                    }}
                  />
                </CardContent>
              </Card>
            </div>

            {/* 右侧：KPI管理、资源关联、笔记功能 (1/3 宽度) */}
            <div class="space-y-6">
              {/* KPI管理 */}
              <Card>
                <CardHeader>
                  <CardTitle class="flex items-center gap-2">
                    <span>📈</span>
                    KPI管理
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <KPIManager
                    projectId={projectId}
                    dataSource={kpiDataSource}
                    config={{
                      allowCreate: true,
                      allowEdit: true,
                      showProgress: true,
                      showTrends: true,
                      layout: 'compact'
                    }}
                  />
                </CardContent>
              </Card>

              {/* 资源关联 */}
              <Card>
                <CardHeader>
                  <CardTitle class="flex items-center gap-2">
                    <span>📎</span>
                    资源关联
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResourceAssociation
                    entityType="project"
                    entityId={projectId}
                    dataSource={resourceDataSource}
                    config={{
                      enableMarkdownAssociation: true,
                      enableLinkManagement: true,
                      enableFileUpload: true,
                      layout: 'compact',
                      showResourcePreview: true,
                      allowCreate: true,
                      allowEdit: true
                    }}
                  />
                </CardContent>
              </Card>

              {/* 笔记功能 */}
              <Card>
                <CardHeader>
                  <CardTitle class="flex items-center gap-2">
                    <span>📝</span>
                    项目笔记
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Show when={!isEditingNotes()}>
                    <div class="space-y-3">
                      <Show when={notes()} fallback={
                        <p class="text-muted-foreground text-sm">暂无笔记</p>
                      }>
                        <div class="prose prose-sm max-w-none">
                          <pre class="whitespace-pre-wrap text-sm">{notes()}</pre>
                        </div>
                      </Show>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsEditingNotes(true)}
                      >
                        {notes() ? '编辑笔记' : '添加笔记'}
                      </Button>
                    </div>
                  </Show>

                  <Show when={isEditingNotes()}>
                    <div class="space-y-3">
                      <textarea
                        class="w-full h-32 p-3 border rounded-md resize-none"
                        placeholder="在这里记录项目相关的笔记..."
                        value={notes()}
                        onInput={(e) => setNotes(e.currentTarget.value)}
                      />
                      <div class="flex gap-2">
                        <Button size="sm" onClick={saveNotes}>
                          保存
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setIsEditingNotes(false)}
                        >
                          取消
                        </Button>
                      </div>
                    </div>
                  </Show>
                </CardContent>
              </Card>
            </div>
          </div>


                </div>

                {/* 右侧：时间信息和统计 */}
                <div class="space-y-6">
                  {/* 时间信息 */}
                  <Card>
                    <CardHeader>
                      <CardTitle>时间信息</CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-3">
                      <Show when={project()?.startDate}>
                        <div class="flex justify-between">
                          <span class="text-sm text-muted-foreground">开始时间</span>
                          <span class="text-sm">{formatDate(project()?.startDate)}</span>
                        </div>
                      </Show>
                      <Show when={project()?.dueDate}>
                        <div class="flex justify-between">
                          <span class="text-sm text-muted-foreground">截止时间</span>
                          <span class="text-sm">{formatDate(project()?.dueDate)}</span>
                        </div>
                      </Show>
                      <Show when={remainingDays() !== null}>
                        <div class="flex justify-between">
                          <span class="text-sm text-muted-foreground">剩余天数</span>
                          <span class={cn(
                            'text-sm font-medium',
                            remainingDays()! < 0 ? 'text-red-600' : remainingDays()! <= 7 ? 'text-yellow-600' : 'text-green-600'
                          )}>
                            {remainingDays()! < 0 ? `逾期 ${Math.abs(remainingDays()!)} 天` : `${remainingDays()} 天`}
                          </span>
                        </div>
                      </Show>
                      <div class="flex justify-between">
                        <span class="text-sm text-muted-foreground">更新时间</span>
                        <span class="text-sm">{formatDate(project()?.updatedAt)}</span>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 任务统计 */}
                  <Card>
                    <CardHeader>
                      <CardTitle>任务统计</CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-3">
                      <div class="flex justify-between">
                        <span class="text-sm text-muted-foreground">总任务数</span>
                        <span class="text-sm font-medium">{tasks().length}</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="text-sm text-muted-foreground">已完成</span>
                        <span class="text-sm font-medium text-green-600">
                          {tasks().filter(t => t.status === 'completed').length}
                        </span>
                      </div>
                      <div class="flex justify-between">
                        <span class="text-sm text-muted-foreground">进行中</span>
                        <span class="text-sm font-medium text-blue-600">
                          {tasks().filter(t => t.status === 'in_progress').length}
                        </span>
                      </div>
                      <div class="flex justify-between">
                        <span class="text-sm text-muted-foreground">待开始</span>
                        <span class="text-sm font-medium text-gray-600">
                          {tasks().filter(t => t.status === 'todo').length}
                        </span>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 项目标签 */}
                  <Show when={project()?.tags && project()!.tags.length > 0}>
                    <Card>
                      <CardHeader>
                        <CardTitle>项目标签</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div class="flex flex-wrap gap-2">
                          <For each={project()!.tags}>
                            {(tag) => (
                              <span class="px-2 py-1 bg-secondary text-secondary-foreground rounded text-xs">
                                {tag}
                              </span>
                            )}
                          </For>
                        </div>
                      </CardContent>
                    </Card>
                  </Show>
                </div>
              </div>
            </Show>

            <Show when={activeTab() === 'tasks'}>
              <div class="space-y-6">
                {/* 任务操作栏 */}
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold">项目任务</h3>
                  <Button>
                    <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    新建任务
                  </Button>
                </div>

                {/* 任务列表 */}
                <Show when={tasks().length > 0} fallback={
                  <Card>
                    <CardContent class="p-12 text-center">
                      <div class="text-muted-foreground">
                        <svg class="mx-auto h-12 w-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        <p class="text-lg font-medium">暂无任务</p>
                        <p class="text-sm">点击上方按钮创建第一个任务</p>
                      </div>
                    </CardContent>
                  </Card>
                }>
                  <div class="space-y-4">
                    <For each={tasks()}>
                      {(task) => (
                        <Card class="hover:shadow-md transition-shadow">
                          <CardContent class="p-4">
                            <div class="flex items-start justify-between">
                              <div class="flex-1 space-y-2">
                                <div class="flex items-center space-x-3">
                                  <div class="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={task.status === 'completed'}
                                      class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                      aria-label={`标记任务"${task.title}"为${task.status === 'completed' ? '未完成' : '已完成'}`}
                                    />
                                  </div>
                                  <h4 class={cn(
                                    'font-medium',
                                    task.status === 'completed' ? 'line-through text-muted-foreground' : ''
                                  )}>
                                    {task.title}
                                  </h4>
                                  <span class={cn(
                                    'px-2 py-1 rounded-full text-xs font-medium',
                                    task.status === 'completed' ? 'bg-green-100 text-green-700' :
                                    task.status === 'in_progress' ? 'bg-blue-100 text-blue-700' :
                                    'bg-gray-100 text-gray-700'
                                  )}>
                                    {task.status === 'completed' ? '已完成' :
                                     task.status === 'in_progress' ? '进行中' : '待开始'}
                                  </span>
                                </div>
                                <Show when={task.description}>
                                  <p class="text-sm text-muted-foreground">{task.description}</p>
                                </Show>
                                <div class="flex items-center space-x-4 text-xs text-muted-foreground">
                                  <Show when={task.dueDate}>
                                    <span>截止: {formatDate(task.dueDate)}</span>
                                  </Show>
                                  <span>进度: {Math.round((task.progress || 0) * 100)}%</span>
                                </div>
                              </div>
                              <div class="flex items-center space-x-2">
                                <Button variant="ghost" size="sm">
                                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                  </svg>
                                </Button>
                                <Button variant="ghost" size="sm">
                                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                  </svg>
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )}
                    </For>
                  </div>
                </Show>
              </div>
            </Show>

            <Show when={activeTab() === 'kpi'}>
              <div class="space-y-6">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold">项目KPI指标</h3>
                  <Button variant="outline">
                    <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                    </svg>
                    配置指标
                  </Button>
                </div>

                <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {/* 进度指标 */}
                  <Card>
                    <CardHeader class="pb-3">
                      <CardTitle class="text-base">完成进度</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div class="space-y-2">
                        <div class="flex items-center justify-between">
                          <span class="text-2xl font-bold text-primary">
                            {Math.round((project()?.progress || 0) * 100)}%
                          </span>
                          <span class="text-xs text-muted-foreground">目标: 100%</span>
                        </div>
                        <div class="w-full bg-muted rounded-full h-2 relative overflow-hidden">
                          <div
                            class="bg-primary h-full rounded-full transition-all duration-300 absolute top-0 left-0"
                            classList={{
                              'w-0': (project()?.progress || 0) === 0,
                              'w-1/4': (project()?.progress || 0) > 0 && (project()?.progress || 0) <= 0.25,
                              'w-2/4': (project()?.progress || 0) > 0.25 && (project()?.progress || 0) <= 0.5,
                              'w-3/4': (project()?.progress || 0) > 0.5 && (project()?.progress || 0) <= 0.75,
                              'w-full': (project()?.progress || 0) > 0.75,
                            }}
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 任务完成率 */}
                  <Card>
                    <CardHeader class="pb-3">
                      <CardTitle class="text-base">任务完成率</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div class="space-y-2">
                        <div class="flex items-center justify-between">
                          <span class="text-2xl font-bold text-green-600">
                            {tasks().length > 0 ? Math.round((tasks().filter(t => t.status === 'completed').length / tasks().length) * 100) : 0}%
                          </span>
                          <span class="text-xs text-muted-foreground">
                            {tasks().filter(t => t.status === 'completed').length}/{tasks().length}
                          </span>
                        </div>
                        <div class="w-full bg-muted rounded-full h-2 relative overflow-hidden">
                          <div
                            class="bg-green-500 h-full rounded-full transition-all duration-300 absolute top-0 left-0"
                            classList={{
                              'w-0': tasks().length === 0 || tasks().filter(t => t.status === 'completed').length === 0,
                              'w-1/4': tasks().length > 0 && (tasks().filter(t => t.status === 'completed').length / tasks().length) <= 0.25,
                              'w-2/4': tasks().length > 0 && (tasks().filter(t => t.status === 'completed').length / tasks().length) > 0.25 && (tasks().filter(t => t.status === 'completed').length / tasks().length) <= 0.5,
                              'w-3/4': tasks().length > 0 && (tasks().filter(t => t.status === 'completed').length / tasks().length) > 0.5 && (tasks().filter(t => t.status === 'completed').length / tasks().length) <= 0.75,
                              'w-full': tasks().length > 0 && (tasks().filter(t => t.status === 'completed').length / tasks().length) > 0.75,
                            }}
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 时间效率 */}
                  <Card>
                    <CardHeader class="pb-3">
                      <CardTitle class="text-base">时间效率</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div class="space-y-2">
                        <Show when={project()?.startDate && project()?.dueDate} fallback={
                          <div class="text-center text-muted-foreground">
                            <span class="text-sm">暂无时间数据</span>
                          </div>
                        }>
                          <div class="flex items-center justify-between">
                            <span class="text-2xl font-bold text-blue-600">
                              {(() => {
                                const start = new Date(project()!.startDate!);
                                const due = new Date(project()!.dueDate!);
                                const now = new Date();
                                const totalDays = Math.ceil((due.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
                                const usedDays = Math.ceil((now.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
                                const efficiency = totalDays > 0 ? Math.round(((project()?.progress || 0) * 100) / (usedDays / totalDays * 100)) : 0;
                                return Math.max(0, Math.min(200, efficiency));
                              })()}%
                            </span>
                            <span class="text-xs text-muted-foreground">效率指数</span>
                          </div>
                          <div class="text-xs text-muted-foreground">
                            进度与时间消耗的比率
                          </div>
                        </Show>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 质量指标 */}
                  <Card>
                    <CardHeader class="pb-3">
                      <CardTitle class="text-base">质量评分</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div class="space-y-2">
                        <div class="flex items-center justify-between">
                          <span class="text-2xl font-bold text-purple-600">8.5</span>
                          <span class="text-xs text-muted-foreground">满分: 10</span>
                        </div>
                        <div class="flex space-x-1">
                          <For each={Array(5).fill(0)}>
                            {(_, index) => (
                              <svg
                                class={cn(
                                  "h-4 w-4",
                                  index() < 4 ? "text-yellow-400 fill-current" : "text-gray-300"
                                )}
                                viewBox="0 0 20 20"
                              >
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            )}
                          </For>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 风险指标 */}
                  <Card>
                    <CardHeader class="pb-3">
                      <CardTitle class="text-base">风险等级</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div class="space-y-2">
                        <div class="flex items-center justify-between">
                          <span class={cn(
                            "text-2xl font-bold",
                            remainingDays() !== null && remainingDays()! < 0 ? "text-red-600" :
                            remainingDays() !== null && remainingDays()! <= 7 ? "text-yellow-600" : "text-green-600"
                          )}>
                            {remainingDays() !== null && remainingDays()! < 0 ? "高" :
                             remainingDays() !== null && remainingDays()! <= 7 ? "中" : "低"}
                          </span>
                          <span class="text-xs text-muted-foreground">风险评估</span>
                        </div>
                        <div class="text-xs text-muted-foreground">
                          {remainingDays() !== null && remainingDays()! < 0 ? "项目已逾期" :
                           remainingDays() !== null && remainingDays()! <= 7 ? "临近截止日期" : "进展正常"}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 团队效率 */}
                  <Card>
                    <CardHeader class="pb-3">
                      <CardTitle class="text-base">团队效率</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div class="space-y-2">
                        <div class="flex items-center justify-between">
                          <span class="text-2xl font-bold text-indigo-600">92%</span>
                          <span class="text-xs text-muted-foreground">本周</span>
                        </div>
                        <div class="text-xs text-muted-foreground">
                          基于任务完成速度计算
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* KPI趋势图表区域 */}
                <Card>
                  <CardHeader>
                    <CardTitle>KPI趋势</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div class="h-64 flex items-center justify-center text-muted-foreground">
                      <div class="text-center">
                        <svg class="mx-auto h-12 w-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        <p>图表组件开发中...</p>
                        <p class="text-sm">将显示项目KPI的历史趋势</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </Show>

            <Show when={activeTab() === 'resources'}>
              <div class="space-y-6">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold">项目资源</h3>
                  <Button variant="outline">
                    <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    添加资源
                  </Button>
                </div>

                <div class="grid gap-6 lg:grid-cols-2">
                  {/* 文档资源 */}
                  <Card>
                    <CardHeader>
                      <CardTitle class="flex items-center space-x-2">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <span>项目文档</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-3">
                      <div class="space-y-2">
                        <div class="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-muted/50 transition-colors">
                          <div class="flex items-center space-x-3">
                            <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <div>
                              <p class="font-medium text-sm">项目需求文档</p>
                              <p class="text-xs text-muted-foreground">更新于 2天前</p>
                            </div>
                          </div>
                          <Button variant="ghost" size="sm">
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                          </Button>
                        </div>
                        <div class="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-muted/50 transition-colors">
                          <div class="flex items-center space-x-3">
                            <svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <div>
                              <p class="font-medium text-sm">技术设计方案</p>
                              <p class="text-xs text-muted-foreground">更新于 1周前</p>
                            </div>
                          </div>
                          <Button variant="ghost" size="sm">
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                          </Button>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" class="w-full">
                        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        添加文档
                      </Button>
                    </CardContent>
                  </Card>

                  {/* 相关链接 */}
                  <Card>
                    <CardHeader>
                      <CardTitle class="flex items-center space-x-2">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                        </svg>
                        <span>相关链接</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-3">
                      <div class="space-y-2">
                        <div class="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-muted/50 transition-colors">
                          <div class="flex items-center space-x-3">
                            <svg class="h-4 w-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                            </svg>
                            <div>
                              <p class="font-medium text-sm">GitHub仓库</p>
                              <p class="text-xs text-muted-foreground">代码托管平台</p>
                            </div>
                          </div>
                          <Button variant="ghost" size="sm">
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                          </Button>
                        </div>
                        <div class="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-muted/50 transition-colors">
                          <div class="flex items-center space-x-3">
                            <svg class="h-4 w-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                            </svg>
                            <div>
                              <p class="font-medium text-sm">测试环境</p>
                              <p class="text-xs text-muted-foreground">在线演示地址</p>
                            </div>
                          </div>
                          <Button variant="ghost" size="sm">
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                          </Button>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" class="w-full">
                        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        添加链接
                      </Button>
                    </CardContent>
                  </Card>

                  {/* 团队成员 */}
                  <Card>
                    <CardHeader>
                      <CardTitle class="flex items-center space-x-2">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                        </svg>
                        <span>团队成员</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-3">
                      <div class="space-y-2">
                        <div class="flex items-center justify-between p-3 border border-border rounded-lg">
                          <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-medium">
                              张
                            </div>
                            <div>
                              <p class="font-medium text-sm">张三</p>
                              <p class="text-xs text-muted-foreground">项目经理</p>
                            </div>
                          </div>
                          <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">负责人</span>
                        </div>
                        <div class="flex items-center justify-between p-3 border border-border rounded-lg">
                          <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                              李
                            </div>
                            <div>
                              <p class="font-medium text-sm">李四</p>
                              <p class="text-xs text-muted-foreground">前端开发</p>
                            </div>
                          </div>
                          <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">开发者</span>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" class="w-full">
                        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        邀请成员
                      </Button>
                    </CardContent>
                  </Card>

                  {/* 工具集成 */}
                  <Card>
                    <CardHeader>
                      <CardTitle class="flex items-center space-x-2">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                        </svg>
                        <span>工具集成</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-3">
                      <div class="space-y-2">
                        <div class="flex items-center justify-between p-3 border border-border rounded-lg">
                          <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 bg-red-500 rounded flex items-center justify-center">
                              <svg class="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 0C5.374 0 0 5.373 0 12 0 17.302 3.438 21.8 8.207 23.387c.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z"/>
                              </svg>
                            </div>
                            <div>
                              <p class="font-medium text-sm">GitHub</p>
                              <p class="text-xs text-muted-foreground">已连接</p>
                            </div>
                          </div>
                          <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">已连接</span>
                        </div>
                        <div class="flex items-center justify-between p-3 border border-border rounded-lg">
                          <div class="flex items-center space-x-3">
                            <div class="h-8 w-8 bg-blue-600 rounded flex items-center justify-center">
                              <svg class="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M21 8.29l-1.95-1.95c-.78-.78-2.05-.78-2.83 0L12 10.59 7.78 6.37c-.78-.78-2.05-.78-2.83 0L3 8.32c-.78.78-.78 2.05 0 2.83L7.22 15.4c.78.78 2.05.78 2.83 0L12 13.45l2.95 2.95c.78.78 2.05.78 2.83 0L21 12.17c.78-.78.78-2.05 0-2.83z"/>
                              </svg>
                            </div>
                            <div>
                              <p class="font-medium text-sm">Slack</p>
                              <p class="text-xs text-muted-foreground">未连接</p>
                            </div>
                          </div>
                          <Button variant="outline" size="sm">连接</Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </Show>
          </div>
        </div>
      </Show>
    </PageContainer>
  );
}
