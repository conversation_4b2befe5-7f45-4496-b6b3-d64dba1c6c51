# PaoLife 测试指南

## 📋 概述

本文档提供了PaoLife项目的完整测试策略和实践指南，包括单元测试、集成测试、端到端测试的编写和执行方法。

## 🧪 测试策略

### 1. 测试金字塔

```
    /\
   /  \     E2E Tests (10%)
  /____\    - 用户流程测试
 /      \   - 关键业务场景
/__________\ Integration Tests (20%)
/          \ - API集成测试
/____________\ - 组件集成测试
/            \
/______________\ Unit Tests (70%)
                 - 函数单元测试
                 - 组件单元测试
```

### 2. 测试覆盖率目标

- **整体覆盖率**: 最低80%，目标90%
- **关键业务逻辑**: 95%以上
- **API接口**: 100%
- **工具函数**: 95%以上

### 3. 测试分类

#### 按测试层级
- **单元测试**: 测试单个函数、方法或组件
- **集成测试**: 测试模块间的交互
- **端到端测试**: 测试完整的用户流程

#### 按测试类型
- **功能测试**: 验证功能是否正确实现
- **性能测试**: 验证性能指标
- **安全测试**: 验证安全性
- **兼容性测试**: 验证跨平台兼容性

## 🦀 Rust 后端测试

### 1. 单元测试

#### 基本测试结构
```rust
// src/domain/entities/user.rs
#[cfg(test)]
mod tests {
    use super::*;
    use crate::shared::errors::AppError;

    #[test]
    fn test_user_creation_with_valid_data() {
        let user_data = CreateUserData {
            username: "testuser".to_string(),
            email: "<EMAIL>".to_string(),
            password: "password123".to_string(),
            full_name: Some("Test User".to_string()),
        };

        let result = User::new(user_data);
        assert!(result.is_ok());
        
        let user = result.unwrap();
        assert_eq!(user.username, "testuser");
        assert_eq!(user.email, Some("<EMAIL>".to_string()));
        assert!(!user.password_hash.is_empty());
    }

    #[test]
    fn test_user_creation_with_invalid_email() {
        let user_data = CreateUserData {
            username: "testuser".to_string(),
            email: "invalid-email".to_string(),
            password: "password123".to_string(),
            full_name: None,
        };

        let result = User::new(user_data);
        assert!(result.is_err());
        
        match result.unwrap_err() {
            AppError::Validation(msg) => assert!(msg.contains("email")),
            _ => panic!("Expected validation error"),
        }
    }

    #[test]
    fn test_password_verification() {
        let user = create_test_user();
        let password = "password123";
        
        assert!(user.verify_password(password));
        assert!(!user.verify_password("wrong_password"));
    }

    // 测试辅助函数
    fn create_test_user() -> User {
        let user_data = CreateUserData {
            username: "testuser".to_string(),
            email: "<EMAIL>".to_string(),
            password: "password123".to_string(),
            full_name: Some("Test User".to_string()),
        };
        User::new(user_data).unwrap()
    }
}
```

#### 异步测试
```rust
// src/application/services/user_service.rs
#[cfg(test)]
mod tests {
    use super::*;
    use crate::infrastructure::database::test_utils::setup_test_db;
    use crate::infrastructure::repositories::user_repository::UserRepository;

    #[tokio::test]
    async fn test_create_user_success() {
        let pool = setup_test_db().await;
        let repository = UserRepository::new(pool.clone());
        let service = UserService::new(repository);

        let user_data = CreateUserData {
            username: "newuser".to_string(),
            email: "<EMAIL>".to_string(),
            password: "password123".to_string(),
            full_name: Some("New User".to_string()),
        };

        let result = service.create_user(user_data).await;
        assert!(result.is_ok());

        let user = result.unwrap();
        assert_eq!(user.username, "newuser");
        assert!(user.id.len() > 0);
    }

    #[tokio::test]
    async fn test_create_user_duplicate_username() {
        let pool = setup_test_db().await;
        let repository = UserRepository::new(pool.clone());
        let service = UserService::new(repository);

        // 创建第一个用户
        let user_data1 = CreateUserData {
            username: "duplicate".to_string(),
            email: "<EMAIL>".to_string(),
            password: "password123".to_string(),
            full_name: None,
        };
        service.create_user(user_data1).await.unwrap();

        // 尝试创建重复用户名的用户
        let user_data2 = CreateUserData {
            username: "duplicate".to_string(),
            email: "<EMAIL>".to_string(),
            password: "password123".to_string(),
            full_name: None,
        };

        let result = service.create_user(user_data2).await;
        assert!(result.is_err());
        
        match result.unwrap_err() {
            AppError::Conflict(msg) => assert!(msg.contains("username")),
            _ => panic!("Expected conflict error"),
        }
    }
}
```

### 2. 集成测试

#### 数据库集成测试
```rust
// tests/integration/database_tests.rs
use paolife::infrastructure::database::{create_pool, configure_database};
use paolife::infrastructure::repositories::user_repository::UserRepository;
use sqlx::SqlitePool;
use tempfile::NamedTempFile;

async fn setup_test_database() -> SqlitePool {
    let temp_file = NamedTempFile::new().unwrap();
    let database_url = format!("sqlite:{}", temp_file.path().display());
    
    let pool = create_pool(&database_url).await.unwrap();
    configure_database(&pool).await.unwrap();
    
    // 运行迁移
    sqlx::migrate!("./migrations")
        .run(&pool)
        .await
        .unwrap();
    
    pool
}

#[tokio::test]
async fn test_user_repository_crud_operations() {
    let pool = setup_test_database().await;
    let repository = UserRepository::new(pool);

    // 测试创建
    let user_data = CreateUserData {
        username: "testuser".to_string(),
        email: "<EMAIL>".to_string(),
        password_hash: "hashed_password".to_string(),
        full_name: Some("Test User".to_string()),
    };

    let created_user = repository.create(user_data).await.unwrap();
    assert!(!created_user.id.is_empty());

    // 测试查询
    let found_user = repository.find_by_id(&created_user.id).await.unwrap();
    assert!(found_user.is_some());
    assert_eq!(found_user.unwrap().username, "testuser");

    // 测试更新
    let update_data = UpdateUserData {
        full_name: Some("Updated Name".to_string()),
        ..Default::default()
    };
    let updated_user = repository.update(&created_user.id, update_data).await.unwrap();
    assert_eq!(updated_user.full_name, Some("Updated Name".to_string()));

    // 测试删除
    let deleted = repository.delete(&created_user.id).await.unwrap();
    assert!(deleted);

    let not_found = repository.find_by_id(&created_user.id).await.unwrap();
    assert!(not_found.is_none());
}
```

#### API集成测试
```rust
// tests/integration/api_tests.rs
use paolife::api::commands::user_commands::*;
use paolife::infrastructure::database::test_utils::setup_test_db;
use tauri::test::{mock_app, MockRuntime};

#[tokio::test]
async fn test_create_user_command() {
    let pool = setup_test_db().await;
    let app = mock_app();
    
    let request = CreateUserRequest {
        username: "testuser".to_string(),
        email: "<EMAIL>".to_string(),
        password: "password123".to_string(),
        full_name: Some("Test User".to_string()),
    };

    let result = create_user_command(app.state(), request).await;
    assert!(result.is_ok());

    let response = result.unwrap();
    assert!(response.success);
    assert!(response.data.is_some());
    
    let user = response.data.unwrap();
    assert_eq!(user.username, "testuser");
}
```

### 3. 性能测试

#### 基准测试
```rust
// benches/user_operations.rs
use criterion::{black_box, criterion_group, criterion_main, Criterion};
use paolife::domain::entities::user::User;
use paolife::domain::entities::user::CreateUserData;

fn benchmark_user_creation(c: &mut Criterion) {
    c.bench_function("user_creation", |b| {
        b.iter(|| {
            let user_data = CreateUserData {
                username: black_box("testuser".to_string()),
                email: black_box("<EMAIL>".to_string()),
                password: black_box("password123".to_string()),
                full_name: Some(black_box("Test User".to_string())),
            };
            User::new(user_data)
        })
    });
}

fn benchmark_password_verification(c: &mut Criterion) {
    let user_data = CreateUserData {
        username: "testuser".to_string(),
        email: "<EMAIL>".to_string(),
        password: "password123".to_string(),
        full_name: Some("Test User".to_string()),
    };
    let user = User::new(user_data).unwrap();

    c.bench_function("password_verification", |b| {
        b.iter(|| {
            user.verify_password(black_box("password123"))
        })
    });
}

criterion_group!(benches, benchmark_user_creation, benchmark_password_verification);
criterion_main!(benches);
```

## ⚡ 前端测试

### 1. 组件单元测试

#### 基础组件测试
```typescript
// src/components/ui/Button.test.tsx
import { render, screen, fireEvent } from '@solidjs/testing-library';
import { describe, it, expect, vi } from 'vitest';
import Button from './Button';

describe('Button', () => {
  it('renders with correct text', () => {
    render(() => <Button>Click me</Button>);
    expect(screen.getByRole('button')).toHaveTextContent('Click me');
  });

  it('applies correct variant classes', () => {
    render(() => <Button variant="secondary">Secondary</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-secondary');
  });

  it('handles click events', () => {
    const handleClick = vi.fn();
    render(() => <Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('shows loading state', () => {
    render(() => <Button loading>Loading</Button>);
    const button = screen.getByRole('button');
    
    expect(button).toBeDisabled();
    expect(button).toHaveClass('cursor-not-allowed');
    expect(screen.getByRole('button')).toContainHTML('animate-spin');
  });

  it('is disabled when disabled prop is true', () => {
    render(() => <Button disabled>Disabled</Button>);
    expect(screen.getByRole('button')).toBeDisabled();
  });
});
```

#### 复杂组件测试
```typescript
// src/components/ProjectCard.test.tsx
import { render, screen, fireEvent, waitFor } from '@solidjs/testing-library';
import { describe, it, expect, vi } from 'vitest';
import ProjectCard from './ProjectCard';
import { Project } from '../types/project';

const mockProject: Project = {
  id: 'project-1',
  name: 'Test Project',
  description: 'Test Description',
  status: 'in_progress',
  progress: 65,
  deadline: '2024-12-31',
  tasksCount: 10,
  completedTasksCount: 6,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
};

describe('ProjectCard', () => {
  it('displays project information correctly', () => {
    render(() => <ProjectCard project={mockProject} />);
    
    expect(screen.getByText('Test Project')).toBeInTheDocument();
    expect(screen.getByText('Test Description')).toBeInTheDocument();
    expect(screen.getByText('65%')).toBeInTheDocument();
    expect(screen.getByText('6/10 tasks completed')).toBeInTheDocument();
  });

  it('shows correct status badge', () => {
    render(() => <ProjectCard project={mockProject} />);
    
    const statusBadge = screen.getByText('In Progress');
    expect(statusBadge).toBeInTheDocument();
    expect(statusBadge).toHaveClass('bg-blue-100');
  });

  it('calls onEdit when edit button is clicked', async () => {
    const onEdit = vi.fn();
    render(() => <ProjectCard project={mockProject} onEdit={onEdit} />);
    
    const editButton = screen.getByLabelText('Edit project');
    fireEvent.click(editButton);
    
    await waitFor(() => {
      expect(onEdit).toHaveBeenCalledWith(mockProject);
    });
  });

  it('shows overdue indicator when deadline is passed', () => {
    const overdueProject = {
      ...mockProject,
      deadline: '2023-12-31',
    };
    
    render(() => <ProjectCard project={overdueProject} />);
    expect(screen.getByText('Overdue')).toBeInTheDocument();
  });
});
```

### 2. Store 测试

#### 状态管理测试
```typescript
// src/stores/userStore.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { userStore } from './userStore';
import { userService } from '../services/userService';

// Mock userService
vi.mock('../services/userService', () => ({
  userService: {
    login: vi.fn(),
    logout: vi.fn(),
    getUsers: vi.fn(),
  },
}));

describe('userStore', () => {
  beforeEach(() => {
    // Reset store state
    userStore.clearError();
    vi.clearAllMocks();
  });

  it('should login successfully', async () => {
    const mockUser = {
      id: 'user-1',
      username: 'testuser',
      email: '<EMAIL>',
    };

    vi.mocked(userService.login).mockResolvedValue(mockUser);

    const credentials = {
      username: 'testuser',
      password: 'password123',
    };

    const result = await userStore.login(credentials);

    expect(userService.login).toHaveBeenCalledWith(credentials);
    expect(result).toEqual(mockUser);
    expect(userStore.state.currentUser).toEqual(mockUser);
    expect(userStore.isAuthenticated).toBe(true);
    expect(userStore.state.loading).toBe(false);
  });

  it('should handle login error', async () => {
    const error = new Error('Invalid credentials');
    vi.mocked(userService.login).mockRejectedValue(error);

    const credentials = {
      username: 'testuser',
      password: 'wrongpassword',
    };

    await expect(userStore.login(credentials)).rejects.toThrow('Invalid credentials');
    
    expect(userStore.state.currentUser).toBeNull();
    expect(userStore.isAuthenticated).toBe(false);
    expect(userStore.state.error).toBe('Invalid credentials');
    expect(userStore.state.loading).toBe(false);
  });

  it('should logout successfully', async () => {
    // Set initial authenticated state
    userStore.state.currentUser = { id: 'user-1', username: 'testuser' };
    
    vi.mocked(userService.logout).mockResolvedValue(undefined);

    await userStore.logout();

    expect(userService.logout).toHaveBeenCalled();
    expect(userStore.state.currentUser).toBeNull();
    expect(userStore.isAuthenticated).toBe(false);
  });
});
```

### 3. 端到端测试

#### Playwright 配置
```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:1420',
    trace: 'on-first-retry',
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],

  webServer: {
    command: 'pnpm tauri dev',
    url: 'http://localhost:1420',
    reuseExistingServer: !process.env.CI,
  },
});
```

#### E2E 测试用例
```typescript
// e2e/project-management.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Project Management', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    // 假设已经登录
    await page.waitForSelector('[data-testid="dashboard"]');
  });

  test('should create a new project', async ({ page }) => {
    // 导航到项目页面
    await page.click('[data-testid="nav-projects"]');
    await page.waitForSelector('[data-testid="projects-page"]');

    // 点击创建项目按钮
    await page.click('[data-testid="create-project-btn"]');
    await page.waitForSelector('[data-testid="create-project-modal"]');

    // 填写项目信息
    await page.fill('[data-testid="project-name-input"]', 'E2E Test Project');
    await page.fill('[data-testid="project-description-input"]', 'This is a test project');
    await page.selectOption('[data-testid="project-status-select"]', 'not_started');

    // 提交表单
    await page.click('[data-testid="submit-project-btn"]');

    // 验证项目创建成功
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('text=E2E Test Project')).toBeVisible();
  });

  test('should edit project details', async ({ page }) => {
    // 假设已有项目存在
    await page.goto('/projects');
    
    // 点击第一个项目的编辑按钮
    await page.click('[data-testid="project-card"]:first-child [data-testid="edit-btn"]');
    await page.waitForSelector('[data-testid="edit-project-modal"]');

    // 修改项目名称
    await page.fill('[data-testid="project-name-input"]', 'Updated Project Name');
    
    // 保存更改
    await page.click('[data-testid="save-project-btn"]');

    // 验证更改已保存
    await expect(page.locator('text=Updated Project Name')).toBeVisible();
  });

  test('should complete project workflow', async ({ page }) => {
    // 创建项目
    await page.goto('/projects');
    await page.click('[data-testid="create-project-btn"]');
    await page.fill('[data-testid="project-name-input"]', 'Workflow Test Project');
    await page.click('[data-testid="submit-project-btn"]');

    // 进入项目详情
    await page.click('text=Workflow Test Project');
    await page.waitForSelector('[data-testid="project-detail"]');

    // 添加任务
    await page.click('[data-testid="add-task-btn"]');
    await page.fill('[data-testid="task-title-input"]', 'Test Task 1');
    await page.click('[data-testid="submit-task-btn"]');

    // 完成任务
    await page.click('[data-testid="task-checkbox"]');
    await expect(page.locator('[data-testid="task-completed"]')).toBeVisible();

    // 更新项目状态
    await page.click('[data-testid="project-status-btn"]');
    await page.selectOption('[data-testid="status-select"]', 'completed');
    await page.click('[data-testid="update-status-btn"]');

    // 验证项目已完成
    await expect(page.locator('[data-testid="project-status"]')).toHaveText('Completed');
  });
});
```

## 🔧 测试工具配置

### 1. Vitest 配置
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import solid from 'vite-plugin-solid';

export default defineConfig({
  plugins: [solid()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
      ],
    },
  },
});
```

### 2. 测试工具函数
```typescript
// src/test/utils.tsx
import { render } from '@solidjs/testing-library';
import { Router } from '@solidjs/router';
import { AppProvider } from '../contexts/AppContext';

export function renderWithProviders(ui: () => JSX.Element) {
  return render(() => (
    <AppProvider>
      <Router>
        {ui()}
      </Router>
    </AppProvider>
  ));
}

export function createMockProject(overrides = {}): Project {
  return {
    id: 'mock-project-1',
    name: 'Mock Project',
    description: 'Mock Description',
    status: 'in_progress',
    progress: 50,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    ...overrides,
  };
}
```

---

**文档版本**: 1.0  
**创建日期**: 2024年12月  
**维护者**: 开发团队  
**下次更新**: 根据测试策略演进需要
