/**
 * 资源关联工具函数
 * 提供资源处理、验证、格式化等实用功能
 */

import type { 
  UnifiedResource, 
  ResourceType,
  ResourceStatistics,
  BidirectionalLink
} from './types'

/**
 * 获取资源类型图标
 */
export function getResourceIcon(resource: UnifiedResource): string {
  switch (resource.type) {
    case 'markdown':
      return 'FileText'
    case 'link':
      return 'ExternalLink'
    case 'file':
    case 'attachment':
      const fileResource = resource as any
      if (fileResource.mimeType?.startsWith('image/')) return 'Image'
      if (fileResource.mimeType?.startsWith('video/')) return 'Video'
      if (fileResource.mimeType?.startsWith('audio/')) return 'Music'
      if (fileResource.mimeType?.includes('zip') || fileResource.mimeType?.includes('archive')) return 'Archive'
      return 'File'
    case 'reference':
      return 'Link'
    default:
      return 'FileText'
  }
}

/**
 * 获取资源类型颜色
 */
export function getResourceTypeColor(type: ResourceType): string {
  switch (type) {
    case 'markdown':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'link':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'file':
    case 'attachment':
      return 'bg-purple-100 text-purple-800 border-purple-200'
    case 'reference':
      return 'bg-orange-100 text-orange-800 border-orange-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

/**
 * 格式化日期显示
 */
export function formatDate(date: Date, format: 'short' | 'medium' | 'long' = 'medium'): string {
  const options: Intl.DateTimeFormatOptions = {
    short: { month: 'short', day: 'numeric' },
    medium: { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' },
    long: { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' }
  }
  
  return new Intl.DateTimeFormat('en-US', options[format]).format(date)
}

/**
 * 验证URL格式
 */
export function validateUrl(url: string): boolean {
  const urlPattern = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/
  return urlPattern.test(url)
}

/**
 * 提取域名
 */
export function extractDomain(url: string): string {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname.replace('www.', '')
  } catch {
    return ''
  }
}

/**
 * 生成资源ID
 */
export function generateResourceId(type: ResourceType, entityId: string): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 9)
  return `${type}_${entityId}_${timestamp}_${random}`
}

/**
 * 计算资源统计信息
 */
export function calculateResourceStats(resources: UnifiedResource[]): ResourceStatistics {
  const stats: ResourceStatistics = {
    total: resources.length,
    byType: {
      markdown: 0,
      link: 0,
      file: 0,
      attachment: 0,
      reference: 0
    },
    byEntity: {
      project: 0,
      area: 0,
      task: 0,
      habit: 0,
      note: 0
    },
    totalSize: 0,
    recentlyAdded: 0,
    recentlyAccessed: 0,
    orphaned: 0,
    broken: 0
  }
  
  const now = new Date()
  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
  
  resources.forEach(resource => {
    // 按类型统计
    stats.byType[resource.type]++
    
    // 按实体类型统计
    if (resource.entityType) {
      stats.byEntity[resource.entityType]++
    }
    
    // 文件大小统计
    if (resource.type === 'file' || resource.type === 'attachment') {
      const fileResource = resource as any
      stats.totalSize += fileResource.fileSize || 0
    }
    
    // 最近添加
    if (resource.createdAt > oneDayAgo) {
      stats.recentlyAdded++
    }
    
    // 最近访问
    if (resource.lastAccessedAt && resource.lastAccessedAt > oneDayAgo) {
      stats.recentlyAccessed++
    }
    
    // 孤立资源
    if (resource.tags.length === 0 && !resource.description && resource.accessCount === 0) {
      stats.orphaned++
    }
    
    // 损坏的链接
    if (resource.type === 'link') {
      const linkResource = resource as any
      if (linkResource.isValidUrl === false) {
        stats.broken++
      }
    }
  })
  
  return stats
}

/**
 * 检查文件类型是否被允许
 */
export function isFileTypeAllowed(file: File, allowedTypes: string[]): boolean {
  if (allowedTypes.includes('*')) return true
  
  return allowedTypes.some(type => {
    if (type.startsWith('.')) {
      return file.name.toLowerCase().endsWith(type.toLowerCase())
    }
    if (type.includes('/')) {
      return file.type === type
    }
    if (type.includes('*')) {
      const baseType = type.split('/')[0]
      return file.type.startsWith(baseType + '/')
    }
    return false
  })
}

/**
 * 获取文件扩展名
 */
export function getFileExtension(fileName: string): string {
  const lastDot = fileName.lastIndexOf('.')
  return lastDot > 0 ? fileName.substring(lastDot + 1).toLowerCase() : ''
}

/**
 * 检查是否为图片文件
 */
export function isImageFile(file: File): boolean {
  return file.type.startsWith('image/')
}

/**
 * 检查是否为视频文件
 */
export function isVideoFile(file: File): boolean {
  return file.type.startsWith('video/')
}

/**
 * 检查是否为音频文件
 */
export function isAudioFile(file: File): boolean {
  return file.type.startsWith('audio/')
}

/**
 * 检查是否为文档文件
 */
export function isDocumentFile(file: File): boolean {
  const documentTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/markdown',
    'text/csv'
  ]
  
  return documentTypes.includes(file.type) || file.type.startsWith('text/')
}

/**
 * 生成文件缩略图URL
 */
export function generateThumbnailUrl(filePath: string, size: number = 200): string {
  // 这里应该根据实际的缩略图服务来实现
  return `/api/thumbnails/${encodeURIComponent(filePath)}?size=${size}`
}

/**
 * 计算链接强度
 */
export function calculateLinkStrength(link: BidirectionalLink): number {
  let strength = 0.5 // 基础强度
  
  // 根据链接类型调整强度
  switch (link.linkType) {
    case 'wikilink':
      strength += 0.2
      break
    case 'reference':
      strength += 0.3
      break
    case 'embed':
      strength += 0.1
      break
  }
  
  // 根据上下文长度调整强度
  const contextLength = (link.contextBefore?.length || 0) + (link.contextAfter?.length || 0)
  if (contextLength > 100) {
    strength += 0.1
  }
  
  // 根据显示文本质量调整强度
  if (link.displayText && link.displayText !== link.linkText) {
    strength += 0.1
  }
  
  return Math.min(strength, 1.0)
}

/**
 * 搜索资源
 */
export function searchResources(
  resources: UnifiedResource[], 
  query: string, 
  options: {
    searchFields?: ('title' | 'description' | 'tags')[]
    caseSensitive?: boolean
    exactMatch?: boolean
  } = {}
): UnifiedResource[] {
  if (!query.trim()) return resources
  
  const {
    searchFields = ['title', 'description', 'tags'],
    caseSensitive = false,
    exactMatch = false
  } = options
  
  const searchQuery = caseSensitive ? query : query.toLowerCase()
  
  return resources.filter(resource => {
    return searchFields.some(field => {
      let fieldValue: string
      
      switch (field) {
        case 'title':
          fieldValue = resource.title
          break
        case 'description':
          fieldValue = resource.description || ''
          break
        case 'tags':
          fieldValue = resource.tags.join(' ')
          break
        default:
          return false
      }
      
      if (!caseSensitive) {
        fieldValue = fieldValue.toLowerCase()
      }
      
      return exactMatch 
        ? fieldValue === searchQuery
        : fieldValue.includes(searchQuery)
    })
  })
}

/**
 * 排序资源
 */
export function sortResources(
  resources: UnifiedResource[],
  sortBy: 'name' | 'date' | 'type' | 'access' | 'size',
  sortOrder: 'asc' | 'desc' = 'asc'
): UnifiedResource[] {
  const sorted = [...resources].sort((a, b) => {
    let comparison = 0
    
    switch (sortBy) {
      case 'name':
        comparison = a.title.localeCompare(b.title)
        break
      case 'date':
        comparison = a.updatedAt.getTime() - b.updatedAt.getTime()
        break
      case 'type':
        comparison = a.type.localeCompare(b.type)
        break
      case 'access':
        comparison = a.accessCount - b.accessCount
        break
      case 'size':
        const aSize = (a as any).fileSize || 0
        const bSize = (b as any).fileSize || 0
        comparison = aSize - bSize
        break
    }
    
    return sortOrder === 'desc' ? -comparison : comparison
  })
  
  return sorted
}

/**
 * 分组资源
 */
export function groupResources(
  resources: UnifiedResource[],
  groupBy: 'type' | 'date' | 'entity' | 'tags'
): Record<string, UnifiedResource[]> {
  const groups: Record<string, UnifiedResource[]> = {}
  
  resources.forEach(resource => {
    let groupKey: string
    
    switch (groupBy) {
      case 'type':
        groupKey = resource.type
        break
      case 'date':
        groupKey = resource.updatedAt.toDateString()
        break
      case 'entity':
        groupKey = resource.entityType || 'unknown'
        break
      case 'tags':
        // 为每个标签创建一个组
        resource.tags.forEach(tag => {
          if (!groups[tag]) {
            groups[tag] = []
          }
          groups[tag].push(resource)
        })
        return // 早期返回，避免重复添加
      default:
        groupKey = 'all'
    }
    
    if (!groups[groupKey]) {
      groups[groupKey] = []
    }
    groups[groupKey].push(resource)
  })
  
  return groups
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}
