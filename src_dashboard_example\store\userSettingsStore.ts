import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface UserSettings {
  username: string
  workspaceDirectory: string
  resourcePath?: string // 资源库路径
  isFirstTime: boolean
  lastLoginTime?: string
  editorMode?: 'wysiwyg' | 'ir'
  editorTheme?: 'classic' | 'dark'
  focusMode?: boolean
  autoSave?: boolean
  autoSaveInterval?: number // 自动保存间隔（秒）
  // 退出确认设置
  showExitConfirm?: boolean
}

interface UserSettingsState {
  settings: UserSettings
  isInitialized: boolean

  // Actions
  updateSettings: (settings: Partial<UserSettings>) => void
  setWorkspaceDirectory: (directory: string) => void
  setUsername: (username: string) => void
  setEditorMode: (mode: 'wysiwyg' | 'ir') => void
  setEditorTheme: (theme: 'classic' | 'dark') => void
  setFocusMode: (enabled: boolean) => void
  setAutoSave: (enabled: boolean) => void
  setAutoSaveInterval: (interval: number) => void
  setResourcePath: (path: string) => void
  setShowExitConfirm: (show: boolean) => void
  completeFirstTimeSetup: () => void
  resetSettings: () => void
}

const defaultSettings: UserSettings = {
  username: '',
  workspaceDirectory: '',
  isFirstTime: true,
  editorMode: 'ir', // 默认使用即时渲染模式
  editorTheme: 'dark', // 默认使用深色主题
  focusMode: false, // 默认关闭专注模式
  autoSave: true, // 默认开启自动保存
  autoSaveInterval: 30, // 默认30秒自动保存
  showExitConfirm: true // 默认显示退出确认
}

export const useUserSettingsStore = create<UserSettingsState>()(
  persist(
    (set) => ({
      settings: defaultSettings,
      isInitialized: false,

      updateSettings: (newSettings) => {
        set((state) => ({
          settings: { ...state.settings, ...newSettings }
        }))
      },

      setWorkspaceDirectory: (directory) => {
        set((state) => ({
          settings: { ...state.settings, workspaceDirectory: directory }
        }))
      },

      setUsername: (username) => {
        set((state) => ({
          settings: { ...state.settings, username }
        }))
      },

      setEditorMode: (mode) => {
        set((state) => ({
          settings: { ...state.settings, editorMode: mode }
        }))
      },

      setEditorTheme: (theme) => {
        set((state) => ({
          settings: { ...state.settings, editorTheme: theme }
        }))
      },

      setFocusMode: (enabled) => {
        set((state) => ({
          settings: { ...state.settings, focusMode: enabled }
        }))
      },

      setAutoSave: (enabled) => {
        set((state) => ({
          settings: { ...state.settings, autoSave: enabled }
        }))
      },

      setAutoSaveInterval: (interval) => {
        set((state) => ({
          settings: { ...state.settings, autoSaveInterval: interval }
        }))
      },

      setResourcePath: (path) => {
        set((state) => ({
          settings: { ...state.settings, resourcePath: path }
        }))
      },

      setShowExitConfirm: (show) => {
        set((state) => ({
          settings: { ...state.settings, showExitConfirm: show }
        }))
      },

      completeFirstTimeSetup: () => {
        set((state) => ({
          settings: {
            ...state.settings,
            isFirstTime: false,
            lastLoginTime: new Date().toISOString()
          },
          isInitialized: true
        }))
      },

      resetSettings: () => {
        set({
          settings: defaultSettings,
          isInitialized: false
        })
      }
    }),
    {
      name: 'user-settings',
      partialize: (state) => ({ settings: state.settings })
    }
  )
)
