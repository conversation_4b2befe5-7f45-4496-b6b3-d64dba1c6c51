// User DTOs - 用户数据传输对象

use crate::domain::entities::{User, UserPreferences};
use crate::shared::types::{Id, EntityStatus};
use serde::{Deserialize, Serialize};

/// 创建用户请求
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CreateUserRequest {
    pub username: String,
    pub email: Option<String>,
    pub display_name: Option<String>,
}

/// 更新用户请求
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UpdateUserRequest {
    pub display_name: Option<String>,
    pub email: Option<String>,
    pub avatar_url: Option<String>,
}

/// 更新用户偏好设置请求
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UpdateUserPreferencesRequest {
    pub theme: Option<String>,
    pub language: Option<String>,
    pub timezone: Option<String>,
    pub date_format: Option<String>,
    pub time_format: Option<String>,
    pub notifications_enabled: Option<bool>,
    pub auto_save_enabled: Option<bool>,
}

/// 用户响应
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UserResponse {
    pub id: Id,
    pub username: String,
    pub email: Option<String>,
    pub display_name: Option<String>,
    pub avatar_url: Option<String>,
    pub preferences: UserPreferencesResponse,
    pub status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub version: u64,
}

/// 用户偏好设置响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserPreferencesResponse {
    pub theme: String,
    pub language: String,
    pub timezone: String,
    pub date_format: String,
    pub time_format: String,
    pub notifications_enabled: bool,
    pub auto_save_enabled: bool,
}

/// 用户统计响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserStatisticsResponse {
    pub total_users: u64,
    pub active_users: u64,
    pub inactive_users: u64,
}

/// 用户列表响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserListResponse {
    pub users: Vec<UserResponse>,
    pub total: u64,
    pub page: u64,
    pub size: u64,
}

/// 用户名可用性响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UsernameAvailabilityResponse {
    pub available: bool,
    pub username: String,
}

/// 邮箱可用性响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailAvailabilityResponse {
    pub available: bool,
    pub email: String,
}

impl From<User> for UserResponse {
    fn from(user: User) -> Self {
        Self {
            id: user.id,
            username: user.username,
            email: user.email,
            display_name: user.display_name,
            avatar_url: user.avatar_url,
            preferences: UserPreferencesResponse::from(user.preferences),
            status: match user.entity_status {
                EntityStatus::Active => "active".to_string(),
                EntityStatus::Inactive => "inactive".to_string(),
                EntityStatus::Deleted => "deleted".to_string(),
                EntityStatus::Archived => "archived".to_string(),
            },
            created_at: user.metadata.created_at,
            updated_at: user.metadata.updated_at,
            version: user.metadata.version,
        }
    }
}

impl From<UserPreferences> for UserPreferencesResponse {
    fn from(prefs: UserPreferences) -> Self {
        Self {
            theme: prefs.theme,
            language: prefs.language,
            timezone: prefs.timezone,
            date_format: prefs.date_format,
            time_format: prefs.time_format,
            notifications_enabled: prefs.notifications_enabled,
            auto_save_enabled: prefs.auto_save_enabled,
        }
    }
}

impl From<UpdateUserPreferencesRequest> for UserPreferences {
    fn from(req: UpdateUserPreferencesRequest) -> Self {
        let mut prefs = UserPreferences::default();
        
        if let Some(theme) = req.theme {
            prefs.theme = theme;
        }
        if let Some(language) = req.language {
            prefs.language = language;
        }
        if let Some(timezone) = req.timezone {
            prefs.timezone = timezone;
        }
        if let Some(date_format) = req.date_format {
            prefs.date_format = date_format;
        }
        if let Some(time_format) = req.time_format {
            prefs.time_format = time_format;
        }
        if let Some(notifications_enabled) = req.notifications_enabled {
            prefs.notifications_enabled = notifications_enabled;
        }
        if let Some(auto_save_enabled) = req.auto_save_enabled {
            prefs.auto_save_enabled = auto_save_enabled;
        }
        
        prefs
    }
}

impl From<crate::application::services::UserStatistics> for UserStatisticsResponse {
    fn from(stats: crate::application::services::UserStatistics) -> Self {
        Self {
            total_users: stats.total_users,
            active_users: stats.active_users,
            inactive_users: stats.inactive_users,
        }
    }
}

/// 验证创建用户请求
impl CreateUserRequest {
    pub fn validate(&self) -> Result<(), String> {
        if self.username.trim().is_empty() {
            return Err("Username cannot be empty".to_string());
        }
        
        if self.username.len() < 3 {
            return Err("Username must be at least 3 characters long".to_string());
        }
        
        if self.username.len() > 50 {
            return Err("Username cannot be longer than 50 characters".to_string());
        }
        
        if let Some(ref email) = self.email {
            if !email.contains('@') {
                return Err("Invalid email format".to_string());
            }
        }
        
        Ok(())
    }
}

/// 验证更新用户请求
impl UpdateUserRequest {
    pub fn validate(&self) -> Result<(), String> {
        if let Some(ref email) = self.email {
            if !email.contains('@') {
                return Err("Invalid email format".to_string());
            }
        }
        
        if let Some(ref display_name) = self.display_name {
            if display_name.len() > 100 {
                return Err("Display name cannot be longer than 100 characters".to_string());
            }
        }
        
        Ok(())
    }
}
