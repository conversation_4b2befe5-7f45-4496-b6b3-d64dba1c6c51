/**
 * 统一KPI跟踪组件
 * 可复用的KPI指标跟踪和管理组件，支持项目KPI和领域指标
 */

import { createSignal, createEffect, onMount, onCleanup, For, Show } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Plus, Target, TrendingUp, BarChart3, Settings } from 'lucide-react'
import { cn } from '../../lib/utils'

import type {
  KPITrackerProps,
  BaseKPI,
  KPIProgress,
  KPIStatistics,
  KPIComponentConfig,
  DEFAULT_KPI_CONFIG
} from './types'

// 导入子组件
import KPIChart from './KPIChart'
import KPIInput from './KPIInput'
import KPIDialog from './KPIDialog'
import KPIStatisticsCard from './KPIStatisticsCard'

export function KPITracker(props: KPITrackerProps) {
  // 合并配置
  const config = (): KPIComponentConfig => ({
    ...DEFAULT_KPI_CONFIG,
    ...props.config
  })

  // 状态管理
  const [kpis, setKPIs] = createSignal<BaseKPI[]>(props.initialKPIs || [])
  const [statistics, setStatistics] = createSignal<KPIStatistics | null>(null)
  const [loading, setLoading] = createSignal(false)
  const [error, setError] = createSignal<string | null>(null)
  
  // 对话框状态
  const [showCreateDialog, setShowCreateDialog] = createSignal(false)
  const [editingKPI, setEditingKPI] = createSignal<BaseKPI | null>(null)
  
  // 自动刷新定时器
  let refreshTimer: number | undefined

  // 加载KPI数据
  const loadKPIs = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const [kpiData, statsData] = await Promise.all([
        props.dataSource.getKPIs(props.parentId),
        props.dataSource.getStatistics(props.parentId)
      ])
      
      setKPIs(kpiData)
      setStatistics(statsData)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load KPIs'
      setError(errorMessage)
      props.eventHandlers?.onError?.(err instanceof Error ? err : new Error(errorMessage), 'loadKPIs')
    } finally {
      setLoading(false)
    }
  }

  // 创建KPI
  const handleCreateKPI = async (data: any) => {
    try {
      const newKPI = await props.dataSource.createKPI(props.parentId, data)
      setKPIs(prev => [newKPI, ...prev])
      setShowCreateDialog(false)
      
      // 触发事件
      props.eventHandlers?.onCreate?.(newKPI)
      
      // 重新加载统计信息
      loadStatistics()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to create KPI')
      props.eventHandlers?.onError?.(error, 'createKPI')
      throw error
    }
  }

  // 更新KPI
  const handleUpdateKPI = async (id: string, data: any) => {
    try {
      const updatedKPI = await props.dataSource.updateKPI(id, data)
      setKPIs(prev => prev.map(kpi => kpi.id === id ? updatedKPI : kpi))
      setEditingKPI(null)
      
      // 触发事件
      props.eventHandlers?.onUpdate?.(updatedKPI, data)
      
      loadStatistics()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to update KPI')
      props.eventHandlers?.onError?.(error, 'updateKPI')
      throw error
    }
  }

  // 删除KPI
  const handleDeleteKPI = async (id: string) => {
    try {
      await props.dataSource.deleteKPI(id)
      setKPIs(prev => prev.filter(kpi => kpi.id !== id))
      
      // 触发事件
      props.eventHandlers?.onDelete?.(id)
      
      loadStatistics()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to delete KPI')
      props.eventHandlers?.onError?.(error, 'deleteKPI')
      throw error
    }
  }

  // 记录KPI数据
  const handleRecordKPI = async (kpiId: string, recordData: any) => {
    try {
      const record = await props.dataSource.createRecord(kpiId, recordData)
      
      // 更新KPI的当前值
      setKPIs(prev => prev.map(kpi => 
        kpi.id === kpiId 
          ? { ...kpi, value: recordData.value, updatedAt: new Date() }
          : kpi
      ))
      
      // 触发事件
      props.eventHandlers?.onRecord?.(record)
      
      loadStatistics()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to record KPI data')
      props.eventHandlers?.onError?.(error, 'recordKPI')
      throw error
    }
  }

  // 仅加载统计信息
  const loadStatistics = async () => {
    try {
      const statsData = await props.dataSource.getStatistics(props.parentId)
      setStatistics(statsData)
    } catch (err) {
      console.warn('Failed to load statistics:', err)
    }
  }

  // 设置自动刷新
  const setupAutoRefresh = () => {
    if (config().autoRefresh && config().refreshInterval) {
      refreshTimer = setInterval(loadKPIs, config().refreshInterval) as any
    }
  }

  // 清理自动刷新
  const clearAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = undefined
    }
  }

  // 生命周期
  onMount(() => {
    loadKPIs()
    setupAutoRefresh()
  })

  onCleanup(() => {
    clearAutoRefresh()
  })

  // 监听配置变化
  createEffect(() => {
    clearAutoRefresh()
    setupAutoRefresh()
  })

  // 渲染加载状态
  const renderLoading = () => (
    <Card class={cn("w-full", props.className)}>
      <CardContent class="flex items-center justify-center py-8">
        <div class="text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p class="text-muted-foreground">Loading KPIs...</p>
        </div>
      </CardContent>
    </Card>
  )

  // 渲染错误状态
  const renderError = () => (
    <Card class={cn("w-full", props.className)}>
      <CardContent class="flex items-center justify-center py-8">
        <div class="text-center">
          <div class="text-red-500 mb-4">
            <Target class="h-12 w-12 mx-auto" />
          </div>
          <h3 class="text-lg font-medium mb-2">Failed to load KPIs</h3>
          <p class="text-muted-foreground mb-4">{error()}</p>
          <Button onClick={loadKPIs}>Retry</Button>
        </div>
      </CardContent>
    </Card>
  )

  // 渲染空状态
  const renderEmpty = () => (
    <Card class={cn("w-full", props.className)}>
      <CardContent class="flex items-center justify-center py-8">
        <div class="text-center">
          <Target class="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 class="text-lg font-medium mb-2">No KPIs yet</h3>
          <p class="text-muted-foreground mb-4">
            Create your first KPI to start tracking progress
          </p>
          <Show when={config().allowCreate}>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus class="h-4 w-4 mr-2" />
              Create KPI
            </Button>
          </Show>
        </div>
      </CardContent>
    </Card>
  )

  // 主渲染
  return (
    <div class={cn("space-y-6", props.className)}>
      <Show when={loading()}>
        {renderLoading()}
      </Show>
      
      <Show when={error() && !loading()}>
        {renderError()}
      </Show>
      
      <Show when={!loading() && !error()}>
        <Show when={kpis().length === 0}>
          {renderEmpty()}
        </Show>
        
        <Show when={kpis().length > 0}>
          {/* 统计概览 */}
          <Show when={config().showStatistics && statistics()}>
            <KPIStatisticsCard statistics={statistics()!} />
          </Show>

          {/* 图表展示 */}
          <Show when={config().showChart}>
            <KPIChart 
              kpis={kpis()} 
              type={config().chartType}
              onKPIClick={(kpi) => setEditingKPI(kpi)}
            />
          </Show>

          {/* KPI列表 */}
          <Card>
            <CardHeader>
              <div class="flex items-center justify-between">
                <div>
                  <CardTitle class="flex items-center gap-2">
                    <Target class="h-5 w-5" />
                    KPI Management
                  </CardTitle>
                  <CardDescription>
                    Track and manage your key performance indicators
                  </CardDescription>
                </div>
                <Show when={config().allowCreate}>
                  <Button onClick={() => setShowCreateDialog(true)}>
                    <Plus class="h-4 w-4 mr-2" />
                    Add KPI
                  </Button>
                </Show>
              </div>
            </CardHeader>
            <CardContent>
              <div class={cn(
                "space-y-4",
                config().layout === 'grid' && "grid grid-cols-1 md:grid-cols-2 gap-4 space-y-0"
              )}>
                <For each={kpis()}>
                  {(kpi) => (
                    <KPIInput
                      kpi={kpi}
                      onRecord={(recordData) => handleRecordKPI(kpi.id, recordData)}
                      onEdit={() => setEditingKPI(kpi)}
                      onDelete={() => handleDeleteKPI(kpi.id)}
                      showHistory={config().showHistory}
                      allowEdit={config().allowEdit}
                      allowDelete={config().allowDelete}
                    />
                  )}
                </For>
              </div>
            </CardContent>
          </Card>
        </Show>
      </Show>

      {/* 创建对话框 */}
      <Show when={showCreateDialog()}>
        <KPIDialog
          open={showCreateDialog()}
          mode="create"
          config={props.kpiConfig}
          onSubmit={handleCreateKPI}
          onCancel={() => setShowCreateDialog(false)}
        />
      </Show>

      {/* 编辑对话框 */}
      <Show when={editingKPI()}>
        <KPIDialog
          open={!!editingKPI()}
          mode="edit"
          kpi={editingKPI()!}
          config={props.kpiConfig}
          onSubmit={(data) => handleUpdateKPI(editingKPI()!.id, data)}
          onCancel={() => setEditingKPI(null)}
        />
      </Show>
    </div>
  )
}

export default KPITracker
