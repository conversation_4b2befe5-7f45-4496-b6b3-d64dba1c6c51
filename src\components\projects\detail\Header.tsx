import { Show } from 'solid-js';
import { Button } from '../../ui/button';
import { DialogTitle } from '../../ui/dialog';
import { cn } from '@/lib/utils';
import type { Project } from '../../../types/business';

interface DetailHeaderProps {
  project: Project | null;
  onClose: () => void;
  getStatusLabel: (status: string) => string;
  getStatusColor: (status: string) => string;
}

export default function DetailHeader(props: DetailHeaderProps) {
  return (
    <div class="p-6 border-b border-border">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <DialogTitle class="text-xl">项目详情</DialogTitle>
          <Show when={props.project}>
            <span class={cn('px-3 py-1 rounded-full text-sm font-medium', props.getStatusColor(props.project?.status || ''))}>
              {props.getStatusLabel(props.project?.status || '')}
            </span>
          </Show>
        </div>
        <Button variant="ghost" size="sm" onClick={props.onClose} aria-label="关闭">
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </Button>
      </div>
    </div>
  );
}

