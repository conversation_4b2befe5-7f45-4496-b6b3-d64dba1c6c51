/**
 * 资源关联组件常量定义
 * 定义各种预设值、配置选项和常量
 */

import type { ResourceType, ResourceTypeColorMap } from './types'

// Markdown文件扩展名
export const MARKDOWN_FILE_EXTENSIONS = [
  '.md',
  '.markdown',
  '.mdown',
  '.mkdn',
  '.mkd',
  '.mdwn',
  '.mdtxt',
  '.mdtext'
]

// 支持的文件类型
export const SUPPORTED_FILE_TYPES = {
  // 图片文件
  images: [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
    'image/bmp',
    'image/tiff'
  ],
  
  // 视频文件
  videos: [
    'video/mp4',
    'video/avi',
    'video/mov',
    'video/wmv',
    'video/flv',
    'video/webm',
    'video/mkv',
    'video/m4v'
  ],
  
  // 音频文件
  audios: [
    'audio/mp3',
    'audio/wav',
    'audio/flac',
    'audio/aac',
    'audio/ogg',
    'audio/wma',
    'audio/m4a'
  ],
  
  // 文档文件
  documents: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/markdown',
    'text/csv',
    'text/rtf'
  ],
  
  // 压缩文件
  archives: [
    'application/zip',
    'application/x-rar-compressed',
    'application/x-7z-compressed',
    'application/x-tar',
    'application/gzip'
  ],
  
  // 代码文件
  code: [
    'text/javascript',
    'text/typescript',
    'text/html',
    'text/css',
    'text/xml',
    'application/json',
    'text/python',
    'text/java',
    'text/cpp',
    'text/csharp'
  ]
}

// 默认上传限制
export const DEFAULT_UPLOAD_LIMITS = {
  // 文件大小限制（字节）
  maxFileSize: 50 * 1024 * 1024, // 50MB
  maxTotalSize: 500 * 1024 * 1024, // 500MB
  
  // 文件数量限制
  maxFiles: 10,
  maxFilesPerUpload: 5,
  
  // 特定类型的大小限制
  imageSizeLimit: 10 * 1024 * 1024, // 10MB
  videoSizeLimit: 100 * 1024 * 1024, // 100MB
  documentSizeLimit: 20 * 1024 * 1024, // 20MB
  
  // 允许的文件类型（默认全部允许）
  allowedTypes: ['*'],
  
  // 禁止的文件类型
  blockedTypes: [
    'application/x-executable',
    'application/x-msdownload',
    'application/x-msdos-program'
  ]
}

// 链接验证模式
export const LINK_VALIDATION_PATTERNS = {
  // HTTP/HTTPS URL
  http: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  
  // FTP URL
  ftp: /^ftp:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  
  // 邮箱地址
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  
  // 电话号码
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  
  // IP地址
  ip: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  
  // 本地文件路径
  localPath: /^(\/|[A-Za-z]:\\).*$/,
  
  // Wiki链接
  wikiLink: /^\[\[([^\]]+)\]\]$/,
  
  // Markdown链接
  markdownLink: /^\[([^\]]*)\]\(([^)]+)\)$/
}

// 资源类型图标映射
export const RESOURCE_TYPE_ICONS: Record<ResourceType, string> = {
  markdown: 'FileText',
  link: 'ExternalLink',
  file: 'File',
  attachment: 'Paperclip',
  reference: 'Link'
}

// 资源类型颜色映射
export const RESOURCE_TYPE_COLORS: ResourceTypeColorMap = {
  markdown: {
    bg: 'bg-blue-100',
    text: 'text-blue-800',
    border: 'border-blue-200',
    icon: 'text-blue-600'
  },
  link: {
    bg: 'bg-green-100',
    text: 'text-green-800',
    border: 'border-green-200',
    icon: 'text-green-600'
  },
  file: {
    bg: 'bg-purple-100',
    text: 'text-purple-800',
    border: 'border-purple-200',
    icon: 'text-purple-600'
  },
  attachment: {
    bg: 'bg-orange-100',
    text: 'text-orange-800',
    border: 'border-orange-200',
    icon: 'text-orange-600'
  },
  reference: {
    bg: 'bg-gray-100',
    text: 'text-gray-800',
    border: 'border-gray-200',
    icon: 'text-gray-600'
  }
}

// MIME类型到图标的映射
export const MIME_TYPE_ICONS: Record<string, string> = {
  // 图片
  'image/jpeg': 'Image',
  'image/jpg': 'Image',
  'image/png': 'Image',
  'image/gif': 'Image',
  'image/webp': 'Image',
  'image/svg+xml': 'Image',
  
  // 视频
  'video/mp4': 'Video',
  'video/avi': 'Video',
  'video/mov': 'Video',
  'video/wmv': 'Video',
  'video/webm': 'Video',
  
  // 音频
  'audio/mp3': 'Music',
  'audio/wav': 'Music',
  'audio/flac': 'Music',
  'audio/aac': 'Music',
  'audio/ogg': 'Music',
  
  // 文档
  'application/pdf': 'FileText',
  'application/msword': 'FileText',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'FileText',
  'application/vnd.ms-excel': 'FileSpreadsheet',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'FileSpreadsheet',
  'application/vnd.ms-powerpoint': 'Presentation',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'Presentation',
  'text/plain': 'FileText',
  'text/markdown': 'FileText',
  'text/csv': 'FileSpreadsheet',
  
  // 压缩文件
  'application/zip': 'Archive',
  'application/x-rar-compressed': 'Archive',
  'application/x-7z-compressed': 'Archive',
  'application/x-tar': 'Archive',
  'application/gzip': 'Archive',
  
  // 代码文件
  'text/javascript': 'Code',
  'text/typescript': 'Code',
  'text/html': 'Code',
  'text/css': 'Code',
  'text/xml': 'Code',
  'application/json': 'Code'
}

// 文件扩展名到MIME类型的映射
export const EXTENSION_TO_MIME: Record<string, string> = {
  // 图片
  'jpg': 'image/jpeg',
  'jpeg': 'image/jpeg',
  'png': 'image/png',
  'gif': 'image/gif',
  'webp': 'image/webp',
  'svg': 'image/svg+xml',
  'bmp': 'image/bmp',
  
  // 视频
  'mp4': 'video/mp4',
  'avi': 'video/avi',
  'mov': 'video/mov',
  'wmv': 'video/wmv',
  'webm': 'video/webm',
  'mkv': 'video/mkv',
  
  // 音频
  'mp3': 'audio/mp3',
  'wav': 'audio/wav',
  'flac': 'audio/flac',
  'aac': 'audio/aac',
  'ogg': 'audio/ogg',
  'm4a': 'audio/m4a',
  
  // 文档
  'pdf': 'application/pdf',
  'doc': 'application/msword',
  'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'xls': 'application/vnd.ms-excel',
  'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'ppt': 'application/vnd.ms-powerpoint',
  'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'txt': 'text/plain',
  'md': 'text/markdown',
  'csv': 'text/csv',
  'rtf': 'text/rtf',
  
  // 压缩文件
  'zip': 'application/zip',
  'rar': 'application/x-rar-compressed',
  '7z': 'application/x-7z-compressed',
  'tar': 'application/x-tar',
  'gz': 'application/gzip',
  
  // 代码文件
  'js': 'text/javascript',
  'ts': 'text/typescript',
  'html': 'text/html',
  'css': 'text/css',
  'xml': 'text/xml',
  'json': 'application/json',
  'py': 'text/python',
  'java': 'text/java',
  'cpp': 'text/cpp',
  'cs': 'text/csharp'
}

// 链接类型标签
export const LINK_TYPE_LABELS = {
  wikilink: 'Wiki Link',
  reference: 'Reference',
  embed: 'Embed',
  citation: 'Citation',
  external: 'External Link'
}

// 上传状态标签
export const UPLOAD_STATUS_LABELS = {
  pending: 'Pending',
  uploading: 'Uploading',
  completed: 'Completed',
  failed: 'Failed'
}

// 上传状态颜色
export const UPLOAD_STATUS_COLORS = {
  pending: 'text-yellow-600 bg-yellow-100',
  uploading: 'text-blue-600 bg-blue-100',
  completed: 'text-green-600 bg-green-100',
  failed: 'text-red-600 bg-red-100'
}

// 默认缩略图大小
export const THUMBNAIL_SIZES = {
  small: 64,
  medium: 128,
  large: 256,
  xlarge: 512
}

// 搜索配置
export const SEARCH_CONFIG = {
  minQueryLength: 2,
  maxResults: 50,
  debounceDelay: 300,
  highlightClass: 'bg-yellow-200'
}

// 分页配置
export const PAGINATION_CONFIG = {
  defaultPageSize: 20,
  pageSizeOptions: [10, 20, 50, 100],
  maxVisiblePages: 5
}

// 排序选项
export const SORT_OPTIONS = [
  { value: 'name', label: 'Name' },
  { value: 'date', label: 'Date Modified' },
  { value: 'type', label: 'Type' },
  { value: 'access', label: 'Access Count' },
  { value: 'size', label: 'File Size' }
]

// 分组选项
export const GROUP_OPTIONS = [
  { value: 'none', label: 'No Grouping' },
  { value: 'type', label: 'By Type' },
  { value: 'date', label: 'By Date' },
  { value: 'entity', label: 'By Entity' },
  { value: 'tags', label: 'By Tags' }
]

// 布局选项
export const LAYOUT_OPTIONS = [
  { value: 'list', label: 'List View', icon: 'List' },
  { value: 'grid', label: 'Grid View', icon: 'Grid' },
  { value: 'compact', label: 'Compact View', icon: 'Menu' },
  { value: 'tree', label: 'Tree View', icon: 'GitBranch' }
]

// 错误消息
export const ERROR_MESSAGES = {
  fileTooBig: 'File size exceeds the maximum limit',
  fileTypeNotAllowed: 'File type is not allowed',
  uploadFailed: 'Failed to upload file',
  invalidUrl: 'Please enter a valid URL',
  networkError: 'Network error occurred',
  permissionDenied: 'Permission denied',
  resourceNotFound: 'Resource not found',
  validationFailed: 'Validation failed'
}

// 成功消息
export const SUCCESS_MESSAGES = {
  fileUploaded: 'File uploaded successfully',
  linkCreated: 'Link created successfully',
  resourceDeleted: 'Resource deleted successfully',
  resourceUpdated: 'Resource updated successfully',
  linkValidated: 'Link validated successfully'
}
