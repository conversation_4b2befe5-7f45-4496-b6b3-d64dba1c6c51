import { useState, useEffect } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { Badge } from '../ui/badge'
import { TrendingUp, Target } from 'lucide-react'
import { useLanguage } from '../../contexts/LanguageContext'
import type { AreaMetric } from '../../../../shared/types'

interface CreateAreaMetricDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (metric: Omit<AreaMetric, 'id' | 'updatedAt' | 'areaId'>) => void
  initialData?: AreaMetric | null
  areaId: string
}

const getCommonUnits = (t: any) => [
  { value: 'kg', label: t('pages.areas.detail.createMetric.units.kg') },
  { value: 'lbs', label: t('pages.areas.detail.createMetric.units.lbs') },
  { value: 'cm', label: t('pages.areas.detail.createMetric.units.cm') },
  { value: 'ft', label: t('pages.areas.detail.createMetric.units.ft') },
  { value: '%', label: t('pages.areas.detail.createMetric.units.percent') },
  { value: 'hours', label: t('pages.areas.detail.createMetric.units.hours') },
  { value: 'minutes', label: t('pages.areas.detail.createMetric.units.minutes') },
  { value: 'days', label: t('pages.areas.detail.createMetric.units.days') },
  { value: 'count', label: t('pages.areas.detail.createMetric.units.count') },
  { value: 'score', label: t('pages.areas.detail.createMetric.units.score') },
  { value: 'rating', label: t('pages.areas.detail.createMetric.units.rating') },
  { value: 'custom', label: t('pages.areas.detail.createMetric.customUnit') }
]

export function CreateAreaMetricDialog({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  areaId
}: CreateAreaMetricDialogProps) {
  const { t } = useLanguage()
  const COMMON_UNITS = getCommonUnits(t)
  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    value: initialData?.value || '0',
    target: initialData?.target || '',
    unit: initialData?.unit || '',
    frequency: initialData?.frequency || 'daily',
    // {{ AURA-X: Add - 添加方向选择. Approval: 寸止(ID:1738157400). }}
    direction: 'increase' as 'increase' | 'decrease'
  })
  const [customUnit, setCustomUnit] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    if (initialData) {
      setFormData({
        name: initialData.name,
        value: initialData.value,
        target: initialData.target || '',
        unit: initialData.unit || '',
        frequency: initialData.frequency || 'daily',
        // {{ AURA-X: Add - 包含方向字段. Approval: 寸止(ID:1738157400). }}
        direction: (initialData as any).direction || 'increase'
      })
      
      // Check if unit is custom
      const isCommonUnit = COMMON_UNITS.some(u => u.value === initialData.unit)
      if (!isCommonUnit && initialData.unit) {
        setCustomUnit(initialData.unit)
        setFormData(prev => ({ ...prev, unit: 'custom' }))
      }
    }
  }, [initialData])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      return
    }

    setIsSubmitting(true)
    try {
      const metricData = {
        name: formData.name.trim(),
        value: formData.value.trim() || '0',
        target: formData.target.trim() || undefined,
        unit: formData.unit === 'custom' ? customUnit.trim() || undefined :
              formData.unit === 'none' ? undefined : formData.unit || undefined,
        frequency: formData.frequency,
        // {{ AURA-X: Add - 包含方向字段. Approval: 寸止(ID:1738157400). }}
        direction: formData.direction
      }

      await onSubmit(metricData)
      
      // Reset form
      setFormData({
        name: '',
        value: '0',
        target: '',
        unit: '',
        frequency: 'daily',
        // {{ AURA-X: Add - 重置方向字段. Approval: 寸止(ID:1738157400). }}
        direction: 'increase'
      })
      setCustomUnit('')
      onClose()
    } catch (error) {
      console.error('Failed to submit metric:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  const getPreviewText = () => {
    const name = formData.name || 'Metric Name'
    const value = formData.value || '0'
    const unit = formData.unit === 'custom' ? customUnit : formData.unit
    const target = formData.target
    
    let preview = `${name}: ${value}`
    if (unit) preview += ` ${unit}`
    if (target) preview += ` / ${target}${unit ? ` ${unit}` : ''}`
    
    return preview
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            {initialData ? t('pages.areas.detail.createMetric.editTitle') : t('pages.areas.detail.createMetric.title')}
          </DialogTitle>
          <DialogDescription>
            {initialData
              ? t('pages.areas.detail.createMetric.editDescription')
              : t('pages.areas.detail.createMetric.description')
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Metric Name */}
          <div className="space-y-2">
            <Label htmlFor="name">{t('pages.areas.detail.createMetric.metricNameRequired')}</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder={t('pages.areas.detail.createMetric.metricNamePlaceholder')}
              required
            />
          </div>

          {/* Current Value */}
          <div className="space-y-2">
            <Label htmlFor="value">{t('pages.areas.detail.createMetric.currentValue')}</Label>
            <Input
              id="value"
              type="text"
              value={formData.value}
              onChange={(e) => setFormData({ ...formData, value: e.target.value })}
              placeholder={t('pages.areas.detail.createMetric.currentValuePlaceholder')}
            />
          </div>

          {/* Target Value */}
          <div className="space-y-2">
            <Label htmlFor="target">{t('pages.areas.detail.createMetric.targetValue')}</Label>
            <Input
              id="target"
              type="text"
              value={formData.target}
              onChange={(e) => setFormData({ ...formData, target: e.target.value })}
              placeholder={t('pages.areas.detail.createMetric.targetValuePlaceholder')}
            />
          </div>

          {/* Unit Selection */}
          <div className="space-y-2">
            <Label htmlFor="unit">{t('pages.areas.detail.createMetric.unit')}</Label>
            <Select
              value={formData.unit}
              onValueChange={(value) => setFormData({ ...formData, unit: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('pages.areas.detail.createMetric.unitPlaceholder')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">{t('pages.areas.detail.createMetric.noUnit')}</SelectItem>
                {COMMON_UNITS.map((unit) => (
                  <SelectItem key={unit.value} value={unit.value}>
                    {unit.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {formData.unit === 'custom' && (
              <Input
                value={customUnit}
                onChange={(e) => setCustomUnit(e.target.value)}
                placeholder={t('pages.areas.detail.createMetric.customUnitPlaceholder')}
                className="mt-2"
              />
            )}
          </div>

          {/* {{ AURA-X: Add - 方向选择器. Approval: 寸止(ID:1738157400). }} */}
          {/* Metric Direction */}
          <div className="space-y-2">
            <Label htmlFor="direction">{t('pages.areas.detail.createMetric.metricDirection')}</Label>
            <Select
              value={formData.direction}
              onValueChange={(value: 'increase' | 'decrease') => setFormData({ ...formData, direction: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="increase">
                  {t('pages.areas.detail.createMetric.increaseDescription')}
                </SelectItem>
                <SelectItem value="decrease">
                  {t('pages.areas.detail.createMetric.decreaseDescription')}
                </SelectItem>
              </SelectContent>
            </Select>
            <div className="text-xs text-muted-foreground">
              {formData.direction === 'decrease'
                ? t('pages.areas.detail.createMetric.decreaseExamples')
                : t('pages.areas.detail.createMetric.increaseExamples')
              }
            </div>
          </div>

          {/* Recording Frequency */}
          <div className="space-y-2">
            <Label htmlFor="frequency">{t('pages.areas.detail.createMetric.recordingFrequency')}</Label>
            <Select
              value={formData.frequency}
              onValueChange={(value) => setFormData({ ...formData, frequency: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('pages.areas.detail.createMetric.frequencyPlaceholder')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">{t('pages.areas.detail.createMetric.daily')}</SelectItem>
                <SelectItem value="weekly">{t('pages.areas.detail.createMetric.weekly')}</SelectItem>
                <SelectItem value="monthly">{t('pages.areas.detail.createMetric.monthly')}</SelectItem>
                <SelectItem value="quarterly">{t('pages.areas.detail.createMetric.quarterly')}</SelectItem>
                <SelectItem value="as-needed">{t('pages.areas.detail.createMetric.asNeeded')}</SelectItem>
              </SelectContent>
            </Select>
            <div className="text-xs text-muted-foreground">
              {t('pages.areas.detail.createMetric.frequencyDescription')}
            </div>
          </div>

          {/* Preview */}
          <div className="space-y-2">
            <Label>{t('pages.areas.detail.createMetric.preview')}</Label>
            <div className="p-3 border rounded-lg bg-muted/50">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">{getPreviewText()}</span>
                {formData.target && (
                  <Badge variant="outline" className="text-xs">
                    <Target className="h-3 w-3 mr-1" />
                    {t('pages.areas.detail.createMetric.target')}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              {t('pages.areas.detail.createMetric.cancel')}
            </Button>
            <Button type="submit" disabled={isSubmitting || !formData.name.trim()}>
              {isSubmitting
                ? (initialData ? t('pages.areas.detail.createMetric.updating') : t('pages.areas.detail.createMetric.creating'))
                : (initialData ? t('pages.areas.detail.createMetric.updateMetric') : t('pages.areas.detail.createMetric.createMetric'))
              }
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default CreateAreaMetricDialog
