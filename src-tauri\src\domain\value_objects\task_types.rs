// Task Value Objects - 任务相关值对象

use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Ser<PERSON><PERSON>, Deserialize, PartialEq)]
pub struct TaskTitle {
    pub value: String,
}

impl TaskTitle {
    pub fn new(title: String) -> Result<Self, String> {
        if !title.trim().is_empty() && title.len() <= 200 {
            Ok(Self { value: title })
        } else {
            Err("Task title cannot be empty and must be less than 200 characters".to_string())
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub struct TaskDescription {
    pub value: String,
}

impl TaskDescription {
    pub fn new(description: String) -> Result<Self, String> {
        if description.len() <= 1000 {
            Ok(Self { value: description })
        } else {
            Err("Task description must be less than 1000 characters".to_string())
        }
    }
}
