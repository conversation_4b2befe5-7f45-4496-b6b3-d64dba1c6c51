// File Entity - 文件实体

use crate::shared::types::{Id, Metadata, EntityStatus};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct File {
    pub id: Id,
    pub name: String,
    pub path: String,
    pub file_type: FileType,
    pub size: u64,
    pub mime_type: Option<String>,
    pub checksum: Option<String>,
    pub entity_status: EntityStatus,
    pub metadata: Metadata,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum FileType {
    Document,
    Image,
    Audio,
    Video,
    Archive,
    Other,
}

impl File {
    pub fn new(name: String, path: String, size: u64) -> Self {
        Self {
            id: crate::shared::utils::IdGenerator::new_id_with_prefix("file"),
            name,
            path,
            file_type: FileType::Other,
            size,
            mime_type: None,
            checksum: None,
            entity_status: EntityStatus::Active,
            metadata: Metadata::default(),
        }
    }
}
