# PaoLife 开发环境搭建指南

## 📋 环境要求

### 系统要求
- **操作系统**: Windows 10/11, macOS 10.15+, Linux (Ubuntu 20.04+)
- **内存**: 最低 8GB RAM，推荐 16GB
- **存储**: 至少 5GB 可用空间
- **网络**: 稳定的互联网连接（用于下载依赖）

### 必需软件版本
- **Rust**: 1.81.0 或更高版本
- **Node.js**: 18.0.0 或更高版本
- **pnpm**: 8.0.0 或更高版本
- **Git**: 2.30.0 或更高版本

## 🛠️ 安装步骤

### 1. 安装 Rust 开发环境

#### Windows
```powershell
# 下载并安装 Rustup
Invoke-WebRequest -Uri "https://win.rustup.rs/" -OutFile "rustup-init.exe"
.\rustup-init.exe

# 重启终端后验证安装
rustc --version
cargo --version
```

#### macOS
```bash
# 使用 Homebrew 安装
brew install rustup-init
rustup-init

# 或使用官方脚本
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 验证安装
rustc --version
cargo --version
```

#### Linux
```bash
# 使用官方脚本安装
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# 验证安装
rustc --version
cargo --version
```

### 2. 安装 Node.js 和 pnpm

#### 使用 Node Version Manager (推荐)

**Windows (使用 nvm-windows)**:
```powershell
# 下载并安装 nvm-windows
# 从 https://github.com/coreybutler/nvm-windows/releases 下载安装包

# 安装 Node.js
nvm install 20.10.0
nvm use 20.10.0

# 安装 pnpm
npm install -g pnpm@8.15.0
```

**macOS/Linux**:
```bash
# 安装 nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc

# 安装 Node.js
nvm install 20.10.0
nvm use 20.10.0

# 安装 pnpm
npm install -g pnpm@8.15.0
```

### 3. 安装 Tauri 依赖

#### Windows
```powershell
# 安装 Microsoft C++ Build Tools
# 下载 Visual Studio Installer，安装 "C++ build tools" 工作负载

# 安装 WebView2 (通常已预装在 Windows 11)
# 如需手动安装，访问: https://developer.microsoft.com/en-us/microsoft-edge/webview2/
```

#### macOS
```bash
# 安装 Xcode Command Line Tools
xcode-select --install

# 验证安装
xcode-select -p
```

#### Linux (Ubuntu/Debian)
```bash
# 安装必需的系统依赖
sudo apt update
sudo apt install -y \
    libwebkit2gtk-4.0-dev \
    build-essential \
    curl \
    wget \
    file \
    libssl-dev \
    libgtk-3-dev \
    libayatana-appindicator3-dev \
    librsvg2-dev
```

#### Linux (Fedora)
```bash
# 安装必需的系统依赖
sudo dnf install -y \
    webkit2gtk4.0-devel \
    openssl-devel \
    curl \
    wget \
    file \
    libappindicator-gtk3-devel \
    librsvg2-devel
```

### 4. 安装开发工具

#### Rust 工具链
```bash
# 安装常用 Rust 工具
cargo install cargo-watch      # 文件监控和自动重建
cargo install cargo-nextest    # 更快的测试运行器
cargo install cargo-audit      # 安全审计
cargo install cargo-outdated   # 依赖更新检查
cargo install sqlx-cli         # SQLx 数据库工具

# 安装 Tauri CLI
cargo install tauri-cli@2.0.0-beta
```

#### 前端工具
```bash
# 全局安装前端工具
pnpm add -g @biomejs/biome@1.5.0  # 代码格式化和检查
pnpm add -g typescript@5.3.0      # TypeScript 编译器
pnpm add -g vite@5.0.0            # 构建工具
```

## 📁 项目设置

### 1. 克隆项目
```bash
# 克隆仓库
git clone https://github.com/your-org/paolife.git
cd paolife

# 检查分支
git branch -a
git checkout main
```

### 2. 安装项目依赖

#### 后端依赖
```bash
# 进入 Tauri 目录
cd src-tauri

# 安装 Rust 依赖
cargo build

# 运行测试确保环境正常
cargo test
```

#### 前端依赖
```bash
# 返回项目根目录
cd ..

# 安装前端依赖
pnpm install

# 验证安装
pnpm run type-check
```

### 3. 数据库设置
```bash
# 进入 Tauri 目录
cd src-tauri

# 创建数据库
cargo run --bin setup-db

# 运行数据库迁移
sqlx migrate run

# 验证数据库连接
cargo test test_database_connection
```

### 4. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
# 根据需要修改数据库路径、日志级别等配置
```

## 🚀 运行项目

### 开发模式
```bash
# 启动开发服务器（自动重载）
pnpm tauri dev

# 或分别启动前后端
# 终端 1: 启动前端开发服务器
pnpm dev

# 终端 2: 启动 Tauri 开发模式
cd src-tauri
cargo tauri dev
```

### 构建模式
```bash
# 构建生产版本
pnpm tauri build

# 仅构建前端
pnpm build

# 仅构建后端
cd src-tauri
cargo build --release
```

## 🔧 IDE 配置

### Visual Studio Code (推荐)

#### 必需扩展
```json
{
  "recommendations": [
    "rust-lang.rust-analyzer",      // Rust 语言支持
    "tauri-apps.tauri-vscode",      // Tauri 扩展
    "bradlc.vscode-tailwindcss",    // Tailwind CSS 支持
    "biomejs.biome",                // 代码格式化
    "ms-vscode.vscode-typescript-next" // TypeScript 支持
  ]
}
```

#### 工作区设置
```json
{
  "rust-analyzer.cargo.features": "all",
  "rust-analyzer.checkOnSave.command": "clippy",
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "biomejs.biome",
  "files.associations": {
    "*.rs": "rust"
  }
}
```

### JetBrains IDEs

#### IntelliJ IDEA / CLion
- 安装 Rust 插件
- 安装 Tauri 插件
- 配置 Rust 工具链路径
- 设置代码格式化规则

## 🧪 验证安装

### 运行完整测试套件
```bash
# 后端测试
cd src-tauri
cargo test

# 前端测试
cd ..
pnpm test

# 集成测试
pnpm test:e2e
```

### 检查代码质量
```bash
# Rust 代码检查
cd src-tauri
cargo clippy -- -D warnings
cargo fmt --check

# 前端代码检查
cd ..
pnpm lint
pnpm format:check
```

### 性能基准测试
```bash
# 构建性能测试
time pnpm tauri build

# 运行时性能测试
cd src-tauri
cargo bench
```

## 🐛 常见问题

### 1. Rust 编译错误
```bash
# 更新 Rust 工具链
rustup update

# 清理构建缓存
cargo clean

# 重新构建
cargo build
```

### 2. 前端依赖问题
```bash
# 清理 node_modules
rm -rf node_modules pnpm-lock.yaml

# 重新安装
pnpm install
```

### 3. Tauri 构建失败
```bash
# 检查系统依赖
tauri info

# 更新 Tauri CLI
cargo install tauri-cli@2.0.0-beta --force
```

### 4. 数据库连接问题
```bash
# 检查数据库文件权限
ls -la data/

# 重新创建数据库
rm data/paolife.db
cargo run --bin setup-db
```

## 📚 开发资源

### 官方文档
- [Rust 官方文档](https://doc.rust-lang.org/)
- [Tauri 官方文档](https://tauri.app/)
- [SolidJS 官方文档](https://www.solidjs.com/)
- [SQLx 文档](https://docs.rs/sqlx/)

### 社区资源
- [Rust 用户论坛](https://users.rust-lang.org/)
- [Tauri Discord](https://discord.com/invite/tauri)
- [SolidJS Discord](https://discord.com/invite/solidjs)

### 开发工具
- [Rust Playground](https://play.rust-lang.org/)
- [TypeScript Playground](https://www.typescriptlang.org/play)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)

---

**文档版本**: 1.0  
**创建日期**: 2024年12月  
**维护者**: 开发团队  
**下次更新**: 根据工具版本更新需要
