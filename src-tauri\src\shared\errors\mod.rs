// Error Handling - 错误处理系统
// 统一的错误定义和处理

use serde::{Deserialize, Serialize};
use std::fmt;

// 应用程序结果类型
pub type Result<T> = std::result::Result<T, AppError>;

// 主要错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AppError {
    // 数据库错误
    DatabaseError(String),
    
    // 验证错误
    ValidationError(String),
    
    // 业务逻辑错误
    BusinessError(String),
    
    // 资源未找到
    NotFound(String),
    
    // 权限错误
    Unauthorized(String),
    
    // 配置错误
    ConfigError(String),
    
    // 文件系统错误
    FileSystemError(String),
    
    // 搜索引擎错误
    SearchError(String),
    
    // 外部服务错误
    ExternalServiceError(String),
    
    // 内部错误
    InternalError(String),
}

impl fmt::Display for AppError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AppError::DatabaseError(msg) => write!(f, "Database error: {}", msg),
            AppError::ValidationError(msg) => write!(f, "Validation error: {}", msg),
            AppError::BusinessError(msg) => write!(f, "Business error: {}", msg),
            AppError::NotFound(msg) => write!(f, "Not found: {}", msg),
            AppError::Unauthorized(msg) => write!(f, "Unauthorized: {}", msg),
            AppError::ConfigError(msg) => write!(f, "Configuration error: {}", msg),
            AppError::FileSystemError(msg) => write!(f, "File system error: {}", msg),
            AppError::SearchError(msg) => write!(f, "Search error: {}", msg),
            AppError::ExternalServiceError(msg) => write!(f, "External service error: {}", msg),
            AppError::InternalError(msg) => write!(f, "Internal error: {}", msg),
        }
    }
}

impl std::error::Error for AppError {}

// 从 SQLx 错误转换
impl From<sqlx::Error> for AppError {
    fn from(err: sqlx::Error) -> Self {
        AppError::DatabaseError(err.to_string())
    }
}

// 从 IO 错误转换
impl From<std::io::Error> for AppError {
    fn from(err: std::io::Error) -> Self {
        AppError::FileSystemError(err.to_string())
    }
}

// 从 Serde JSON 错误转换
impl From<serde_json::Error> for AppError {
    fn from(err: serde_json::Error) -> Self {
        AppError::ValidationError(format!("JSON parsing error: {}", err))
    }
}

// 错误代码枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ErrorCode {
    // 通用错误码
    Unknown = 1000,
    InvalidInput = 1001,
    ResourceNotFound = 1002,
    AccessDenied = 1003,
    
    // 数据库错误码
    DatabaseConnectionFailed = 2001,
    DatabaseQueryFailed = 2002,
    DatabaseConstraintViolation = 2003,
    
    // 业务逻辑错误码
    ProjectNotFound = 3001,
    TaskNotFound = 3002,
    UserNotFound = 3003,
    AreaNotFound = 3004,
    
    // 文件系统错误码
    FileNotFound = 4001,
    FileAccessDenied = 4002,
    FileCorrupted = 4003,
    
    // 搜索错误码
    SearchIndexCorrupted = 5001,
    SearchQueryInvalid = 5002,
}

impl AppError {
    pub fn error_code(&self) -> ErrorCode {
        match self {
            AppError::DatabaseError(_) => ErrorCode::DatabaseQueryFailed,
            AppError::ValidationError(_) => ErrorCode::InvalidInput,
            AppError::BusinessError(_) => ErrorCode::Unknown,
            AppError::NotFound(_) => ErrorCode::ResourceNotFound,
            AppError::Unauthorized(_) => ErrorCode::AccessDenied,
            AppError::ConfigError(_) => ErrorCode::Unknown,
            AppError::FileSystemError(_) => ErrorCode::FileNotFound,
            AppError::SearchError(_) => ErrorCode::SearchQueryInvalid,
            AppError::ExternalServiceError(_) => ErrorCode::Unknown,
            AppError::InternalError(_) => ErrorCode::Unknown,
        }
    }
}
