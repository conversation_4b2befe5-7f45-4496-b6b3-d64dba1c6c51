// Template Entity - 模板实体

use crate::shared::types::{Id, Metadata, EntityStatus};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Template {
    pub id: Id,
    pub name: String,
    pub description: Option<String>,
    pub template_type: TemplateType,
    pub content: String,
    pub variables: Vec<TemplateVariable>,
    pub entity_status: EntityStatus,
    pub metadata: Metadata,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum TemplateType {
    Project,
    Review,
    Checklist,
    Note,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TemplateVariable {
    pub name: String,
    pub variable_type: VariableType,
    pub default_value: Option<String>,
    pub required: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum VariableType {
    Text,
    Number,
    Date,
    Boolean,
    Select(Vec<String>),
}

impl Template {
    pub fn new(name: String, template_type: TemplateType, content: String) -> Self {
        Self {
            id: crate::shared::utils::IdGenerator::new_id_with_prefix("template"),
            name,
            description: None,
            template_type,
            content,
            variables: Vec::new(),
            entity_status: EntityStatus::Active,
            metadata: Metadata::default(),
        }
    }
}
