/**
 * KPI快速输入组件
 * 支持KPI数据录入、编辑和历史查看
 */

import { createSignal, createMemo, Show } from 'solid-js'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { 
  Plus, 
  Edit, 
  Trash2, 
  TrendingUp, 
  TrendingDown, 
  Target,
  ChevronDown,
  ChevronUp,
  Calendar,
  Save,
  X
} from 'lucide-react'
import { cn } from '../../lib/utils'

import type { KPIInputProps, BaseKPI, CreateRecordData } from './types'

// 计算KPI进度
function calculateProgress(kpi: BaseKPI): number {
  if (!kpi.target) return 0
  
  if (kpi.direction === 'decrease') {
    const estimatedStart = Math.max(kpi.value * 1.5, kpi.target * 2)
    const totalReduction = estimatedStart - kpi.target
    const currentReduction = estimatedStart - kpi.value
    
    if (totalReduction <= 0) return 100
    return Math.min((currentReduction / totalReduction) * 100, 100)
  } else {
    if (kpi.target === 0) return 0
    return Math.min((kpi.value / kpi.target) * 100, 100)
  }
}

// 获取状态颜色
function getStatusColor(progress: number, hasTarget: boolean): string {
  if (!hasTarget) return 'text-gray-600'
  if (progress >= 100) return 'text-green-600'
  if (progress >= 75) return 'text-blue-600'
  if (progress >= 50) return 'text-yellow-600'
  return 'text-red-600'
}

export function KPIInput(props: KPIInputProps) {
  // 状态管理
  const [isExpanded, setIsExpanded] = createSignal(false)
  const [isRecording, setIsRecording] = createSignal(false)
  const [recordValue, setRecordValue] = createSignal('')
  const [recordNote, setRecordNote] = createSignal('')
  const [isSubmitting, setIsSubmitting] = createSignal(false)

  // 计算进度和状态
  const progress = createMemo(() => calculateProgress(props.kpi))
  const hasTarget = createMemo(() => !!props.kpi.target)
  const statusColor = createMemo(() => getStatusColor(progress(), hasTarget()))

  // 格式化日期
  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  // 提交记录
  const handleSubmitRecord = async () => {
    const value = parseFloat(recordValue())
    if (isNaN(value)) return

    setIsSubmitting(true)
    try {
      const recordData: CreateRecordData = {
        value,
        note: recordNote().trim() || undefined,
        recordedAt: new Date(),
        source: 'manual'
      }

      await props.onRecord?.(recordData)
      
      // 重置表单
      setRecordValue('')
      setRecordNote('')
      setIsRecording(false)
    } catch (error) {
      console.error('Failed to record KPI data:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // 取消记录
  const handleCancelRecord = () => {
    setRecordValue('')
    setRecordNote('')
    setIsRecording(false)
  }

  // 切换展开状态
  const toggleExpanded = () => {
    if (!isRecording()) {
      setIsExpanded(!isExpanded())
    }
  }

  return (
    <Card class={cn('group relative transition-all hover:shadow-md', props.class)}>
      {/* 主要内容区域 */}
      <div
        class="cursor-pointer"
        onClick={toggleExpanded}
      >
        <CardHeader class="pb-3">
          <div class="flex items-start justify-between">
            <div class="flex-1 min-w-0">
              <CardTitle class="text-sm font-medium truncate flex items-center gap-2">
                {props.kpi.name}
                <Show when={props.kpi.direction === 'decrease'}>
                  <TrendingDown class="h-3 w-3 text-red-500" />
                </Show>
                <Show when={props.kpi.direction === 'increase'}>
                  <TrendingUp class="h-3 w-3 text-green-500" />
                </Show>
              </CardTitle>
              <div class="flex items-center gap-2 mt-1">
                <span class={cn('text-lg font-bold', statusColor())}>
                  {props.kpi.value}
                </span>
                <Show when={props.kpi.unit}>
                  <span class="text-xs text-muted-foreground">{props.kpi.unit}</span>
                </Show>
                <Show when={hasTarget()}>
                  <span class="text-xs text-muted-foreground">
                    / {props.kpi.target}{props.kpi.unit && ` ${props.kpi.unit}`}
                  </span>
                </Show>
                <Show when={hasTarget()}>
                  <Badge variant="outline" class="text-xs">
                    {Math.round(progress())}%
                  </Badge>
                </Show>
              </div>
            </div>
            
            {/* 操作按钮 */}
            <div class="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  setIsRecording(true)
                  setIsExpanded(true)
                }}
              >
                <Plus class="h-3 w-3" />
              </Button>
              <Show when={props.allowEdit}>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    props.onEdit?.(props.kpi)
                  }}
                >
                  <Edit class="h-3 w-3" />
                </Button>
              </Show>
              <Show when={props.allowDelete}>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    props.onDelete?.(props.kpi.id)
                  }}
                >
                  <Trash2 class="h-3 w-3" />
                </Button>
              </Show>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  toggleExpanded()
                }}
              >
                <Show when={isExpanded()}>
                  <ChevronUp class="h-3 w-3" />
                </Show>
                <Show when={!isExpanded()}>
                  <ChevronDown class="h-3 w-3" />
                </Show>
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* 进度条 */}
        <Show when={hasTarget() && !isExpanded()}>
          <div class="px-6 pb-3">
            <Progress 
              value={progress()} 
              class="h-1"
              indicatorClass={cn(
                progress() >= 100 ? 'bg-green-500' :
                progress() >= 75 ? 'bg-blue-500' :
                progress() >= 50 ? 'bg-yellow-500' : 'bg-red-500'
              )}
            />
          </div>
        </Show>
      </div>

      {/* 展开内容 */}
      <Show when={isExpanded()}>
        <CardContent class="pt-0 space-y-4">
          {/* 详细信息 */}
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-muted-foreground">Current:</span>
              <span class="ml-2 font-medium">
                {props.kpi.value}{props.kpi.unit && ` ${props.kpi.unit}`}
              </span>
            </div>
            <Show when={hasTarget()}>
              <div>
                <span class="text-muted-foreground">Target:</span>
                <span class="ml-2 font-medium">
                  {props.kpi.target}{props.kpi.unit && ` ${props.kpi.unit}`}
                </span>
              </div>
            </Show>
            <div>
              <span class="text-muted-foreground">Direction:</span>
              <span class="ml-2 font-medium capitalize">{props.kpi.direction}</span>
            </div>
            <div>
              <span class="text-muted-foreground">Updated:</span>
              <span class="ml-2 font-medium">{formatDate(props.kpi.updatedAt)}</span>
            </div>
          </div>

          {/* 进度条（展开状态） */}
          <Show when={hasTarget()}>
            <div class="space-y-2">
              <div class="flex justify-between text-sm">
                <span class="text-muted-foreground">Progress</span>
                <span class="font-medium">{Math.round(progress())}%</span>
              </div>
              <Progress 
                value={progress()} 
                class="h-2"
                indicatorClass={cn(
                  progress() >= 100 ? 'bg-green-500' :
                  progress() >= 75 ? 'bg-blue-500' :
                  progress() >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                )}
              />
            </div>
          </Show>

          {/* 记录输入表单 */}
          <Show when={isRecording()}>
            <div class="space-y-3 p-4 bg-gray-50 rounded-lg">
              <h4 class="text-sm font-medium">Record New Value</h4>
              <div class="space-y-3">
                <div>
                  <label class="text-xs text-muted-foreground">Value</label>
                  <Input
                    type="number"
                    placeholder={`Enter value${props.kpi.unit ? ` (${props.kpi.unit})` : ''}`}
                    value={recordValue()}
                    onInput={(e) => setRecordValue(e.currentTarget.value)}
                    class="mt-1"
                  />
                </div>
                <div>
                  <label class="text-xs text-muted-foreground">Note (optional)</label>
                  <Textarea
                    placeholder="Add a note about this record..."
                    value={recordNote()}
                    onInput={(e) => setRecordNote(e.currentTarget.value)}
                    rows={2}
                    class="mt-1"
                  />
                </div>
                <div class="flex gap-2">
                  <Button
                    size="sm"
                    onClick={handleSubmitRecord}
                    disabled={!recordValue() || isSubmitting()}
                  >
                    <Save class="h-3 w-3 mr-1" />
                    Save
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancelRecord}
                    disabled={isSubmitting()}
                  >
                    <X class="h-3 w-3 mr-1" />
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          </Show>

          {/* 描述信息 */}
          <Show when={props.kpi.description}>
            <div class="text-sm text-muted-foreground">
              {props.kpi.description}
            </div>
          </Show>
        </CardContent>
      </Show>
    </Card>
  )
}

export default KPIInput
