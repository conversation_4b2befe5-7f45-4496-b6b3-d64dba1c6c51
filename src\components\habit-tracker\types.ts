/**
 * 习惯追踪组件类型定义
 * 为可复用的习惯追踪组件提供完整的类型支持
 */

import { JSX } from 'solid-js'

// ============================================================================
// 基础数据类型
// ============================================================================

export type HabitType = 'boolean' | 'numeric' | 'duration'
export type HabitFrequency = 'daily' | 'weekly' | 'monthly'
export type HabitStatus = 'active' | 'paused' | 'archived'
export type RecordSource = 'manual' | 'auto' | 'import'

// 基础习惯数据结构
export interface BaseHabit {
  id: string
  name: string
  description?: string
  areaId: string
  type: HabitType
  frequency: HabitFrequency
  target: number
  unit?: string
  color: string
  status: HabitStatus
  streak: number
  longestStreak: number
  completionRate: number
  createdAt: Date
  updatedAt: Date
  isActive: boolean
}

// 习惯记录数据结构
export interface HabitRecord {
  id: string
  habitId: string
  date: string // YYYY-MM-DD format
  completed: boolean
  value?: number
  note?: string
  source: RecordSource
  createdAt: Date
}

// 习惯统计信息
export interface HabitStatistics {
  totalHabits: number
  activeHabits: number
  completedToday: number
  averageCompletion: number
  totalStreak: number
  longestStreak: number
  weeklyProgress: number[]
  monthlyProgress: number[]
  habitsByType: Record<HabitType, number>
  habitsByFrequency: Record<HabitFrequency, number>
}

// 习惯进度信息
export interface HabitProgress {
  habitId: string
  currentStreak: number
  weekProgress: number
  monthProgress: number
  yearProgress: number
  trend: 'up' | 'down' | 'stable'
  lastCompleted?: Date
  nextDue?: Date
}

// 习惯创建数据
export interface CreateHabitData {
  name: string
  description?: string
  areaId: string
  type: HabitType
  frequency: HabitFrequency
  target: number
  unit?: string
  color: string
}

// 习惯更新数据
export interface UpdateHabitData extends Partial<CreateHabitData> {
  status?: HabitStatus
}

// 记录创建数据
export interface CreateRecordData {
  habitId: string
  date: string
  completed: boolean
  value?: number
  note?: string
}

// ============================================================================
// 配置接口
// ============================================================================

// 组件配置选项
export interface HabitTrackerConfig {
  // 显示选项
  showStatistics?: boolean
  showProgress?: boolean
  showCalendar?: boolean
  showChart?: boolean
  showStreak?: boolean
  
  // 交互选项
  allowCreate?: boolean
  allowEdit?: boolean
  allowDelete?: boolean
  allowBulkOperations?: boolean
  
  // 布局选项
  layout?: 'grid' | 'list' | 'compact' | 'calendar'
  calendarView?: 'month' | 'week' | 'year'
  chartType?: 'line' | 'bar' | 'heatmap' | 'progress'
  
  // 功能选项
  enableReminders?: boolean
  enableNotes?: boolean
  enableStreaks?: boolean
  enableGoals?: boolean
  
  // 样式选项
  compactMode?: boolean
  showColors?: boolean
  animateProgress?: boolean
  
  // 数据选项
  maxRecords?: number
  defaultView?: 'today' | 'week' | 'month' | 'year'
  
  // 主题
  theme?: 'light' | 'dark' | 'auto'
  className?: string
}

// 习惯过滤选项
export interface HabitFilter {
  areaId?: string
  type?: HabitType[]
  frequency?: HabitFrequency[]
  status?: HabitStatus[]
  dateRange?: {
    start: Date
    end: Date
  }
  search?: string
  hasTarget?: boolean
  isCompleted?: boolean
}

// ============================================================================
// 事件接口
// ============================================================================

// 习惯事件类型
export type HabitEventType = 
  | 'create'
  | 'update'
  | 'delete'
  | 'record'
  | 'streak-achieved'
  | 'goal-reached'
  | 'reminder'

// 习惯事件数据
export interface HabitEvent<T = any> {
  type: HabitEventType
  habitId: string
  data: T
  timestamp: Date
}

// 事件回调函数
export interface HabitEventHandlers {
  onCreate?: (habit: BaseHabit) => void | Promise<void>
  onUpdate?: (habit: BaseHabit, changes: Partial<BaseHabit>) => void | Promise<void>
  onDelete?: (habitId: string) => void | Promise<void>
  onRecord?: (record: HabitRecord) => void | Promise<void>
  onStreakAchieved?: (habitId: string, streak: number) => void | Promise<void>
  onGoalReached?: (habitId: string, progress: HabitProgress) => void | Promise<void>
  onReminder?: (habitId: string, reminderData: any) => void | Promise<void>
  onError?: (error: Error, context?: string) => void
}

// ============================================================================
// API接口
// ============================================================================

// 习惯数据源接口
export interface HabitDataSource {
  // 习惯CRUD操作
  getHabits(areaId: string, filter?: HabitFilter): Promise<BaseHabit[]>
  getHabit(id: string): Promise<BaseHabit | null>
  createHabit(data: CreateHabitData): Promise<BaseHabit>
  updateHabit(id: string, data: UpdateHabitData): Promise<BaseHabit>
  deleteHabit(id: string): Promise<void>
  
  // 记录操作
  getRecords(habitId: string, dateRange?: { start: Date; end: Date }): Promise<HabitRecord[]>
  createRecord(data: CreateRecordData): Promise<HabitRecord>
  updateRecord(id: string, data: Partial<CreateRecordData>): Promise<HabitRecord>
  deleteRecord(id: string): Promise<void>
  
  // 统计信息
  getStatistics(areaId: string): Promise<HabitStatistics>
  getProgress(habitId: string): Promise<HabitProgress>
  
  // 批量操作
  bulkCreateRecords(records: CreateRecordData[]): Promise<HabitRecord[]>
  bulkUpdateHabits(updates: Array<{ id: string; data: UpdateHabitData }>): Promise<BaseHabit[]>
}

// 查询选项
export interface QueryOptions {
  limit?: number
  offset?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  include?: string[]
}

// ============================================================================
// 组件Props接口
// ============================================================================

// 基础组件Props
export interface BaseComponentProps {
  class?: string
  children?: JSX.Element
}

// 习惯追踪主组件Props
export interface HabitTrackerProps extends BaseComponentProps {
  // 必需属性
  areaId: string
  dataSource: HabitDataSource
  
  // 可选配置
  config?: Partial<HabitTrackerConfig>
  eventHandlers?: HabitEventHandlers
  
  // 初始数据
  initialHabits?: BaseHabit[]
  
  // 样式和主题
  theme?: 'light' | 'dark' | 'auto'
  className?: string
}

// 习惯列表组件Props
export interface HabitListProps extends BaseComponentProps {
  habits: BaseHabit[]
  records: HabitRecord[]
  config?: Partial<HabitTrackerConfig>
  onHabitClick?: (habit: BaseHabit) => void
  onHabitEdit?: (habit: BaseHabit) => void
  onHabitDelete?: (habitId: string) => void
  onRecordToggle?: (habitId: string, date: string, completed: boolean) => void
  selectedDate?: Date
}

// 习惯卡片组件Props
export interface HabitCardProps extends BaseComponentProps {
  habit: BaseHabit
  records: HabitRecord[]
  selectedDate?: Date
  onEdit?: (habit: BaseHabit) => void
  onDelete?: (habitId: string) => void
  onRecordToggle?: (habitId: string, date: string, completed: boolean) => void
  onValueSet?: (habitId: string, date: string, value: number) => void
  showProgress?: boolean
  showStreak?: boolean
  compact?: boolean
}

// 习惯创建对话框Props
export interface HabitDialogProps extends BaseComponentProps {
  open: boolean
  mode: 'create' | 'edit'
  habit?: BaseHabit
  areaId: string
  onSubmit: (data: CreateHabitData | UpdateHabitData) => Promise<void>
  onCancel: () => void
}

// 习惯统计组件Props
export interface HabitStatisticsProps extends BaseComponentProps {
  statistics: HabitStatistics
  showChart?: boolean
  chartType?: 'line' | 'bar' | 'pie' | 'donut'
  timeRange?: 'week' | 'month' | 'year'
}

// 习惯日历组件Props
export interface HabitCalendarProps extends BaseComponentProps {
  habits: BaseHabit[]
  records: HabitRecord[]
  selectedDate: Date
  onDateSelect: (date: Date) => void
  onRecordToggle?: (habitId: string, date: string, completed: boolean) => void
  view?: 'month' | 'week'
  showHeatmap?: boolean
}

// 习惯图表组件Props
export interface HabitChartProps extends BaseComponentProps {
  habits: BaseHabit[]
  records: HabitRecord[]
  type: 'line' | 'bar' | 'heatmap' | 'progress'
  timeRange: 'week' | 'month' | 'year'
  showLegend?: boolean
  height?: number
}

// ============================================================================
// 工具类型
// ============================================================================

// 深度可选类型
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

// 选择性必需类型
export type RequireFields<T, K extends keyof T> = T & Required<Pick<T, K>>

// 习惯类型颜色映射
export interface HabitTypeColorMap {
  [K in HabitType]: {
    bg: string
    text: string
    border: string
    icon: string
  }
}

// 导出默认配置
export const DEFAULT_HABIT_CONFIG: HabitTrackerConfig = {
  showStatistics: true,
  showProgress: true,
  showCalendar: true,
  showChart: true,
  showStreak: true,
  allowCreate: true,
  allowEdit: true,
  allowDelete: true,
  allowBulkOperations: false,
  layout: 'grid',
  calendarView: 'month',
  chartType: 'line',
  enableReminders: true,
  enableNotes: true,
  enableStreaks: true,
  enableGoals: true,
  compactMode: false,
  showColors: true,
  animateProgress: true,
  maxRecords: 365,
  defaultView: 'today',
  theme: 'auto'
}
