# Mock API 使用指南

## 概述

本项目实现了一套完整的Mock API方案，支持在前端开发过程中使用模拟数据，同时可以轻松切换到真实的Tauri API。

## 方案特点

✅ **环境变量控制** - 通过环境变量灵活控制Mock开关  
✅ **开发时启用** - 开发环境自动启用Mock数据  
✅ **生产时禁用** - 生产环境自动使用真实API  
✅ **易于维护** - 统一的API接口，无需修改业务代码  
✅ **实时切换** - 开发工具支持运行时状态查看  

## 文件结构

```
src/
├── services/
│   ├── api.ts          # 真实Tauri API服务
│   ├── mockApi.ts      # Mock API服务
│   └── apiFactory.ts   # API工厂（自动选择）
├── components/
│   └── DevTools.tsx    # 开发工具组件
├── .env.example        # 环境变量示例
└── .env.development    # 开发环境配置
```

## 使用方法

### 1. 环境配置

复制 `.env.example` 为 `.env.local` 并配置：

```bash
# 开发时使用Mock API
VITE_USE_MOCK_API=true

# 强制使用Mock API（所有环境）
VITE_FORCE_MOCK_API=false
```

### 2. 在代码中使用

```typescript
// 从apiFactory导入，自动选择合适的API
import { getProjects, createProject } from '../services/apiFactory';

// 使用方式完全相同
const projects = await getProjects();
const newProject = await createProject(projectData);
```

### 3. 开发工具

在开发环境中，右下角会显示开发工具按钮（🛠️），点击可以：

- 查看当前API类型（Mock/Real）
- 检查API连接状态
- 查看环境变量配置
- 快捷操作（重载、清缓存等）

## 环境变量说明

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `VITE_USE_MOCK_API` | 开发环境是否使用Mock API | `false` |
| `VITE_FORCE_MOCK_API` | 强制使用Mock API（优先级最高） | `false` |
| `VITE_API_BASE_URL` | API基础URL | `http://localhost:1420` |

## 切换场景

### 场景1：纯前端开发
```bash
# .env.local
VITE_USE_MOCK_API=true
```

### 场景2：前后端联调
```bash
# .env.local
VITE_USE_MOCK_API=false
```

### 场景3：演示环境
```bash
# .env.local
VITE_FORCE_MOCK_API=true
```

## Mock数据管理

### 添加新的Mock数据

在 `src/services/mockApi.ts` 中：

```typescript
// 1. 添加模拟数据
const mockNewEntities: NewEntity[] = [
  {
    id: 'entity-1',
    name: '示例实体',
    // ... 其他字段
  }
];

// 2. 实现Mock方法
async getNewEntities(): Promise<NewEntity[]> {
  await this.mockDelay();
  return [...mockNewEntities];
}
```

### 修改Mock延迟

```typescript
// 在mockApi.ts中调整延迟时间
private async mockDelay(): Promise<void> {
  await delay(300 + Math.random() * 700); // 300-1000ms
}
```

## 最佳实践

### 1. 数据一致性
确保Mock数据结构与真实API返回的数据结构完全一致。

### 2. 错误模拟
在Mock API中也要模拟各种错误情况：

```typescript
async createUser(data: any): Promise<User> {
  await this.mockDelay();
  
  // 模拟验证错误
  if (!data.username) {
    throw new Error('Username is required');
  }
  
  // 模拟重复用户错误
  if (mockUsers.some(u => u.username === data.username)) {
    throw new Error('Username already exists');
  }
  
  // 正常创建逻辑...
}
```

### 3. 状态持久化
Mock数据在页面刷新后会重置，如需持久化可以使用localStorage：

```typescript
// 保存到localStorage
localStorage.setItem('mockProjects', JSON.stringify(mockProjects));

// 从localStorage恢复
const savedProjects = localStorage.getItem('mockProjects');
if (savedProjects) {
  mockProjects.splice(0, mockProjects.length, ...JSON.parse(savedProjects));
}
```

## 故障排除

### 问题1：API类型不正确
检查环境变量配置和开发工具显示的API类型。

### 问题2：Mock数据不更新
确认是否正确导入了apiFactory而不是直接导入api.ts。

### 问题3：Tauri命令调用失败
在Mock模式下，Tauri命令不会被调用，这是正常现象。

## 部署注意事项

### 生产环境
确保生产环境的环境变量正确配置：

```bash
VITE_USE_MOCK_API=false
VITE_FORCE_MOCK_API=false
```

### 构建优化
Mock相关代码在生产构建时会被自动排除（通过环境变量判断）。

## 扩展功能

### 1. API响应拦截
可以在apiFactory中添加响应拦截器：

```typescript
// 添加日志记录
const originalMethod = api.getProjects;
api.getProjects = async (...args) => {
  console.log('API Call: getProjects', args);
  const result = await originalMethod.apply(api, args);
  console.log('API Response: getProjects', result);
  return result;
};
```

### 2. 动态切换
可以实现运行时动态切换API类型（仅开发环境）。

---

通过这套Mock API方案，你可以：
- 在后端API未完成时进行前端开发
- 创建稳定的演示环境
- 进行离线开发和测试
- 模拟各种边界情况和错误状态
