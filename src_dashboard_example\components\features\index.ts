// Export all feature-specific components

// Dashboard feature components
export { default as QuickCapture } from './QuickCapture'
export { default as TodayTask<PERSON> } from './TodayTasks'
export { default as UpcomingProjects } from './UpcomingProjects'
export { default as RecentActivity } from './RecentActivity'
export { default as GlobalSearch } from './GlobalSearch'

// Project management components
export { default as ProjectCard } from './ProjectCard'
export { default as CreateProjectDialog } from './CreateProjectDialog'
export { default as ProjectDetailPage } from './ProjectDetailPage'

// Task management components
export { default as TaskItem } from './TaskItem'
export { default as TaskList } from './TaskList'
export { default as CreateTaskDialog } from './CreateTaskDialog'

// Area management components
export { default as AreaCard } from './AreaCard'
export { default as CreateAreaDialog } from './CreateAreaDialog'
export { default as AreaDetailPage } from './AreaDetailPage'

// Habit tracking components
export { default as HabitItem } from './HabitItem'
export { default as CreateHabitDialog } from './CreateHabitDialog'

// Resource management components
export { default as FileTree } from './FileTree'
export { default as FileTreeNode } from './FileTreeNode'
export { ContextMenu, useContextMenu } from './ContextMenu'
export { CreateTaskFromTextDialog } from './CreateTaskFromTextDialog'

// Markdown editor components - removed

// Inbox management components
export { default as InboxItem } from './InboxItem'
export { default as QuickCaptureDialog } from './QuickCaptureDialog'

// Archive management components
export { default as ArchiveItem } from './ArchiveItem'

// Other feature components will be added here

// Placeholder export to make this a valid module
export {}
