/**
 * KPI工具函数
 * 提供KPI计算、验证、格式化等实用功能
 */

import type { 
  BaseKPI, 
  KPIStatus, 
  CreateKPIData, 
  ValidationRules 
} from './types'

/**
 * 计算KPI进度百分比
 */
export function calculateKPIProgress(kpi: BaseKPI): number {
  if (!kpi.target) return 0
  
  if (kpi.direction === 'decrease') {
    // 减少型指标：需要从当前值减少到目标值
    if (kpi.target >= kpi.value) {
      // 已经达到或超过目标
      return 100
    }
    
    // 估算起始值进行进度计算
    const estimatedStart = Math.max(kpi.value * 1.5, kpi.target * 2)
    const totalReduction = estimatedStart - kpi.target
    const currentReduction = estimatedStart - kpi.value
    
    if (totalReduction <= 0) return 100
    return Math.min((currentReduction / totalReduction) * 100, 100)
  } else {
    // 增长型指标：需要从当前值增长到目标值
    if (kpi.target === 0) return 0
    return Math.min((kpi.value / kpi.target) * 100, 100)
  }
}

/**
 * 获取KPI状态
 */
export function getKPIStatus(progress: number): {
  status: KPIStatus
  color: string
  label: string
} {
  if (progress >= 100) {
    return {
      status: 'achieved',
      color: 'text-green-600',
      label: 'Achieved'
    }
  } else if (progress >= 75) {
    return {
      status: 'on-track',
      color: 'text-blue-600',
      label: 'On Track'
    }
  } else if (progress >= 50) {
    return {
      status: 'at-risk',
      color: 'text-yellow-600',
      label: 'At Risk'
    }
  } else {
    return {
      status: 'behind',
      color: 'text-red-600',
      label: 'Behind'
    }
  }
}

/**
 * 获取状态对应的CSS类名
 */
export function getStatusColor(status: KPIStatus): string {
  switch (status) {
    case 'achieved':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'on-track':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'at-risk':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'behind':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'no-target':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

/**
 * 格式化KPI值显示
 */
export function formatKPIValue(value: number, unit?: string, precision: number = 2): string {
  // 处理大数值的显示
  let formattedValue: string
  
  if (Math.abs(value) >= 1000000) {
    formattedValue = (value / 1000000).toFixed(precision) + 'M'
  } else if (Math.abs(value) >= 1000) {
    formattedValue = (value / 1000).toFixed(precision) + 'K'
  } else {
    formattedValue = value.toFixed(precision)
  }
  
  // 移除不必要的小数点
  formattedValue = formattedValue.replace(/\.?0+$/, '')
  
  return unit ? `${formattedValue} ${unit}` : formattedValue
}

/**
 * 验证KPI数据
 */
export function validateKPIData(
  data: Partial<CreateKPIData>, 
  rules?: ValidationRules,
  isUpdate: boolean = false
): {
  isValid: boolean
  errors: Record<string, string>
} {
  const errors: Record<string, string> = {}
  
  // 默认验证规则
  const defaultRules: ValidationRules = {
    nameRequired: true,
    nameMaxLength: 100,
    valueRequired: true,
    valueMin: 0,
    targetRequired: false,
    unitMaxLength: 20,
    noteMaxLength: 500
  }
  
  const validationRules = { ...defaultRules, ...rules }
  
  // 验证名称
  if (validationRules.nameRequired && !isUpdate && !data.name?.trim()) {
    errors.name = 'Name is required'
  } else if (data.name && data.name.length > (validationRules.nameMaxLength || 100)) {
    errors.name = `Name must be less than ${validationRules.nameMaxLength} characters`
  }
  
  // 验证值
  if (validationRules.valueRequired && !isUpdate && data.value === undefined) {
    errors.value = 'Value is required'
  } else if (data.value !== undefined) {
    if (isNaN(data.value)) {
      errors.value = 'Value must be a valid number'
    } else if (validationRules.valueMin !== undefined && data.value < validationRules.valueMin) {
      errors.value = `Value must be at least ${validationRules.valueMin}`
    } else if (validationRules.valueMax !== undefined && data.value > validationRules.valueMax) {
      errors.value = `Value must be at most ${validationRules.valueMax}`
    }
  }
  
  // 验证目标值
  if (validationRules.targetRequired && !data.target) {
    errors.target = 'Target is required'
  } else if (data.target !== undefined) {
    if (isNaN(data.target)) {
      errors.target = 'Target must be a valid number'
    } else if (validationRules.valueMin !== undefined && data.target < validationRules.valueMin) {
      errors.target = `Target must be at least ${validationRules.valueMin}`
    }
  }
  
  // 验证单位
  if (data.unit && data.unit.length > (validationRules.unitMaxLength || 20)) {
    errors.unit = `Unit must be less than ${validationRules.unitMaxLength} characters`
  }
  
  // 验证描述
  if (data.description && data.description.length > (validationRules.noteMaxLength || 500)) {
    errors.description = `Description must be less than ${validationRules.noteMaxLength} characters`
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

/**
 * 计算趋势方向
 */
export function calculateTrend(current: number, previous: number): 'up' | 'down' | 'stable' {
  const threshold = 0.01 // 1% 的变化阈值
  const change = Math.abs(current - previous) / Math.max(previous, 1)
  
  if (change < threshold) return 'stable'
  return current > previous ? 'up' : 'down'
}

/**
 * 生成KPI建议
 */
export function generateKPIRecommendations(kpi: BaseKPI): string[] {
  const recommendations: string[] = []
  const progress = calculateKPIProgress(kpi)
  
  if (!kpi.target) {
    recommendations.push('Consider setting a target value to track progress')
  } else if (progress < 25) {
    recommendations.push('This KPI is significantly behind target - consider reviewing your strategy')
    recommendations.push('Break down the target into smaller, achievable milestones')
  } else if (progress < 50) {
    recommendations.push('This KPI needs attention - consider increasing effort or adjusting approach')
  } else if (progress < 75) {
    recommendations.push('Good progress! Stay focused to reach your target')
  } else if (progress < 100) {
    recommendations.push('Excellent progress! You\'re very close to achieving your target')
  } else {
    recommendations.push('Congratulations! You\'ve achieved your target')
    recommendations.push('Consider setting a new, more ambitious target')
  }
  
  // 基于方向的建议
  if (kpi.direction === 'decrease' && kpi.value > (kpi.target || 0)) {
    recommendations.push('Focus on strategies to reduce this metric')
  } else if (kpi.direction === 'increase' && kpi.value < (kpi.target || 0)) {
    recommendations.push('Look for opportunities to increase this metric')
  }
  
  return recommendations
}

/**
 * 格式化日期显示
 */
export function formatDate(date: Date, format: 'short' | 'medium' | 'long' = 'medium'): string {
  const options: Intl.DateTimeFormatOptions = {
    short: { month: 'short', day: 'numeric' },
    medium: { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' },
    long: { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' }
  }
  
  return new Intl.DateTimeFormat('en-US', options[format]).format(date)
}

/**
 * 计算时间差
 */
export function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / 60000)
  const diffHours = Math.floor(diffMins / 60)
  const diffDays = Math.floor(diffHours / 24)
  
  if (diffMins < 1) return 'just now'
  if (diffMins < 60) return `${diffMins}m ago`
  if (diffHours < 24) return `${diffHours}h ago`
  if (diffDays < 7) return `${diffDays}d ago`
  
  return formatDate(date, 'short')
}

/**
 * 深度合并对象
 */
export function deepMerge<T>(target: T, source: Partial<T>): T {
  const result = { ...target }
  
  for (const key in source) {
    if (source[key] !== undefined) {
      if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
        result[key] = deepMerge(result[key] as any, source[key] as any)
      } else {
        result[key] = source[key] as any
      }
    }
  }
  
  return result
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}
