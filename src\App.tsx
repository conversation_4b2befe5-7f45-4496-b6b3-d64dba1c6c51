// App Component - 应用程序主组件
// 应用程序的根组件，包含路由和全局状态管理

import { Component, createSignal, createEffect } from 'solid-js';
import { Layout } from './components/layout/Layout';
import Dashboard from './pages/Dashboard';
import Inbox from './pages/Inbox';
import Projects from './pages/Projects';
import ProjectDetailNew from './pages/ProjectDetailNew';
import { defaultMenuItems, MenuItem } from './components/layout/Sidebar';
import { DevTools } from './components/DevTools';
import { ThemeProvider } from './contexts/ThemeContext';
import { cn } from '@/lib/utils';
import './app.css';

// 全局应用状态
interface AppState {
  user: {
    name: string;
    email: string;
    avatar?: string;
  } | null;
  theme: 'light' | 'dark' | 'system';
  sidebarCollapsed: boolean;
  notifications: Array<{
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message?: string;
    timestamp: string;
  }>;
}

const App: Component = () => {
  const [appState, setAppState] = createSignal<AppState>({
    user: {
      name: 'John <PERSON>e',
      email: '<EMAIL>',
      avatar: undefined,
    },
    theme: 'system',
    sidebarCollapsed: false,
    notifications: [
      {
        id: '1',
        type: 'info',
        title: '系统更新',
        message: '新版本已发布，包含性能优化和bug修复',
        timestamp: '2024-01-15T10:30:00Z',
      },
      {
        id: '2',
        type: 'success',
        title: '项目完成',
        message: 'BubbleSay 项目已成功部署到生产环境',
        timestamp: '2024-01-15T09:15:00Z',
      },
    ],
  });

  const [currentPath, setCurrentPath] = createSignal('/dashboard');
  const [activeMenuItem, setActiveMenuItem] = createSignal('dashboard');

  // 主题管理
  createEffect(() => {
    const theme = appState().theme;
    const root = document.documentElement;

    if (theme === 'dark') {
      root.classList.add('dark');
    } else if (theme === 'light') {
      root.classList.remove('dark');
    } else {
      // system theme
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      if (prefersDark) {
        root.classList.add('dark');
      } else {
        root.classList.remove('dark');
      }
    }
  });

  // 路由变化处理
  createEffect(() => {
    const path = window.location.pathname;
    setCurrentPath(path);

    // 根据路径设置活跃菜单项
    if (path.startsWith('/dashboard')) {
      setActiveMenuItem('dashboard');
    } else if (path.startsWith('/projects')) {
      setActiveMenuItem('projects');
    } else if (path.startsWith('/inbox')) {
      setActiveMenuItem('inbox');
    } else if (path.startsWith('/areas')) {
      setActiveMenuItem('areas');
    } else if (path.startsWith('/resources')) {
      setActiveMenuItem('resources');
    } else if (path.startsWith('/archive')) {
      setActiveMenuItem('archive');
    } else if (path.startsWith('/review')) {
      setActiveMenuItem('review');
    } else if (path.startsWith('/settings')) {
      setActiveMenuItem('settings');
    }
  });

  // 监听浏览器前进后退事件和自定义路由变化事件
  createEffect(() => {
    const handlePopState = () => {
      setCurrentPath(window.location.pathname);
    };

    const handleRouteChange = () => {
      setCurrentPath(window.location.pathname);
    };

    window.addEventListener('popstate', handlePopState);
    window.addEventListener('routechange', handleRouteChange);

    // 清理事件监听器
    return () => {
      window.removeEventListener('popstate', handlePopState);
      window.removeEventListener('routechange', handleRouteChange);
    };
  });

  // 简单的路由导航函数
  const navigate = (path: string) => {
    window.history.pushState({}, '', path);
    setCurrentPath(path);
  };

  // 菜单项点击处理
  const handleMenuItemClick = (item: MenuItem) => {
    if (item.path) {
      window.history.pushState({}, '', item.path);
      setCurrentPath(item.path);
      setActiveMenuItem(item.id);
    }
    item.onClick?.();
  };

  // 用户菜单点击处理
  const handleUserMenuClick = () => {
    console.log('User menu clicked');
  };

  // 设置点击处理
  const handleSettingsClick = () => {
    window.history.pushState({}, '', '/settings');
    setCurrentPath('/settings');
    setActiveMenuItem('settings');
  };

  // 通知点击处理
  const handleNotificationClick = () => {
    console.log('Notifications clicked');
  };

  // 搜索处理
  const handleSearch = (query: string) => {
    console.log('Search query:', query);
    // TODO: 实现全局搜索功能
  };

  // 获取页面标题
  const getPageTitle = () => {
    switch (activeMenuItem()) {
      case 'dashboard':
        return '仪表盘';
      case 'projects':
        return '项目管理';
      case 'inbox':
        return '收件箱';
      case 'areas':
        return '领域管理';
      case 'resources':
        return '资源库';
      case 'archive':
        return '归档管理';
      case 'review':
        return '复盘总结';
      case 'settings':
        return '设置';
      default:
        return 'PaoLife';
    }
  };

  // 渲染页面内容
  const renderPageContent = () => {
    const path = currentPath();

    // 检查是否是项目详情页面
    if (path.startsWith('/projects/') && path !== '/projects') {
      return <ProjectDetailNew />;
    }

    switch (activeMenuItem()) {
      case 'dashboard':
        return <Dashboard />;
      case 'projects':
        return <Projects />;
      case 'inbox':
        return <Inbox />;
      case 'areas':
        return <AreasPlaceholder />;
      case 'resources':
        return <ResourcesPlaceholder />;
      case 'archive':
        return <ArchivePlaceholder />;
      case 'review':
        return <ReviewPlaceholder />;
      case 'settings':
        return <SettingsPlaceholder />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <ThemeProvider>
      <div class={cn('h-screen overflow-hidden bg-background font-sans antialiased')}>
        <Layout
          title={getPageTitle()}
          user={appState().user || undefined}
          menuItems={defaultMenuItems}
          activeMenuItem={activeMenuItem()}
          onMenuItemClick={handleMenuItemClick}
          onUserMenuClick={handleUserMenuClick}
          onSettingsClick={handleSettingsClick}
          onNotificationClick={handleNotificationClick}
          onSearch={handleSearch}
          notificationCount={appState().notifications.length}
          fullBleed
          headerFullBleed
        >
          {renderPageContent()}
        </Layout>
        <DevTools />
      </div>
    </ThemeProvider>
  );
};

// 占位符组件 - 将来会被实际页面替换
const InboxPlaceholder = () => (
  <div class="flex items-center justify-center h-96">
    <div class="text-center">
      <h2 class="text-2xl font-bold mb-4">收件箱</h2>
      <p class="text-muted-foreground">收件箱功能正在开发中...</p>
    </div>
  </div>
);

const AreasPlaceholder = () => (
  <div class="flex items-center justify-center h-96">
    <div class="text-center">
      <h2 class="text-2xl font-bold mb-4">领域管理</h2>
      <p class="text-muted-foreground">领域管理功能正在开发中...</p>
    </div>
  </div>
);

const ResourcesPlaceholder = () => (
  <div class="flex items-center justify-center h-96">
    <div class="text-center">
      <h2 class="text-2xl font-bold mb-4">资源库</h2>
      <p class="text-muted-foreground">资源库功能正在开发中...</p>
    </div>
  </div>
);

const ArchivePlaceholder = () => (
  <div class="flex items-center justify-center h-96">
    <div class="text-center">
      <h2 class="text-2xl font-bold mb-4">归档管理</h2>
      <p class="text-muted-foreground">归档管理功能正在开发中...</p>
    </div>
  </div>
);

const ReviewPlaceholder = () => (
  <div class="flex items-center justify-center h-96">
    <div class="text-center">
      <h2 class="text-2xl font-bold mb-4">复盘总结</h2>
      <p class="text-muted-foreground">复盘总结功能正在开发中...</p>
    </div>
  </div>
);

const SettingsPlaceholder = () => (
  <div class="flex items-center justify-center h-96">
    <div class="text-center">
      <h2 class="text-2xl font-bold mb-4">设置</h2>
      <p class="text-muted-foreground">设置功能正在开发中...</p>
    </div>
  </div>
);

export default App;
