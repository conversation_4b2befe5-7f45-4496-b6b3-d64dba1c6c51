/**
 * 统一KPI组件类型定义
 * 为可复用的KPI指标跟踪组件提供完整的类型支持
 */

import { JSX } from 'solid-js'

// ============================================================================
// 基础数据类型
// ============================================================================

export type KPIDirection = 'increase' | 'decrease'
export type KPIFrequency = 'daily' | 'weekly' | 'monthly' | 'quarterly'
export type KPIStatus = 'achieved' | 'on-track' | 'at-risk' | 'behind' | 'no-target'
export type KPIType = 'project' | 'area' | 'custom'

// 基础KPI数据结构
export interface BaseKPI {
  id: string
  name: string
  description?: string
  value: number
  target?: number
  unit?: string
  direction: KPIDirection
  frequency?: KPIFrequency
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// KPI记录数据结构
export interface KPIRecord {
  id: string
  kpiId: string
  value: number
  note?: string
  recordedAt: Date
  source: 'manual' | 'auto' | 'import'
}

// KPI进度信息
export interface KPIProgress {
  current: number
  target?: number
  progress: number // 0-100
  status: KPIStatus
  trend?: 'up' | 'down' | 'stable'
  lastUpdated: Date
}

// KPI统计信息
export interface KPIStatistics {
  total: number
  achieved: number
  onTrack: number
  atRisk: number
  behind: number
  noTarget: number
  averageProgress: number
}

// ============================================================================
// 配置接口
// ============================================================================

// 组件配置选项
export interface KPIComponentConfig {
  // 显示选项
  showChart?: boolean
  showQuickInput?: boolean
  showHistory?: boolean
  showStatistics?: boolean
  
  // 交互选项
  allowCreate?: boolean
  allowEdit?: boolean
  allowDelete?: boolean
  allowBatchRecord?: boolean
  
  // 布局选项
  layout?: 'grid' | 'list' | 'compact'
  chartType?: 'bar' | 'line' | 'pie' | 'donut'
  
  // 数据选项
  maxRecords?: number
  autoRefresh?: boolean
  refreshInterval?: number
  
  // 样式选项
  className?: string
  theme?: 'light' | 'dark' | 'auto'
}

// KPI模板定义
export interface KPITemplate {
  name: string
  description?: string
  unit?: string
  target?: number
  direction: KPIDirection
  frequency?: KPIFrequency
  category?: string
}

// 验证规则
export interface ValidationRules {
  nameRequired?: boolean
  nameMaxLength?: number
  valueRequired?: boolean
  valueMin?: number
  valueMax?: number
  targetRequired?: boolean
  unitMaxLength?: number
  noteMaxLength?: number
}

// 完整的KPI配置
export interface KPIConfig {
  type: KPIType
  templates: KPITemplate[]
  allowedUnits: string[]
  validationRules: ValidationRules
  defaultFrequency: KPIFrequency
  defaultDirection: KPIDirection
}

// ============================================================================
// 事件接口
// ============================================================================

// KPI事件类型
export type KPIEventType = 
  | 'create'
  | 'update' 
  | 'delete'
  | 'record'
  | 'progress-change'
  | 'status-change'

// KPI事件数据
export interface KPIEvent<T = any> {
  type: KPIEventType
  kpiId: string
  data: T
  timestamp: Date
}

// 事件回调函数
export interface KPIEventHandlers {
  onCreate?: (kpi: BaseKPI) => void | Promise<void>
  onUpdate?: (kpi: BaseKPI, changes: Partial<BaseKPI>) => void | Promise<void>
  onDelete?: (kpiId: string) => void | Promise<void>
  onRecord?: (record: KPIRecord) => void | Promise<void>
  onProgressChange?: (kpiId: string, progress: KPIProgress) => void | Promise<void>
  onStatusChange?: (kpiId: string, oldStatus: KPIStatus, newStatus: KPIStatus) => void | Promise<void>
  onError?: (error: Error, context?: string) => void
}

// ============================================================================
// API接口
// ============================================================================

// KPI数据源接口
export interface KPIDataSource {
  // KPI CRUD操作
  getKPIs(parentId: string, options?: QueryOptions): Promise<BaseKPI[]>
  getKPI(id: string): Promise<BaseKPI | null>
  createKPI(parentId: string, data: CreateKPIData): Promise<BaseKPI>
  updateKPI(id: string, data: Partial<CreateKPIData>): Promise<BaseKPI>
  deleteKPI(id: string): Promise<void>
  
  // 记录操作
  getRecords(kpiId: string, options?: QueryOptions): Promise<KPIRecord[]>
  createRecord(kpiId: string, data: CreateRecordData): Promise<KPIRecord>
  deleteRecord(recordId: string): Promise<void>
  
  // 统计信息
  getStatistics(parentId: string): Promise<KPIStatistics>
}

// 查询选项
export interface QueryOptions {
  limit?: number
  offset?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  filter?: Record<string, any>
  dateRange?: {
    start: Date
    end: Date
  }
}

// 创建KPI数据
export interface CreateKPIData {
  name: string
  description?: string
  value: number
  target?: number
  unit?: string
  direction: KPIDirection
  frequency?: KPIFrequency
}

// 创建记录数据
export interface CreateRecordData {
  value: number
  note?: string
  recordedAt?: Date
  source?: 'manual' | 'auto' | 'import'
}

// ============================================================================
// 组件Props接口
// ============================================================================

// 基础组件Props
export interface BaseComponentProps {
  class?: string
  children?: JSX.Element
}

// KPI跟踪器主组件Props
export interface KPITrackerProps extends BaseComponentProps {
  // 必需属性
  parentId: string
  parentType: KPIType
  dataSource: KPIDataSource
  
  // 可选配置
  config?: Partial<KPIComponentConfig>
  kpiConfig?: Partial<KPIConfig>
  eventHandlers?: KPIEventHandlers
  
  // 初始数据
  initialKPIs?: BaseKPI[]
  
  // 样式和主题
  theme?: 'light' | 'dark' | 'auto'
  className?: string
}

// KPI图表组件Props
export interface KPIChartProps extends BaseComponentProps {
  kpis: BaseKPI[]
  type?: 'bar' | 'line' | 'pie' | 'donut' | 'progress'
  showLegend?: boolean
  showTooltip?: boolean
  height?: number
  onKPIClick?: (kpi: BaseKPI) => void
}

// KPI输入组件Props
export interface KPIInputProps extends BaseComponentProps {
  kpi: BaseKPI
  onRecord?: (record: KPIRecord) => void
  onEdit?: (kpi: BaseKPI) => void
  onDelete?: (kpiId: string) => void
  showHistory?: boolean
  allowEdit?: boolean
  allowDelete?: boolean
}

// KPI管理对话框Props
export interface KPIDialogProps extends BaseComponentProps {
  open: boolean
  mode: 'create' | 'edit'
  kpi?: BaseKPI
  config?: KPIConfig
  onSubmit: (data: CreateKPIData) => Promise<void>
  onCancel: () => void
}

// ============================================================================
// 工具类型
// ============================================================================

// 深度可选类型
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

// 选择性必需类型
export type RequireFields<T, K extends keyof T> = T & Required<Pick<T, K>>

// KPI状态颜色映射
export interface StatusColorMap {
  [K in KPIStatus]: {
    bg: string
    text: string
    border: string
    progress: string
  }
}

// 导出默认配置
export const DEFAULT_KPI_CONFIG: KPIComponentConfig = {
  showChart: true,
  showQuickInput: true,
  showHistory: false,
  showStatistics: true,
  allowCreate: true,
  allowEdit: true,
  allowDelete: true,
  allowBatchRecord: false,
  layout: 'grid',
  chartType: 'bar',
  maxRecords: 20,
  autoRefresh: false,
  refreshInterval: 30000,
  theme: 'auto'
}
