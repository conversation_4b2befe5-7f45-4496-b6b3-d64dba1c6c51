# PaoLife 前端开发指南

## 📋 概述

本文档提供了PaoLife项目前端开发的完整指南，包括SolidJS框架使用、状态管理、组件开发、样式系统等内容。

## ⚡ SolidJS 开发指南

### 1. 项目结构

#### 目录组织
```
src/
├── components/           # 通用组件
│   ├── ui/              # 基础UI组件
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   ├── Modal.tsx
│   │   └── index.ts
│   ├── layout/          # 布局组件
│   │   ├── Header.tsx
│   │   ├── Sidebar.tsx
│   │   └── Layout.tsx
│   └── common/          # 通用业务组件
├── pages/               # 页面组件
│   ├── Dashboard.tsx
│   ├── Projects.tsx
│   ├── Tasks.tsx
│   └── Settings.tsx
├── stores/              # 状态管理
│   ├── userStore.ts
│   ├── projectStore.ts
│   └── appStore.ts
├── services/            # API服务
│   ├── api.ts
│   ├── userService.ts
│   └── projectService.ts
├── types/               # 类型定义
│   ├── user.ts
│   ├── project.ts
│   └── api.ts
├── utils/               # 工具函数
│   ├── date.ts
│   ├── validation.ts
│   └── format.ts
└── assets/              # 静态资源
    ├── icons/
    └── images/
```

### 2. 组件开发

#### 基础组件模板
```typescript
// src/components/ui/Button.tsx
import { Component, JSX, splitProps } from 'solid-js';
import { cn } from '../../utils/cn';

interface ButtonProps extends JSX.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  children: JSX.Element;
}

const Button: Component<ButtonProps> = (props) => {
  const [local, others] = splitProps(props, ['variant', 'size', 'loading', 'children', 'class']);

  const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50';
  
  const variantClasses = {
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
  };
  
  const sizeClasses = {
    sm: 'h-9 px-3 text-sm',
    md: 'h-10 px-4 py-2',
    lg: 'h-11 px-8 text-lg',
  };

  return (
    <button
      class={cn(
        baseClasses,
        variantClasses[local.variant || 'primary'],
        sizeClasses[local.size || 'md'],
        local.loading && 'cursor-not-allowed',
        local.class
      )}
      disabled={local.loading || others.disabled}
      {...others}
    >
      {local.loading && (
        <svg class="mr-2 h-4 w-4 animate-spin" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" />
          <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
        </svg>
      )}
      {local.children}
    </button>
  );
};

export default Button;
```

#### 表单组件
```typescript
// src/components/ui/Input.tsx
import { Component, JSX, splitProps } from 'solid-js';
import { cn } from '../../utils/cn';

interface InputProps extends JSX.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
}

const Input: Component<InputProps> = (props) => {
  const [local, others] = splitProps(props, ['label', 'error', 'helperText', 'class']);

  return (
    <div class="space-y-2">
      {local.label && (
        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {local.label}
        </label>
      )}
      <input
        class={cn(
          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
          local.error && 'border-destructive focus-visible:ring-destructive',
          local.class
        )}
        {...others}
      />
      {local.error && (
        <p class="text-sm text-destructive">{local.error}</p>
      )}
      {local.helperText && !local.error && (
        <p class="text-sm text-muted-foreground">{local.helperText}</p>
      )}
    </div>
  );
};

export default Input;
```

### 3. 状态管理

#### Store 模式
```typescript
// src/stores/userStore.ts
import { createStore } from 'solid-js/store';
import { createSignal } from 'solid-js';
import { User } from '../types/user';
import { userService } from '../services/userService';

interface UserState {
  currentUser: User | null;
  users: User[];
  loading: boolean;
  error: string | null;
}

const [userState, setUserState] = createStore<UserState>({
  currentUser: null,
  users: [],
  loading: false,
  error: null,
});

const [isAuthenticated, setIsAuthenticated] = createSignal(false);

export const userStore = {
  // State
  get state() {
    return userState;
  },
  
  get isAuthenticated() {
    return isAuthenticated();
  },

  // Actions
  async login(credentials: LoginCredentials) {
    setUserState('loading', true);
    setUserState('error', null);
    
    try {
      const user = await userService.login(credentials);
      setUserState('currentUser', user);
      setIsAuthenticated(true);
      return user;
    } catch (error) {
      setUserState('error', error.message);
      throw error;
    } finally {
      setUserState('loading', false);
    }
  },

  async logout() {
    try {
      await userService.logout();
      setUserState('currentUser', null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Logout failed:', error);
    }
  },

  async fetchUsers() {
    setUserState('loading', true);
    try {
      const users = await userService.getUsers();
      setUserState('users', users);
    } catch (error) {
      setUserState('error', error.message);
    } finally {
      setUserState('loading', false);
    }
  },

  updateUser(userId: string, updates: Partial<User>) {
    setUserState('users', (user) => user.id === userId, updates);
    if (userState.currentUser?.id === userId) {
      setUserState('currentUser', updates);
    }
  },

  clearError() {
    setUserState('error', null);
  },
};
```

#### Context 模式
```typescript
// src/contexts/AppContext.tsx
import { createContext, useContext, ParentComponent } from 'solid-js';
import { createStore } from 'solid-js/store';

interface AppState {
  theme: 'light' | 'dark' | 'system';
  sidebarCollapsed: boolean;
  notifications: Notification[];
}

interface AppContextValue {
  state: AppState;
  actions: {
    setTheme: (theme: AppState['theme']) => void;
    toggleSidebar: () => void;
    addNotification: (notification: Omit<Notification, 'id'>) => void;
    removeNotification: (id: string) => void;
  };
}

const AppContext = createContext<AppContextValue>();

export const AppProvider: ParentComponent = (props) => {
  const [state, setState] = createStore<AppState>({
    theme: 'system',
    sidebarCollapsed: false,
    notifications: [],
  });

  const actions = {
    setTheme: (theme: AppState['theme']) => {
      setState('theme', theme);
      localStorage.setItem('theme', theme);
    },

    toggleSidebar: () => {
      setState('sidebarCollapsed', !state.sidebarCollapsed);
    },

    addNotification: (notification: Omit<Notification, 'id'>) => {
      const newNotification = {
        ...notification,
        id: crypto.randomUUID(),
      };
      setState('notifications', (prev) => [...prev, newNotification]);
    },

    removeNotification: (id: string) => {
      setState('notifications', (prev) => prev.filter((n) => n.id !== id));
    },
  };

  return (
    <AppContext.Provider value={{ state, actions }}>
      {props.children}
    </AppContext.Provider>
  );
};

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within AppProvider');
  }
  return context;
};
```

### 4. API 集成

#### Tauri 命令调用
```typescript
// src/services/api.ts
import { invoke } from '@tauri-apps/api/tauri';

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    total?: number;
    page?: number;
    pageSize?: number;
  };
}

export class ApiError extends Error {
  constructor(
    public code: string,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export async function apiCall<T>(
  command: string,
  args?: Record<string, any>
): Promise<T> {
  try {
    const response = await invoke<ApiResponse<T>>(command, args);
    
    if (response.success && response.data !== undefined) {
      return response.data;
    } else if (response.error) {
      throw new ApiError(
        response.error.code,
        response.error.message,
        response.error.details
      );
    } else {
      throw new ApiError('UNKNOWN_ERROR', 'Unknown API error');
    }
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('NETWORK_ERROR', 'Network or system error', error);
  }
}
```

#### 服务层实现
```typescript
// src/services/projectService.ts
import { apiCall } from './api';
import { Project, CreateProjectData, UpdateProjectData } from '../types/project';

export const projectService = {
  async getProjects(params?: {
    page?: number;
    pageSize?: number;
    search?: string;
    status?: string[];
  }): Promise<{ projects: Project[]; total: number }> {
    return apiCall('get_projects', params);
  },

  async getProject(id: string): Promise<Project> {
    return apiCall('get_project_detail', { id });
  },

  async createProject(data: CreateProjectData): Promise<Project> {
    return apiCall('create_project', data);
  },

  async updateProject(id: string, data: UpdateProjectData): Promise<Project> {
    return apiCall('update_project', { id, ...data });
  },

  async deleteProject(id: string): Promise<void> {
    return apiCall('delete_project', { id });
  },

  async getProjectStats(id: string): Promise<ProjectStats> {
    return apiCall('get_project_analytics', { projectId: id });
  },
};
```

### 5. 路由管理

#### 路由配置
```typescript
// src/App.tsx
import { Router, Route, Routes } from '@solidjs/router';
import { Component, lazy } from 'solid-js';
import Layout from './components/layout/Layout';
import { AppProvider } from './contexts/AppContext';

// 懒加载页面组件
const Dashboard = lazy(() => import('./pages/Dashboard'));
const Projects = lazy(() => import('./pages/Projects'));
const ProjectDetail = lazy(() => import('./pages/ProjectDetail'));
const Tasks = lazy(() => import('./pages/Tasks'));
const Areas = lazy(() => import('./pages/Areas'));
const Resources = lazy(() => import('./pages/Resources'));
const Settings = lazy(() => import('./pages/Settings'));

const App: Component = () => {
  return (
    <AppProvider>
      <Router>
        <Routes>
          <Route path="/" component={Layout}>
            <Route path="/" component={Dashboard} />
            <Route path="/projects" component={Projects} />
            <Route path="/projects/:id" component={ProjectDetail} />
            <Route path="/tasks" component={Tasks} />
            <Route path="/areas" component={Areas} />
            <Route path="/resources" component={Resources} />
            <Route path="/settings" component={Settings} />
          </Route>
        </Routes>
      </Router>
    </AppProvider>
  );
};

export default App;
```

#### 路由守卫
```typescript
// src/components/layout/Layout.tsx
import { Component, Show, createEffect } from 'solid-js';
import { Outlet, useNavigate } from '@solidjs/router';
import { userStore } from '../stores/userStore';
import Header from './Header';
import Sidebar from './Sidebar';

const Layout: Component = () => {
  const navigate = useNavigate();

  createEffect(() => {
    if (!userStore.isAuthenticated) {
      navigate('/login', { replace: true });
    }
  });

  return (
    <Show when={userStore.isAuthenticated}>
      <div class="flex h-screen bg-background">
        <Sidebar />
        <div class="flex flex-1 flex-col overflow-hidden">
          <Header />
          <main class="flex-1 overflow-auto p-6">
            <Outlet />
          </main>
        </div>
      </div>
    </Show>
  );
};

export default Layout;
```

### 6. 样式系统

#### Tailwind CSS 配置
```javascript
// tailwind.config.js
/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace'],
      },
    },
  },
  plugins: [],
};
```

#### CSS 变量定义
```css
/* src/styles/globals.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
```

---

**文档版本**: 1.0  
**创建日期**: 2024年12月  
**维护者**: 开发团队  
**下次更新**: 根据前端技术栈演进需要
