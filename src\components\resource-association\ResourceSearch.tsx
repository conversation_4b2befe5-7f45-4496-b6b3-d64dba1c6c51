/**
 * 资源搜索组件
 * 支持实时搜索、过滤、排序等功能
 */

import { createSignal, createEffect, For, Show } from 'solid-js'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { 
  Search, 
  Filter,
  X,
  SortAsc,
  SortDesc
} from 'lucide-solid'
import { cn } from '../../lib/utils'

interface ResourceSearchProps {
  onSearch: (query: string) => void
  placeholder?: string
  class?: string
  disabled?: boolean
}

export function ResourceSearch(props: ResourceSearchProps) {
  // 状态管理
  const [query, setQuery] = createSignal('')
  const [isSearching, setIsSearching] = createSignal(false)

  // 防抖搜索
  createEffect(() => {
    const searchQuery = query()
    setIsSearching(true)
    
    const timeoutId = setTimeout(() => {
      props.onSearch(searchQuery)
      setIsSearching(false)
    }, 300)
    
    return () => clearTimeout(timeoutId)
  })

  // 清除搜索
  const clearSearch = () => {
    setQuery('')
  }

  return (
    <div class={cn("relative", props.class)}>
      <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
      <Input
        placeholder={props.placeholder || "Search resources..."}
        value={query()}
        onInput={(e) => setQuery(e.currentTarget.value)}
        class="pl-10 pr-10"
        disabled={props.disabled}
      />
      <Show when={query()}>
        <Button
          variant="ghost"
          size="sm"
          class="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
          onClick={clearSearch}
        >
          <X class="h-3 w-3" />
        </Button>
      </Show>
      <Show when={isSearching()}>
        <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
        </div>
      </Show>
    </div>
  )
}

export default ResourceSearch
