/**
 * 引用解析引擎
 * 解析文档中的各种引用类型
 */

import { UnifiedReference, WikiLinkReference, ProjectReference, AreaReference } from './unifiedReferenceService'

export interface ParsedReference {
  type: 'wikilink' | 'project' | 'area'
  target: string
  display?: string
  context: string
  startIndex: number
  endIndex: number
  lineNumber: number
  columnNumber: number
  strength: number
}

/**
 * 引用解析器类
 */
export class ReferenceParser {
  /**
   * 解析文档中的所有引用
   */
  parseAllReferences(content: string, documentPath: string): ParsedReference[] {
    console.log('🔍 开始解析文档引用:', documentPath)

    const references: ParsedReference[] = []

    // 解析 WikiLink 引用
    const wikiLinks = this.parseWikiLinks(content)
    references.push(...wikiLinks)

    // 解析项目引用（基于特定语法）
    const projectRefs = this.parseProjectReferences(content)
    references.push(...projectRefs)

    // 解析领域引用（基于特定语法）
    const areaRefs = this.parseAreaReferences(content)
    references.push(...areaRefs)

    console.log('✅ 引用解析完成:', {
      总数: references.length,
      WikiLink: wikiLinks.length,
      项目引用: projectRefs.length,
      领域引用: areaRefs.length,
      详细信息: {
        WikiLink: wikiLinks.map(ref => ref.target),
        项目引用: projectRefs.map(ref => ref.target),
        领域引用: areaRefs.map(ref => ref.target)
      }
    })

    return references
  }

  /**
   * 解析 WikiLink 引用：[[文档名]] 或 [[文档名|显示文本]]
   */
  parseWikiLinks(content: string): ParsedReference[] {
    const references: ParsedReference[] = []
    const wikiLinkRegex = /\[\[([^\]|]+)(\|([^\]]+))?\]\]/g
    
    let match
    while ((match = wikiLinkRegex.exec(content)) !== null) {
      const [fullMatch, target, , display] = match
      const startIndex = match.index
      const endIndex = match.index + fullMatch.length

      // 计算行号和列号
      const beforeText = content.substring(0, startIndex)
      const lines = beforeText.split('\n')
      const lineNumber = lines.length
      const columnNumber = lines[lines.length - 1].length + 1

      // 获取上下文
      const contextBefore = content.substring(Math.max(0, startIndex - 50), startIndex)
      const contextAfter = content.substring(endIndex, Math.min(content.length, endIndex + 50))
      const context = `${contextBefore}${fullMatch}${contextAfter}`

      references.push({
        type: 'wikilink',
        target: target.trim(),
        display: display?.trim(),
        context,
        startIndex,
        endIndex,
        lineNumber,
        columnNumber,
        strength: 1.0 // WikiLink 默认强度为 1.0
      })
    }

    return references
  }

  /**
   * 解析项目引用：@project:项目名 或 @task:任务名
   */
  parseProjectReferences(content: string): ParsedReference[] {
    console.log('🔍 开始解析项目引用，内容长度:', content.length)
    const references: ParsedReference[] = []

    // 项目引用语法：@project:项目名（支持中文、英文、数字、下划线、连字符）
    const projectRegex = /@project:([^\s\]，。！？；：""''（）【】]+)/g
    console.log('📋 项目引用正则表达式:', projectRegex)
    let match
    while ((match = projectRegex.exec(content)) !== null) {
      const [fullMatch, projectName] = match
      console.log('✅ 找到项目引用匹配:', { fullMatch, projectName, index: match.index })
      const startIndex = match.index
      const endIndex = match.index + fullMatch.length

      const beforeText = content.substring(0, startIndex)
      const lines = beforeText.split('\n')
      const lineNumber = lines.length
      const columnNumber = lines[lines.length - 1].length + 1

      const contextBefore = content.substring(Math.max(0, startIndex - 50), startIndex)
      const contextAfter = content.substring(endIndex, Math.min(content.length, endIndex + 50))
      const context = `${contextBefore}${fullMatch}${contextAfter}`

      references.push({
        type: 'project',
        target: projectName.trim(),
        context,
        startIndex,
        endIndex,
        lineNumber,
        columnNumber,
        strength: 0.8 // 项目引用强度稍低
      })
    }

    // 任务引用语法：@task:任务名（支持中文、英文、数字、下划线、连字符）
    const taskRegex = /@task:([^\s\]，。！？；：""''（）【】]+)/g
    console.log('✅ 任务引用正则表达式:', taskRegex)
    while ((match = taskRegex.exec(content)) !== null) {
      const [fullMatch, taskName] = match
      console.log('✅ 找到任务引用匹配:', { fullMatch, taskName, index: match.index })
      const startIndex = match.index
      const endIndex = match.index + fullMatch.length

      const beforeText = content.substring(0, startIndex)
      const lines = beforeText.split('\n')
      const lineNumber = lines.length
      const columnNumber = lines[lines.length - 1].length + 1

      const contextBefore = content.substring(Math.max(0, startIndex - 50), startIndex)
      const contextAfter = content.substring(endIndex, Math.min(content.length, endIndex + 50))
      const context = `${contextBefore}${fullMatch}${contextAfter}`

      references.push({
        type: 'project',
        target: taskName.trim(),
        context,
        startIndex,
        endIndex,
        lineNumber,
        columnNumber,
        strength: 0.9 // 任务引用强度较高
      })
    }

    return references
  }

  /**
   * 解析领域引用：#领域名 或 @area:领域名
   */
  parseAreaReferences(content: string): ParsedReference[] {
    console.log('🔍 开始解析领域引用，内容长度:', content.length)
    const references: ParsedReference[] = []

    // 领域标签语法：#领域名（但排除 Markdown 标题）
    // 改进正则表达式，支持中文和更好的边界检测
    const lines = content.split('\n')

    lines.forEach((line, lineIndex) => {
      // 跳过 Markdown 标题行
      if (line.trim().startsWith('#')) {
        return
      }

      // 查找行内的 # 标签
      const tagRegex = /#([^\s#，。！？；：""''（）【】\]]+)/g
      let match
      while ((match = tagRegex.exec(line)) !== null) {
        const [fullMatch, areaName] = match
        console.log('🏷️ 找到领域标签匹配:', { fullMatch, areaName, line: lineIndex + 1 })
        const lineStartIndex = content.split('\n').slice(0, lineIndex).join('\n').length + (lineIndex > 0 ? 1 : 0)
        const startIndex = lineStartIndex + match.index
        const endIndex = startIndex + fullMatch.length

        const contextBefore = content.substring(Math.max(0, startIndex - 50), startIndex)
        const contextAfter = content.substring(endIndex, Math.min(content.length, endIndex + 50))
        const context = `${contextBefore}${fullMatch}${contextAfter}`

        references.push({
          type: 'area',
          target: areaName.trim(),
          context,
          startIndex,
          endIndex,
          lineNumber: lineIndex + 1,
          columnNumber: match.index + 1,
          strength: 0.6 // 标签引用强度较低
        })
      }
    })

    // 显式领域引用语法：@area:领域名（支持中文、英文、数字、下划线、连字符）
    const areaRefRegex = /@area:([^\s\]，。！？；：""''（）【】]+)/g
    console.log('🏷️ 显式领域引用正则表达式:', areaRefRegex)
    let match
    while ((match = areaRefRegex.exec(content)) !== null) {
      const [fullMatch, areaName] = match
      console.log('✅ 找到显式领域引用匹配:', { fullMatch, areaName, index: match.index })
      const startIndex = match.index
      const endIndex = match.index + fullMatch.length

      const beforeText = content.substring(0, startIndex)
      const lines = beforeText.split('\n')
      const lineNumber = lines.length
      const columnNumber = lines[lines.length - 1].length + 1

      const contextBefore = content.substring(Math.max(0, startIndex - 50), startIndex)
      const contextAfter = content.substring(endIndex, Math.min(content.length, endIndex + 50))
      const context = `${contextBefore}${fullMatch}${contextAfter}`

      references.push({
        type: 'area',
        target: areaName.trim(),
        context,
        startIndex,
        endIndex,
        lineNumber,
        columnNumber,
        strength: 0.8 // 显式领域引用强度较高
      })
    }

    return references
  }

  /**
   * 验证引用目标是否有效
   */
  validateReference(reference: ParsedReference): boolean {
    // 基本验证
    if (!reference.target || reference.target.trim().length === 0) {
      return false
    }

    // 检查目标名称长度
    if (reference.target.length > 255) {
      return false
    }

    // 检查非法字符
    const invalidChars = /[<>:"/\\|?*]/
    if (invalidChars.test(reference.target)) {
      return false
    }

    return true
  }

  /**
   * 计算引用强度
   * 基于引用类型、上下文、位置等因素
   */
  calculateReferenceStrength(reference: ParsedReference, content: string): number {
    let strength = reference.strength

    // 基于上下文调整强度
    const contextWords = reference.context.toLowerCase()
    
    // 如果在重要位置（标题、列表项开头），增加强度
    if (contextWords.includes('# ') || contextWords.includes('## ') || contextWords.includes('- ')) {
      strength += 0.1
    }

    // 如果有描述性文字，增加强度
    if (contextWords.includes('参考') || contextWords.includes('相关') || contextWords.includes('详见')) {
      strength += 0.1
    }

    // 确保强度在 0-1 范围内
    return Math.min(1.0, Math.max(0.0, strength))
  }

  /**
   * 去重引用
   */
  deduplicateReferences(references: ParsedReference[]): ParsedReference[] {
    const seen = new Set<string>()
    const deduplicated: ParsedReference[] = []

    for (const ref of references) {
      const key = `${ref.type}-${ref.target}-${ref.startIndex}`
      if (!seen.has(key)) {
        seen.add(key)
        deduplicated.push(ref)
      }
    }

    return deduplicated
  }
}

// 导出单例实例
export const referenceParser = new ReferenceParser()
