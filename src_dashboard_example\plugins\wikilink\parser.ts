import { visit } from 'unist-util-visit'
import type { Plugin } from 'unified'
import type { Node as UnistNode } from 'unist'
import type { Text, Parent, Root } from 'mdast'

/**
 * WikiLink AST 节点类型定义
 * 符合 Milkdown 期望的 Remark AST 节点结构
 */
interface WikiLinkNode extends UnistNode {
  type: 'wikilink'
  // 将属性存储在 data 中，这是 Milkdown 期望的方式
  data: {
    target: string
    display?: string
    // 用于 HTML 转换的属性
    hName: 'a'
    hProperties: {
      'data-wikilink': 'true'
      'data-target': string
      'data-valid': 'true'
      'class': 'wikilink'
      'href': '#'
    }
  }
}

/**
 * 检查节点是否为 WikiLink 节点
 */
function isWikiLinkNode(node: UnistNode): node is WikiLinkNode {
  return node.type === 'wikilink'
}

/**
 * WikiLink 解析器 - 将 Markdown 文本解析为 AST
 * 
 * 支持的语法：
 * - [[页面名称]]
 * - [[页面名称|显示文本]]
 */
export const wikLinkParser: Plugin = function () {
  return function transformer(tree: Root) {
    console.log('🔍 WikiLink 解析器开始处理 AST')
    visit(tree, 'text', (node: Text, index: number, parent: Parent) => {
      if (!parent || typeof index !== 'number') {
        return
      }

      const text = node.value
      console.log('📝 处理文本节点:', text.substring(0, 100) + (text.length > 100 ? '...' : ''))

      // 跳过转义的 WikiLink (以 \ 开头的)
      if (text.includes('\\[\\[')) {
        console.log('⏭️ 跳过转义的 WikiLink')
        return
      }

      const wikiLinkRegex = /\[\[([^\]|]+)(\|([^\]]+))?\]\]/g
      
      let match
      const replacements: Array<{ start: number; end: number; node: WikiLinkNode }> = []
      
      // 查找所有 WikiLink 匹配
      while ((match = wikiLinkRegex.exec(text)) !== null) {
        // 添加安全检查
        if (!match || !match[0] || !match[1]) {
          console.warn('WikiLink 解析器: 无效的匹配结果', match)
          continue
        }

        const [fullMatch, target, , display] = match
        const start = match.index
        const end = start + fullMatch.length

        console.log('🔍 WikiLink 解析器找到匹配:', { fullMatch, target, display, start, end })
        
        // 创建 WikiLink 节点 - 使用正确的 Remark AST 结构
        const wikiLinkNode: WikiLinkNode = {
          type: 'wikilink',
          data: {
            target: target.trim(),
            display: display?.trim(),
            hName: 'a',
            hProperties: {
              'data-wikilink': 'true',
              'data-target': target.trim(),
              'data-valid': 'true',
              'class': 'wikilink',
              'href': '#',
            },
          },
          // 添加必要的 Unist 节点属性
          position: undefined,
        }
        
        replacements.push({ start, end, node: wikiLinkNode })
      }
      
      // 如果没有找到 WikiLink，直接返回
      if (replacements.length === 0) {
        return
      }
      
      // 从后往前替换，避免索引偏移问题
      replacements.reverse()
      
      const newNodes: UnistNode[] = []
      let lastEnd = text.length
      
      // 处理每个 WikiLink
      for (const replacement of replacements) {
        const { start, end, node: wikiLinkNode } = replacement
        
        // 添加 WikiLink 后面的文本
        if (end < lastEnd) {
          const afterText = text.slice(end, lastEnd)
          if (afterText) {
            newNodes.unshift({
              type: 'text',
              value: afterText,
            } as Text)
          }
        }
        
        // 添加 WikiLink 节点
        newNodes.unshift(wikiLinkNode)
        
        lastEnd = start
      }
      
      // 添加第一个 WikiLink 前面的文本
      if (lastEnd > 0) {
        const beforeText = text.slice(0, lastEnd)
        if (beforeText) {
          newNodes.unshift({
            type: 'text',
            value: beforeText,
          } as Text)
        }
      }
      
      // 替换原节点
      parent.children.splice(index, 1, ...(newNodes as any[]))
      
      // 返回跳过的索引数量
      return index + newNodes.length
    })
  }
}

/**
 * WikiLink 序列化器 - 将 AST 转换回 Markdown 文本
 */
export const wikLinkSerializer: Plugin = function () {
  return function transformer(tree: UnistNode) {
    visit(tree, isWikiLinkNode, (node: WikiLinkNode) => {
      const { target, display } = node.data
      
      // 生成 Markdown 文本
      let markdownText: string
      if (!display || display === target) {
        markdownText = `[[${target}]]`
      } else {
        markdownText = `[[${target}|${display}]]`
      }
      
      // 转换为文本节点
      ;(node as any).type = 'text'
      ;(node as any).value = markdownText
      
      // 清理 WikiLink 特有属性
      delete (node as any).target
      delete (node as any).display
    })
  }
}

/**
 * WikiLink HTML 渲染器 - 将 WikiLink 节点渲染为 HTML
 */
export function renderWikiLinkToHTML(node: WikiLinkNode, options: {
  onPageClick?: (target: string) => void
  checkPageExists?: (target: string) => boolean
} = {}): string {
  const { target, display } = node.data
  const { checkPageExists } = options
  
  const displayText = display || target
  const exists = checkPageExists ? checkPageExists(target) : true
  const className = exists ? 'wikilink wikilink-valid' : 'wikilink wikilink-invalid'
  const title = exists ? `链接到: ${target}` : `页面不存在: ${target}`
  
  if (exists) {
    return `<a href="#" class="${className}" data-wikilink="true" data-target="${target}" title="${title}">${displayText}</a>`
  } else {
    return `<span class="${className}" data-wikilink="true" data-target="${target}" title="${title}">${displayText}</span>`
  }
}

/**
 * 工具函数：从文本中提取所有 WikiLink
 */
export function extractWikiLinks(text: string): Array<{ target: string; display?: string; match: string }> {
  const wikiLinkRegex = /\[\[([^\]|]+)(\|([^\]]+))?\]\]/g
  const links: Array<{ target: string; display?: string; match: string }> = []
  
  let match
  while ((match = wikiLinkRegex.exec(text)) !== null) {
    const [fullMatch, target, , display] = match
    links.push({
      target: target.trim(),
      display: display?.trim(),
      match: fullMatch,
    })
  }
  
  return links
}

/**
 * 工具函数：替换文本中的 WikiLink
 */
export function replaceWikiLinks(
  text: string,
  replacer: (target: string, display?: string) => string
): string {
  return text.replace(/\[\[([^\]|]+)(\|([^\]]+))?\]\]/g, (match, target, _pipe, display) => {
    return replacer(target.trim(), display?.trim())
  })
}

/**
 * 工具函数：验证 WikiLink 语法
 */
export function isValidWikiLinkSyntax(text: string): boolean {
  const wikiLinkRegex = /^\[\[([^\]|]+)(\|([^\]]+))?\]\]$/
  return wikiLinkRegex.test(text)
}

export default {
  wikLinkParser,
  wikLinkSerializer,
  extractWikiLinks,
  replaceWikiLinks,
  isValidWikiLinkSyntax,
}

