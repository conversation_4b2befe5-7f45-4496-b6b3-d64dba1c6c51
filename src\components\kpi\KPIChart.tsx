/**
 * KPI图表组件
 * 支持多种图表类型的KPI数据可视化
 */

import { createMemo, For, Show } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { TrendingUp, TrendingDown, Target } from 'lucide-react'
import { cn } from '../../lib/utils'

import type { KPIChartProps, BaseKPI, KPIStatus } from './types'

// 计算KPI进度的工具函数
function calculateProgress(kpi: BaseKPI): number {
  if (!kpi.target) return 0
  
  if (kpi.direction === 'decrease') {
    // 减少型指标：假设起始值为当前值的1.5倍
    const estimatedStart = Math.max(kpi.value * 1.5, kpi.target * 2)
    const totalReduction = estimatedStart - kpi.target
    const currentReduction = estimatedStart - kpi.value
    
    if (totalReduction <= 0) return 100
    return Math.min((currentReduction / totalReduction) * 100, 100)
  } else {
    // 增长型指标
    if (kpi.target === 0) return 0
    return Math.min((kpi.value / kpi.target) * 100, 100)
  }
}

// 获取KPI状态
function getKPIStatus(progress: number): KPIStatus {
  if (progress >= 100) return 'achieved'
  if (progress >= 75) return 'on-track'
  if (progress >= 50) return 'at-risk'
  return 'behind'
}

// 状态颜色映射
const statusColors = {
  achieved: { bg: 'bg-green-100', text: 'text-green-800', progress: 'bg-green-500' },
  'on-track': { bg: 'bg-blue-100', text: 'text-blue-800', progress: 'bg-blue-500' },
  'at-risk': { bg: 'bg-yellow-100', text: 'text-yellow-800', progress: 'bg-yellow-500' },
  behind: { bg: 'bg-red-100', text: 'text-red-800', progress: 'bg-red-500' },
  'no-target': { bg: 'bg-gray-100', text: 'text-gray-800', progress: 'bg-gray-500' }
}

// 状态标签
const statusLabels = {
  achieved: 'Achieved',
  'on-track': 'On Track',
  'at-risk': 'At Risk',
  behind: 'Behind',
  'no-target': 'No Target'
}

export function KPIChart(props: KPIChartProps) {
  // 计算图表数据
  const chartData = createMemo(() => {
    return props.kpis.map(kpi => {
      const progress = kpi.target ? calculateProgress(kpi) : 0
      const status = kpi.target ? getKPIStatus(progress) : 'no-target'
      
      return {
        kpi,
        progress,
        status,
        color: statusColors[status]
      }
    })
  })

  // 计算统计信息
  const statistics = createMemo(() => {
    const data = chartData()
    const total = data.length
    
    const stats = data.reduce((acc, item) => {
      acc[item.status] = (acc[item.status] || 0) + 1
      if (item.status !== 'no-target') {
        acc.totalProgress += item.progress
        acc.withTarget += 1
      }
      return acc
    }, {
      achieved: 0,
      'on-track': 0,
      'at-risk': 0,
      behind: 0,
      'no-target': 0,
      totalProgress: 0,
      withTarget: 0
    } as any)

    const avgProgress = stats.withTarget > 0 ? Math.round(stats.totalProgress / stats.withTarget) : 0

    return {
      total,
      achieved: stats.achieved,
      onTrack: stats['on-track'],
      atRisk: stats['at-risk'],
      behind: stats.behind,
      noTarget: stats['no-target'],
      avgProgress
    }
  })

  // 渲染进度条图表
  const renderProgressChart = () => (
    <div class="space-y-3">
      <h4 class="text-sm font-medium text-muted-foreground">Individual Progress</h4>
      <div class="space-y-3">
        <For each={chartData()}>
          {(data) => (
            <div 
              class="space-y-2 cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors"
              onClick={() => props.onKPIClick?.(data.kpi)}
            >
              <div class="flex items-center justify-between text-sm">
                <div class="flex items-center gap-2 flex-1 min-w-0">
                  <span class="font-medium truncate">{data.kpi.name}</span>
                  <Badge variant="outline" class={cn('text-xs', data.color.bg, data.color.text)}>
                    {statusLabels[data.status]}
                  </Badge>
                  <Show when={data.kpi.direction === 'decrease'}>
                    <TrendingDown class="h-3 w-3 text-red-500" />
                  </Show>
                  <Show when={data.kpi.direction === 'increase'}>
                    <TrendingUp class="h-3 w-3 text-green-500" />
                  </Show>
                </div>
                <div class="flex items-center gap-2 text-xs text-muted-foreground">
                  <span>
                    {data.kpi.value}{data.kpi.unit && ` ${data.kpi.unit}`}
                  </span>
                  <Show when={data.kpi.target}>
                    <>
                      <span>/</span>
                      <span>
                        {data.kpi.target}{data.kpi.unit && ` ${data.kpi.unit}`}
                      </span>
                    </>
                  </Show>
                  <Show when={data.status !== 'no-target'}>
                    <span class="font-medium">
                      ({Math.round(data.progress)}%)
                    </span>
                  </Show>
                </div>
              </div>
              
              <Show when={data.kpi.target}>
                <Progress 
                  value={data.progress} 
                  class="h-2"
                  indicatorClass={data.color.progress}
                />
              </Show>
            </div>
          )}
        </For>
      </div>
    </div>
  )

  // 渲染环形图
  const renderDonutChart = () => {
    const stats = statistics()
    const total = stats.total
    
    if (total === 0) return null

    // 计算角度
    const segments = [
      { label: 'Achieved', value: stats.achieved, color: '#10b981' },
      { label: 'On Track', value: stats.onTrack, color: '#3b82f6' },
      { label: 'At Risk', value: stats.atRisk, color: '#f59e0b' },
      { label: 'Behind', value: stats.behind, color: '#ef4444' },
      { label: 'No Target', value: stats.noTarget, color: '#6b7280' }
    ].filter(segment => segment.value > 0)

    let currentAngle = 0
    const radius = 40
    const strokeWidth = 8

    return (
      <div class="flex items-center justify-center">
        <div class="relative">
          <svg width="100" height="100" class="transform -rotate-90">
            <For each={segments}>
              {(segment) => {
                const percentage = segment.value / total
                const strokeDasharray = `${percentage * 251.2} 251.2`
                const strokeDashoffset = -currentAngle * 251.2 / 360
                
                currentAngle += percentage * 360
                
                return (
                  <circle
                    cx="50"
                    cy="50"
                    r={radius}
                    fill="none"
                    stroke={segment.color}
                    stroke-width={strokeWidth}
                    stroke-dasharray={strokeDasharray}
                    stroke-dashoffset={strokeDashoffset}
                    class="transition-all duration-500"
                  />
                )
              }}
            </For>
          </svg>
          
          {/* 中心文本 */}
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="text-center">
              <div class="text-lg font-bold">{total}</div>
              <div class="text-xs text-muted-foreground">KPIs</div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 渲染统计卡片
  const renderStatistics = () => {
    const stats = statistics()
    
    return (
      <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div class="text-center p-3 bg-green-50 rounded-lg border border-green-200">
          <div class="text-2xl font-bold text-green-600">{stats.achieved}</div>
          <div class="text-xs text-green-700">Achieved</div>
        </div>
        <div class="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div class="text-2xl font-bold text-blue-600">{stats.onTrack}</div>
          <div class="text-xs text-blue-700">On Track</div>
        </div>
        <div class="text-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
          <div class="text-2xl font-bold text-yellow-600">{stats.atRisk}</div>
          <div class="text-xs text-yellow-700">At Risk</div>
        </div>
        <div class="text-center p-3 bg-red-50 rounded-lg border border-red-200">
          <div class="text-2xl font-bold text-red-600">{stats.behind}</div>
          <div class="text-xs text-red-700">Behind</div>
        </div>
        <div class="text-center p-3 bg-gray-50 rounded-lg border border-gray-200">
          <div class="text-2xl font-bold text-gray-600">{stats.avgProgress}%</div>
          <div class="text-xs text-gray-700">Avg Progress</div>
        </div>
      </div>
    )
  }

  if (props.kpis.length === 0) {
    return null
  }

  return (
    <Card class={props.class}>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          KPI Overview
          <Badge variant="outline" class="text-xs">
            {statistics().total} KPIs
          </Badge>
        </CardTitle>
        <CardDescription>
          Visual overview of your key performance indicators
        </CardDescription>
      </CardHeader>

      <CardContent class="space-y-6">
        {/* 统计概览 */}
        {renderStatistics()}

        {/* 图表内容 */}
        <Show when={props.type === 'donut' || props.type === 'pie'}>
          {renderDonutChart()}
        </Show>
        
        <Show when={!props.type || props.type === 'bar' || props.type === 'progress'}>
          {renderProgressChart()}
        </Show>
      </CardContent>
    </Card>
  )
}

export default KPIChart
