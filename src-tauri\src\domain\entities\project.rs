// Project Entity - 项目实体
// 项目聚合根

use crate::shared::types::{Id, Metadata, EntityStatus, Priority, Tag};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Project {
    pub id: Id,
    pub name: String,
    pub description: Option<String>,
    pub goals: Vec<String>,
    pub deliverables: Vec<String>,
    pub start_date: Option<chrono::DateTime<chrono::Utc>>,
    pub due_date: Option<chrono::DateTime<chrono::Utc>>,
    pub area_id: Option<Id>,
    pub status: ProjectStatus,
    pub priority: Priority,
    pub progress: f32, // 0.0 to 1.0
    pub tags: Vec<Tag>,
    pub entity_status: EntityStatus,
    pub metadata: Metadata,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ProjectStatus {
    NotStarted,
    InProgress,
    AtRisk,
    OnHold,
    Completed,
    Cancelled,
}

impl Default for ProjectStatus {
    fn default() -> Self {
        ProjectStatus::NotStarted
    }
}

impl Project {
    pub fn new(name: String, area_id: Option<Id>) -> Self {
        Self {
            id: crate::shared::utils::IdGenerator::new_id_with_prefix("proj"),
            name,
            description: None,
            goals: Vec::new(),
            deliverables: Vec::new(),
            start_date: Some(chrono::Utc::now()),
            due_date: None,
            area_id,
            status: ProjectStatus::default(),
            priority: Priority::default(),
            progress: 0.0,
            tags: Vec::new(),
            entity_status: EntityStatus::Active,
            metadata: Metadata::default(),
        }
    }

    pub fn update_status(&mut self, status: ProjectStatus) {
        self.status = status;
        if status == ProjectStatus::Completed {
            self.progress = 1.0;
        }
        self.metadata.updated_at = chrono::Utc::now();
        self.metadata.version += 1;
    }

    pub fn update_progress(&mut self, progress: f32) {
        self.progress = progress.clamp(0.0, 1.0);
        if self.progress >= 1.0 {
            self.status = ProjectStatus::Completed;
        } else if self.progress > 0.0 && self.status == ProjectStatus::NotStarted {
            self.status = ProjectStatus::InProgress;
        }
        self.metadata.updated_at = chrono::Utc::now();
        self.metadata.version += 1;
    }

    pub fn add_tag(&mut self, tag: Tag) {
        if !self.tags.iter().any(|t| t.id == tag.id) {
            self.tags.push(tag);
            self.metadata.updated_at = chrono::Utc::now();
            self.metadata.version += 1;
        }
    }

    pub fn remove_tag(&mut self, tag_id: &Id) {
        if let Some(pos) = self.tags.iter().position(|t| &t.id == tag_id) {
            self.tags.remove(pos);
            self.metadata.updated_at = chrono::Utc::now();
            self.metadata.version += 1;
        }
    }

    pub fn is_overdue(&self) -> bool {
        if let Some(due_date) = self.due_date {
            chrono::Utc::now() > due_date && self.status != ProjectStatus::Completed
        } else {
            false
        }
    }

    pub fn days_until_due(&self) -> Option<i64> {
        self.due_date.map(|due| {
            let now = chrono::Utc::now();
            (due - now).num_days()
        })
    }
}
