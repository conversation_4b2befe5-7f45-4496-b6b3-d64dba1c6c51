/**
 * 统一资源关联组件
 * 可复用的资源关联和管理组件，支持Markdown文档、外部链接、文件附件和双向链接
 */

import { createSignal, createEffect, onMount, onCleanup, For, Show, Switch, Match } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { 
  Plus, 
  FileText, 
  Link as LinkIcon, 
  Upload, 
  Search,
  Filter,
  Grid,
  List,
  MoreVertical,
  Eye,
  Edit,
  Trash2
} from 'lucide-solid'
import { cn } from '../../lib/utils'

import type {
  ResourceAssociationProps,
  UnifiedResource,
  ResourceStatistics,
  ResourceAssociationConfig,
  ResourceFilter,
  DEFAULT_RESOURCE_CONFIG
} from './types'

// 导入子组件
import ResourceList from './ResourceList'
import FileUpload from './FileUpload'
import LinkCreate from './LinkCreate'
import MarkdownAssociation from './MarkdownAssociation'
import BidirectionalLinks from './BidirectionalLinks'
import ResourcePreview from './ResourcePreview'
import ResourceSearch from './ResourceSearch'

export function ResourceAssociation(props: ResourceAssociationProps) {
  // 合并配置
  const config = (): ResourceAssociationConfig => ({
    ...DEFAULT_RESOURCE_CONFIG,
    ...props.config
  })

  // 状态管理
  const [resources, setResources] = createSignal<UnifiedResource[]>(props.initialResources || [])
  const [statistics, setStatistics] = createSignal<ResourceStatistics | null>(null)
  const [loading, setLoading] = createSignal(false)
  const [error, setError] = createSignal<string | null>(null)
  
  // 过滤和搜索状态
  const [filter, setFilter] = createSignal<ResourceFilter>({})
  const [searchQuery, setSearchQuery] = createSignal('')
  const [selectedResources, setSelectedResources] = createSignal<string[]>([])
  
  // UI状态
  const [activeTab, setActiveTab] = createSignal('all')
  const [showCreateDialog, setShowCreateDialog] = createSignal(false)
  const [createType, setCreateType] = createSignal<'markdown' | 'link' | 'file'>('file')
  const [selectedResource, setSelectedResource] = createSignal<UnifiedResource | null>(null)
  const [showPreview, setShowPreview] = createSignal(false)

  // 加载资源数据
  const loadResources = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const [resourceData, statsData] = await Promise.all([
        props.dataSource.getResources(props.entityType, props.entityId, filter()),
        props.dataSource.getStatistics(props.entityType, props.entityId)
      ])
      
      setResources(resourceData)
      setStatistics(statsData)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load resources'
      setError(errorMessage)
      props.eventHandlers?.onError?.(err instanceof Error ? err : new Error(errorMessage), 'loadResources')
    } finally {
      setLoading(false)
    }
  }

  // 创建文件资源
  const handleFileUpload = async (uploadData: any) => {
    try {
      props.eventHandlers?.onUploadStart?.(uploadData)
      
      const newResource = await props.dataSource.uploadFile(uploadData, (progress) => {
        props.eventHandlers?.onUploadProgress?.(uploadData.file.name, progress)
      })
      
      setResources(prev => [newResource, ...prev])
      setShowCreateDialog(false)
      
      props.eventHandlers?.onUploadComplete?.(newResource)
      loadStatistics()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to upload file')
      props.eventHandlers?.onUploadError?.(error, uploadData)
      throw error
    }
  }

  // 创建链接资源
  const handleLinkCreate = async (linkData: any) => {
    try {
      const newResource = await props.dataSource.createLink(linkData)
      setResources(prev => [newResource, ...prev])
      setShowCreateDialog(false)
      
      props.eventHandlers?.onCreate?.(newResource)
      loadStatistics()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to create link')
      props.eventHandlers?.onError?.(error, 'createLink')
      throw error
    }
  }

  // 关联Markdown文档
  const handleMarkdownAssociation = async (data: any) => {
    try {
      const newResource = await props.dataSource.associateMarkdown(data)
      setResources(prev => [newResource, ...prev])
      setShowCreateDialog(false)
      
      props.eventHandlers?.onCreate?.(newResource)
      loadStatistics()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to associate markdown')
      props.eventHandlers?.onError?.(error, 'associateMarkdown')
      throw error
    }
  }

  // 更新资源
  const handleResourceUpdate = async (id: string, data: any) => {
    try {
      const updatedResource = await props.dataSource.updateResource(id, data)
      setResources(prev => prev.map(resource => resource.id === id ? updatedResource : resource))
      
      props.eventHandlers?.onUpdate?.(updatedResource, data)
      loadStatistics()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to update resource')
      props.eventHandlers?.onError?.(error, 'updateResource')
      throw error
    }
  }

  // 删除资源
  const handleResourceDelete = async (id: string) => {
    try {
      await props.dataSource.deleteResource(id)
      setResources(prev => prev.filter(resource => resource.id !== id))
      
      props.eventHandlers?.onDelete?.(id)
      loadStatistics()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to delete resource')
      props.eventHandlers?.onError?.(error, 'deleteResource')
      throw error
    }
  }

  // 访问资源
  const handleResourceAccess = async (resource: UnifiedResource) => {
    try {
      // 更新访问计数
      await handleResourceUpdate(resource.id, {
        accessCount: resource.accessCount + 1,
        lastAccessedAt: new Date()
      })
      
      props.eventHandlers?.onAccess?.(resource)
      
      // 显示预览或打开资源
      setSelectedResource(resource)
      setShowPreview(true)
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to access resource')
      props.eventHandlers?.onError?.(error, 'accessResource')
    }
  }

  // 搜索资源
  const handleSearch = async (query: string) => {
    try {
      setSearchQuery(query)
      if (query.trim()) {
        const searchResults = await props.dataSource.searchResources(query, filter())
        setResources(searchResults)
      } else {
        loadResources()
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to search resources')
      props.eventHandlers?.onError?.(error, 'searchResources')
    }
  }

  // 仅加载统计信息
  const loadStatistics = async () => {
    try {
      const statsData = await props.dataSource.getStatistics(props.entityType, props.entityId)
      setStatistics(statsData)
    } catch (err) {
      console.warn('Failed to load statistics:', err)
    }
  }

  // 过滤资源
  const filteredResources = () => {
    let filtered = resources()
    
    if (activeTab() !== 'all') {
      filtered = filtered.filter(resource => resource.type === activeTab())
    }
    
    return filtered
  }

  // 生命周期
  onMount(() => {
    loadResources()
  })

  // 监听过滤器变化
  createEffect(() => {
    if (filter()) {
      loadResources()
    }
  })

  // 渲染加载状态
  const renderLoading = () => (
    <Card class={cn("w-full", props.className)}>
      <CardContent class="flex items-center justify-center py-8">
        <div class="text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p class="text-muted-foreground">Loading resources...</p>
        </div>
      </CardContent>
    </Card>
  )

  // 渲染错误状态
  const renderError = () => (
    <Card class={cn("w-full", props.className)}>
      <CardContent class="flex items-center justify-center py-8">
        <div class="text-center">
          <div class="text-red-500 mb-4">
            <FileText class="h-12 w-12 mx-auto" />
          </div>
          <h3 class="text-lg font-medium mb-2">Failed to load resources</h3>
          <p class="text-muted-foreground mb-4">{error()}</p>
          <Button onClick={loadResources}>Retry</Button>
        </div>
      </CardContent>
    </Card>
  )

  // 渲染空状态
  const renderEmpty = () => (
    <Card class={cn("w-full", props.className)}>
      <CardContent class="flex items-center justify-center py-8">
        <div class="text-center">
          <FileText class="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 class="text-lg font-medium mb-2">No resources yet</h3>
          <p class="text-muted-foreground mb-4">
            Add your first resource to get started
          </p>
          <div class="flex gap-2 justify-center">
            <Show when={config().enableFileUpload}>
              <Button onClick={() => { setCreateType('file'); setShowCreateDialog(true) }}>
                <Upload class="h-4 w-4 mr-2" />
                Upload File
              </Button>
            </Show>
            <Show when={config().enableLinkManagement}>
              <Button variant="outline" onClick={() => { setCreateType('link'); setShowCreateDialog(true) }}>
                <LinkIcon class="h-4 w-4 mr-2" />
                Add Link
              </Button>
            </Show>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  // 主渲染
  return (
    <div class={cn("space-y-6", props.className)}>
      <Show when={loading()}>
        {renderLoading()}
      </Show>
      
      <Show when={error() && !loading()}>
        {renderError()}
      </Show>
      
      <Show when={!loading() && !error()}>
        <Show when={filteredResources().length === 0}>
          {renderEmpty()}
        </Show>
        
        <Show when={filteredResources().length > 0}>
          {/* 头部工具栏 */}
          <Card>
            <CardHeader>
              <div class="flex items-center justify-between">
                <div>
                  <CardTitle class="flex items-center gap-2">
                    <FileText class="h-5 w-5" />
                    Resource Management
                    <Show when={statistics()}>
                      <Badge variant="outline">
                        {statistics()!.total} resources
                      </Badge>
                    </Show>
                  </CardTitle>
                  <CardDescription>
                    Manage documents, links, files and references
                  </CardDescription>
                </div>
                <div class="flex gap-2">
                  <Show when={config().showResourceSearch}>
                    <ResourceSearch
                      onSearch={handleSearch}
                      placeholder="Search resources..."
                    />
                  </Show>
                  <Button onClick={() => setShowCreateDialog(true)}>
                    <Plus class="h-4 w-4 mr-2" />
                    Add Resource
                  </Button>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* 资源标签页 */}
          <Tabs value={activeTab()} onValueChange={setActiveTab}>
            <TabsList class="grid w-full grid-cols-5">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="markdown">Markdown</TabsTrigger>
              <TabsTrigger value="link">Links</TabsTrigger>
              <TabsTrigger value="file">Files</TabsTrigger>
              <TabsTrigger value="reference">References</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab()} class="space-y-4">
              {/* 资源列表 */}
              <ResourceList
                resources={filteredResources()}
                config={config()}
                onResourceClick={handleResourceAccess}
                onResourceEdit={(resource) => handleResourceUpdate(resource.id, resource)}
                onResourceDelete={handleResourceDelete}
                selectedResources={selectedResources()}
                onSelectionChange={setSelectedResources}
              />
            </TabsContent>
          </Tabs>
        </Show>
      </Show>

      {/* 创建资源对话框 */}
      <Show when={showCreateDialog()}>
        <Switch>
          <Match when={createType() === 'file'}>
            <FileUpload
              onUpload={handleFileUpload}
              config={config()}
              onCancel={() => setShowCreateDialog(false)}
            />
          </Match>
          <Match when={createType() === 'link'}>
            <LinkCreate
              onCreateLink={handleLinkCreate}
              config={config()}
              onCancel={() => setShowCreateDialog(false)}
            />
          </Match>
          <Match when={createType() === 'markdown'}>
            <MarkdownAssociation
              onAssociate={handleMarkdownAssociation}
              config={config()}
              onCancel={() => setShowCreateDialog(false)}
            />
          </Match>
        </Switch>
      </Show>

      {/* 资源预览 */}
      <Show when={showPreview() && selectedResource()}>
        <ResourcePreview
          resource={selectedResource()!}
          open={showPreview()}
          onClose={() => setShowPreview(false)}
          dataSource={props.dataSource}
        />
      </Show>
    </div>
  )
}

export default ResourceAssociation
