/**
 * 项目KPI使用示例
 * 展示如何在项目详情页面中集成KPI跟踪组件
 */

import { createSignal, onMount } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card'
import { Button } from '../../ui/button'
import { Badge } from '../../ui/badge'
import { Target, Settings, RefreshCw } from 'lucide-react'

// 导入KPI组件
import { 
  KPITracker,
  createKPIDataSource,
  PROJECT_KPI_CONFIG,
  type KPIEventHandlers,
  type KPIComponentConfig
} from '../index'

interface ProjectKPIExampleProps {
  projectId: string
  projectName?: string
  className?: string
}

export function ProjectKPIExample(props: ProjectKPIExampleProps) {
  // 状态管理
  const [refreshKey, setRefreshKey] = createSignal(0)
  const [showSettings, setShowSettings] = createSignal(false)

  // 创建数据源
  const dataSource = createKPIDataSource('project')

  // 组件配置
  const componentConfig: KPIComponentConfig = {
    showChart: true,
    showQuickInput: true,
    showHistory: false,
    showStatistics: true,
    allowCreate: true,
    allowEdit: true,
    allowDelete: true,
    allowBatchRecord: false,
    layout: 'grid',
    chartType: 'bar',
    maxRecords: 20,
    autoRefresh: true,
    refreshInterval: 30000, // 30秒自动刷新
    theme: 'auto'
  }

  // 事件处理器
  const eventHandlers: KPIEventHandlers = {
    onCreate: (kpi) => {
      console.log('KPI created:', kpi)
      // 可以在这里添加通知、日志记录等
    },
    
    onUpdate: (kpi, changes) => {
      console.log('KPI updated:', kpi, changes)
      // 可以在这里触发项目进度重新计算
    },
    
    onDelete: (kpiId) => {
      console.log('KPI deleted:', kpiId)
      // 可以在这里清理相关数据
    },
    
    onRecord: (record) => {
      console.log('KPI record created:', record)
      // 可以在这里触发进度更新通知
    },
    
    onProgressChange: (kpiId, progress) => {
      console.log('KPI progress changed:', kpiId, progress)
      // 可以在这里更新项目整体进度
    },
    
    onStatusChange: (kpiId, oldStatus, newStatus) => {
      console.log('KPI status changed:', kpiId, oldStatus, '->', newStatus)
      // 可以在这里发送状态变更通知
    },
    
    onError: (error, context) => {
      console.error('KPI error:', error, context)
      // 可以在这里显示错误通知
    }
  }

  // 手动刷新
  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1)
  }

  return (
    <div class="space-y-6">
      {/* 页面头部 */}
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <div>
              <CardTitle class="flex items-center gap-2">
                <Target class="h-5 w-5" />
                Project KPIs
                <Badge variant="outline">
                  {props.projectName || `Project ${props.projectId}`}
                </Badge>
              </CardTitle>
              <CardDescription>
                Track key performance indicators for this project
              </CardDescription>
            </div>
            <div class="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
              >
                <RefreshCw class="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(!showSettings())}
              >
                <Settings class="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* KPI跟踪器 */}
      <KPITracker
        key={refreshKey()} // 用于强制刷新
        parentId={props.projectId}
        parentType="project"
        dataSource={dataSource}
        config={componentConfig}
        kpiConfig={PROJECT_KPI_CONFIG}
        eventHandlers={eventHandlers}
        className={props.className}
      />

      {/* 设置面板（可选） */}
      {/* 这里可以添加配置面板来动态调整组件设置 */}
    </div>
  )
}

export default ProjectKPIExample
