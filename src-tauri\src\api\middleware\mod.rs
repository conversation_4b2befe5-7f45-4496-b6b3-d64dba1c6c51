// API Middleware - API 中间件

use crate::shared::errors::{AppError, Result};

pub mod validation;
pub mod error_handling;
pub mod logging;

// 重新导出中间件
pub use validation::*;
pub use error_handling::*;
pub use logging::*;

// 基础中间件特征
pub trait Middleware {
    fn process(&self, request: &str) -> Result<String>;
}

// 请求验证中间件
pub struct ValidationMiddleware;

impl Middleware for ValidationMiddleware {
    fn process(&self, request: &str) -> Result<String> {
        if request.is_empty() {
            Err(AppError::ValidationError("Empty request".to_string()))
        } else {
            Ok(request.to_string())
        }
    }
}

// 错误处理中间件
pub struct ErrorHandlingMiddleware;

impl ErrorHandlingMiddleware {
    pub fn handle_error(error: AppError) -> String {
        match error {
            AppError::ValidationError(msg) => format!("Validation Error: {}", msg),
            AppError::DatabaseError(msg) => format!("Database Error: {}", msg),
            AppError::NotFound(msg) => format!("Not Found: {}", msg),
            _ => "Internal Server Error".to_string(),
        }
    }
}
