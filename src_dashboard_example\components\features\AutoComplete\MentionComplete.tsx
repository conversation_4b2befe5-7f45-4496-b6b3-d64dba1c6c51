import type { CompletionProvider, CompletionContext, CompletionItem } from './types'

/**
 * 用户数据接口
 */
interface User {
  id: string
  name: string
  username: string
  avatar?: string
  email?: string
  role?: string
}

/**
 * 模拟用户数据
 * 在实际应用中，这些数据应该从API或数据库获取
 */
const MOCK_USERS: User[] = [
  {
    id: '1',
    name: '张三',
    username: 'z<PERSON><PERSON>',
    email: 'zhang<PERSON>@example.com',
    role: '开发者'
  },
  {
    id: '2',
    name: '李四',
    username: 'lisi',
    email: '<EMAIL>',
    role: '设计师'
  },
  {
    id: '3',
    name: '王五',
    username: 'wangwu',
    email: '<EMAIL>',
    role: '产品经理'
  },
  {
    id: '4',
    name: 'Alice',
    username: 'alice',
    email: '<EMAIL>',
    role: '测试工程师'
  },
  {
    id: '5',
    name: '<PERSON>',
    username: 'bob',
    email: '<EMAIL>',
    role: '运维工程师'
  },
  {
    id: '6',
    name: '小明',
    username: 'xiaoming',
    email: '<EMAIL>',
    role: '实习生'
  },
  {
    id: '7',
    name: 'Charlie',
    username: 'charlie',
    email: '<EMAIL>',
    role: '架构师'
  },
  {
    id: '8',
    name: '小红',
    username: 'xiaohong',
    email: '<EMAIL>',
    role: 'UI设计师'
  }
]

/**
 * @用户补全提供者
 * 支持通过 @用户名 触发用户提及补全
 */
export class MentionCompleteProvider implements CompletionProvider {
  trigger = '@'
  private users: User[] = MOCK_USERS

  /**
   * 设置用户数据
   * 允许外部更新用户列表
   */
  setUsers(users: User[]): void {
    this.users = users
  }

  async getCompletions(context: CompletionContext): Promise<CompletionItem[]> {
    const query = context.query.toLowerCase()

    if (query.length === 0) {
      // 显示最近使用的用户或常用用户
      return this.users.slice(0, 8).map((user) => this.userToCompletionItem(user))
    }

    // 模糊搜索匹配用户名、姓名或邮箱
    const matches = this.users.filter(
      (user) =>
        user.name.toLowerCase().includes(query) ||
        user.username.toLowerCase().includes(query) ||
        (user.email && user.email.toLowerCase().includes(query))
    )

    return matches.slice(0, 10).map((user) => this.userToCompletionItem(user))
  }

  applyCompletion(item: CompletionItem, context: CompletionContext): void {
    const { view, position } = context
    const user = item.data as User

    // 插入 @用户名 格式
    const mentionText = `@${user.username}`

    const transaction = view.state.tr.replaceWith(
      position.from - context.trigger.length - context.query.length,
      position.to,
      view.state.schema.text(mentionText)
    )

    view.dispatch(transaction)
  }

  /**
   * 将用户对象转换为补全项
   */
  private userToCompletionItem(user: User): CompletionItem {
    return {
      id: `mention-${user.id}`,
      label: `${user.name} (@${user.username})`,
      insertText: `@${user.username}`,
      type: 'mention',
      description: user.role ? `${user.role} • ${user.email}` : user.email,
      data: user
    }
  }

  /**
   * 搜索用户
   * 支持异步搜索，可以连接到API
   */
  async searchUsers(query: string): Promise<User[]> {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 100))

    const lowerQuery = query.toLowerCase()
    return this.users.filter(
      (user) =>
        user.name.toLowerCase().includes(lowerQuery) ||
        user.username.toLowerCase().includes(lowerQuery) ||
        (user.email && user.email.toLowerCase().includes(lowerQuery))
    )
  }
}

/**
 * 创建@用户补全提供者实例
 */
export const mentionCompleteProvider = new MentionCompleteProvider()
