/**
 * 领域指标使用示例
 * 展示如何在领域详情页面中集成KPI跟踪组件
 */

import { createSignal, onMount } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card'
import { Button } from '../../ui/button'
import { Badge } from '../../ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../ui/tabs'
import { Target, TrendingUp, BarChart3, Calendar } from 'lucide-react'

// 导入KPI组件
import { 
  KPITracker,
  KPIChart,
  createKPIDataSource,
  AREA_METRIC_CONFIG,
  type KPIEventHandlers,
  type KPIComponentConfig
} from '../index'

interface AreaMetricExampleProps {
  areaId: string
  areaName?: string
  className?: string
}

export function AreaMetricExample(props: AreaMetricExampleProps) {
  // 状态管理
  const [activeTab, setActiveTab] = createSignal('overview')
  const [kpis, setKPIs] = createSignal([])

  // 创建数据源
  const dataSource = createKPIDataSource('area')

  // 组件配置 - 针对个人指标优化
  const componentConfig: KPIComponentConfig = {
    showChart: true,
    showQuickInput: true,
    showHistory: true, // 个人指标更关注历史趋势
    showStatistics: true,
    allowCreate: true,
    allowEdit: true,
    allowDelete: true,
    allowBatchRecord: true, // 支持批量记录
    layout: 'list', // 列表布局更适合个人指标
    chartType: 'line', // 线性图更适合趋势展示
    maxRecords: 30, // 更多历史记录
    autoRefresh: false, // 个人指标不需要频繁自动刷新
    theme: 'auto'
  }

  // 紧凑模式配置（用于仪表盘）
  const compactConfig: KPIComponentConfig = {
    ...componentConfig,
    showChart: false,
    showHistory: false,
    layout: 'compact',
    allowCreate: false,
    allowEdit: false,
    allowDelete: false
  }

  // 事件处理器
  const eventHandlers: KPIEventHandlers = {
    onCreate: (kpi) => {
      console.log('Area metric created:', kpi)
      // 可以在这里触发习惯关联检查
    },
    
    onUpdate: (kpi, changes) => {
      console.log('Area metric updated:', kpi, changes)
      // 可以在这里更新相关习惯
    },
    
    onDelete: (kpiId) => {
      console.log('Area metric deleted:', kpiId)
      // 可以在这里清理习惯关联
    },
    
    onRecord: (record) => {
      console.log('Area metric recorded:', record)
      // 可以在这里检查是否需要创建习惯记录
      checkHabitIntegration(record)
    },
    
    onProgressChange: (kpiId, progress) => {
      console.log('Area metric progress changed:', kpiId, progress)
      // 可以在这里更新领域健康度评分
    },
    
    onStatusChange: (kpiId, oldStatus, newStatus) => {
      console.log('Area metric status changed:', kpiId, oldStatus, '->', newStatus)
      // 可以在这里发送个人成就通知
    },
    
    onError: (error, context) => {
      console.error('Area metric error:', error, context)
    }
  }

  // 检查习惯集成
  const checkHabitIntegration = (record: any) => {
    // 这里可以实现与习惯系统的集成逻辑
    // 例如：如果记录了运动时长，自动标记运动习惯为完成
  }

  // 加载KPI数据
  const loadKPIs = async () => {
    try {
      const data = await dataSource.getKPIs(props.areaId)
      setKPIs(data)
    } catch (error) {
      console.error('Failed to load area metrics:', error)
    }
  }

  onMount(() => {
    loadKPIs()
  })

  return (
    <div class="space-y-6">
      {/* 页面头部 */}
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <div>
              <CardTitle class="flex items-center gap-2">
                <Target class="h-5 w-5" />
                Area Metrics
                <Badge variant="outline">
                  {props.areaName || `Area ${props.areaId}`}
                </Badge>
              </CardTitle>
              <CardDescription>
                Track personal metrics and key indicators for this life area
              </CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 标签页导航 */}
      <Tabs value={activeTab()} onValueChange={setActiveTab}>
        <TabsList class="grid w-full grid-cols-3">
          <TabsTrigger value="overview" class="flex items-center gap-2">
            <BarChart3 class="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="metrics" class="flex items-center gap-2">
            <Target class="h-4 w-4" />
            Metrics
          </TabsTrigger>
          <TabsTrigger value="trends" class="flex items-center gap-2">
            <TrendingUp class="h-4 w-4" />
            Trends
          </TabsTrigger>
        </TabsList>

        {/* 概览标签页 */}
        <TabsContent value="overview" class="space-y-6">
          {/* 快速统计 */}
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent class="p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-muted-foreground">Active Metrics</p>
                    <p class="text-2xl font-bold">{kpis().length}</p>
                  </div>
                  <Target class="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent class="p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-muted-foreground">Today's Records</p>
                    <p class="text-2xl font-bold">5</p>
                  </div>
                  <Calendar class="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent class="p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-muted-foreground">Avg Progress</p>
                    <p class="text-2xl font-bold">78%</p>
                  </div>
                  <TrendingUp class="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 图表概览 */}
          <KPIChart 
            kpis={kpis()} 
            type="donut"
            showLegend={true}
            showTooltip={true}
            height={300}
          />
        </TabsContent>

        {/* 指标管理标签页 */}
        <TabsContent value="metrics" class="space-y-6">
          <KPITracker
            parentId={props.areaId}
            parentType="area"
            dataSource={dataSource}
            config={componentConfig}
            kpiConfig={AREA_METRIC_CONFIG}
            eventHandlers={eventHandlers}
            className={props.className}
          />
        </TabsContent>

        {/* 趋势分析标签页 */}
        <TabsContent value="trends" class="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Trend Analysis</CardTitle>
              <CardDescription>
                Analyze your progress trends over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <KPIChart 
                kpis={kpis()} 
                type="line"
                showLegend={true}
                showTooltip={true}
                height={400}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default AreaMetricExample
