import { For, Show, type Component } from 'solid-js';
import type { Project, Task } from './types';
import { getStatusDotColor, getRemainingDaysColor, getStatusText } from './types';

interface ListViewProps {
    projects: Project[];
    onSelect: (id: string) => void;
    onEdit: (project: Project) => void;
    onArchive: (project: Project) => void;
    onDelete: (id: string) => void;
}

export const ProjectListView: Component<ListViewProps> = (props) => {
    const calculateProgress = (project: Project) => {
        const countTasks = (tasks: Task[]): { total: number, completed: number } => {
            let total = tasks.length;
            let completed = tasks.filter(t => t.completed).length;
            for (const task of tasks) {
                if (task.subtasks.length > 0) {
                    const subCounts = countTasks(task.subtasks);
                    total += subCounts.total;
                    completed += subCounts.completed;
                }
            }
            return { total, completed };
        }
        const { total, completed } = countTasks(project.tasks);
        if (total === 0) return 0;
        return (completed / total) * 100;
    };
    
    const getRemainingDaysInfo = (dueDate: string | undefined): { text: string; color: string } => {
        if (!dueDate) return { text: 'N/A', color: 'text-gray-500' };
        const today = new Date();
        const due = new Date(dueDate);
        today.setHours(0, 0, 0, 0);
        due.setHours(0, 0, 0, 0);
        const diffDays = Math.ceil((due.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        const color = getRemainingDaysColor(diffDays);
        let text = diffDays < 0 ? `逾期 ${Math.abs(diffDays)} 天` : diffDays === 0 ? '今天截止' : `剩余 ${diffDays} 天`;
        return { text, color };
    };

    return (
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-x-auto border border-gray-200 dark:border-gray-700">
            <table class="w-full min-w-[900px] text-sm text-left">
                <thead class="bg-gray-50 dark:bg-gray-700/50 text-xs text-gray-700 dark:text-gray-400 uppercase">
                    <tr>
                        <th class="p-4 w-1/3">项目名称</th>
                        <th class="p-4">状态</th>
                        <th class="p-4">领域</th>
                        <th class="p-4">进度</th>
                        <th class="p-4">剩余时间</th>
                        <th class="p-4">操作</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    <For each={props.projects}>
                        {project => {
                            const progress = calculateProgress(project);
                            const remainingDays = getRemainingDaysInfo(project.dueDate);
                            return (
                            <tr onClick={() => props.onSelect(project.id)} class="hover:bg-gray-50 dark:hover:bg-gray-700/30 cursor-pointer group">
                                <td class="p-4 font-semibold text-gray-900 dark:text-white">{project.name}</td>
                                <td class="p-4"><div class={`w-3 h-3 rounded-full ${getStatusDotColor(project.status)}`} title={getStatusText(project.status)}></div></td>
                                <td class="p-4">
                                    <Show when={project.areaName} fallback={<span class="text-gray-400">--</span>}>
                                        <a href="#" onClick={e => e.stopPropagation()} class="hover:underline">{project.areaName}</a>
                                    </Show>
                                </td>
                                <td class="p-4">
                                    <div class="flex items-center gap-3">
                                        <div class="w-24 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                            <div class="bg-blue-500 h-2 rounded-full" style={{ width: `${progress}%` }}></div>
                                        </div>
                                        <span class="text-xs font-medium text-gray-600 dark:text-gray-400 w-8 text-right">{progress.toFixed(0)}%</span>
                                    </div>
                                </td>
                                <td class={`p-4 font-medium ${remainingDays.color}`} title={`截止日期: ${project.dueDate || 'N/A'}`}>{remainingDays.text}</td>
                                <td class="p-4">
                                    <div class="opacity-0 group-hover:opacity-100 transition-opacity flex items-center gap-2">
                                        <button onClick={(e) => { e.stopPropagation(); props.onEdit(project); }} class="p-1.5 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600" title="编辑"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path></svg></button>
                                        <button onClick={(e) => { e.stopPropagation(); props.onArchive(project); }} class="p-1.5 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600" title="归档"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 8v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8"/><path d="M10 12h4"/><path d="M22 3H2a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2z"/></svg></button>
                                        <button onClick={(e) => { e.stopPropagation(); props.onDelete(project.id); }} class="p-1.5 rounded-md text-red-600 hover:bg-red-100 dark:hover:bg-red-900/50" title="删除"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path></svg></button>
                                    </div>
                                </td>
                            </tr>
                        )}}
                    </For>
                </tbody>
            </table>
        </div>
    );
};

