# 任务管理模块（TaskManager）设计与使用说明

该模块位于 src/components/tasks，使用 SolidJS 编写，遵循“可配置 + 可替换数据源 + 通用 UI”原则，便于在应用多个页面中复用。

## 目录结构
- src/components/tasks/
  - index.ts
  - types.ts            类型与配置项定义
  - TaskManager.tsx     通用容器，包含列表视图、筛选、CRUD、拖拽排序、完成率
  - TaskFilters.tsx     可选的筛选器（当前容器内置简化搜索输入）

## 能力点
- 任务创建、编辑、删除（内联编辑）
- 任务状态管理（checkbox 完成切换 -> completed/todo）
- 任务分类与筛选（搜索、状态、优先级、日期范围：接口预留）
- 任务优先级（下拉选择）
- 到期日期（日期输入）
- 子任务（支持 parentTaskId，简单层级数据结构已预留，UI可扩展缩进/折叠）
- 任务拖拽排序（同列表内交换顺序）
- 自动计算完成率（过滤后集合维度）

## 配置项（TaskManagerProps）
- mode: 'list' | 'board'（当前实现 list，board 预留）
- groupBy: 'none' | 'status' | 'priority' | 'dueDate'（预留）
- context: { projectId?: string; areaId?: string }
- initialFilters: { search,status[],priority[],dateRange,onlyToday,onlyOverdue }
- features: { enableSubtasks, enableInlineEdit, enableDragSort, enableFilters, showStats }
- dataSource: 可覆盖 fetchTasks/create/update/updateStatus/delete
- 回调：onTaskCreated/onTaskUpdated/onTaskDeleted/onSelectionChange

## 数据源
默认使用 src/services/apiFactory 暴露的：
- getTasks/getTasksByProject/getTodayTasks/getOverdueTasks
- createTask/updateTask/updateTaskStatus/deleteTask

mockApi 已补全对应方法，真实 API 则走 Tauri 命令封装。

## 示例用法
- 在页面中复用：

```tsx
import { TaskManager } from '@/components/tasks'

export default function TasksPage(){
  return (
    <TaskManager mode="list" features={{ enableDragSort: true }} />
  )
}
```

- 在项目详情中，仅展示某项目任务：
```tsx
<TaskManager context={{ projectId: project.id }} initialFilters={{ status: ['todo','in_progress'] }} />
```

- 自定义数据源（例如仅展示临时任务、或接入其他后端）：
```tsx
<TaskManager dataSource={{ fetchTasks: async (ctx, filters)=> myFetch(ctx, filters) }} />
```

## 后续可扩展
- 看板视图（按状态列拖拽）
- 子任务折叠/缩进、批量操作工具栏
- 复杂筛选器（TaskFiltersBar）集成到容器头部（当前仅简单搜索）
- 任务详情侧抽屉/模态
- 键盘快捷键、行虚拟化

