// User Repository Implementation - 用户仓储实现

use crate::domain::entities::{User, UserPreferences};
use crate::domain::repositories::UserRepository;
use crate::infrastructure::database::{DatabasePool, UserModel};
use crate::shared::errors::{AppError, Result};
use crate::shared::types::{Id, QueryParams, EntityStatus, Metadata};
use async_trait::async_trait;
use sqlx::Row;

pub struct UserRepositoryImpl {
    pool: DatabasePool,
}

impl UserRepositoryImpl {
    pub fn new(pool: DatabasePool) -> Self {
        Self { pool }
    }

    /// 将数据库模型转换为领域实体
    fn model_to_entity(&self, model: UserModel) -> Result<User> {
        let preferences: UserPreferences = serde_json::from_str(&model.preferences)
            .map_err(|e| AppError::DatabaseError(format!("Failed to parse user preferences: {}", e)))?;

        let status = match model.status.as_str() {
            "active" => EntityStatus::Active,
            "inactive" => EntityStatus::Inactive,
            "deleted" => EntityStatus::Deleted,
            "archived" => EntityStatus::Archived,
            _ => EntityStatus::Active,
        };

        let metadata = Metadata {
            created_at: model.created_at,
            updated_at: model.updated_at,
            created_by: None,
            updated_by: None,
            version: model.version as u64,
        };

        Ok(User {
            id: model.id,
            username: model.username,
            email: model.email,
            display_name: model.display_name,
            avatar_url: model.avatar_url,
            preferences,
            status,
            metadata,
        })
    }

    /// 将领域实体转换为数据库模型
    fn entity_to_model(&self, user: &User) -> Result<UserModel> {
        let preferences = serde_json::to_string(&user.preferences)
            .map_err(|e| AppError::DatabaseError(format!("Failed to serialize user preferences: {}", e)))?;

        let status = match user.status {
            EntityStatus::Active => "active",
            EntityStatus::Inactive => "inactive",
            EntityStatus::Deleted => "deleted",
            EntityStatus::Archived => "archived",
        };

        Ok(UserModel {
            id: user.id.clone(),
            username: user.username.clone(),
            email: user.email.clone(),
            display_name: user.display_name.clone(),
            avatar_url: user.avatar_url.clone(),
            preferences,
            status: status.to_string(),
            created_at: user.metadata.created_at,
            updated_at: user.metadata.updated_at,
            version: user.metadata.version as i64,
        })
    }
}

#[async_trait]
impl UserRepository for UserRepositoryImpl {
    async fn find_by_id(&self, id: &Id) -> Result<Option<User>> {
        let model = sqlx::query_as::<_, UserModel>(
            "SELECT * FROM users WHERE id = ? AND entity_status != 'deleted'"
        )
        .bind(id)
        .fetch_optional(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find user by id: {}", e)))?;

        match model {
            Some(m) => Ok(Some(self.model_to_entity(m)?)),
            None => Ok(None),
        }
    }

    async fn find_by_username(&self, username: &str) -> Result<Option<User>> {
        let model = sqlx::query_as::<_, UserModel>(
            "SELECT * FROM users WHERE username = ? AND entity_status != 'deleted'"
        )
        .bind(username)
        .fetch_optional(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find user by username: {}", e)))?;

        match model {
            Some(m) => Ok(Some(self.model_to_entity(m)?)),
            None => Ok(None),
        }
    }

    async fn find_by_email(&self, email: &str) -> Result<Option<User>> {
        let model = sqlx::query_as::<_, UserModel>(
            "SELECT * FROM users WHERE email = ? AND entity_status != 'deleted'"
        )
        .bind(email)
        .fetch_optional(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find user by email: {}", e)))?;

        match model {
            Some(m) => Ok(Some(self.model_to_entity(m)?)),
            None => Ok(None),
        }
    }

    async fn save(&self, user: &User) -> Result<()> {
        let model = self.entity_to_model(user)?;
        
        sqlx::query(
            r#"
            INSERT INTO users (id, username, email, display_name, avatar_url, preferences, status, created_at, updated_at, version)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&model.id)
        .bind(&model.username)
        .bind(&model.email)
        .bind(&model.display_name)
        .bind(&model.avatar_url)
        .bind(&model.preferences)
        .bind(&model.status)
        .bind(&model.created_at)
        .bind(&model.updated_at)
        .bind(&model.version)
        .execute(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to save user: {}", e)))?;

        Ok(())
    }

    async fn update(&self, user: &User) -> Result<()> {
        let model = self.entity_to_model(user)?;
        
        let result = sqlx::query(
            r#"
            UPDATE users 
            SET username = ?, email = ?, display_name = ?, avatar_url = ?, 
                preferences = ?, status = ?, updated_at = ?, version = version + 1
            WHERE id = ? AND version = ?
            "#
        )
        .bind(&model.username)
        .bind(&model.email)
        .bind(&model.display_name)
        .bind(&model.avatar_url)
        .bind(&model.preferences)
        .bind(&model.status)
        .bind(chrono::Utc::now())
        .bind(&model.id)
        .bind(&model.version)
        .execute(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to update user: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::DatabaseError("User not found or version conflict".to_string()));
        }

        Ok(())
    }

    async fn delete(&self, id: &Id) -> Result<()> {
        let result = sqlx::query(
            "UPDATE users SET entity_status = 'deleted', updated_at = ? WHERE id = ?"
        )
        .bind(chrono::Utc::now())
        .bind(id)
        .execute(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to delete user: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::NotFound("User not found".to_string()));
        }

        Ok(())
    }

    async fn find_all_active(&self) -> Result<Vec<User>> {
        let models = sqlx::query_as::<_, UserModel>(
            "SELECT * FROM users WHERE entity_status = 'active' ORDER BY created_at DESC"
        )
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find active users: {}", e)))?;

        let mut users = Vec::new();
        for model in models {
            users.push(self.model_to_entity(model)?);
        }

        Ok(users)
    }

    async fn find_with_pagination(&self, params: &QueryParams) -> Result<(Vec<User>, u64)> {
        let pagination = params.pagination.as_ref().unwrap_or(&crate::shared::types::Pagination::default());
        let offset = (pagination.page - 1) * pagination.size;

        // 获取总数
        let total = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM users WHERE entity_status != 'deleted'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count users: {}", e)))? as u64;

        // 获取分页数据
        let models = sqlx::query_as::<_, UserModel>(
            "SELECT * FROM users WHERE entity_status != 'deleted' ORDER BY created_at DESC LIMIT ? OFFSET ?"
        )
        .bind(pagination.size as i64)
        .bind(offset as i64)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find users with pagination: {}", e)))?;

        let mut users = Vec::new();
        for model in models {
            users.push(self.model_to_entity(model)?);
        }

        Ok((users, total))
    }

    async fn username_exists(&self, username: &str) -> Result<bool> {
        let count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM users WHERE username = ? AND entity_status != 'deleted'"
        )
        .bind(username)
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to check username existence: {}", e)))?;

        Ok(count > 0)
    }

    async fn email_exists(&self, email: &str) -> Result<bool> {
        let count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM users WHERE email = ? AND entity_status != 'deleted'"
        )
        .bind(email)
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to check email existence: {}", e)))?;

        Ok(count > 0)
    }

    async fn count_active_users(&self) -> Result<u64> {
        let count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM users WHERE entity_status = 'active'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count active users: {}", e)))?;

        Ok(count as u64)
    }

    async fn find_by_status(&self, status: &str) -> Result<Vec<User>> {
        let models = sqlx::query_as::<_, UserModel>(
            "SELECT * FROM users WHERE status = ? AND entity_status != 'deleted' ORDER BY created_at DESC"
        )
        .bind(status)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find users by status: {}", e)))?;

        let mut users = Vec::new();
        for model in models {
            users.push(self.model_to_entity(model)?);
        }

        Ok(users)
    }
}
