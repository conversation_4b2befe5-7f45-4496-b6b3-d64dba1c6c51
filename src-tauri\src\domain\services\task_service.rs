// Task Domain Service - 任务领域服务

use crate::domain::entities::{Task, TaskStatus};
use crate::shared::errors::Result;

pub struct TaskDomainService;

impl TaskDomainService {
    pub fn new() -> Self {
        Self
    }

    pub fn validate_task_title(&self, title: &str) -> Result<()> {
        crate::shared::utils::validate_required(title, "task title")?;
        crate::shared::utils::validate_length(title, "task title", 1, 200)?;
        Ok(())
    }

    pub fn can_complete_task(&self, task: &Task) -> bool {
        task.status != TaskStatus::Completed && task.status != TaskStatus::Cancelled
    }

    pub fn is_overdue(&self, task: &Task) -> bool {
        if let Some(due_date) = task.due_date {
            chrono::Utc::now() > due_date && task.status != TaskStatus::Completed
        } else {
            false
        }
    }

    pub fn calculate_priority_score(&self, task: &Task) -> u32 {
        let mut score = match task.priority {
            crate::shared::types::Priority::Critical => 100,
            crate::shared::types::Priority::High => 75,
            crate::shared::types::Priority::Medium => 50,
            crate::shared::types::Priority::Low => 25,
        };

        // Increase score if overdue
        if self.is_overdue(task) {
            score += 50;
        }

        // Increase score if due soon
        if let Some(due_date) = task.due_date {
            let days_until_due = (due_date - chrono::Utc::now()).num_days();
            if days_until_due <= 1 {
                score += 25;
            } else if days_until_due <= 3 {
                score += 10;
            }
        }

        score
    }
}

impl Default for TaskDomainService {
    fn default() -> Self {
        Self::new()
    }
}
