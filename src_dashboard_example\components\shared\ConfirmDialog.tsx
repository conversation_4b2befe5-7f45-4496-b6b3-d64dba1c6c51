import React from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogDes<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { useLanguage } from '../../contexts/LanguageContext'

interface ConfirmDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  description?: string
  confirmText?: string
  cancelText?: string
  variant?: 'default' | 'destructive' | 'warning'
  onConfirm: () => void | Promise<void>
  onCancel?: () => void
  loading?: boolean
  icon?: React.ReactNode
}

const variantConfig = {
  default: {
    badge: 'default' as const,
    confirmVariant: 'default' as const,
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
    )
  },
  destructive: {
    badge: 'destructive' as const,
    confirmVariant: 'destructive' as const,
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"
        />
      </svg>
    )
  },
  warning: {
    badge: 'secondary' as const,
    confirmVariant: 'default' as const,
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"
        />
      </svg>
    )
  }
}

export function ConfirmDialog({
  open,
  onOpenChange,
  title,
  description,
  confirmText,
  cancelText,
  variant = 'default',
  onConfirm,
  onCancel,
  loading = false,
  icon
}: ConfirmDialogProps) {
  const config = variantConfig[variant]
  const { t } = useLanguage()

  const defaultConfirmText = confirmText || t('common.confirm')
  const defaultCancelText = cancelText || t('common.cancel')

  const handleConfirm = async () => {
    try {
      await onConfirm()
      onOpenChange(false)
    } catch (error) {
      console.error('Confirmation action failed:', error)
      // Keep dialog open on error
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    }
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="text-muted-foreground">{icon || config.icon}</div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <DialogTitle>{title}</DialogTitle>
                <Badge variant={config.badge}>
                  {variant === 'destructive' ? t('messages.warning.title') : t('common.confirm')}
                </Badge>
              </div>
              <DialogDescription>
                {description || ' '}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <DialogFooter className="flex-row justify-end gap-2">
          <Button variant="outline" onClick={handleCancel} disabled={loading}>
            {defaultCancelText}
          </Button>
          <Button variant={config.confirmVariant} onClick={handleConfirm} disabled={loading}>
            {loading ? t('messages.info.processing') : defaultConfirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Hook for managing confirmation dialogs
export function useConfirmDialog() {
  const [state, setState] = React.useState<{
    open: boolean
    title: string
    description?: string
    variant?: 'default' | 'destructive' | 'warning'
    confirmText?: string
    cancelText?: string
    onConfirm?: () => void
    onCancel?: () => void
  }>({
    open: false,
    title: ''
  })

  const confirm = React.useCallback(
    (options: {
      title: string
      description?: string
      message?: string // 支持message字段作为description的别名
      variant?: 'default' | 'destructive' | 'warning'
      confirmText?: string
      cancelText?: string
    }): Promise<boolean> => {
      return new Promise((resolve) => {
        setState({
          open: true,
          title: options.title,
          description: options.description || options.message, // 使用message作为description
          variant: options.variant,
          confirmText: options.confirmText,
          cancelText: options.cancelText,
          onConfirm: () => {
            setState((prev) => ({ ...prev, open: false }))
            resolve(true)
          },
          onCancel: () => {
            setState((prev) => ({ ...prev, open: false }))
            resolve(false)
          }
        })
      })
    },
    []
  )

  const close = React.useCallback(() => {
    setState((prev) => ({ ...prev, open: false }))
  }, [])

  const ConfirmDialogComponent = React.useCallback(
    () => (
      <ConfirmDialog
        open={state.open}
        onOpenChange={close}
        title={state.title}
        description={state.description}
        variant={state.variant}
        confirmText={state.confirmText}
        cancelText={state.cancelText}
        onConfirm={state.onConfirm || (() => {})}
        onCancel={state.onCancel}
      />
    ),
    [state, close]
  )

  return {
    confirm,
    close,
    ConfirmDialog: ConfirmDialogComponent
  }
}

// Preset confirmation dialogs
export const ConfirmDialogs = {
  Delete: ({
    itemName,
    onConfirm,
    t
  }: {
    itemName: string
    onConfirm: () => void | Promise<void>
    t: (key: string, params?: any) => string
  }) => ({
    title: t('components.dialogs.confirmDelete', { name: itemName }),
    description: t('components.dialogs.confirmDelete', { name: itemName }),
    variant: 'destructive' as const,
    confirmText: t('common.delete'),
    onConfirm
  }),

  Archive: ({
    itemName,
    onConfirm,
    t
  }: {
    itemName: string
    onConfirm: () => void | Promise<void>
    t: (key: string, params?: any) => string
  }) => ({
    title: t('components.dialogs.confirmArchive', { name: itemName }),
    description: t('components.dialogs.confirmArchive', { name: itemName }),
    variant: 'warning' as const,
    confirmText: t('components.actions.archive'),
    onConfirm
  }),

  Save: ({ onConfirm }: { onConfirm: () => void | Promise<void> }) => ({
    title: 'Save Changes',
    description: 'Do you want to save your changes before leaving?',
    variant: 'default' as const,
    confirmText: 'Save',
    cancelText: 'Discard',
    onConfirm
  }),

  Leave: ({ onConfirm }: { onConfirm: () => void | Promise<void> }) => ({
    title: 'Unsaved Changes',
    description: 'You have unsaved changes. Are you sure you want to leave without saving?',
    variant: 'warning' as const,
    confirmText: 'Leave',
    cancelText: 'Stay',
    onConfirm
  })
}

export default ConfirmDialog
