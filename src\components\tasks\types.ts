// Task Module Types - 任务模块类型定义
// 该模块在应用多个位置复用，提供灵活的配置与回调

import type { Task, Project } from '@/types/business'
import type { BaseComponentProps } from '@/types/common'

export type TaskViewMode = 'list' | 'board'
export type TaskGroupBy = 'none' | 'status' | 'priority' | 'dueDate'

export interface TaskFilters {
  search?: string
  status?: Array<Task['status']>
  priority?: Array<Project['priority']> // 与业务统一：low/medium/high/critical
  dateRange?: { start?: string; end?: string }
  onlyOverdue?: boolean
  onlyToday?: boolean
}

export interface TaskModuleContext {
  projectId?: string
  areaId?: string
}

export interface TaskModuleFeatureToggles {
  enableSubtasks?: boolean
  enableInlineEdit?: boolean
  enableDragSort?: boolean
  enableFilters?: boolean
  showStats?: boolean
}

// 可覆盖的API接口，便于不同场景下定制数据来源
export interface TaskDataSourceOverrides {
  fetchTasks?: (ctx: TaskModuleContext, filters: TaskFilters) => Promise<Task[]>
  createTask?: (payload: Partial<Task>) => Promise<Task>
  updateTask?: (id: string, patch: Partial<Task>) => Promise<Task>
  updateTaskStatus?: (id: string, status: Task['status']) => Promise<Task>
  deleteTask?: (id: string) => Promise<void>
}

export interface TaskManagerProps extends BaseComponentProps {
  mode?: TaskViewMode
  groupBy?: TaskGroupBy
  context?: TaskModuleContext
  initialFilters?: TaskFilters
  features?: TaskModuleFeatureToggles
  dataSource?: TaskDataSourceOverrides
  // 事件回调（可选）
  onTaskCreated?: (task: Task) => void
  onTaskUpdated?: (task: Task) => void
  onTaskDeleted?: (id: string) => void
  onSelectionChange?: (ids: string[]) => void
}

export interface TaskWithChildren extends Task {
  children?: TaskWithChildren[]
}

export interface ReorderEvent {
  sourceId: string
  targetId: string
  parentId?: string
}

