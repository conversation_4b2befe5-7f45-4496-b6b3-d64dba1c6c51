// Inbox Entity - 收件箱实体

use crate::shared::types::{Id, Metadata, EntityStatus, Priority, Tag};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InboxItem {
    pub id: Id,
    pub content: String,
    pub item_type: InboxItemType,
    pub priority: Priority,
    pub tags: Vec<Tag>,
    pub processed: bool,
    pub processed_at: Option<chrono::DateTime<chrono::Utc>>,
    pub entity_status: EntityStatus,
    pub metadata: Metadata,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum InboxItemType {
    Note,
    Task,
    Idea,
    Link,
    File,
}

impl InboxItem {
    pub fn new(content: String, item_type: InboxItemType) -> Self {
        Self {
            id: crate::shared::utils::IdGenerator::new_id_with_prefix("inbox"),
            content,
            item_type,
            priority: Priority::default(),
            tags: Vec::new(),
            processed: false,
            processed_at: None,
            entity_status: EntityStatus::Active,
            metadata: Metadata::default(),
        }
    }

    pub fn mark_processed(&mut self) {
        self.processed = true;
        self.processed_at = Some(chrono::Utc::now());
        self.metadata.updated_at = chrono::Utc::now();
        self.metadata.version += 1;
    }
}
