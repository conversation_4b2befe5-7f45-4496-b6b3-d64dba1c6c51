/**
 * 批量项目KPI记录对话框
 * 基于BatchAreaMetricRecordDialog设计，适配项目KPI数据结构
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Textarea } from '../ui/textarea'
import { Checkbox } from '../ui/checkbox'
import { ScrollArea } from '../ui/scroll-area'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog'
import { Calendar, Target, TrendingUp, TrendingDown } from 'lucide-react'
import { useUIStore } from '../../store/uiStore'
import { useLanguage } from '../../contexts/LanguageContext'
import { databaseApi } from '../../lib/api'
import { cn } from '../../lib/utils'
import type { ProjectKPI } from '../../../../shared/types'

interface KPIRecordInput {
  kpiId: string
  value: string
  note: string
  enabled: boolean
}

interface BatchProjectKPIRecordDialogProps {
  isOpen: boolean
  onClose: () => void
  kpis: ProjectKPI[]
  onRecordsCreated?: () => void
}

export function BatchProjectKPIRecordDialog({ 
  isOpen, 
  onClose, 
  kpis, 
  onRecordsCreated 
}: BatchProjectKPIRecordDialogProps) {
  const { t } = useLanguage()
  const [recordInputs, setRecordInputs] = useState<Record<string, KPIRecordInput>>({})
  const [globalNote, setGlobalNote] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { addNotification } = useUIStore()

  // 初始化记录输入
  const initializeInputs = () => {
    const inputs: Record<string, KPIRecordInput> = {}
    kpis.forEach(kpi => {
      inputs[kpi.id] = {
        kpiId: kpi.id,
        value: kpi.value, // 使用当前值作为默认值
        note: '',
        enabled: true
      }
    })
    setRecordInputs(inputs)
  }

  // 当对话框打开时初始化输入
  useEffect(() => {
    if (isOpen) {
      initializeInputs()
      setGlobalNote('')
    }
  }, [isOpen, kpis])

  // 更新单个记录输入
  const updateRecordInput = (kpiId: string, field: keyof Omit<KPIRecordInput, 'kpiId'>, value: string | boolean) => {
    setRecordInputs(prev => ({
      ...prev,
      [kpiId]: {
        ...prev[kpiId],
        [field]: value
      }
    }))
  }

  // 应用全局备注到所有启用的记录
  const applyGlobalNote = () => {
    if (!globalNote.trim()) return
    
    setRecordInputs(prev => {
      const updated = { ...prev }
      Object.keys(updated).forEach(kpiId => {
        if (updated[kpiId].enabled) {
          updated[kpiId].note = globalNote.trim()
        }
      })
      return updated
    })
    setGlobalNote('')
  }

  // 提交批量记录
  const handleSubmit = async () => {
    const enabledRecords = Object.values(recordInputs).filter(input => input.enabled)
    
    if (enabledRecords.length === 0) {
      addNotification({
        type: 'warning',
        title: t('common.warning'),
        message: t('pages.projects.detail.projectKPI.batch.selectAtLeastOne')
      })
      return
    }

    setIsSubmitting(true)
    try {
      const promises = enabledRecords.map(input => 
        databaseApi.createKPIRecord({
          kpiId: input.kpiId,
          value: input.value,
          note: input.note || undefined
        })
      )

      const results = await Promise.all(promises)
      const failedCount = results.filter(result => !result.success).length
      
      if (failedCount === 0) {
        addNotification({
          type: 'success',
          title: t('common.success'),
          message: t('pages.projects.detail.projectKPI.batch.recordSuccess', { count: enabledRecords.length })
        })
        onRecordsCreated?.()
        onClose()
      } else {
        addNotification({
          type: 'warning',
          title: t('common.warning'),
          message: t('pages.projects.detail.projectKPI.batch.recordPartialSuccess', {
            success: enabledRecords.length - failedCount,
            failed: failedCount
          })
        })
      }
    } catch (error) {
      console.error('Failed to create batch records:', error)
      addNotification({
        type: 'error',
        title: t('common.error'),
        message: t('pages.projects.detail.projectKPI.batch.recordError')
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const enabledCount = Object.values(recordInputs).filter(input => input.enabled).length

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            {t('pages.projects.detail.projectKPI.batchRecordTitle')}
          </DialogTitle>
          <DialogDescription>
            {t('pages.projects.detail.projectKPI.batchRecordDescription')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Global Note Section */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">{t('pages.projects.detail.projectKPI.globalNote')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex gap-2">
                <Textarea
                  value={globalNote}
                  onChange={(e) => setGlobalNote(e.target.value)}
                  placeholder={t('pages.projects.detail.projectKPI.globalNotePlaceholder')}
                  rows={2}
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  onClick={applyGlobalNote}
                  disabled={!globalNote.trim()}
                  className="self-start"
                >
                  {t('pages.projects.detail.projectKPI.applyToAll')}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* KPI Records Section */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">{t('pages.projects.detail.projectKPI.batch.kpiRecords', { count: enabledCount })}</h4>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setRecordInputs(prev => {
                      const updated = { ...prev }
                      Object.keys(updated).forEach(kpiId => {
                        updated[kpiId].enabled = true
                      })
                      return updated
                    })
                  }}
                >
                  {t('pages.projects.detail.projectKPI.batch.enableAll')}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setRecordInputs(prev => {
                      const updated = { ...prev }
                      Object.keys(updated).forEach(kpiId => {
                        updated[kpiId].enabled = false
                      })
                      return updated
                    })
                  }}
                >
                  {t('pages.projects.detail.projectKPI.batch.disableAll')}
                </Button>
              </div>
            </div>

            <ScrollArea className="h-[400px] pr-4">
              <div className="space-y-3">
                {kpis.map((kpi) => {
                  const input = recordInputs[kpi.id]
                  if (!input) return null

                  return (
                    <Card key={kpi.id} className={cn(
                      "transition-all duration-200",
                      input.enabled
                        ? 'border-primary/20 bg-primary/5'
                        : 'border-muted bg-muted/20'
                    )}>
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Checkbox
                              checked={input.enabled}
                              onCheckedChange={(checked) =>
                                updateRecordInput(kpi.id, 'enabled', checked as boolean)
                              }
                            />
                            <div className="flex items-center gap-1">
                              {(kpi as any).direction === 'decrease' ? (
                                <TrendingDown className="h-4 w-4 text-orange-500" />
                              ) : (
                                <TrendingUp className="h-4 w-4 text-green-500" />
                              )}
                              <CardTitle className="text-sm">{kpi.name}</CardTitle>
                            </div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {t('pages.projects.detail.projectKPI.batch.current')}: {kpi.value}{kpi.unit && ` ${kpi.unit}`}
                            {kpi.target && ` / ${kpi.target}${kpi.unit && ` ${kpi.unit}`}`}
                          </div>
                        </div>
                      </CardHeader>
                      
                      {input.enabled && (
                        <CardContent className="pt-0 space-y-2">
                          <div className="grid grid-cols-2 gap-2">
                            <div>
                              <Label className="text-xs">{t('pages.projects.detail.projectKPI.batch.newValue')}</Label>
                              <Input
                                value={input.value}
                                onChange={(e) => updateRecordInput(kpi.id, 'value', e.target.value)}
                                placeholder={`${t('pages.projects.detail.projectKPI.batch.enterValue')}${kpi.unit ? ` (${kpi.unit})` : ''}`}
                                className="h-8"
                              />
                            </div>
                            <div>
                              <Label className="text-xs">{t('pages.projects.detail.projectKPI.batch.note')}</Label>
                              <Input
                                value={input.note}
                                onChange={(e) => updateRecordInput(kpi.id, 'note', e.target.value)}
                                placeholder={t('pages.projects.detail.projectKPI.batch.optionalNote')}
                                className="h-8"
                              />
                            </div>
                          </div>
                        </CardContent>
                      )}
                    </Card>
                  )
                })}
              </div>
            </ScrollArea>
          </div>

          {/* Recording Time */}
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Calendar className="h-3 w-3" />
            <span>{t('pages.projects.detail.projectKPI.batch.recordingTime')}: {new Date().toLocaleString()}</span>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
          >
            {t('common.cancel')}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || enabledCount === 0}
          >
            {isSubmitting ? t('pages.projects.detail.projectKPI.batch.recording') : t('pages.projects.detail.projectKPI.batch.recordValues', { count: enabledCount })}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default BatchProjectKPIRecordDialog
