// API Service - API 服务层
// 封装与后端 Tauri 命令的交互

import { invoke } from '@tauri-apps/api/core';
import { ApiResponse } from '../types/common';
import { User, Project, Task, Area } from '../types/business';

// 基础 API 调用封装
class ApiService {
  private async invokeCommand<T>(command: string, args?: Record<string, any>): Promise<T> {
    try {
      const response = await invoke<ApiResponse<T>>(command, args);
      if (response.success && response.data !== undefined) {
        return response.data;
      } else {
        throw new Error(response.error || 'Unknown error occurred');
      }
    } catch (error) {
      console.error(`API Error [${command}]:`, error);
      throw error;
    }
  }

  // 用户相关 API
  async getUsers(): Promise<User[]> {
    return this.invokeCommand('get_active_users');
  }

  async getUserById(id: string): Promise<User> {
    return this.invokeCommand('get_user_by_id', { id });
  }

  async createUser(data: {
    username: string;
    email?: string;
    displayName?: string;
  }): Promise<User> {
    return this.invokeCommand('create_user', { request: data });
  }

  async updateUser(id: string, data: {
    displayName?: string;
    email?: string;
    avatarUrl?: string;
  }): Promise<User> {
    return this.invokeCommand('update_user', { id, request: data });
  }

  async deleteUser(id: string): Promise<void> {
    return this.invokeCommand('delete_user', { id });
  }

  // 项目相关 API
  async getProjects(): Promise<Project[]> {
    return this.invokeCommand('get_active_projects');
  }

  async getProjectById(id: string): Promise<Project> {
    return this.invokeCommand('get_project_by_id', { id });
  }

  async createProject(data: {
    name: string;
    description?: string;
    areaId?: string;
    priority?: string;
    startDate?: string;
    dueDate?: string;
    goals?: string[];
    deliverables?: string[];
    tags?: string[];
  }): Promise<Project> {
    return this.invokeCommand('create_project', { request: data });
  }

  async updateProject(id: string, data: {
    name?: string;
    description?: string;
    goals?: string[];
    deliverables?: string[];
    startDate?: string;
    dueDate?: string;
    areaId?: string;
    priority?: string;
    tags?: string[];
  }): Promise<Project> {
    return this.invokeCommand('update_project', { id, request: data });
  }

  async updateProjectStatus(id: string, status: string): Promise<Project> {
    return this.invokeCommand('update_project_status', { id, request: { status } });
  }

  async updateProjectProgress(id: string, progress: number): Promise<Project> {
    return this.invokeCommand('update_project_progress', { id, request: { progress } });
  }

  async deleteProject(id: string): Promise<void> {
    return this.invokeCommand('delete_project', { id });
  }

  async getProjectStatistics(): Promise<any> {
    return this.invokeCommand('get_project_statistics');
  }

  // 任务相关 API
  async getTasks(): Promise<Task[]> {
    return this.invokeCommand('get_active_tasks');
  }

  async getTaskById(id: string): Promise<Task> {
    return this.invokeCommand('get_task_by_id', { id });
  }

  async getTasksByProject(projectId: string): Promise<Task[]> {
    return this.invokeCommand('get_tasks_by_project', { project_id: projectId });
  }

  async getTodayTasks(): Promise<Task[]> {
    return this.invokeCommand('get_today_tasks');
  }

  async getOverdueTasks(): Promise<Task[]> {
    return this.invokeCommand('get_overdue_tasks');
  }

  async createTask(data: {
    title: string;
    description?: string;
    projectId?: string;
    parentTaskId?: string;
    dueDate?: string;
    priority?: string;
  }): Promise<Task> {
    return this.invokeCommand('create_task', { request: data });
  }

  async updateTask(id: string, data: {
    title?: string;
    description?: string;
    projectId?: string;
    parentTaskId?: string;
    dueDate?: string;
    priority?: string;
  }): Promise<Task> {
    return this.invokeCommand('update_task', { id, request: data });
  }

  async updateTaskStatus(id: string, status: string): Promise<Task> {
    return this.invokeCommand('update_task_status', { id, request: { status } });
  }

  async updateTaskProgress(id: string, progress: number): Promise<Task> {
    return this.invokeCommand('update_task_progress', { id, request: { progress } });
  }

  async completeTask(id: string): Promise<Task> {
    return this.invokeCommand('complete_task', { id });
  }

  async deleteTask(id: string): Promise<void> {
    return this.invokeCommand('delete_task', { id });
  }

  async getTaskStatistics(): Promise<any> {
    return this.invokeCommand('get_task_statistics');
  }

  // 领域相关 API
  async getAreas(): Promise<Area[]> {
    return this.invokeCommand('get_active_areas');
  }

  async getAreaById(id: string): Promise<Area> {
    return this.invokeCommand('get_area_by_id', { id });
  }

  async createArea(data: {
    name: string;
    description?: string;
    standards?: string[];
    color?: string;
    icon?: string;
  }): Promise<Area> {
    return this.invokeCommand('create_area', { request: data });
  }

  async updateArea(id: string, data: {
    name?: string;
    description?: string;
    standards?: string[];
    color?: string;
    icon?: string;
  }): Promise<Area> {
    return this.invokeCommand('update_area', { id, request: data });
  }

  async updateAreaStatus(id: string, status: string): Promise<Area> {
    return this.invokeCommand('update_area_status', { id, request: { status } });
  }

  async deleteArea(id: string): Promise<void> {
    return this.invokeCommand('delete_area', { id });
  }

  async getAreaStatistics(): Promise<any> {
    return this.invokeCommand('get_area_statistics');
  }

  // 系统相关 API
  async healthCheck(): Promise<any> {
    return this.invokeCommand('health_check');
  }

  async getSystemInfo(): Promise<any> {
    return this.invokeCommand('get_system_info');
  }

  // 搜索 API
  async searchProjects(query: string): Promise<Project[]> {
    return this.invokeCommand('search_projects', { request: { query } });
  }

  async searchTasks(query: string): Promise<Task[]> {
    return this.invokeCommand('search_tasks', { request: { query } });
  }

  async searchAreas(query: string): Promise<Area[]> {
    return this.invokeCommand('search_areas', { request: { query } });
  }
}

// 创建单例实例
export const apiService = new ApiService();

// 导出具体的 API 方法以便直接使用
export const {
  // 用户 API
  getUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  
  // 项目 API
  getProjects,
  getProjectById,
  createProject,
  updateProject,
  updateProjectStatus,
  updateProjectProgress,
  deleteProject,
  getProjectStatistics,
  
  // 任务 API
  getTasks,
  getTaskById,
  getTasksByProject,
  getTodayTasks,
  getOverdueTasks,
  createTask,
  updateTask,
  updateTaskStatus,
  updateTaskProgress,
  completeTask,
  deleteTask,
  getTaskStatistics,
  
  // 领域 API
  getAreas,
  getAreaById,
  createArea,
  updateArea,
  updateAreaStatus,
  deleteArea,
  getAreaStatistics,
  
  // 系统 API
  healthCheck,
  getSystemInfo,
  
  // 搜索 API
  searchProjects,
  searchTasks,
  searchAreas,
} = apiService;
