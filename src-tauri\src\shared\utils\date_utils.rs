// Date Utilities - 日期工具函数

use chrono::{DateTime, Utc, Local, TimeZone};

pub fn now_utc() -> DateTime<Utc> {
    Utc::now()
}

pub fn now_local() -> DateTime<Local> {
    Local::now()
}

pub fn format_date(date: DateTime<Utc>, format: &str) -> String {
    date.format(format).to_string()
}

pub fn parse_date(date_str: &str, format: &str) -> Result<DateTime<Utc>, chrono::ParseError> {
    DateTime::parse_from_str(date_str, format)
        .map(|dt| dt.with_timezone(&Utc))
}

pub fn days_between(start: DateTime<Utc>, end: DateTime<Utc>) -> i64 {
    (end - start).num_days()
}

pub fn is_today(date: DateTime<Utc>) -> bool {
    let now = Utc::now();
    date.date_naive() == now.date_naive()
}

pub fn is_past_due(date: DateTime<Utc>) -> bool {
    date < Utc::now()
}

pub fn start_of_day(date: DateTime<Utc>) -> DateTime<Utc> {
    date.date_naive().and_hms_opt(0, 0, 0)
        .map(|dt| Utc.from_utc_datetime(&dt))
        .unwrap_or(date)
}

pub fn end_of_day(date: DateTime<Utc>) -> DateTime<Utc> {
    date.date_naive().and_hms_opt(23, 59, 59)
        .map(|dt| Utc.from_utc_datetime(&dt))
        .unwrap_or(date)
}
