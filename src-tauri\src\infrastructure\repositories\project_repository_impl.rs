// Project Repository Implementation - 项目仓储实现

use crate::domain::entities::{Project, ProjectStatus};
use crate::domain::repositories::{ProjectRepository, ProjectStatistics};
use crate::infrastructure::database::{DatabasePool, ProjectModel};
use crate::shared::errors::{AppError, Result};
use crate::shared::types::{Id, QueryParams, EntityStatus, Metadata, Priority, Tag};
use async_trait::async_trait;

pub struct ProjectRepositoryImpl {
    pool: DatabasePool,
}

impl ProjectRepositoryImpl {
    pub fn new(pool: DatabasePool) -> Self {
        Self { pool }
    }

    /// 将数据库模型转换为领域实体
    fn model_to_entity(&self, model: ProjectModel) -> Result<Project> {
        let goals: Vec<String> = serde_json::from_str(&model.goals.unwrap_or_default())
            .map_err(|e| AppError::DatabaseError(format!("Failed to parse project goals: {}", e)))?;

        let deliverables: Vec<String> = serde_json::from_str(&model.deliverables.unwrap_or_default())
            .map_err(|e| AppError::DatabaseError(format!("Failed to parse project deliverables: {}", e)))?;

        let tags: Vec<Tag> = serde_json::from_str(&model.tags.unwrap_or_default())
            .map_err(|e| AppError::DatabaseError(format!("Failed to parse project tags: {}", e)))?;

        let status = match model.status.as_str() {
            "not_started" => ProjectStatus::NotStarted,
            "in_progress" => ProjectStatus::InProgress,
            "at_risk" => ProjectStatus::AtRisk,
            "on_hold" => ProjectStatus::OnHold,
            "completed" => ProjectStatus::Completed,
            "cancelled" => ProjectStatus::Cancelled,
            _ => ProjectStatus::NotStarted,
        };

        let priority = match model.priority.as_str() {
            "low" => Priority::Low,
            "medium" => Priority::Medium,
            "high" => Priority::High,
            "critical" => Priority::Critical,
            _ => Priority::Medium,
        };

        let entity_status = match model.entity_status.as_str() {
            "active" => EntityStatus::Active,
            "inactive" => EntityStatus::Inactive,
            "deleted" => EntityStatus::Deleted,
            "archived" => EntityStatus::Archived,
            _ => EntityStatus::Active,
        };

        let metadata = Metadata {
            created_at: model.created_at,
            updated_at: model.updated_at,
            created_by: None,
            updated_by: None,
            version: model.version as u64,
        };

        Ok(Project {
            id: model.id,
            name: model.name,
            description: model.description,
            goals,
            deliverables,
            start_date: model.start_date,
            due_date: model.due_date,
            area_id: model.area_id,
            status,
            priority,
            progress: model.progress as f32,
            tags,
            entity_status,
            metadata,
        })
    }

    /// 将领域实体转换为数据库模型
    fn entity_to_model(&self, project: &Project) -> Result<ProjectModel> {
        let goals = serde_json::to_string(&project.goals)
            .map_err(|e| AppError::DatabaseError(format!("Failed to serialize project goals: {}", e)))?;

        let deliverables = serde_json::to_string(&project.deliverables)
            .map_err(|e| AppError::DatabaseError(format!("Failed to serialize project deliverables: {}", e)))?;

        let tags = serde_json::to_string(&project.tags)
            .map_err(|e| AppError::DatabaseError(format!("Failed to serialize project tags: {}", e)))?;

        let status = match project.status {
            ProjectStatus::NotStarted => "not_started",
            ProjectStatus::InProgress => "in_progress",
            ProjectStatus::AtRisk => "at_risk",
            ProjectStatus::OnHold => "on_hold",
            ProjectStatus::Completed => "completed",
            ProjectStatus::Cancelled => "cancelled",
        };

        let priority = match project.priority {
            Priority::Low => "low",
            Priority::Medium => "medium",
            Priority::High => "high",
            Priority::Critical => "critical",
        };

        let entity_status = match project.entity_status {
            EntityStatus::Active => "active",
            EntityStatus::Inactive => "inactive",
            EntityStatus::Deleted => "deleted",
            EntityStatus::Archived => "archived",
        };

        Ok(ProjectModel {
            id: project.id.clone(),
            name: project.name.clone(),
            description: project.description.clone(),
            goals: Some(goals),
            deliverables: Some(deliverables),
            start_date: project.start_date,
            due_date: project.due_date,
            area_id: project.area_id.clone(),
            status: status.to_string(),
            priority: priority.to_string(),
            progress: project.progress as f64,
            tags: Some(tags),
            entity_status: entity_status.to_string(),
            created_at: project.metadata.created_at,
            updated_at: project.metadata.updated_at,
            version: project.metadata.version as i64,
        })
    }
}

#[async_trait]
impl ProjectRepository for ProjectRepositoryImpl {
    async fn find_by_id(&self, id: &Id) -> Result<Option<Project>> {
        let model = sqlx::query_as::<_, ProjectModel>(
            "SELECT * FROM projects WHERE id = ? AND entity_status != 'deleted'"
        )
        .bind(id)
        .fetch_optional(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find project by id: {}", e)))?;

        match model {
            Some(m) => Ok(Some(self.model_to_entity(m)?)),
            None => Ok(None),
        }
    }

    async fn find_by_name(&self, name: &str) -> Result<Option<Project>> {
        let model = sqlx::query_as::<_, ProjectModel>(
            "SELECT * FROM projects WHERE name = ? AND entity_status != 'deleted'"
        )
        .bind(name)
        .fetch_optional(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find project by name: {}", e)))?;

        match model {
            Some(m) => Ok(Some(self.model_to_entity(m)?)),
            None => Ok(None),
        }
    }

    async fn save(&self, project: &Project) -> Result<()> {
        let model = self.entity_to_model(project)?;
        
        sqlx::query(
            r#"
            INSERT INTO projects (id, name, description, goals, deliverables, start_date, due_date, 
                                area_id, status, priority, progress, tags, entity_status, 
                                created_at, updated_at, version)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&model.id)
        .bind(&model.name)
        .bind(&model.description)
        .bind(&model.goals)
        .bind(&model.deliverables)
        .bind(&model.start_date)
        .bind(&model.due_date)
        .bind(&model.area_id)
        .bind(&model.status)
        .bind(&model.priority)
        .bind(&model.progress)
        .bind(&model.tags)
        .bind(&model.entity_status)
        .bind(&model.created_at)
        .bind(&model.updated_at)
        .bind(&model.version)
        .execute(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to save project: {}", e)))?;

        Ok(())
    }

    async fn update(&self, project: &Project) -> Result<()> {
        let model = self.entity_to_model(project)?;
        
        let result = sqlx::query(
            r#"
            UPDATE projects 
            SET name = ?, description = ?, goals = ?, deliverables = ?, start_date = ?, 
                due_date = ?, area_id = ?, status = ?, priority = ?, progress = ?, 
                tags = ?, entity_status = ?, updated_at = ?, version = version + 1
            WHERE id = ? AND version = ?
            "#
        )
        .bind(&model.name)
        .bind(&model.description)
        .bind(&model.goals)
        .bind(&model.deliverables)
        .bind(&model.start_date)
        .bind(&model.due_date)
        .bind(&model.area_id)
        .bind(&model.status)
        .bind(&model.priority)
        .bind(&model.progress)
        .bind(&model.tags)
        .bind(&model.entity_status)
        .bind(chrono::Utc::now())
        .bind(&model.id)
        .bind(&model.version)
        .execute(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to update project: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::DatabaseError("Project not found or version conflict".to_string()));
        }

        Ok(())
    }

    async fn delete(&self, id: &Id) -> Result<()> {
        let result = sqlx::query(
            "UPDATE projects SET entity_status = 'deleted', updated_at = ? WHERE id = ?"
        )
        .bind(chrono::Utc::now())
        .bind(id)
        .execute(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to delete project: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::NotFound("Project not found".to_string()));
        }

        Ok(())
    }

    async fn find_all_active(&self) -> Result<Vec<Project>> {
        let models = sqlx::query_as::<_, ProjectModel>(
            "SELECT * FROM projects WHERE entity_status = 'active' ORDER BY created_at DESC"
        )
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find active projects: {}", e)))?;

        let mut projects = Vec::new();
        for model in models {
            projects.push(self.model_to_entity(model)?);
        }

        Ok(projects)
    }

    async fn find_by_area_id(&self, area_id: &Id) -> Result<Vec<Project>> {
        let models = sqlx::query_as::<_, ProjectModel>(
            "SELECT * FROM projects WHERE area_id = ? AND entity_status != 'deleted' ORDER BY created_at DESC"
        )
        .bind(area_id)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find projects by area: {}", e)))?;

        let mut projects = Vec::new();
        for model in models {
            projects.push(self.model_to_entity(model)?);
        }

        Ok(projects)
    }

    async fn find_by_status(&self, status: &ProjectStatus) -> Result<Vec<Project>> {
        let status_str = match status {
            ProjectStatus::NotStarted => "not_started",
            ProjectStatus::InProgress => "in_progress",
            ProjectStatus::AtRisk => "at_risk",
            ProjectStatus::OnHold => "on_hold",
            ProjectStatus::Completed => "completed",
            ProjectStatus::Cancelled => "cancelled",
        };

        let models = sqlx::query_as::<_, ProjectModel>(
            "SELECT * FROM projects WHERE status = ? AND entity_status != 'deleted' ORDER BY created_at DESC"
        )
        .bind(status_str)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find projects by status: {}", e)))?;

        let mut projects = Vec::new();
        for model in models {
            projects.push(self.model_to_entity(model)?);
        }

        Ok(projects)
    }

    async fn find_by_priority(&self, priority: &Priority) -> Result<Vec<Project>> {
        let priority_str = match priority {
            Priority::Low => "low",
            Priority::Medium => "medium",
            Priority::High => "high",
            Priority::Critical => "critical",
        };

        let models = sqlx::query_as::<_, ProjectModel>(
            "SELECT * FROM projects WHERE priority = ? AND entity_status != 'deleted' ORDER BY created_at DESC"
        )
        .bind(priority_str)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find projects by priority: {}", e)))?;

        let mut projects = Vec::new();
        for model in models {
            projects.push(self.model_to_entity(model)?);
        }

        Ok(projects)
    }

    async fn find_due_soon(&self, days: u32) -> Result<Vec<Project>> {
        let cutoff_date = chrono::Utc::now() + chrono::Duration::days(days as i64);

        let models = sqlx::query_as::<_, ProjectModel>(
            r#"
            SELECT * FROM projects 
            WHERE due_date IS NOT NULL 
              AND due_date <= ? 
              AND status NOT IN ('completed', 'cancelled')
              AND entity_status != 'deleted'
            ORDER BY due_date ASC
            "#
        )
        .bind(cutoff_date)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find projects due soon: {}", e)))?;

        let mut projects = Vec::new();
        for model in models {
            projects.push(self.model_to_entity(model)?);
        }

        Ok(projects)
    }

    async fn find_overdue(&self) -> Result<Vec<Project>> {
        let now = chrono::Utc::now();

        let models = sqlx::query_as::<_, ProjectModel>(
            r#"
            SELECT * FROM projects 
            WHERE due_date IS NOT NULL 
              AND due_date < ? 
              AND status NOT IN ('completed', 'cancelled')
              AND entity_status != 'deleted'
            ORDER BY due_date ASC
            "#
        )
        .bind(now)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find overdue projects: {}", e)))?;

        let mut projects = Vec::new();
        for model in models {
            projects.push(self.model_to_entity(model)?);
        }

        Ok(projects)
    }

    async fn find_with_pagination(&self, params: &QueryParams) -> Result<(Vec<Project>, u64)> {
        let pagination = params.pagination.as_ref().unwrap_or(&crate::shared::types::Pagination::default());
        let offset = (pagination.page - 1) * pagination.size;

        // 获取总数
        let total = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM projects WHERE entity_status != 'deleted'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count projects: {}", e)))? as u64;

        // 获取分页数据
        let models = sqlx::query_as::<_, ProjectModel>(
            "SELECT * FROM projects WHERE entity_status != 'deleted' ORDER BY created_at DESC LIMIT ? OFFSET ?"
        )
        .bind(pagination.size as i64)
        .bind(offset as i64)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find projects with pagination: {}", e)))?;

        let mut projects = Vec::new();
        for model in models {
            projects.push(self.model_to_entity(model)?);
        }

        Ok((projects, total))
    }

    async fn count_active_projects(&self) -> Result<u64> {
        let count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM projects WHERE entity_status = 'active'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count active projects: {}", e)))?;

        Ok(count as u64)
    }

    async fn find_by_progress_range(&self, min_progress: f32, max_progress: f32) -> Result<Vec<Project>> {
        let models = sqlx::query_as::<_, ProjectModel>(
            r#"
            SELECT * FROM projects 
            WHERE progress >= ? AND progress <= ? 
              AND entity_status != 'deleted'
            ORDER BY progress DESC
            "#
        )
        .bind(min_progress as f64)
        .bind(max_progress as f64)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find projects by progress range: {}", e)))?;

        let mut projects = Vec::new();
        for model in models {
            projects.push(self.model_to_entity(model)?);
        }

        Ok(projects)
    }

    async fn search(&self, query: &str) -> Result<Vec<Project>> {
        let search_pattern = format!("%{}%", query);

        let models = sqlx::query_as::<_, ProjectModel>(
            r#"
            SELECT * FROM projects 
            WHERE (name LIKE ? OR description LIKE ?)
              AND entity_status != 'deleted'
            ORDER BY 
              CASE WHEN name LIKE ? THEN 1 ELSE 2 END,
              created_at DESC
            "#
        )
        .bind(&search_pattern)
        .bind(&search_pattern)
        .bind(&search_pattern)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to search projects: {}", e)))?;

        let mut projects = Vec::new();
        for model in models {
            projects.push(self.model_to_entity(model)?);
        }

        Ok(projects)
    }

    async fn get_statistics(&self) -> Result<ProjectStatistics> {
        // 获取基础统计
        let total_projects = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM projects WHERE entity_status != 'deleted'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count total projects: {}", e)))? as u64;

        let active_projects = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM projects WHERE entity_status = 'active'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count active projects: {}", e)))? as u64;

        let completed_projects = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM projects WHERE status = 'completed' AND entity_status != 'deleted'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count completed projects: {}", e)))? as u64;

        let overdue_projects = sqlx::query_scalar::<_, i64>(
            r#"
            SELECT COUNT(*) FROM projects 
            WHERE due_date IS NOT NULL 
              AND due_date < ? 
              AND status NOT IN ('completed', 'cancelled')
              AND entity_status != 'deleted'
            "#
        )
        .bind(chrono::Utc::now())
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count overdue projects: {}", e)))? as u64;

        let average_progress = sqlx::query_scalar::<_, Option<f64>>(
            "SELECT AVG(progress) FROM projects WHERE entity_status != 'deleted'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to calculate average progress: {}", e)))?
        .unwrap_or(0.0) as f32;

        // 按状态统计
        let status_stats = sqlx::query_as::<_, (String, i64)>(
            "SELECT status, COUNT(*) FROM projects WHERE entity_status != 'deleted' GROUP BY status"
        )
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to get status statistics: {}", e)))?;

        let projects_by_status = status_stats.into_iter().map(|(status, count)| {
            let project_status = match status.as_str() {
                "not_started" => ProjectStatus::NotStarted,
                "in_progress" => ProjectStatus::InProgress,
                "at_risk" => ProjectStatus::AtRisk,
                "on_hold" => ProjectStatus::OnHold,
                "completed" => ProjectStatus::Completed,
                "cancelled" => ProjectStatus::Cancelled,
                _ => ProjectStatus::NotStarted,
            };
            (project_status, count as u64)
        }).collect();

        // 按优先级统计
        let priority_stats = sqlx::query_as::<_, (String, i64)>(
            "SELECT priority, COUNT(*) FROM projects WHERE entity_status != 'deleted' GROUP BY priority"
        )
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to get priority statistics: {}", e)))?;

        let projects_by_priority = priority_stats.into_iter().map(|(priority, count)| {
            let project_priority = match priority.as_str() {
                "low" => Priority::Low,
                "medium" => Priority::Medium,
                "high" => Priority::High,
                "critical" => Priority::Critical,
                _ => Priority::Medium,
            };
            (project_priority, count as u64)
        }).collect();

        Ok(ProjectStatistics {
            total_projects,
            active_projects,
            completed_projects,
            overdue_projects,
            average_progress,
            projects_by_status,
            projects_by_priority,
        })
    }
}
