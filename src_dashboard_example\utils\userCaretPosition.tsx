import React, { useState, useCallback, useEffect, useRef } from 'react'

// 1. 定义光标位置的类型接口
interface CaretPosition {
  top: number
  left: number
  height: number
}

/**
 * 一个自定义React Hook，用于追踪并返回当前光标（Caret）在任何可编辑元素或输入框中的位置。
 * @returns {CaretPosition | null} 返回一个包含top, left, height的对象，如果无法获取位置则返回null。
 */
const useCaretPosition = (): CaretPosition | null => {
  // 2. 为 state 添加类型
  const [position, setPosition] = useState<CaretPosition | null>(null)

  const updatePosition = useCallback(() => {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0).cloneRange()
      const rect = range.getBoundingClientRect()

      // rect.height > 0 是一个很好的判断标准，确保光标确实可见
      if (rect.height > 0) {
        setPosition({
          top: rect.top,
          left: rect.left,
          height: rect.height
        })
      }
    }
  }, [])

  useEffect(() => {
    document.addEventListener('selectionchange', updatePosition)
    document.addEventListener('keyup', updatePosition)
    document.addEventListener('mouseup', updatePosition)

    return () => {
      document.removeEventListener('selectionchange', updatePosition)
      document.removeEventListener('keyup', updatePosition)
      document.removeEventListener('mouseup', updatePosition)
    }
  }, [updatePosition])

  return position
}

// --- 使用示例 (TypeScript / .tsx) ---
// 下面是一个演示如何在您的应用中使用 `useCaretPosition` Hook 的示例组件。

const App: React.FC = () => {
  // 调用 Hook
  const caretPosition = useCaretPosition()

  // 3. 为 ref 添加明确的元素类型
  const editorRef = useRef<HTMLDivElement>(null)

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 flex flex-col items-center justify-center p-4 font-sans">
      <div className="w-full max-w-3xl mx-auto bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl">
        <h1 className="text-2xl font-bold text-center text-gray-800 dark:text-white mb-4">
          `useCaretPosition` Hook 演示 (TypeScript)
        </h1>
        <p className="text-center text-gray-600 dark:text-gray-300 mb-6">
          在下面的文本框中任意点击或用键盘移动光标，观察指示器和坐标的变化。
        </p>

        {/* 这是一个可编辑区域 */}
        <div
          ref={editorRef}
          contentEditable={true}
          suppressContentEditableWarning={true}
          className="w-full h-64 p-3 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100 overflow-y-auto"
        >
          <p>这是第一段。你可以在这里自由地编辑文本。</p>
          <p>
            尝试在不同的<span className="text-blue-400 font-semibold">位置</span>
            点击，或者使用键盘的
            <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">
              ↑
            </kbd>{' '}
            <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">
              ↓
            </kbd>{' '}
            <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">
              ←
            </kbd>{' '}
            <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">
              →
            </kbd>
            键移动光标。
          </p>
          <p>观察下面的坐标值如何实时更新。</p>
        </div>

        {/* 显示坐标信息 */}
        <div className="mt-4 p-4 bg-gray-200 dark:bg-gray-700 rounded-md text-center">
          <h3 className="font-semibold text-gray-800 dark:text-white">
            当前光标位置 (Caret Position):
          </h3>
          {caretPosition ? (
            <pre className="text-sm text-green-600 dark:text-green-400 font-mono mt-2">
              {JSON.stringify(caretPosition, null, 2)}
            </pre>
          ) : (
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              请在上方框内点击以获取光标位置
            </p>
          )}
        </div>
      </div>

      {/* 视觉指示器：一个红色的小点，会跟随光标移动 */}
      {caretPosition && (
        <div
          className="fixed w-2 h-2 bg-red-500 rounded-full pointer-events-none"
          style={{
            transform: `translate(${caretPosition.left}px, ${caretPosition.top}px)`,
            height: `${caretPosition.height}px`,
            width: '3px',
            backgroundColor: 'rgba(239, 68, 68, 0.7)'
          }}
          title="光标位置指示器"
        />
      )}
    </div>
  )
}

export default App
