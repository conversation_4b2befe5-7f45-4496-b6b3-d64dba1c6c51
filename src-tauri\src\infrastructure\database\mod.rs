// Database Infrastructure - 数据库基础设施
// SQLite 数据库连接和配置

use sqlx::{Pool, Sqlite, SqlitePool};
use std::sync::Arc;

pub mod connection;
pub mod migrations;
pub mod models;
pub mod initializer;
pub mod health_check;
pub mod manager;

// 重新导出数据库类型
pub use connection::*;
pub use migrations::*;
pub use models::*;
pub use initializer::*;
pub use health_check::*;
pub use manager::*;

// 数据库连接池类型
pub type DatabasePool = Arc<Pool<Sqlite>>;

// 数据库配置
#[derive(Debug, Clone)]
pub struct DatabaseConfig {
    pub database_url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connect_timeout: u64,
    pub idle_timeout: u64,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            database_url: "sqlite:data/paolife.db".to_string(),
            max_connections: 10,
            min_connections: 1,
            connect_timeout: 30,
            idle_timeout: 600,
        }
    }
}
