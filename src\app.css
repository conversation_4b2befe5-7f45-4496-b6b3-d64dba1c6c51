@import "tailwindcss";
@import "tw-animate-css";

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    /* 更新为蓝色主色调 */
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;

    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --info: 204 94% 94%;
    --info-foreground: 199 89% 48%;

    --success: 149 80% 90%;
    --success-foreground: 160 84% 39%;

    --warning: 48 96% 89%;
    --warning-foreground: 25 95% 53%;

    --error: 0 93% 94%;
    --error-foreground: 0 84% 60%;

    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;
  }

  .dark,
  [data-kb-theme="dark"] {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    /* 暗色模式的蓝色主色调 */
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --info: 204 94% 94%;
    --info-foreground: 199 89% 48%;

    --success: 149 80% 90%;
    --success-foreground: 160 84% 39%;

    --warning: 48 96% 89%;
    --warning-foreground: 25 95% 53%;

    --error: 0 93% 94%;
    --error-foreground: 0 84% 60%;

    --ring: 212.7 26.8% 83.9%;

    --radius: 0.5rem;
  }
}

/* --- Tailwind v4 tokens mapping --- */
@theme inline {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-info: hsl(var(--info));
  --color-info-foreground: hsl(var(--info-foreground));

  --color-success: hsl(var(--success));
  --color-success-foreground: hsl(var(--success-foreground));

  --color-warning: hsl(var(--warning));
  --color-warning-foreground: hsl(var(--warning-foreground));

  --color-error: hsl(var(--error));
  --color-error-foreground: hsl(var(--error-foreground));

  --color-ring: hsl(var(--ring));
  --radius: var(--radius);
}

/* （可选）让 `.dark` 充当一个变体类 */
@custom-variant dark (&:is(.dark *));

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
  }
}

@layer utilities {
  .step {
    counter-increment: step;
  }

  .step:before {
    @apply absolute w-9 h-9 bg-muted rounded-full font-mono font-medium text-center text-base inline-flex items-center justify-center -indent-px border-4 border-background;
    @apply ml-[-50px] mt-[-4px];
    content: counter(step);
  }
}

@media (max-width: 640px) {
  .container {
    @apply px-4;
  }
}

::-webkit-scrollbar {
  width: 16px;
}

::-webkit-scrollbar-thumb {
  border-radius: 9999px;
  border: 4px solid transparent;
  background-clip: content-box;
  @apply bg-accent;
}

::-webkit-scrollbar-corner {
  display: none;
}
