// Header Component - 头部组件
// 应用程序顶部导航栏

import { JSX, Show, createSignal } from 'solid-js';
import { cn } from '@/lib/utils';
import { Button } from '../ui/button';

export interface HeaderProps {
  title?: string;
  onMenuToggle?: () => void;
  onNotificationClick?: () => void;
  notificationCount?: number;
  onSearch?: (query: string) => void;
  class?: string;
  fullBleed?: boolean;
}

export function Header(props: HeaderProps) {
  const [searchQuery, setSearchQuery] = createSignal('');

  const handleSearchInput = (e: Event) => {
    const target = e.target as HTMLInputElement;
    setSearchQuery(target.value);
  };

  const handleSearchSubmit = (e: Event) => {
    e.preventDefault();
    const query = searchQuery().trim();
    if (query && props.onSearch) {
      props.onSearch(query);
    }
  };

  const handleSearchKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearchSubmit(e);
    }
  };

  return (
    <header class={cn(
      'sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60',
      props.class
    )}>
      <div class={cn(
        "flex h-14 items-center",
        props.fullBleed ? "w-full max-w-none px-0 md:px-2" : "container max-w-screen-2xl"
      )}>
        {/* 左侧：菜单按钮和标题 */}
        <div class="flex items-center space-x-4">
          {/* 菜单切换按钮 */}
          <Button
            variant="ghost"
            size="icon-sm"
            onClick={props.onMenuToggle}
            aria-label="Toggle menu"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="4" x2="20" y1="6" y2="6" />
              <line x1="4" x2="20" y1="12" y2="12" />
              <line x1="4" x2="20" y1="18" y2="18" />
            </svg>
          </Button>

          {/* 应用标题 */}
          <Show when={props.title}>
            <h1 class="text-lg font-semibold">{props.title}</h1>
          </Show>
        </div>

        {/* 中间：搜索框（可选） */}
        <div class="flex-1 flex justify-center px-6">
          <div class="w-full max-w-sm">
            <div class="relative">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="m21 21-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
              <input
                type="search"
                placeholder="搜索项目、任务、资源..."
                value={searchQuery()}
                onInput={handleSearchInput}
                onKeyDown={handleSearchKeyDown}
                class="flex h-9 w-full rounded-md border border-input bg-background pl-8 pr-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
          </div>
        </div>

        {/* 右侧：通知按钮 */}
        <div class="flex items-center space-x-2">
          {/* 通知按钮 */}
          <div class="relative">
            <Button
              variant="ghost"
              size="icon-sm"
              onClick={props.onNotificationClick}
              aria-label="通知"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9" />
                <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0" />
              </svg>
            </Button>
            {/* 通知计数徽章 */}
            <Show when={props.notificationCount && props.notificationCount > 0}>
              <span class="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-destructive text-destructive-foreground text-xs font-medium flex items-center justify-center">
                {props.notificationCount > 99 ? '99+' : props.notificationCount}
              </span>
            </Show>
          </div>
        </div>
      </div>
    </header>
  );
}
