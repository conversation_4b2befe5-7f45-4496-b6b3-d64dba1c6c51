import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import ResourceLinkDialog from './ResourceLinkDialog'
import ResourceViewer from './ResourceViewer'
import { databaseApi, fileSystemApi } from '../../lib/api'
import { useUIStore } from '../../store/uiStore'
import { useConfirmDialog } from '../shared/ConfirmDialog'
import { cn } from '../../lib/utils'
import type { ResourceLink } from '../../../../shared/types'

interface AreaResourcesManagerProps {
  areaId: string
  className?: string
}

export function AreaResourcesManager({ areaId, className }: AreaResourcesManagerProps) {
  const [resources, setResources] = useState<ResourceLink[]>([])
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false)
  const [loading, setLoading] = useState(true)
  const { addNotification } = useUIStore()
  const { confirm: showConfirmDialog, ConfirmDialog } = useConfirmDialog()

  // Load area resources on component mount
  useEffect(() => {
    loadAreaResources()
  }, [areaId])

  const loadAreaResources = async () => {
    try {
      setLoading(true)
      const result = await databaseApi.getAreaResources(areaId)
      if (result.success) {
        setResources(result.data || [])
      } else {
        console.error('Failed to load area resources:', result.error)
        addNotification({
          type: 'error',
          title: 'Failed to load resources',
          message: result.error || 'Unknown error occurred'
        })
      }
    } catch (error) {
      console.error('Error loading area resources:', error)
      addNotification({
        type: 'error',
        title: 'Error loading resources',
        message: 'An unexpected error occurred'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleResourcesLinked = (newResources: ResourceLink[]) => {
    setResources(prev => [...prev, ...newResources])
  }

  const handleUnlinkResource = async (resource: ResourceLink) => {
    const confirmed = await showConfirmDialog({
      title: t('pages.areas.detail.resources.confirmDialogs.unlinkResource.title'),
      message: t('pages.areas.detail.resources.confirmDialogs.unlinkResource.message', {
        name: getResourceDisplayName(resource),
        type: t('common.area')
      }),
      confirmText: t('pages.areas.detail.resources.confirmDialogs.unlinkResource.confirmText'),
      cancelText: t('pages.areas.detail.resources.confirmDialogs.unlinkResource.cancelText'),
      variant: 'destructive'
    })

    if (confirmed) {
      try {
        const result = await databaseApi.unlinkResourceFromArea(resource.id, areaId)
        if (result.success) {
          setResources(prev => prev.filter(r => r.id !== resource.id))
          addNotification({
            type: 'success',
            title: t('pages.areas.detail.resources.notifications.unlinkSuccess'),
            message: t('pages.areas.detail.resources.notifications.unlinkSuccessMessage', {
              name: getResourceDisplayName(resource),
              type: t('common.area')
            })
          })
        } else {
          throw new Error(result.error || t('pages.areas.detail.resources.notifications.unlinkError'))
        }
      } catch (error) {
        console.error('Failed to unlink resource:', error)
        addNotification({
          type: 'error',
          title: t('pages.areas.detail.resources.notifications.unlinkError'),
          message: error instanceof Error ? error.message : 'Unknown error occurred'
        })
      }
    }
  }

  const [selectedResource, setSelectedResource] = useState<ResourceLink | null>(null)
  const [isResourceViewerOpen, setIsResourceViewerOpen] = useState(false)

  const handleOpenResource = async (resource: ResourceLink) => {
    try {
      // Check if file exists
      const existsResult = await fileSystemApi.fileExists(resource.resourcePath)

      if (!existsResult.success || !existsResult.data) {
        addNotification({
          type: 'error',
          title: 'File not found',
          message: `The resource file could not be found at: ${resource.resourcePath}`
        })
        return
      }

      // Open resource in modal viewer
      setSelectedResource(resource)
      setIsResourceViewerOpen(true)

    } catch (error) {
      console.error('Failed to open resource:', error)
      addNotification({
        type: 'error',
        title: 'Failed to open resource',
        message: 'An unexpected error occurred'
      })
    }
  }

  const getResourceDisplayName = (resource: ResourceLink) => {
    if (resource.title) {
      return resource.title
    }
    const fileName = resource.resourcePath.split(/[/\\]/).pop() || 'Unknown'
    return fileName.replace(/\.md$/, '')
  }

  const getResourcePath = (resource: ResourceLink) => {
    const pathParts = resource.resourcePath.split(/[/\\]/)
    if (pathParts.length > 3) {
      return `.../${pathParts.slice(-3).join('/')}`
    }
    return resource.resourcePath
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>关联资源</CardTitle>
          <CardDescription>Loading resources...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                关联资源
                {resources.length > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {resources.length}
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                与该领域相关的核心知识和参考资料
              </CardDescription>
            </div>
            <Button size="sm" onClick={() => setIsLinkDialogOpen(true)}>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                />
              </svg>
              关联资源
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          {resources.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <div className="text-4xl mb-2">🔗</div>
              <p className="text-sm">尚未关联任何资源</p>
              <p className="text-xs mt-1">从知识库中关联资源到该领域</p>
            </div>
          ) : (
            <div className="space-y-3">
              {resources.map((resource) => (
                <div
                  key={resource.id}
                  className="group flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors"
                >
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                      </div>
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm truncate">
                          {getResourceDisplayName(resource)}
                        </h4>
                        <Badge variant="outline" className="text-xs">
                          .md
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground truncate">
                        {getResourcePath(resource)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleOpenResource(resource)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                        />
                      </svg>
                    </Button>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 5v.01M12 12v.01M12 19v.01"
                            />
                          </svg>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleOpenResource(resource)}>
                          打开资源
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleUnlinkResource(resource)}
                          className="text-red-600 focus:text-red-600"
                        >
                          取消关联
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Resource Link Dialog */}
      <ResourceLinkDialog
        isOpen={isLinkDialogOpen}
        onClose={() => setIsLinkDialogOpen(false)}
        areaId={areaId}
        onResourcesLinked={handleResourcesLinked}
      />

      {/* Resource Viewer */}
      <ResourceViewer
        resource={selectedResource}
        isOpen={isResourceViewerOpen}
        onClose={() => {
          setIsResourceViewerOpen(false)
          setSelectedResource(null)
        }}
      />

      {/* Confirm Dialog */}
      <ConfirmDialog />
    </>
  )
}

export default AreaResourcesManager
