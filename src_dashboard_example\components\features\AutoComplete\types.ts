/**
 * 智能补全系统类型定义
 */

export interface CompletionItem {
  /** 唯一标识符 */
  id: string
  /** 显示文本 */
  label: string
  /** 插入文本 */
  insertText: string
  /** 补全类型 */
  type: 'emoji' | 'mention' | 'hashtag' | 'wikilink'
  /** 描述信息 */
  description?: string
  /** 图标或表情符号 */
  icon?: string
  /** 额外数据 */
  data?: any
}

export interface CompletionContext {
  /** 触发字符 */
  trigger: string
  /** 查询文本 */
  query: string
  /** 光标位置 */
  position: {
    from: number
    to: number
  }
  /** 编辑器视图 */
  view: any
}

export interface CompletionProvider {
  /** 触发字符 */
  trigger: string
  /** 获取补全建议 */
  getCompletions: (context: CompletionContext) => Promise<CompletionItem[]>
  /** 应用补全 */
  applyCompletion: (item: CompletionItem, context: CompletionContext) => void
}

export interface CompletionConfig {
  /** 是否启用表情补全 */
  emoji: boolean
  /** 是否启用@用户补全 */
  mention: boolean
  /** 是否启用#话题补全 */
  hashtag: boolean
  /** 是否启用[[文件]]补全 */
  wikilink: boolean
  /** 最大建议数量 */
  maxSuggestions: number
  /** 触发延迟（毫秒） */
  debounceDelay: number
}

export interface CompletionUIProps {
  /** 补全建议列表 */
  items: CompletionItem[]
  /** 当前选中索引 */
  selectedIndex: number
  /** 位置信息 */
  position: {
    x: number
    y: number
  }
  /** 是否可见 */
  visible: boolean
  /** 选择回调 */
  onSelect: (item: CompletionItem) => void
  /** 关闭回调 */
  onClose: () => void
}
