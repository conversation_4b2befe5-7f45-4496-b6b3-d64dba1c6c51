import { createContext, useContext, useState, ReactNode } from 'react'

import { reviewTypeLabels, reviewStatusLabels } from '../../../shared/i18n/reviews'

// 支持的语言类型
export type Language = 'zh' | 'en'

// 语言上下文类型
interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string, params?: Record<string, string | number>) => string
}

// 创建语言上下文
const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

// 翻译数据类型
type TranslationData = Record<string, any>

// 中文翻译
const zhTranslations: TranslationData = {
  // 导航
  nav: {
    dashboard: '仪表盘',
    inbox: '收件箱',
    projects: '项目',
    areas: '领域',
    resources: '资源',
    archive: '归档',
    reviews: '回顾',
    settings: '设置'
  },

  // 分类标签
  categories: {
    main: '主要功能',
    para: 'P.A.R.A. 方法',
    tools: '工具'
  },

  // 通用
  common: {
    search: '搜索',
    filter: '筛选',
    sort: '排序',
    create: '创建',
    edit: '编辑',
    delete: '删除',
    save: '保存',
    cancel: '取消',
    confirm: '确认',
    loading: '加载中...',
    noData: '暂无数据',
    all: '全部',
    active: '活跃',
    completed: '已完成',
    pending: '待处理',
    inProgress: '进行中',
    paused: '已暂停',
    archived: '已归档',
    archive: '归档',
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息',
    templates: '模板',
    close: '关闭',
    done: '完成',
    custom: '自定义',
    customUnit: '自定义单位',
    noUnit: '无单位',
    name: '名称',
    enterName: '输入KPI名称',
    currentValue: '当前值',
    targetValue: '目标值',
    unit: '单位',
    selectUnit: '选择单位',
    direction: '方向',
    increase: '增长',
    decrease: '减少',
    higherIsBetter: '越高越好',
    lowerIsBetter: '越低越好',
    trackingFrequency: '跟踪频率',
    daily: '每日',
    weekly: '每周',
    monthly: '每月',
    quarterly: '每季度',
    optional: '可选',
    creating: '创建中...',
    updating: '更新中...',
    createKPI: '创建KPI',
    updateKPI: '更新KPI',
    nameRequired: '名称为必填项',
    nameTooLong: '名称不能超过 {max} 个字符',
    valueRequired: '数值为必填项',
    valueInvalid: '数值必须是有效数字',
    valueNegative: '数值不能为负数',
    targetRequired: '目标值为必填项',
    targetInvalid: '目标值必须是有效数字',
    targetNegative: '目标值不能为负数',
    saving: '保存中...',
    more: '更多',
    unknown: '未知',
    project: '项目',
    area: '领域'
  },
  // 仪表盘
  dashboard: {
    title: '仪表盘',
    description: '您的生产力系统概览和当前进度',
    quickCapture: '快速记录',
    todayTasks: '今日任务',
    completedTasks: '已完成 {completed} / {total} 个任务',
    upcomingProjects: '即将到期',
    recentActivity: '最近活动',
    activeProjects: '活跃项目',
    totalAreas: '责任领域',
    totalResources: '参考资源',
    inboxItems: '收件箱',
    currentlyActive: '当前进行中',
    ongoingResponsibilities: '持续维护中',
    savedForReference: '已保存备用',
    needsProcessing: '待处理事项',
    inboxProcessing: '收件箱处理',
    processInbox: '处理收件箱',
    // 快速操作
    quickActions: '快速操作',
    quickActionsDescription: '快速访问主要功能',
    // 统计卡片
    responsibilityAreas: '责任领域',
    referenceResources: '参考资源',
    // 主要内容区域
    todayTasksDescription: '今天需要完成的任务和待办事项',
    upcomingProjectsDescription: '近期需要关注的项目和截止日期',
    recentActivityDescription: '您在各个领域的最新更新和进展',
    // 每周进度
    weeklyProgress: '每周进度',
    weeklyProgressDescription: '本周生产力指标',
    taskCompletion: '任务完成度',
    projectProgress: '项目进度'
  },

  // 快速记录
  quickCapture: {
    title: '快速记录',
    description: '即时记录想法、任务和创意',
    placeholder: '你在想什么？({type})',
    hint: '按回车键记录，或使用 ⌘+Enter 快速保存',
    saving: '保存中...',
    capture: '记录',
    types: {
      note: '笔记',
      task: '任务',
      idea: '想法'
    }
  },

  // 设置
  settings: {
    title: '设置',
    description: '配置您的泡生活应用偏好和系统设置',
    general: '常规',
    generalDescription: '基本应用设置和偏好',
    displayName: '显示名称',
    theme: '主题',
    language: '语言',
    selectTheme: '选择主题',
    selectLanguage: '选择语言',
    themes: {
      light: '浅色',
      dark: '深色',
      system: '跟随系统'
    },
    languages: {
      zh: '中文',
      en: 'English'
    },
    save: '保存',
    confirmReset: '确定要重置所有设置吗？此操作不可撤销。',
    // 编辑器设置
    editor: {
      title: '编辑器',
      description: '配置Markdown编辑器的行为和外观',
      mode: '编辑模式',
      selectMode: '选择编辑模式',
      modes: {
        ir: '即时渲染',
        wysiwyg: '所见即所得'
      },
      theme: '编辑器主题',
      selectTheme: '选择编辑器主题',
      themes: {
        dark: '深色',
        classic: '经典'
      },
      focusMode: '专注模式',
      focusModeDescription: '隐藏干扰元素，专注于写作',
      autoSave: '自动保存',
      autoSaveDescription: '编辑时自动保存文档',
      autoSaveInterval: '自动保存间隔',
      autoSaveIntervalDescription: '自动保存的时间间隔',
      seconds: '秒'
    },
    // 通知消息
    notifications: {
      languageChanged: '语言已更改',
      languageChangedMessage: '界面语言已成功更改',
      themeChanged: '主题已更改',
      themeChangedMessage: '应用主题已成功更改',
      usernameSaved: '用户名已保存',
      usernameSavedMessage: '显示名称已成功更新',
      settingsReset: '设置已重置',
      settingsResetMessage: '所有设置已恢复为默认值',
      featureNotImplemented: '功能未实现',
      clearCacheNotImplemented: '清除缓存功能正在开发中',
      resetDataNotImplemented: '重置数据功能正在开发中',
      exportSuccess: '导出成功',
      exportSuccessMessage: '设置数据已成功导出',
      exportFailed: '导出失败',
      exportFailedMessage: '导出设置数据时发生错误',
      importSuccess: '导入成功',
      importSuccessMessage: '设置数据已成功导入',
      importFailed: '导入失败',
      importFailedMessage: '导入设置数据时发生错误，请检查文件格式',
      backupNotImplemented: '备份功能正在开发中',
      backupSuccess: '备份成功',
      backupSuccessMessage: '数据库备份已成功创建',
      backupFailed: '备份失败',
      backupFailedMessage: '创建数据库备份时发生错误',
      shortcutsNotImplemented: '快捷键管理功能正在开发中',
      resourcePathNotImplemented: '资源库路径设置功能正在开发中',
      resourcePathSuccess: '资源库路径已更新',
      resourcePathSuccessMessage: '资源库路径已成功更改',
      resourcePathFailed: '路径更新失败',
      resourcePathFailedMessage: '更新资源库路径时发生错误'
    },
    // 应用设置
    app: {
      exitConfirm: '退出确认',
      exitConfirmDescription: '关闭应用时显示确认对话框'
    },

    // 快捷键设置
    shortcuts: {
      title: '快捷键设置',
      description: '自定义应用程序的键盘快捷键',
      instructions: '点击"编辑"按钮，然后按下您想要的键组合',
      recording: '录制中...',
      edit: '编辑',
      cancel: '取消',
      reset: '重置',
      resetAll: '重置全部',
      conflictTitle: '快捷键冲突',
      conflictMessage: '该键组合已被其他功能使用',
      updateSuccess: '快捷键已更新',
      updateSuccessMessage: '快捷键设置已成功保存',
      resetSuccess: '快捷键已重置',
      resetSuccessMessage: '快捷键已恢复为默认设置',
      resetAllSuccess: '所有快捷键已重置',
      resetAllSuccessMessage: '所有快捷键已恢复为默认设置',
      categories: {
        navigation: '导航',
        editing: '编辑',
        project: '项目',
        inbox: '收件箱',
        general: '通用'
      },
      helpTitle: '快捷键帮助',
      helpDescription: '查看所有可用的键盘快捷键',
      tips: '使用技巧',
      tip1: '在输入框中时快捷键会被禁用',
      tip2: '可以在设置中自定义快捷键',
      tip3: '按Escape键可以关闭大多数对话框'
    },
    // P.A.R.A. 方法设置
    para: {
      title: 'P.A.R.A. 方法',
      description: '配置您的 P.A.R.A. 方法偏好',
      badge: '核心',
      autoArchive: {
        title: '自动归档已完成项目',
        description: '在指定天数后自动将已完成项目移至归档',
        unit: '天'
      },
      weeklyReview: {
        title: '每周回顾提醒',
        description: '收到执行每周回顾的通知',
        days: {
          sunday: '周日',
          monday: '周一',
          friday: '周五'
        }
      },
      projectTemplate: {
        title: '默认项目模板',
        description: '创建新项目时使用的模板',
        options: {
          basic: '基础',
          detailed: '详细',
          agile: '敏捷'
        }
      }
    },
    // 数据存储设置
    dataStorage: {
      title: '数据存储',
      description: '管理您的数据、备份和存储设置',
      stats: {
        projects: '项目数量',
        areas: '领域数量',
        totalItems: '总项目数',
        backups: '备份数'
      },
      actions: {
        exportData: '导出数据',
        importData: '导入数据',
        createBackup: '创建备份'
      }
    },
    // 高级设置
    advanced: {
      title: '高级',
      description: '快捷键、路径和其他高级功能配置',
      shortcuts: {
        title: '快捷键管理',
        description: '自定义应用快捷键和键盘操作',
        configure: '配置快捷键'
      },
      workspaceDirectory: {
        title: '工作目录',
        description: '应用数据和资源的存储位置',
        readonly: '只读',
        notSet: '未设置'
      },
      actions: {
        resetSettings: '重置设置'
      }
    },
    // 关于信息
    about: {
      title: '关于 PaoLife',
      description: '应用程序信息和致谢',
      info: {
        version: '版本：',
        build: '构建：',
        electron: 'Electron：',
        nodejs: 'Node.js：',
        license: '许可证：'
      }
    },
    // 状态标签
    status: {
      enabled: '已启用',
      disabled: '已禁用'
    }
  },

  // 首次设置
  firstTimeSetup: {
    title: '欢迎使用 PaoLife',
    description: '让我们进行一些基本设置来开始使用',
    step1: {
      title: '步骤 1: 设置用户名',
      description: '请输入您的用户名，这将用于个性化您的体验',
      usernameLabel: '用户名',
      usernamePlaceholder: '请输入您的用户名',
      nextButton: '下一步'
    },
    step2: {
      title: '步骤 2: 选择工作目录',
      description: '选择一个目录来存储您的笔记和文件',
      directoryLabel: '工作目录',
      directoryPlaceholder: '输入目录路径或点击浏览...',
      browseButton: '浏览',
      browsing: '选择中...',
      useDefault: '使用默认目录',
      clear: '清空',
      selected: '已选择: {path}',
      previousButton: '上一步',
      completeButton: '完成设置'
    },
    notifications: {
      setupComplete: '设置完成',
      welcomeMessage: '欢迎使用 PaoLife，{username}！',
      setupFailed: '设置失败',
      initializationError: '无法初始化工作目录，请重试',
      useDefaultDirectory: '使用默认目录',
      browserEnvironmentMessage: '浏览器环境下使用默认工作目录: {directory}',
      directoryPickerFailed: '无法打开目录选择器，使用默认目录: {directory}',
      usernameRequired: '请输入用户名',
      usernameEmpty: '用户名不能为空',
      directoryRequired: '请选择工作目录',
      directoryNeeded: '需要选择一个目录来存储您的文件',
      fileSystemInitFailed: '文件系统初始化失败'
    },
    dialog: {
      selectDirectoryTitle: '选择工作目录',
      selectDirectoryButton: '选择此目录'
    }
  },

  // 页面翻译
  pages: {
    projects: {
      title: '项目',
      description: '具有明确截止日期和完成标准的成果。',
      newProject: '新建项目',
      searchPlaceholder: '搜索项目...',
      allAreas: '所有领域',
      allStatus: '所有状态',
      sortBy: '排序方式',
      viewMode: '视图模式',
      grid: '网格',
      list: '列表',
      // 通知消息
      notifications: {
        createSuccess: '项目创建成功',
        createSuccessMessage: '项目 "{name}" 已创建',
        createFailed: '创建项目失败',
        createFailedMessage: '创建项目时发生未知错误',
        deleteConfirmTitle: '删除项目',
        deleteConfirmMessage: '确定要删除"{name}"吗？此操作无法撤销。',
        deleteSuccess: '项目删除成功',
        deleteSuccessMessage: '项目 "{name}" 已删除',
        deleteFailed: '项目删除失败',
        deleteFailedMessage: '删除项目时发生未知错误',
        archiveConfirmTitle: '归档项目',
        archiveConfirmMessage: '归档"{name}"？您可以稍后从归档中恢复。',
        archiveSuccess: '项目已归档',
        archiveSuccessMessage: '"{name}" 已成功归档，您可以在归档页面中找到它。',
        archiveFailed: '归档失败',
        archiveFailedMessage: '归档项目时发生错误，请重试。'
      },
      kanban: '看板',
      noProjects: '暂无项目',
      createFirst: '创建您的第一个项目',
      filters: {
        status: {
          notStarted: '未开始',
          inProgress: '进行中',
          atRisk: '有风险',
          paused: '已暂停',
          completed: '已完成'
        },
        sort: {
          updated: '最近更新',
          name: '名称',
          deadline: '截止日期',
          progress: '进度'
        }
      },
      detail: {
        // 导航
        navigation: {
          backToProjects: '← 返回项目',
          backToArea: '← 返回 {areaName}',
          backToAreaDefault: '← 返回领域'
        },
        // 错误页面
        notFound: {
          title: '项目未找到',
          description: '您要查找的项目不存在或已被删除。',
          backButton: '返回项目'
        },
        // 归档横幅
        archivedBanner: {
          title: '您正在查看一个已归档的项目',
          description: '此项目处于只读状态，您可以查看所有历史信息，但无法进行编辑操作。'
        },
        // 项目信息标签
        projectInfo: {
          description: '项目描述',
          goal: '项目目标',
          deliverable: '最终交付物'
        },
        // 进度统计
        progress: {
          overall: '整体进度',
          totalTasks: '总任务',
          completed: '已完成',
          overdue: '已逾期',
          dueSoon: '即将到期'
        },
        // 倒计时
        countdown: {
          title: '倒计时',
          daysOverdue: '天已逾期',
          dueToday: '今天到期',
          dayLeft: '天剩余',
          daysLeft: '天剩余'
        },
        // 日期标签
        dates: {
          created: '创建时间',
          deadline: '截止日期',
          updated: '更新时间',
          area: '所属领域'
        },
        // 操作按钮
        actions: {
          title: '操作',
          editProject: '编辑项目',
          archiveProject: '归档项目',
          deleteProject: '删除项目'
        },
        // 任务管理
        tasks: {
          title: '项目任务',
          description: '管理此项目的任务和子任务',
          hideFilters: '隐藏筛选器',
          showFilters: '显示筛选器',
          addTask: '添加任务',
          noTasks: '暂无任务',
          noTasksHint: '添加您的第一个任务开始工作',
          // 任务详情面板
          taskDetails: '任务详情',
          taskTitle: '任务标题',
          enterTaskTitle: '输入任务标题...',
          taskDescription: '描述',
          addTaskDescription: '添加任务描述...',
          priority: '优先级',
          priorityNone: '无',
          priorityLow: '低',
          priorityMedium: '中',
          priorityHigh: '高',
          priorityCritical: '紧急',
          status: '状态',
          statusTodo: '待办',
          statusInProgress: '进行中',
          statusBlocked: '阻塞',
          statusReview: '审核',
          statusDone: '完成',
          deadline: '截止日期',
          estimatedHours: '预估时间',
          actualHours: '实际时间',
          project: '项目',
          noProject: '无项目',
          area: '领域',
          noArea: '无领域',
          blockedBy: '阻塞原因',
          describeBlocking: '描述阻塞此任务的原因...',
          subtask: '子任务',
          // 任务筛选器
          filter: {
            searchPlaceholder: '按名称或描述搜索任务...',
            filters: '筛选器',
            filterOptions: '筛选选项',
            allStatus: '所有状态',
            allPriority: '所有优先级',
            createdDate: '创建日期',
            name: '名称',
            dueDate: '截止日期',
            progress: '进度',
            allDates: '所有日期',
            overdue: '已逾期',
            dueToday: '今天到期',
            dueThisWeek: '本周到期',
            noDeadline: '无截止日期',
            showCompleted: '显示已完成任务',
            clearAll: '清除所有筛选器',
            sort: '排序',
            sortBy: '排序方式',
            sortAscending: '升序排列',
            sortDescending: '降序排列'
          },
          // 层级控制
          hierarchyControls: '层级控制',
          hierarchyStats: '{tasks} 个任务，{levels} 层',
          expandAll: '展开全部',
          collapseAll: '折叠全部',
          level1: '层级 1',
          level2: '层级 2',
          // 任务条标签
          overdue: '已逾期',
          due: '截止',
          est: '预估',
          addSubtask: '添加子任务',
          subtasks: '子任务',
          subtaskCount: '个子任务',
          // 层级控制统计
          completion: '完成度',
          expanded: '已展开',
          maxDepth: '最大深度'
        },
        // 笔记功能
        notes: {
          title: '项目笔记',
          edit: '编辑',
          cancel: '取消',
          save: '保存笔记',
          placeholder: '添加项目笔记、会议记录或重要信息...',
          empty: '暂无笔记。点击编辑添加项目笔记。'
        },
        // 快速操作
        quickActions: {
          title: '快速操作',
          addTask: '添加任务',
          linkResource: '关联资源',
          viewAnalytics: '查看分析'
        },
        // 确认对话框
        confirmDialogs: {
          deleteProject: {
            title: '删除项目',
            description: '确定要删除"{name}"吗？此操作无法撤销。',
            confirm: '删除',
            cancel: '取消'
          },
          archiveProject: {
            title: '归档项目',
            description: '归档"{name}"？您可以稍后从归档中恢复。',
            confirm: '归档',
            cancel: '取消'
          },
          batchCompleteTask: {
            title: '批量完成任务',
            description: '是否将此任务及其所有 {count} 个子任务都标记为完成状态？',
            confirm: '是',
            cancel: '否'
          }
        },
        // 通知消息
        notifications: {
          taskCompleted: '任务已完成',
          taskCompletedMessage: '成功完成 {count} 个任务',
          updateFailed: '更新失败',
          updateFailedMessage: '部分任务状态更新失败，请重试',
          operationFailed: '操作失败',
          operationFailedMessage: '任务状态更新失败，请重试'
        },
        // 创建任务对话框
        createTaskDialog: {
          title: {
            create: '创建新任务',
            edit: '编辑任务',
            createSubtask: '创建子任务'
          },
          description: {
            create: '创建新任务来跟踪您的工作。',
            createSubtask: '在选定任务下创建子任务。'
          },
          fields: {
            content: '任务内容',
            contentRequired: '任务内容 *',
            contentPlaceholder: '需要做什么？',
            description: '描述',
            descriptionPlaceholder: '关于此任务的其他详细信息...',
            priority: '优先级',
            priorityPlaceholder: '选择优先级...',
            deadline: '截止日期',
            project: '项目',
            projectPlaceholder: '选择项目...',
            noProject: '无项目',
            area: '领域',
            areaPlaceholder: '选择领域...',
            noArea: '无领域',
            repeat: '重复',
            repeatPlaceholder: '选择重复模式...',
            inheritedFromParent: '继承自父任务'
          },
          buttons: {
            cancel: '取消',
            create: '创建任务',
            update: '更新任务',
            creating: '创建中...',
            updating: '更新中...'
          },
          priority: {
            none: '无',
            low: '低',
            medium: '中',
            high: '高',
            urgent: '紧急'
          },
          repeat: {
            none: '不重复',
            daily: '每日',
            weekly: '每周',
            monthly: '每月'
          }
        },
        // 项目交付物
        deliverables: {
          title: '项目交付物',
          description: '跟踪和管理项目成果和交付物',
          addDeliverable: '添加交付物',
          noDeliverables: '暂无交付物',
          noDeliverablesHint: '添加您的第一个交付物开始跟踪项目成果',
          // 创建交付物弹窗
          createDialog: {
            title: '创建新交付物',
            description: '定义项目成果或交付物以跟踪进度和完成情况。',
            titleLabel: '标题',
            titleRequired: '标题 *',
            titlePlaceholder: '例如：用户手册文档、MVP发布、性能指标',
            descriptionLabel: '描述',
            descriptionPlaceholder: '详细描述需要交付的内容...',
            typeLabel: '类型',
            statusLabel: '状态',
            priorityLabel: '优先级',
            plannedDateLabel: '计划日期',
            deadlineLabel: '截止日期',
            contentLabel: '附加详情',
            contentPlaceholder: '验收标准、需求、备注...',
            cancel: '取消',
            create: '创建交付物',
            creating: '创建中...'
          },
          status: {
            planned: '计划中',
            notStarted: '未开始',
            inProgress: '进行中',
            review: '审核中',
            completed: '已完成',
            onHold: '暂停',
            cancelled: '已取消',
            delivered: '已交付',
            accepted: '已接受'
          },
          statusDescriptions: {
            planned: '尚未开始',
            inProgress: '正在进行中',
            review: '审核或测试中',
            completed: '工作完成，准备交付',
            cancelled: '已取消或不再需要'
          },
          priority: {
            low: '低',
            medium: '中',
            high: '高',
            critical: '紧急'
          },
          type: {
            document: '文档',
            software: '软件',
            report: '报告',
            presentation: '演示',
            prototype: '原型',
            feature: '功能',
            milestone: '里程碑',
            deliverable: '交付物',
            other: '其他'
          },
          typeDescriptions: {
            document: '书面交付物，如报告、规范',
            software: '软件应用程序或系统',
            report: '分析报告和文档',
            presentation: '演示和展示',
            prototype: '原型和概念验证',
            other: '其他类型的交付物'
          },
          actions: {
            edit: '编辑',
            delete: '删除',
            markComplete: '标记完成',
            markInProgress: '标记进行中'
          },
          due: '截止',
          completed: '完成于',
          created: '创建于'
        },
        // 关联资源
        resources: {
          title: '关联资源',
          description: '管理与此项目相关的资源和文档',
          linkResource: '关联资源',
          noResources: '暂无关联资源',
          noResourcesHint: '关联资源以便快速访问项目相关文档',
          // 关联资源弹窗
          linkDialog: {
            titleProject: '关联资源到项目',
            titleArea: '关联资源到领域',
            descriptionProject: '从您的知识库中选择资源关联到此项目。关联的资源将出现在项目详情中并创建双向连接。',
            descriptionArea: '从您的知识库中选择资源关联到此领域。关联的资源将出现在领域详情中并创建双向连接。',
            searchPlaceholder: '搜索资源...',
            noResourcesFound: '未找到资源',
            noResourcesFoundHint: '尝试调整搜索条件或检查您的知识库',
            selectedCount: '已选择 {count} 个资源',
            linkSelected: '关联选中的资源',
            linking: '关联中...',
            cancel: '取消',
            showFileSystem: '显示文件系统资源',
            hideFileSystem: '隐藏文件系统资源',
            fileSystemResources: '文件系统资源',
            databaseResources: '数据库资源',
            selectAll: '全选 ({count} 个{files})',
            file: '文件',
            files: '文件'
          },
          actions: {
            open: '打开',
            unlink: '取消关联'
          },
          confirmDialogs: {
            unlinkResource: {
              title: '取消关联资源',
              message: '确定要取消关联资源"{name}"吗？',
              confirmText: '取消关联',
              cancelText: '取消'
            }
          },
          linkedOn: '关联于',
          notifications: {
            linkSuccess: '资源关联成功',
            linkSuccessMessage: '{count} 个资源已关联到{type}',
            linkError: '关联资源失败',
            unlinkSuccess: '取消关联成功',
            unlinkSuccessMessage: '已取消关联资源"{name}"',
            unlinkError: '取消关联失败',
            fileNotFound: '文件未找到',
            fileNotFoundMessage: '无法在以下路径找到资源文件：{path}'
          }
        },
        projectKPI: {
          title: '项目KPI',
          description: '跟踪和管理项目关键绩效指标',
          batchRecord: '批量记录',
          addKPI: '添加KPI',
          noKPIs: '暂无KPI',
          noKPIsDescription: '开始创建KPI来跟踪项目进度',
          createFirst: '创建第一个KPI',
          createFirstKPI: '创建第一个KPI来开始跟踪',
          noKPIsToTrack: '暂无KPI可跟踪',
          createKPIsFirst: '请先创建一些KPI',
          loadError: '加载KPI失败',
          updateKPI: '更新 {name}',
          recordNewValue: '记录新的数值',
          newValue: '新数值',
          currentValue: '当前值: {value}',
          noteOptional: '备注（可选）',
          addNote: '添加备注...',
          record: '记录',
          recording: '记录中...',
          editKPI: '编辑KPI',
          showHistory: '显示历史',
          hideHistory: '隐藏历史',
          progress: '进度',
          updateSuccess: '{name} 更新成功',
          updateError: '更新KPI失败',
          deleteSuccess: '删除成功',
          deleteError: '删除失败',
          noHistoryRecords: '暂无历史记录',
          batchRecordTitle: '批量记录KPI',
          batchRecordDescription: '为所有KPI批量记录数值',
          globalNote: '全局备注',
          globalNotePlaceholder: '为所有KPI添加统一备注...',
          recentHistory: '历史趋势',
          applyToAll: '应用到所有KPI',
          // 批量记录相关
          batch: {
            selectAtLeastOne: '请至少选择一个KPI进行记录',
            recordSuccess: '成功记录了 {count} 个KPI数值',
            recordPartialSuccess: '创建了 {success} 个记录，{failed} 个失败',
            recordError: '批量记录失败',
            recording: '记录中...',
            recordValues: '记录数值 ({count})',
            recordingTime: '记录时间',
            kpiRecords: 'KPI记录 ({count} 个已启用)',
            enableAll: '启用全部',
            disableAll: '禁用全部',
            current: '当前值',
            newValue: '新数值',
            note: '备注',
            optionalNote: '可选备注',
            enterValue: '输入数值'
          },
          // KPI统计卡片
          stats: {
            totalKPIs: '总KPI数',
            onTrack: '正常进行',
            atRisk: '有风险',
            avgProgress: '平均进度'
          }
        }
      }
    },
    areas: {
      title: '领域',
      description: '需要长期维护的持续责任和标准。',
      newArea: '新建领域',
      searchPlaceholder: '搜索领域...',
      allStatus: '所有状态',
      noAreas: '暂无领域',
      createFirst: '创建您的第一个领域',
      filters: {
        status: {
          active: '活跃',
          needsAttention: '需要关注',
          onHold: '暂停',
          reviewRequired: '需要审核'
        },
        sort: {
          updated: '最近更新',
          name: '名称',
          status: '状态'
        }
      },
      detail: {
        backToAreas: '← 返回领域',
        actions: '操作',
        editArea: '编辑领域',
        archiveArea: '归档领域',
        deleteArea: '删除领域',
        notFound: '领域未找到',
        notFoundDescription: '您要查找的领域不存在或已被删除。',
        backToAreasButton: '返回领域',
        areaStatus: '领域状态',
        standardsChecklist: '标准清单',
        completed: '已完成',
        activeHabits: '活跃习惯',
        areaStandard: '领域标准',
        areaStandardDescription: '您希望在此领域保持的水平',
        standardsChecklistDescription: '跟踪您在领域标准方面的进度',
        addItem: '添加项目',
        noChecklistItems: '暂无清单项目',
        addChecklistHint: '添加项目来跟踪您的领域标准',
        areaNotes: '领域笔记',
        edit: '编辑',
        // 新增翻译键
        loading: '加载领域数据...',
        loadingFailed: '加载失败',
        loadingFailedMessage: '加载领域数据时发生错误。',
        deleteConfirmTitle: '删除领域',
        deleteConfirmMessage: '确定要删除"{name}"吗？此操作无法撤销。',
        archiveConfirmTitle: '归档领域',
        archiveConfirmMessage: '归档"{name}"？您可以稍后从归档中恢复。',
        archiveSuccessTitle: '领域已归档',
        archiveSuccessMessage: '"{name}" 已成功归档，您可以在归档页面中找到它。',
        archiveFailedTitle: '归档失败',
        archiveFailedMessage: '归档领域时发生错误，请重试。',
        projectCreateSuccessTitle: '项目创建成功',
        projectCreateSuccessMessage: '项目 "{name}" 已创建并关联到当前领域',
        projectCreateFailedTitle: '项目创建失败',
        projectCreateFailedMessage: '创建项目失败',
        habitDeleteConfirmTitle: '删除习惯',
        habitDeleteConfirmMessage: '确定要删除这个习惯吗？此操作无法撤销。',
        // 界面文本
        returnToAreasList: '返回领域列表',
        reviewFrequency: '审查频率:',
        createdAt: '创建于:',
        updatedAt: '更新于:',
        weekly: '每周',
        monthly: '每月',
        quarterly: '每季度',
        statusActive: '活跃',
        statusNeedsAttention: '需要关注',
        statusOnHold: '暂停',
        statusReviewRequired: '需要审查',
        archivedBannerTitle: '您正在查看一个已归档的领域',
        archivedBannerMessage: '此领域处于只读状态，您可以查看所有历史信息，但无法进行编辑操作。',
        standardPlaceholder: '例如：保持每月储蓄率不低于30%，并按时还清所有账单。',
        save: '保存',
        cancel: '取消',
        // 布局区域
        executionArea: '执行区',
        planningResourceArea: '规划与资源区',
        keyMetrics: '关键指标',
        recurringTasks: '定期维护任务',
        activeChecklists: '进行中的清单',
        relatedProjects: '关联项目',
        relatedResources: '关联资源',
        checklistTemplates: '清单模板库',
        // 关联资源
        resources: {
          title: '关联资源',
          description: '管理与此领域相关的资源和文档',
          linkResource: '关联资源',
          noResources: '暂无关联资源',
          noResourcesHint: '关联资源以便快速访问领域相关文档',
          actions: {
            open: '打开',
            unlink: '取消关联'
          },
          confirmDialogs: {
            unlinkResource: {
              title: '取消关联资源',
              message: '确定要取消关联资源"{name}"吗？',
              confirmText: '取消关联',
              cancelText: '取消'
            }
          },
          linkedOn: '关联于',
          notifications: {
            linkSuccess: '资源关联成功',
            linkSuccessMessage: '{count} 个资源已关联到{type}',
            linkError: '关联资源失败',
            unlinkSuccess: '取消关联成功',
            unlinkSuccessMessage: '已取消关联资源"{name}"',
            unlinkError: '取消关联失败',
            fileNotFound: '文件未找到',
            fileNotFoundMessage: '无法在以下路径找到资源文件：{path}'
          }
        },
        saveNotes: '保存笔记',
        notesPlaceholder: '添加领域笔记、反思或重要信息...',
        noNotesYet: '暂无笔记。点击编辑添加领域笔记。',
        areaHabits: '领域习惯',
        areaHabitsDescription: '跟踪与此领域相关的日常习惯',
        addHabit: '添加习惯',
        noHabitsYet: '暂无习惯',
        addFirstHabit: '添加您的第一个习惯开始跟踪',
        keyInformation: '关键信息',
        created: '创建时间',
        lastUpdated: '最后更新',
        quickActions: '快速操作',
        linkProject: '关联项目',
        viewAnalytics: '查看分析',
        relatedProjectsDescription: '与此领域关联的项目',
        confirmDeleteArea: '确定要删除"{name}"吗？此操作无法撤销。',
        confirmArchiveArea: '归档"{name}"？您可以稍后从归档中恢复。',
        confirmDeleteHabit: '确定要删除此习惯吗？',
        newChecklistItem: '新清单项目',
        reviewStandardsWeekly: '每周回顾领域标准',
        updateHabitsTracking: '更新习惯跟踪',
        assessProgress: '评估目标进度',
        keyMetricsDescription: '记录和追踪与该领域相关的重要数据指标',
        addMetric: '添加指标',
        createMetric: '创建新指标',
        createMetricDescription: '添加一个新的关键指标来追踪该领域的表现',
        metricName: '指标名称',
        metricNamePlaceholder: '例如：体重、储蓄率、运动时长',
        metricValue: '当前值',
        metricValuePlaceholder: '75',
        metricUnit: '单位',
        metricUnitPlaceholder: 'kg, %, 小时',
        noMetricsYet: '尚未添加任何指标',
        addFirstMetric: '添加第一个关键指标来开始追踪',
        editMetric: '编辑指标',
        editMetricDescription: '更新指标的名称、值或单位',
        deleteMetric: '删除指标',
        saveChanges: '保存更改',
        kpiManagement: {
          title: '关键指标',
          description: '跟踪和管理您领域的关键绩效指标',
          loadingMetrics: '加载指标中...',
          noMetricsYet: '暂无指标',
          createFirstMetric: '创建您的第一个指标开始跟踪进度',
          dailyHabits: '🎯 每日习惯',
          keyMetricsSection: '📊 关键指标',
          noMetricsToTrack: '暂无指标可跟踪',
          createMetricsFirst: '请先创建指标以开始记录数据',
          batchRecordButton: '批量记录',
          addBasic: '添加基础指标',
          setDirections: '设置方向',
          overviewTitle: 'KPI 概览',
          overviewDescription: '关键绩效指标的整体概览和当前状态',
          setMetricDirections: '设置指标方向',
          setDirectionsDescription: '选择每个指标应该增长还是减少以达到目标。系统已根据指标名称和单位提供建议。',
          noMetricsFound: '此领域中未找到指标',
          suggestionAvailable: '有建议可用',
          suggested: '建议',
          decrease: '减少',
          increase: '增长',
          // 新增翻译键
          totalMetrics: '总指标数',
          improving: '改善中',
          needsFocus: '需关注',
          avgProgress: '平均进度',
          manageHabits: '管理习惯',
          addMetric: '添加指标',
          noMetrics: '暂无指标',
          noMetricsDescription: '创建您的第一个指标开始追踪进度',
          createFirst: '创建第一个',
          setupHabits: '设置习惯',
          relatedHabits: '关联习惯',
          dashboard: '仪表板',
          trends: '趋势',
          loadError: '加载失败',
          createSuccess: '创建成功',
          createError: '创建失败',
          updateSuccess: '更新成功',
          updateError: '更新失败',
          deleteSuccess: '删除成功',
          deleteError: '删除失败',
          applyAll: '应用全部',
          cancel: '取消',
          updateDirections: '更新方向',
          updating: '更新中...',
          directionsUpdated: '方向已更新',
          successfullyUpdated: '成功更新了 {count} 个指标的方向',
          partialUpdate: '部分更新',
          failedToUpdate: '{count} 个指标更新失败',
          updateFailed: '更新失败',
          failedToUpdateDirections: '更新指标方向失败',
          suggestedChanges: '{count} 个指标有建议的方向更改',
          kpiChart: {
            title: 'KPI 性能概览',
            description: '所有关键绩效指标的可视化概览',
            kpisCount: '{count} 个KPI',
            achieved: '已达成',
            onTrack: '正常进行',
            atRisk: '有风险',
            behind: '落后',
            noTarget: '无目标',
            avgProgress: '平均进度',
            individualProgress: '各项KPI进度',
            unknown: '未知'
          },
          kpiTrends: {
            title: 'KPI 趋势与分析',
            description: '性能趋势和可操作的洞察',
            kpisCount: '{count} 个KPI',
            export: '导出',
            days7: '7天',
            days30: '30天',
            days90: '90天',
            current: '当前',
            target: '目标',
            trend: '趋势',
            recommendation: '💡 建议',
            keyInsights: '📊 关键洞察',
            improving: '改善中',
            stable: '稳定',
            declining: '下降中',
            improvingDesc: '显示积极趋势的KPI',
            stableDesc: '保持性能的KPI',
            decliningDesc: '需要关注的KPI',
            noData: '无数据',
            up: '上升',
            down: '下降',
            noHistoricalData: '无历史数据可用',
            performanceStable: '性能稳定。继续当前方法。',
            excellentImprovement: '出色的改进！考虑扩大成功策略。',
            goodProgress: '进展良好。保持当前势头。',
            significantDecline: '显著下降。需要立即采取行动。',
            slightDecline: '轻微下降。审查并调整策略。'
          },
          areaAnalytics: {
            title: '领域分析仪表盘',
            description: '深入了解您的领域表现和趋势',
            analyzingData: '正在分析您的领域数据...',
            totalMetrics: '总指标',
            active: '活跃',
            habits: '习惯',
            avgProgress: '平均进度',
            overallProgress: '整体进度',
            complete: '完成',
            topPerformers: '顶级表现者',
            needsAttention: '需要关注',
            noDataAvailable: '无数据可用',
            allMetricsOnTrack: '所有指标都在正轨上！',
            review: '审查',
            weeklyActivityTrends: '每周活动趋势',
            weeklyTrendsDesc: '本周每天创建的记录数',
            categoryDistribution: '分类分布',
            priorityDistribution: '优先级分布',
            keyInsights: '关键洞察',
            recommendations: '建议',
            addMoreDataForInsights: '添加更多数据以生成洞察',
            greatJobKeepUp: '做得很好！保持当前方法。',
            excellentHabitConsistency: '出色的习惯一致性！{count} 个习惯表现良好。',
            habitFormationNeedsAttention: '习惯养成需要关注。考虑减少习惯数量或调整目标。',
            focusOnCoreHabits: '专注于1-2个核心习惯，直到它们变成自动的。',
            outstandingPerformance: '所有指标表现出色！',
            considerChallengingTargets: '考虑设定更具挑战性的目标以继续增长。',
            severalMetricsBelowTarget: '几个指标低于目标。是时候进行策略审查了。',
            identifyTopPriorityMetrics: '确定前3个优先指标并将精力集中在那里。',
            increaseTrackingFrequency: '增加跟踪频率以获得更好的洞察。',
            tabs: {
              overview: '概览',
              trends: '趋势',
              distribution: '分布',
              insights: '洞察'
            }
          },
          batchRecord: {
            title: '批量记录指标值',
            description: '一次性记录多个指标的值。选择要更新的指标并输入新值。',
            globalNote: '全局备注（可选）',
            globalNotePlaceholder: '添加适用于所有记录的备注...',
            metricRecords: '指标记录',
            selectedCount: '{enabled} / {total} 已选择',
            enable: '启用',
            current: '当前',
            target: '目标',
            newValue: '新值',
            note: '备注',
            enterValue: '输入值',
            optionalNote: '可选备注',
            recordingTime: '所有记录将被时间戳标记',
            cancel: '取消',
            recordValues: '记录值',
            recording: '记录中...',
            noRecordsSelected: '未选择记录',
            selectAtLeastOne: '请至少选择一个指标进行记录',
            invalidInput: '输入无效',
            enterValuesForSelected: '请为所有选择的指标输入值',
            recordsCreated: '记录已创建',
            successfullyRecorded: '成功记录了 {count} 个指标值',
            failedToCreateRecords: '创建记录失败'
          },
          tabs: {
            overview: '概览',
            records: '记录',
            dashboard: '仪表盘',
            charts: '图表',
            trends: '趋势',
            analytics: '分析'
          }
        },
        projectKPI: {
          title: '关键绩效指标',
          description: '管理和监控您的关键绩效指标',
          addKPI: '添加KPI',
          overview: 'KPI概览',
          noKPIsDefined: '尚未定义KPI',
          // 新增翻译键
          totalKPIs: '总KPI数',
          onTrack: '正常',
          needsAttention: '需关注',
          avgProgress: '平均进度',
          batchRecord: '批量记录',
          noKPIs: '暂无KPI',
          noKPIsDescription: '创建您的第一个KPI开始追踪项目进度',
          createFirst: '创建第一个',
          dashboard: '仪表板',
          trends: '趋势',
          loadError: '加载失败',
          createSuccess: '创建成功',
          createError: '创建失败',
          updateSuccess: '更新成功',
          updateError: '更新失败',
          deleteSuccess: '删除成功',
          deleteError: '删除失败',
          addKPIsToTrack: '添加关键绩效指标以跟踪项目成功',
          noKPIsToTrack: '无KPI可跟踪',
          createKPIsFirst: '先创建KPI以开始记录数据',
          tabs: {
            overview: '概览',
            records: '记录',
            dashboard: '仪表盘',
            charts: '图表',
            trends: '趋势'
          },
          // 新增的快速录入相关翻译
          updateKPI: '更新 {name}',
          recordNewValue: '为此KPI记录新值',
          newValue: '新值',
          currentValue: '当前: {value}',
          noteOptional: '备注（可选）',
          addNote: '添加关于此次更新的备注...',
          recording: '记录中...',
          record: '记录',
          editKPI: '编辑KPI',
          showHistory: '显示历史',
          hideHistory: '隐藏历史',
          progress: '进度',
          recentHistory: '最近历史',
          noHistoryRecords: '暂无历史记录',
          moreRecords: '+{count} 条记录',
          // 批量记录相关翻译
          batchRecordTitle: '批量记录KPI值',
          batchRecordDescription: '一次性为多个KPI记录新值。您可以启用/禁用单个KPI并添加备注。',
          globalNote: '全局备注',
          globalNotePlaceholder: '输入要应用到所有启用KPI的备注...',
          applyToAll: '应用到全部'
        },
        habitTracker: {
          dailyHabitTracker: '每日习惯追踪器',
          checkIn: '打卡',
          doneToday: '今日已完成',
          currentStreak: '当前连续',
          thisWeek: '本周',
          bestStreak: '最佳连续',
          total: '总计',
          checkInFailed: '打卡失败',
          failedToRecord: '记录习惯完成失败'
        },
        editRecord: {
          title: '编辑记录',
          value: '值',
          valuePlaceholder: '输入值',
          note: '备注',
          notePlaceholder: '添加备注...',
          cancel: '取消',
          saveChanges: '保存更改',
          saving: '保存中...',
          recordUpdated: '记录已更新',
          recordUpdatedMessage: '指标记录已成功更新',
          failedToUpdate: '更新失败',
          edit: '编辑',
          delete: '删除'
        },
        categories: {
          uncategorized: '未分类',
          health: '健康',
          fitness: '健身',
          work: '工作',
          personal: '个人',
          finance: '财务',
          learning: '学习'
        },
        priorities: {
          low: '低',
          medium: '中',
          high: '高'
        },
        metricHistory: {
          title: '历史记录',
          titleWithName: '历史记录 - {name}',
          description: '最近的数据点和变化',
          loadingRecords: '加载记录中...',
          noRecordsYet: '暂无记录',
          startRecording: '开始记录数据以查看历史'
        },
        kpiDashboard: {
          title: 'KPI 仪表盘',
          noKPIsToDisplay: '暂无KPI可显示',
          addKPIsToSee: '添加KPI以查看仪表盘指标',
          realTimeOverview: '实时性能概览和关键洞察',
          healthScore: '健康评分',
          overallHealthScore: '整体KPI健康评分',
          achieved: '已达成',
          onTrack: '正常进行',
          atRisk: '有风险',
          behind: '落后',
          topPerformer: '最佳表现',
          needsAttention: '需要关注',
          quickActions: '💡 快速操作',
          reviewUnderperforming: '检查 {count} 个表现不佳的KPI',
          celebrateAchieved: '庆祝 {count} 个已达成目标！',
          averageProgress: '平均进度',
          healthLabels: {
            excellent: '优秀',
            good: '良好',
            fair: '一般',
            needsImprovement: '需要改进',
            noData: '无数据'
          },
          statusDescriptions: {
            achieved: '100% 完成',
            onTrack: '75%+ 完成',
            atRisk: '50-74% 完成',
            behind: '<50% 完成'
          }
        },
        checklistTemplatesDescription: '创建可复用的标准核查清单模板',
        createTemplate: '创建模板',
        createChecklistTemplate: '创建清单模板',
        createTemplateDescription: '创建一个可重复使用的清单模板',
        templateName: '模板名称',
        templateNamePlaceholder: '例如：每周家庭清洁清单',
        templateItems: '清单项目',
        itemPlaceholder: '输入清单项目...',
        noItemsYet: '尚未添加任何项目',
        templates: '模板',
        noTemplatesYet: '尚未创建任何模板',
        createFirstTemplate: '创建第一个清单模板',
        items: '项目',
        useTemplate: '使用此模板',
        editTemplate: '编辑模板',
        deleteTemplate: '删除模板',
        completedOn: '完成于',
        editTemplateDescription: '修改清单模板的名称和项目',
        noStandardSet: '尚未设置领域标准。点击编辑按钮来定义该领域的成功标准。',
        saveStandard: '保存标准',
        // Area detail page specific
        checklistTemplateManagement: '清单模板管理',
      },
      dialog: {
        createTitle: '创建新领域',
        editTitle: '编辑领域',
        description: '领域是需要长期维护的持续责任和标准。',
        areaName: '领域名称',
        areaNameRequired: '领域名称 *',
        areaNamePlaceholder: '输入领域名称...',
        descriptionLabel: '描述',
        descriptionPlaceholder: '描述此领域包含的内容...',
        standardLabel: '维护标准',
        standardPlaceholder: '您希望在此领域保持什么标准或水平？',
        standardExample: '例如："每周锻炼3次，保持健康体重，睡眠7小时以上"',
        status: '状态',
        reviewFrequency: '回顾频率',
        areaExamples: '领域示例',
        cancel: '取消',
        create: '创建领域',
        update: '更新领域',
        creating: '创建中...',
        updating: '更新中...',
        reviewFrequencyOptions: {
          daily: '每日',
          weekly: '每周',
          monthly: '每月',
          quarterly: '每季度'
        }
      },
      card: {
        standard: '标准',
        habitCompletion: '习惯完成度',
        relatedProjects: '相关项目',
        activeHabits: '活跃习惯',
        review: '回顾',
        updated: '更新于',
        habits: '习惯',
        viewDetails: '查看详情',
        editArea: '编辑领域',
        archiveArea: '归档领域',
        deleteArea: '删除领域'
      }
    },
    habits: {
      dialog: {
        createTitle: '创建新习惯',
        editTitle: '编辑习惯',
        description: '建立积极的习惯并跟踪您的进度。',
        habitName: '习惯名称',
        habitNameRequired: '习惯名称 *',
        habitNamePlaceholder: '输入习惯名称...',
        descriptionLabel: '描述',
        descriptionPlaceholder: '描述这个习惯...',
        frequency: '频率',
        target: '目标（可选）',
        targetPlaceholder: '例如：30',
        unit: '单位（可选）',
        unitPlaceholder: '例如：分钟',
        color: '颜色',
        associatedArea: '关联领域',
        selectAreaPlaceholder: '选择一个领域...',
        noArea: '无领域',
        habitExamples: '习惯示例',
        cancel: '取消',
        create: '创建习惯',
        update: '更新习惯',
        creating: '创建中...',
        updating: '更新中...',
        frequencyOptions: {
          daily: '每日',
          weekly: '每周',
          monthly: '每月'
        },
        frequencyDescriptions: {
          daily: '每天',
          weekly: '每周一次',
          monthly: '每月一次'
        }
      },
      // Mini habit tracker
      miniTracker: {
        title: '习惯追踪器',
        description: '{monthName} · 点击日期完成打卡',
        addButton: '添加',
        noHabits: '暂无习惯追踪',
        createFirstHabitHint: '点击"添加"开始养成好习惯',
        deleteHabit: '删除习惯',
        deleteConfirm: '确定要删除这个习惯吗？此操作无法撤销。',
        target: '目标',
        daily: '每日',
        // Tooltip texts
        futureDate: '未来日期',
        noRecord: '未记录',
        completed: '已完成',
        notCompleted: '未完成',
        completedWithValue: '已完成{value}{unit} / 目标{target}{unit} ({progress}%)',
        targetWithUnit: '目标{target}{unit}',
        clickToModify: '点击修改数值',
        clickToToggle: '点击切换状态'
      },
      // Habit type explanations for create dialog
      typeExplanation: {
        title: '习惯类型说明',
        currentSetting: '当前设置将创建：',
        numericHabit: '数值型习惯',
        booleanHabit: '布尔型习惯',
        numericDescription: '设置目标值 ＞ 1 且有单位',
        booleanDescription: '目标值 ≤ 1 或无单位',
        numericExample: '例如：目标8杯，单位"杯" → 可记录具体数量，支持部分完成显示',
        booleanExample: '例如：冥想、早起 → 简单的完成/未完成状态',
        numericFeature: '支持记录具体数量，部分完成显示蓝色，100%完成显示绿色',
        booleanFeature: '简单的完成/未完成状态，完成显示绿色'
      },
      item: {
        frequencyLabels: {
          daily: '每日',
          weekly: '每周',
          monthly: '每月'
        },
        editHabit: '编辑习惯',
        showDetails: '显示详情',
        hideDetails: '隐藏详情',
        deleteHabit: '删除习惯',
        completedToday: '今日已完成！',
        markAsDoneToday: '标记为今日完成',
      },
      tracker: {
        target: '目标',
        dayStreak: '天连续',
        thisMonth: '本月',
        last7Days: '最近7天',
        monthlyProgress: '月度进度',
        currentStreak: '当前连续',
        totalCompleted: '总完成次数',
        created: '创建于'
      }
    },
    resources: {
      title: '资源',
      description: '供将来参考的持续关注的主题或话题。',
      addResource: '添加资源',
      selectFile: '选择一个文件来查看其内容',
      chooseFromTree: '从左侧的文件树中选择一个文件',
      folderSelected: '已选择文件夹',
      selectFileToView: '选择一个文件来查看其内容',
      links: '链接',
      graph: '图谱',
      selectFileToSeeLinks: '选择一个文件查看其链接',
      exitFocusModeTooltip: '退出专注模式 (F11)',
      enterFocusModeTooltip: '进入专注模式 (F11)',
      exitFocusMode: '退出专注',
      focusMode: '专注模式',
      markdownEditor: 'Markdown 编辑器',
      selectMarkdownToEdit: '请选择一个 Markdown 文件进行编辑',
      notMarkdown: '当前文件不是 Markdown 格式',
      chooseMarkdownFromTree: '请在左侧文件树中选择一个 Markdown 文件',
      addToLibraryWithName: '添加 "{name}" 到资源库',
      file: '文件',
      // 通知消息
      notifications: {
        wikiLinkSuccess: 'WikiLink 跳转成功',
        wikiLinkSuccessMessage: '已打开文件：{fileName}',
        wikiLinkFailed: 'WikiLink 跳转失败',
        wikiLinkFailedMessage: '跳转时发生未知错误',
        openFileFailed: '打开文件失败',
        openFileFailedMessage: '打开文件时发生未知错误',
        fileCreated: '文件创建成功',
        fileCreatedMessage: '文件 "{fileName}" 已创建并打开',
        createFileFailed: '创建文件失败',
        createFileFailedMessage: '创建文件时发生未知错误',
        saveSuccess: '保存成功',
        saveSuccessMessage: '文件 "{fileName}" 已保存',
        saveFailed: '保存失败',
        saveFailedMessage: '保存文件时发生未知错误',
        cannotSave: '无法保存',
        cannotSaveMessage: '请先选择一个文件',
        readFileFailed: '读取文件失败',
        readFileFailedMessage: '无法读取文件内容',
        fileExists: '文件已存在',
        fileExistsMessage: '文件 "{fileName}" 已存在，请使用其他名称',
        folderExists: '文件夹已存在',
        folderExistsMessage: '文件夹 "{name}" 已存在，请使用其他名称',
        folderCreated: '文件夹创建成功',
        folderCreatedMessage: '文件夹 "{name}" 已创建',
        createFailed: '创建失败',
        createFailedMessage: '创建{type}失败',
        selectFile: '请选择文件',
        selectFileMessage: '请先在左侧文件树中点击选择一个文件，然后再点击添加资源按钮',
        selectFileNotFolder: '只能将文件添加到资源库，请选择一个文件而不是文件夹',
        resourceAdded: '资源添加成功',
        resourceAddedMessage: '文件 "{fileName}" 已添加到资源库',
        addResourceFailed: '添加资源失败',
        addResourceFailedMessage: '添加资源时发生未知错误',
        deleteSuccess: '删除成功',
        deleteSuccessMessage: '{type} "{name}" 已删除',
        deleteFailed: '删除失败',
        deleteFailedMessage: '操作过程中发生错误',
        renameSuccess: '重命名成功',
        renameSuccessMessage: '{type} "{oldName}" 已重命名为 "{newName}"',
        renameFailed: '重命名失败',
        renameFailedMessage: '操作过程中发生错误'
      },
      // 界面文本
      unsaved: '未保存',
      saveShortcut: '保存 (Ctrl+S)',
      startWriting: '开始编写 {fileName}...',
      linkStatistics: '链接统计',
      bidirectionalBacklinks: '双向反向链接',
      bidirectionalOutlinks: '双向出链',
      linkGraph: '🔗 链接图谱',
      linkGraphRemoved: '链接图谱功能已移除',
      createTime: '创建时间：{time}',
      // 文件操作
      createNewFile: '创建新文件',
      createNewFileDescription: '文件 "{pageName}.md" 不存在，是否要创建这个新文件？',
      createFile: '创建文件',
      cancel: '取消',
      // 预览相关
      previewLoadFailed: '预览加载失败: {pageName}',
      folder: '文件夹',
      createDialog: {
        title: '创建{type}',
        description: '请输入{type}的名称',
        namePlaceholder: '请输入{type}名称'
      },
      sidebar: {
        title: '资源',
        new: '新建',
        newFile: '新建文件',
        newFolder: '新建文件夹',
        searchPlaceholder: '搜索文件...',
        loading: '正在加载文件...',
        noMatch: '没有文件匹配你的搜索',
        noFiles: '暂时还没有文件',
        tryDifferentSearch: '试试不同的搜索关键词',
        createFirst: '创建你的第一个文件或文件夹'
      }
    },

    archive: {
      title: '归档',
      description: '来自项目、领域和资源的不再相关的非活动项目。',
      settings: '设置',
      archivedProjects: '已归档项目',
      archivedAreas: '已归档领域',
      archivedResources: '已归档资源',
      completedOrCancelled: '已完成或已取消的项目',
      empty: {
        noArchivedItemsYet: '暂无归档项目',
        willAppearWhenArchived: '归档项目将在此处显示'
      },
      noLongerMaintained: '不再维护的领域',
      noLongerRelevant: '不再相关的资源',
      lastArchivedRecently: '最近有归档',
      noArchivedItems: '没有归档项目',
      searchPlaceholder: '搜索归档项目...',
      management: '归档管理',
      managementDescription: '管理您的归档项目和清理策略',
      // Settings页面翻译
      autoArchiveProjects: '自动归档已完成项目',
      autoArchiveDescription: '完成后30天自动将项目移至归档',
      cleanupOldArchives: '清理旧归档',
      cleanupDescription: '移除超过指定月数的归档项目',
      archiveNotifications: '归档通知',
      notificationsDescription: '项目自动归档时获得通知',
      enabled: '已启用',
      disabled: '已禁用',
      months: '个月',
      cleanup: '清理',
      exportArchiveData: '导出归档数据',
      // ArchiveItem组件翻译
      viewDetails: '查看详情',
      restore: '恢复',
      deletePermanently: '永久删除',
      archived: '已归档',
      originalStatus: '原始状态',
      lastActivity: '最后活动',
      attachments: '附件',
      complete: '完成',
      // 过滤与排序
      filters: {
        type: '类型',
        reason: '原因',
        sort: '排序',
        sortBy: {
          dateArchived: '归档时间',
          title: '标题',
          type: '类型'
        },
        reasonOptions: {
          all: '所有原因',
          completed: '已完成',
          cancelled: '已取消',
          inactive: '不活跃',
          outdated: '过时',
          manual: '手动'
        }
      },
      // Tab 文本
      tabs: {
        items: '归档项',
        settings: '设置'
      },

      // 时间格式化
      yesterday: '昨天',
      daysAgo: '天前',
      weeksAgo: '周前',
      monthsAgo: '个月前',
      yearsAgo: '年前'
    },

    inbox: {
      title: '收件箱',
      description: '捕获和处理所有传入的信息和任务。',
      quickCapture: '快速记录',
      searchPlaceholder: '搜索项目...',
      unprocessed: '个未处理',
      allProcessed: '全部已处理',
      // 通知消息
      notifications: {
        contentCaptured: '内容已捕获',
        contentCapturedMessage: '已添加到收件箱',
        resourceCreated: '资源创建成功',
        resourceCreatedMessage: '已创建资源文件"{fileName}"并添加到资源库',
        createFailed: '创建失败',
        createFailedMessage: '创建资源时发生错误',
        taskCreated: '任务创建成功',
        taskCreatedMessage: '已在"{targetName}"中创建任务"{title}"',
        featureInDevelopment: '功能开发中',
        featureInDevelopmentMessage: '{actionType} 创建功能正在开发中',
        createContentFailed: '创建内容时发生错误',
        checklistInDevelopment: '清单创建功能正在开发中',
        kpiCreated: 'KPI创建成功',
        kpiCreatedMessage: '已在"{targetName}"中创建KPI"{name}"',
        kpiCreateFailed: 'KPI创建失败',
        kpiCreateFailedMessage: '创建KPI时发生错误',
        habitCreated: '习惯创建成功',
        habitCreatedMessage: '已在"{targetName}"中创建习惯"{name}"',
        habitCreateFailed: '习惯创建失败',
        habitCreateFailedMessage: '创建习惯时发生错误',
        projectCreated: '项目创建成功',
        projectCreatedMessage: '已创建项目"{name}"',
        projectCreateFailed: '创建项目时发生错误',
        areaCreated: '领域创建成功',
        areaCreatedMessage: '已创建领域"{name}"',
        areaCreateFailed: '创建领域时发生错误',
        maintenanceTaskCreated: '维护任务创建成功',
        maintenanceTaskCreatedMessage: '已在"{targetName}"中创建维护任务'
      },
      filters: {
        typeLabel: '类型',
        statusLabel: '状态',
        type: {
          allTypes: '所有类型',
          notes: '笔记',
          tasks: '任务',
          ideas: '想法',
          links: '链接',
          files: '文件'
        },
        status: {
          allItems: '所有项目',
          unprocessed: '未处理',
          processed: '已处理'
        }
      },
      tabs: {
        inboxItems: '收件箱项目 ({count})',
        unassignedTasks: '未分配任务 ({count})'
      },
      list: {
        title: '收件箱项目',
        description: '需要处理和分类的项目',
        loading: '加载收件箱...'
      },
      unassigned: {
        title: '未分配任务',
        description: '没有关联项目或领域的任务'
      },
      buttons: {
        createTask: '创建任务'
      },
      dialogs: {
        confirmDeleteItem: '确定要删除此项目吗？'
      },
      item: {
        editPlaceholder: '编辑内容...',
        processedTo: '已处理到 {type}: {name}',
        actions: {
          process: '处理',
          unprocess: '取消处理',
          markDone: '标记完成',
          edit: '编辑',
          delete: '删除'
        },
        processToLabel: '处理到:',
        destinations: {
          project: '项目',
          area: '领域',
          resource: '资源',
          archive: '归档'
        }
      },
      quickCaptureDialog: {
        title: '快速记录',
        description: '快速记录想法、任务、创意或任何信息以便后续处理。',
        placeholder: '你在想什么？',
        saveHint: '按 ⌘+Enter 快速保存',
        typeLabel: '类型',
        priorityLabel: '优先级',
        tagsLabel: '标签',
        addTagPlaceholder: '添加标签...',
        addButton: '添加',
        cancel: '取消',
        capturing: '记录中...',
        capture: '记录',
        typeDescriptions: {
          note: '一般信息或想法',
          task: '需要完成的事情',
          idea: '创意想法或概念',
          link: '网页链接或参考',
          file: '文档或附件'
        }
      }
    },
    reviews: {
      title: '回顾',
      description: '定期回顾和反思您的进展。',
      weekly: '周回顾',
      monthly: '月回顾',
      quarterly: '季度回顾',
      weeklyBadge: '每周',
      startReview: '开始回顾',
      schedule: {
        daily: {
          title: '每日回顾'
        },
        weekly: {
          title: '周回顾'
        },
        monthly: {
          title: '月回顾'
        },
        quarterly: {
          title: '季度回顾'
        },
        today: '今天',
        dueDay: '截止{day}',
        dueDate: '截止{date}',
        dueSunday: '截止周日',
        dueJan31: '截止1月31日',
        dueMar31: '截止3月31日'
      },
      status: {
        notStarted: '未开始',
        completed: '已完成',
        inProgress: '进行中'
      },
      progress: {
        tasksCompleted: '{completed}/{total}个任务已完成'
      },
      templates: {
        title: '复盘模板',
        description: '管理不同类型复盘的模板',
        dailyStandup: {
          title: '每日站会',
          description: '快速每日检查'
        },
        weeklyPlanning: {
          title: '周计划',
          description: '规划即将到来的一周'
        },
        monthlyReflection: {
          title: '月度反思',
          description: '深入了解进展情况'
        },
        // Template management
        newTemplate: '新建模板',
        import: '导入',
        export: '导出',
        duplicate: '复制',
        edit: '编辑',
        delete: '删除',
        useTemplate: '使用模板',
        createTemplate: '创建模板',
        editTemplate: '编辑模板',
        templateName: '模板名称',
        templateDescription: '模板描述',
        templateType: '模板类型',
        setAsDefault: '设为默认模板',
        setAsDefaultForType: '设为{typeName}默认模板',
        sections: '模板章节',
        advancedEditor: '高级编辑器',
        addSection: '添加章节',
        sectionTitle: '章节标题',
        sectionId: '章节ID',
        sectionDescription: '章节描述',
        placeholder: '占位符文本',
        required: '必填章节',
        systemTemplate: '系统模板',
        defaultTemplate: '默认',
        // 新增的翻译键
        deleteConfirm: '确定要删除此模板吗？',
        copy: '副本',
        imported: '已导入',
        invalidFormat: '无效的模板文件格式',
        importFailed: '导入模板失败。请检查文件格式。',
        newSectionTitle: '新章节',
        newSectionDescription: '章节描述',
        newSectionPlaceholder: '在此输入您的想法...',
        namePlaceholder: '例如：周度反思',
        descriptionPlaceholder: '描述何时以及如何使用此模板...',
        template: '模板',
        advancedEditorDescription: '自定义复盘模板的章节和结构',
        sectionNumber: '章节 {number}',
        sectionTitlePlaceholder: '例如：关键成就',
        sectionIdPlaceholder: '例如：achievements',
        sectionDescriptionPlaceholder: '简要描述此章节的用途',
        placeholderPlaceholder: '引导用户的占位符文本...',
        sectionsCount: '{count} 个章节',
        systemTemplateIdReadonly: '系统模板的章节ID不可修改',
        // 默认章节模板
        defaultSections: {
          daily: {
            wins: {
              title: '🎉 今日成就',
              description: '今天有什么进展顺利？',
              placeholder: '列出您的成就和积极时刻...'
            },
            challenges: {
              title: '⚡ 挑战',
              description: '什么比较困难？',
              placeholder: '描述遇到的障碍或困难...'
            },
            learnings: {
              title: '💡 学习收获',
              description: '您学到了什么？',
              placeholder: '关键见解和经验教训...'
            },
            tomorrow: {
              title: '🚀 明日重点',
              description: '明天将专注于什么？',
              placeholder: '明天的首要任务...'
            }
          },
          weekly: {
            achievements: {
              title: '🎯 周度成就',
              description: '本周您完成了什么？',
              placeholder: '主要成就和完成的目标...'
            },
            challenges: {
              title: '⚡ 挑战与障碍',
              description: '您面临了哪些挑战？',
              placeholder: '困难以及您如何处理它们...'
            },
            learnings: {
              title: '💡 关键学习',
              description: '您获得了哪些见解？',
              placeholder: '重要的经验教训和认识...'
            },
            nextWeek: {
              title: '🚀 下周目标',
              description: '下周将专注于什么？',
              placeholder: '下周的优先事项和目标...'
            }
          },
          monthly: {
            progress: {
              title: '📈 月度进展',
              description: '您在目标方面取得了怎样的进展？',
              placeholder: '回顾月度目标的进展情况...'
            },
            highlights: {
              title: '⭐ 月度亮点',
              description: '有哪些突出的时刻？',
              placeholder: '最难忘和最有影响力的事件...'
            },
            challenges: {
              title: '🔧 改进领域',
              description: '有什么可以改进的？',
              placeholder: '面临的挑战和改进机会...'
            },
            nextMonth: {
              title: '🎯 下月重点',
              description: '下个月的优先事项是什么？',
              placeholder: '关键目标和重点领域...'
            }
          }
        }
      },
      duration: {
        minutes: '{count}分钟'
      },
      insights: {
        title: '回顾洞察',
        description: '回顾中的关键指标和趋势',
        completionRate: '回顾完成率',
        averageTime: '平均回顾时间',
        actionItemsCompleted: '行动项目完成率'
      },
      recent: {
        title: '最近回顾',
        description: '您最新的回顾会话和结果',
        weeklyReviewTitle: '周回顾 - 第{week}周',
        weeklyReviewContent: '完成了{projects}个项目，识别了{areas}个新的改进领域',
        dailyReviewTitle: '每日回顾 - 今天',
        dailyReviewContent: '进行中：回顾收件箱并规划下一步行动',
        timeStamp: '{date} • {duration}分钟',
        timeStampSoFar: '{date} • 目前{duration}分钟'
      },
      editor: {
        title: '复盘编辑器',
        newReview: '新建复盘',
        editReview: '编辑复盘',
        basicInfo: '基本信息',
        reviewType: '复盘类型',
        period: '周期',
        reviewTitle: '标题',
        titlePlaceholder: '复盘标题（可选）',
        summary: '摘要',
        template: '模板',
        selectTemplate: '选择模板',
        content: '复盘内容',
        contentDescription: '回顾您的成就、挑战和学习收获。使用“自动生成”可基于您的数据填充内容。',
        autoGenerate: '自动生成',
        generating: '生成中...',
        actionItems: '行动项',
        addActionItem: '添加行动项',
        actionItemsDescription: '跟踪本次复盘中的具体行动与承诺',
        noActionItemsTip: '暂未添加行动项。点击“添加行动项”创建。',
        actionItemPlaceholder: '行动项描述...',
        insights: '洞察',
        productivity: '生产力',
        satisfaction: '满意度',
        mood: '整体心情',
        energy: '精力水平',
        aiAnalysis: 'AI分析',
        save: '保存',
        saving: '保存中...',
        cancel: '取消',
        status: reviewStatusLabels.zh,
        types: reviewTypeLabels.zh,
        summaryPlaceholder: '简要总结本次复盘周期的情况...',
        insightsDescription: '评价您在此复盘周期的体验',
        periodPlaceholder: '例如：2025-W03',
        priorities: {
          low: '低',
          medium: '中',
          high: '高'
        },
        placeholders: {
          selectMood: '选择心情',
          selectEnergy: '选择精力水平',
          selectProductivity: '选择生产力水平',
          selectSatisfaction: '选择满意度水平'
        },
        moodLevels: {
          excellent: '非常好',
          good: '良好',
          neutral: '一般',
          poor: '较差',
          terrible: '很差'
        },
        energyLevels: {
          high: '高',
          medium: '中',
          low: '低',
          depleted: '透支'
        },
        productivityLevels: {
          veryHigh: '非常高',
          high: '高',
          medium: '中',
          low: '低',
          veryLow: '非常低'
        },
        satisfactionLevels: {
          verySatisfied: '非常满意',
          satisfied: '满意',
          neutral: '一般',
          dissatisfied: '不满意',
          veryDissatisfied: '非常不满意'
        },
        defaultSections: {
          achievements: '成就与收获',
          challenges: '挑战与障碍',
          learnings: '关键学习',
          nextSteps: '下一步行动'
        },
        fallbackPlaceholders: {
          achievements: '你完成了什么？有哪些进展顺利？',
          challenges: '你遇到了哪些挑战？哪些没有按计划进行？',
          learnings: '你学到了什么？获得了哪些洞察？',
          nextSteps: '你将做出哪些不同的尝试？下一步行动是什么？'
        }
      },
      list: {
        title: '复盘列表',
        description: '管理您的定期复盘和反思',
        newReview: '新建复盘',
        search: '搜索复盘...',
        filterByType: '按类型筛选',
        filterByStatus: '按状态筛选',
        all: '全部',
        selected: '已选择 {count} 项',
        export: '导出',
        compare: '对比',
        clear: '清除',
        exportSelected: '导出 ({count})',
        noReviews: '暂无复盘',
        createFirst: '创建您的第一个复盘',
        tryAdjustFilters: '尝试调整您的筛选条件或搜索查询。',
        deleteConfirm: '确定要删除此复盘吗？',
        tooltip: {
          export: '导出复盘',
          edit: '编辑复盘',
          delete: '删除复盘'
        }
      },
      analysis: {
        title: '智能分析',
        description: 'AI驱动的复盘洞察',
        reviewFor: '复盘',
        overallScore: '总体表现评分',
        trends: '趋势分析',
        predictions: '预测分析',
        recommendations: '改进建议',
        progress: '进度评估',
        projects: '项目',
        habits: '习惯',
        kpis: 'KPI',
        projectCompletion: '项目完成度',
        taskProductivity: '任务生产力',
        habitConsistency: '习惯一致性',
        nextPeriodProjections: '下期预测',
        expectedProjects: '预期项目',
        expectedTasks: '预期任务',
        goalAchievementProbability: '目标达成概率',
        riskFactors: '风险因素',
        recommendedFocus: '建议关注领域',
        immediateActions: '即时行动',
        shortTermGoals: '短期目标',
        longTermStrategy: '长期策略',
        thisWeek: '本周',
        thisMonth: '本月',
        nextQuarter: '下季度',
        achievements: '成就',
        areasForImprovement: '改进领域',
        performanceBreakdown: '表现细分',
        noRiskFactors: '未发现重大风险因素',
        noImmediateActions: '无需即时行动',
        noShortTermRecommendations: '无短期建议',
        noLongTermRecommendations: '无长期建议',
        noAchievements: '本期无重大成就',
        noGaps: '未发现明显差距',
        analysisUnavailable: '分析不可用',
        unableToGenerate: '无法为此期间生成分析',
        tryAgain: '重试',
        loadFailed: '加载分析失败',
        loadError: '加载分析时出错'
      },
      comparison: {
        title: '复盘对比',
        description: '对比 {count} 个复盘',
        contentSimilarity: '内容相似度',
        progressTrend: '进度趋势',
        timeSpan: '时间跨度',
        reviewsCompared: '对比的复盘数',
        overview: '概览',
        details: '详情',
        insights: '洞察',
        keyDifferences: '主要差异',
        commonThemes: '共同主题',
        comparisonInsights: '对比洞察',
        contentAnalysis: '内容分析',
        progressAssessment: '进度评估',
        recommendations: '建议',
        consistent: '一致',
        varied: '多样',
        showingImprovement: '显示改进',
        needsAttention: '需要关注',
        maintainingConsistent: '保持一致表现',
        noDifferences: '未发现显著差异',
        noThemes: '未识别共同主题',
        comparisonUnavailable: '对比不可用',
        needAtLeast2: '需要至少2个复盘进行对比'
      }
    }
  },

  // 组件翻译
  components: {
    dialog: {
      exitConfirm: {
        title: '确认退出',
        message: '您确定要退出应用程序吗？',
        dontShowAgain: '不再显示此提示',
        confirm: '退出',
        minimizeToTray: '最小化到托盘',
        trayHint: '应用将在系统托盘中继续运行，您可以随时从托盘菜单恢复窗口'
      }
    },
    deleteTaskDialog: {
      title: '删除任务',
      confirmMessage: '确定要删除此任务吗：',
      warning: '警告',
      subtaskWarning: '此任务有 {count} 个{subtasks}。删除此任务将同时永久删除所有子任务。',
      subtask: '子任务',
      subtasks: '子任务',
      cannotUndo: '此操作无法撤销。',
      deleteTask: '删除任务',
      deleteWithSubtasks: '删除任务和 {count} 个{subtasks}'
    },
    taskList: {
      notifications: {
        invalidMove: '无效移动',
        circularDependency: '无法移动任务：会创建循环依赖',
        taskMoved: '任务已移动',
        moveFailed: '移动失败',
        moveFailedMessage: '移动任务失败，请重试。',
        taskUpdated: '任务已更新',
        statusChanged: '状态已更改为 {status}',
        updateFailed: '更新任务失败',
        updateFailedMessage: '任务可能在数据库中不存在。请刷新页面。',
        attributesUpdated: '任务属性已更新',
        attributesSaved: '任务属性已成功保存',
        saveAttributesFailed: '保存属性失败'
      },
      dragHints: {
        reorderVertical: '垂直拖拽重新排序 • 水平拖拽改变层级',
        makeSubtask: '→ 设为 "{name}" 的子任务',
        promoteTask: '← 提升到父级',
        reorderSameLevel: '↕ 同级重新排序'
      }
    },
    projectCard: {
      // 状态标签
      status: {
        notStarted: '未开始',
        completed: '已完成',
        inProgress: '进行中',
        atRisk: '有风险',
        paused: '已暂停'
      },
      // 时间相关
      deadline: {
        noDeadline: '无截止日期',
        daysOverdue: '逾期 {days} 天',
        dueToday: '今天到期',
        dueTomorrow: '明天到期',
        daysLeft: '剩余 {days} 天'
      },
      // 操作按钮
      actions: {
        editProject: '编辑项目',
        archiveProject: '归档项目',
        deleteProject: '删除项目'
      },
      // 标签文本
      labels: {
        progress: '进度',
        goal: '目标',
        deliverable: '交付物',
        started: '开始时间',
        updated: '更新时间',
        viewDetails: '查看详情'
      }
    },
    createProjectDialog: {
      title: {
        create: '创建新项目',
        edit: '编辑项目'
      },
      description: '项目是具有明确截止日期和完成标准的成果。',
      fields: {
        name: '项目名称',
        namePlaceholder: '输入项目名称',
        description: '项目描述',
        descriptionPlaceholder: '描述项目目标和范围',
        goal: '项目目标',
        goalPlaceholder: '此项目旨在实现什么？',
        deliverable: '交付物',
        deliverablePlaceholder: '项目完成时将交付什么？',
        status: '状态',
        progress: '进度',
        startDate: '开始日期',
        deadline: '截止日期',
        area: '领域',
        selectArea: '选择领域',
        noArea: '无领域'
      },
      deliverableTypes: {
        examples: '示例：',
        document: '📄 文档/报告',
        documentExample: '例如：[[项目报告.md]]',
        metric: '📊 可量化指标',
        metricExample: '例如：用户满意度 ≥ 95%',
        checklist: '✅ 清单',
        checklistExample: '例如：多个交付物',
        other: '🎯 其他',
        otherExample: '例如：自定义交付物'
      },
      buttons: {
        cancel: '取消',
        create: '创建项目',
        update: '更新项目',
        creating: '创建中...',
        updating: '更新中...'
      }
    },

    dialogs: {
      confirmDelete: '确定要删除"{name}"吗？此操作无法撤销。',
      confirmArchive: '归档"{name}"？您可以稍后从归档中恢复。',
      confirmRestore: '恢复"{name}"？它将被移回原始位置。',
      confirmPermanentDelete: '永久删除"{name}"？此操作无法撤销。',
      exitConfirm: {
        title: '确认退出',
        message: '您确定要退出应用程序吗？',
        dontShowAgain: '不再显示此提示',
        confirm: '退出'
      }
    },
    emptyStates: {
      noTasksToday: '今天没有到期任务！',
      enjoyFreeTime: '享受您的空闲时间或提前规划。',
      noUpcomingDeadlines: '没有即将到期的截止日期',
      allProjectsOnTrack: '所有项目都在正轨上！',
      noRecentActivity: '没有最近活动',
      startWorking: '开始处理您的项目和领域！',
      noItemsFound: '没有项目符合您的筛选条件',
      tryAdjustingFilters: '尝试调整您的搜索或筛选条件',
      noDataAvailable: '无数据可用'
    },
    filters: {
      showCompleted: '显示已完成',
      hideCompleted: '隐藏已完成',
      clearFilters: '清除筛选',
      applyFilters: '应用筛选'
    },
    forms: {
      required: '此字段为必填项',
      invalidEmail: '请输入有效的邮箱地址',
      passwordTooShort: '密码至少需要8个字符',
      confirmPassword: '密码确认不匹配'
    },
    actions: {
      viewDetails: '查看详情',
      viewAll: '查看全部',
      open: '打开',
      close: '关闭',
      expand: '展开',
      collapse: '收起',
      refresh: '刷新',
      export: '导出',
      import: '导入',
      duplicate: '复制',
      archive: '归档',
      restore: '恢复',
      move: '移动',
      copy: '复制',
      paste: '粘贴'
    },
    inspirationCapture: {
      title: '灵感记录',
      shortcut: 'Ctrl+Shift+I',
      placeholder: '记录您的灵感和想法...',
      hint: 'Ctrl+Enter 快速保存，ESC 关闭',
      capture: '记录',
      capturing: '记录中...',
      success: {
        title: '灵感已记录',
        message: '您的想法已保存到收件箱'
      },
      error: {
        title: '记录失败',
        message: '保存灵感时出现错误，请重试'
      }
    },
    checklistTemplates: {
      title: '清单模板库',
      description: '可复用的标准清单模板',
      newTemplate: '新建模板',
      noTemplates: '暂无清单模板',
      createHint: '点击"新建模板"创建可复用的清单',
      itemsCount: '{count} 个项目',
      usedTimes: '已使用 {count} 次',
      activeCount: '{count} 个进行中',
      containsItems: '包含项目',
      moreItems: '还有 {count} 个项目...',
      useTemplate: '+ 使用',
      notifications: {
        created: '清单已创建',
        createdMessage: '"{name}"的新实例已添加到执行区',
        createFailed: '创建失败',
        createFailedMessage: '无法创建清单实例',
        cannotDelete: '无法删除',
        hasActiveInstances: '该模板还有 {count} 个进行中的实例，请先完成或删除这些实例',
        deleted: '已删除',
        deletedMessage: '清单模板已删除'
      },
      deleteConfirm: {
        title: '删除清单模板',
        message: '确定要删除模板"{name}"吗？此操作无法撤销。'
      }
    }
  },

  // 枚举翻译
  enums: {
    status: {
      notStarted: '未开始',
      inProgress: '进行中',
      atRisk: '有风险',
      paused: '已暂停',
      completed: '已完成',
      cancelled: '已取消',
      active: '活跃',
      needsAttention: '需要关注',
      onHold: '暂停',
      reviewRequired: '需要审核'
    },
    priority: {
      low: '低',
      medium: '中',
      high: '高',
      urgent: '紧急'
    },
    types: {
      note: '笔记',
      task: '任务',
      idea: '想法',
      link: '链接',
      file: '文件',
      project: '项目',
      area: '领域',
      resource: '资源'
    }
  },

  // 消息翻译
  messages: {
    success: {
      saved: '保存成功',
      created: '创建成功',
      updated: '更新成功',
      deleted: '删除成功',
      archived: '归档成功',
      restored: '恢复成功',
      imported: '导入成功',
      exported: '导出成功'
    },
    error: {
      saveFailed: '保存失败',
      createFailed: '创建失败',
      updateFailed: '更新失败',
      deleteFailed: '删除失败',
      archiveFailed: '归档失败',
      restoreFailed: '恢复失败',
      importFailed: '导入失败',
      exportFailed: '导出失败',
      networkError: '网络错误',
      unknownError: '未知错误'
    },
    warning: {
      title: '警告',
      unsavedChanges: '您有未保存的更改',
      confirmLeave: '确定要离开吗？',
      dataWillBeLost: '数据将丢失'
    },
    info: {
      processing: '处理中...',
      uploading: '上传中...',
      downloading: '下载中...',
      syncing: '同步中...'
    }
  },

  // Recurring maintenance tasks
  recurringTasks: {
    title: '定期维护任务',
    description: '按截止日期排序的重复性维护任务',
    addTask: '添加任务',
    noTasks: '暂无定期维护任务',
    createFirstTask: '点击"添加任务"创建第一个重复任务',
    deleteTask: '删除定期维护任务',
    deleteConfirm: '确定要删除任务"{title}"吗？此操作无法撤销。',
    deleteButton: '删除',
    cancelButton: '取消',
    taskDeleted: '任务已删除',
    taskDeletedMessage: '定期维护任务"{title}"已删除',
    lastCompleted: '上次',
    weekdays: {
      sunday: '周日',
      monday: '周一',
      tuesday: '周二',
      wednesday: '周三',
      thursday: '周四',
      friday: '周五',
      saturday: '周六'
    },
    // Repeat rules
    repeatRules: {
      daily: '每天',
      weekly: '每周',
      monthly: '每月',
      yearly: '每年',
      unknown: '未知'
    },
    // Create dialog
    createDialog: {
      title: '创建定期维护任务',
      description: '创建一个重复性的维护任务，系统会自动提醒您按时完成。',
      taskTitle: '任务标题',
      taskTitleRequired: '任务标题 *',
      taskTitlePlaceholder: '例如：家庭大扫除',
      taskDescription: '任务描述',
      taskDescriptionPlaceholder: '详细描述这个维护任务的内容...',
      repeatRule: '重复规则',
      repeatInterval: '重复间隔',
      selectDate: '选择日期',
      intervalUnits: {
        days: '天',
        weeks: '周',
        months: '个月',
        years: '年'
      },
      cancel: '取消',
      create: '创建任务',
      creating: '创建中...',
      taskCreated: '任务已创建',
      taskCreatedMessage: '定期维护任务"{title}"已创建',
      createFailed: '创建失败',
      createFailedMessage: '无法创建定期维护任务'
    }
  },

  // 时间相关
  time: {
    justNow: '刚刚',
    minutesAgo: '{count}分钟前',
    hoursAgo: '{count}小时前',
    daysAgo: '{count}天前',
    weeksAgo: '{count}周前',
    monthsAgo: '{count}个月前',
    yearsAgo: '{count}年前',
    due: '到期',
    dueToday: '今天到期',
    dueTomorrow: '明天到期',
    daysLeft: '剩余{count}天',
    overdue: '已逾期',
    overdueBy: '逾期{count}天'
  }
}

// 英文翻译
const enTranslations: TranslationData = {
  // Navigation
  nav: {
    dashboard: 'Dashboard',
    inbox: 'Inbox',
    projects: 'Projects',
    areas: 'Areas',
    resources: 'Resources',
    archive: 'Archive',
    reviews: 'Reviews',
    settings: 'Settings'
  },

  // Categories
  categories: {
    main: 'Main',
    para: 'P.A.R.A.',
    tools: 'Tools'
  },

  // Common
  common: {
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    create: 'Create',
    edit: 'Edit',
    delete: 'Delete',
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    loading: 'Loading...',
    noData: 'No data',
    all: 'All',
    active: 'Active',
    completed: 'Completed',
    pending: 'Pending',
    inProgress: 'In Progress',
    paused: 'Paused',
    archived: 'Archived',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    templates: 'Templates',
    custom: 'Custom',
    customUnit: 'Custom unit',
    noUnit: 'No Unit',
    name: 'Name',
    enterName: 'Enter KPI name',
    currentValue: 'Current Value',
    targetValue: 'Target Value',
    unit: 'Unit',
    selectUnit: 'Select unit',
    direction: 'Direction',
    increase: 'Increase',
    decrease: 'Decrease',
    higherIsBetter: 'Higher is better',
    lowerIsBetter: 'Lower is better',
    trackingFrequency: 'Tracking Frequency',
    daily: 'Daily',
    weekly: 'Weekly',
    monthly: 'Monthly',
    quarterly: 'Quarterly',
    optional: 'Optional',
    creating: 'Creating...',
    updating: 'Updating...',
    createKPI: 'Create KPI',
    updateKPI: 'Update KPI',
    nameRequired: 'Name is required',
    nameTooLong: 'Name must be less than {max} characters',
    valueRequired: 'Value is required',
    valueInvalid: 'Value must be a valid number',
    valueNegative: 'Value cannot be negative',
    targetRequired: 'Target is required',
    targetInvalid: 'Target must be a valid number',
    targetNegative: 'Target cannot be negative',
    saving: 'Saving...',
    more: 'more',
    unknown: 'Unknown',
    project: 'project',
    area: 'area'
  },

  // Dashboard
  dashboard: {
    title: 'Dashboard',
    description: 'Overview of your productivity system and current progress',
    quickCapture: 'Quick Capture',
    todayTasks: "Today's Tasks",
    completedTasks: 'Completed {completed} / {total} tasks',
    upcomingProjects: 'Upcoming Deadlines',
    recentActivity: 'Recent Activity',
    activeProjects: 'Active Projects',
    totalAreas: 'Areas',
    totalResources: 'Resources',
    inboxItems: 'Inbox Items',
    currentlyActive: 'Currently active',
    ongoingResponsibilities: 'Ongoing responsibilities',
    savedForReference: 'Saved for reference',
    needsProcessing: 'Need processing',
    inboxProcessing: 'Inbox Processing',
    processInbox: 'Process Inbox',
    // Quick actions
    quickActions: 'Quick Actions',
    quickActionsDescription: 'Quick access to main features',
    // Statistics cards
    responsibilityAreas: 'Responsibility Areas',
    referenceResources: 'Reference Resources',
    // Main content areas
    todayTasksDescription: 'Tasks and todos that need to be completed today',
    upcomingProjectsDescription: 'Projects and deadlines that need attention soon',
    recentActivityDescription: 'Your latest updates and progress across all areas',
    // Weekly progress
    weeklyProgress: 'Weekly Progress',
    weeklyProgressDescription: 'This week\'s productivity metrics',
    taskCompletion: 'Task Completion',
    projectProgress: 'Project Progress'
  },

  // Quick Capture
  quickCapture: {
    title: 'Quick Capture',
    description: 'Instantly capture thoughts, tasks and ideas',
    placeholder: 'What are you thinking? ({type})',
    hint: 'Press Enter to capture, or use ⌘+Enter to quick save',
    saving: 'Saving...',
    capture: 'Capture',
    types: {
      note: 'Note',
      task: 'Task',
      idea: 'Idea'
    }
  },

  // Settings
  settings: {
    title: 'Settings',
    description: 'Configure your PaoLife application preferences and system settings',
    general: 'General',
    generalDescription: 'Basic application settings and preferences',
    displayName: 'Display Name',
    theme: 'Theme',
    language: 'Language',
    selectTheme: 'Select theme',
    selectLanguage: 'Select language',
    themes: {
      light: 'Light',
      dark: 'Dark',
      system: 'System'
    },
    languages: {
      zh: '中文',
      en: 'English'
    },
    save: 'Save',
    confirmReset: 'Are you sure you want to reset all settings? This action cannot be undone.',
    // Editor settings
    editor: {
      title: 'Editor',
      description: 'Configure Markdown editor behavior and appearance',
      mode: 'Edit Mode',
      selectMode: 'Select edit mode',
      modes: {
        ir: 'Instant Rendering',
        wysiwyg: 'WYSIWYG'
      },
      theme: 'Editor Theme',
      selectTheme: 'Select editor theme',
      themes: {
        dark: 'Dark',
        classic: 'Classic'
      },
      focusMode: 'Focus Mode',
      focusModeDescription: 'Hide distracting elements and focus on writing',
      autoSave: 'Auto Save',
      autoSaveDescription: 'Automatically save documents while editing',
      autoSaveInterval: 'Auto Save Interval',
      autoSaveIntervalDescription: 'Time interval for automatic saving',
      seconds: 'seconds'
    },
    // Notification messages
    notifications: {
      languageChanged: 'Language Changed',
      languageChangedMessage: 'Interface language has been successfully changed',
      themeChanged: 'Theme Changed',
      themeChangedMessage: 'Application theme has been successfully changed',
      usernameSaved: 'Username Saved',
      usernameSavedMessage: 'Display name has been successfully updated',
      settingsReset: 'Settings Reset',
      settingsResetMessage: 'All settings have been restored to default values',
      featureNotImplemented: 'Feature Not Implemented',
      clearCacheNotImplemented: 'Clear cache feature is under development',
      resetDataNotImplemented: 'Reset data feature is under development',
      exportSuccess: 'Export Successful',
      exportSuccessMessage: 'Settings data has been successfully exported',
      exportFailed: 'Export Failed',
      exportFailedMessage: 'An error occurred while exporting settings data',
      importSuccess: 'Import Successful',
      importSuccessMessage: 'Settings data has been successfully imported',
      importFailed: 'Import Failed',
      importFailedMessage: 'An error occurred while importing settings data, please check file format',
      backupNotImplemented: 'Backup feature is under development',
      backupSuccess: 'Backup Successful',
      backupSuccessMessage: 'Database backup has been successfully created',
      backupFailed: 'Backup Failed',
      backupFailedMessage: 'An error occurred while creating database backup',
      shortcutsNotImplemented: 'Shortcut management feature is under development',
      resourcePathNotImplemented: 'Resource library path setting feature is under development',
      resourcePathSuccess: 'Resource Path Updated',
      resourcePathSuccessMessage: 'Resource library path has been successfully changed',
      resourcePathFailed: 'Path Update Failed',
      resourcePathFailedMessage: 'An error occurred while updating resource library path'
    },
    // App settings
    app: {
      exitConfirm: 'Exit Confirmation',
      exitConfirmDescription: 'Show confirmation dialog when closing the application'
    },
    // Shortcuts settings
    shortcuts: {
      title: 'Shortcut Settings',
      description: 'Customize application keyboard shortcuts',
      instructions: 'Click "Edit" button, then press the key combination you want',
      recording: 'Recording...',
      edit: 'Edit',
      cancel: 'Cancel',
      reset: 'Reset',
      resetAll: 'Reset All',
      conflictTitle: 'Shortcut Conflict',
      conflictMessage: 'This key combination is already used by another function',
      updateSuccess: 'Shortcut Updated',
      updateSuccessMessage: 'Shortcut setting has been successfully saved',
      resetSuccess: 'Shortcut Reset',
      resetSuccessMessage: 'Shortcut has been restored to default setting',
      resetAllSuccess: 'All Shortcuts Reset',
      resetAllSuccessMessage: 'All shortcuts have been restored to default settings',
      categories: {
        navigation: 'Navigation',
        editing: 'Editing',
        project: 'Project',
        inbox: 'Inbox',
        general: 'General'
      },
      helpTitle: 'Shortcut Help',
      helpDescription: 'View all available keyboard shortcuts',
      tips: 'Usage Tips',
      tip1: 'Shortcuts are disabled when typing in input fields',
      tip2: 'You can customize shortcuts in settings',
      tip3: 'Press Escape to close most dialogs'
    },
    // P.A.R.A. Method settings
    para: {
      title: 'P.A.R.A. Method',
      description: 'Configure your P.A.R.A. methodology preferences',
      badge: 'Core',
      autoArchive: {
        title: 'Auto-archive completed projects',
        description: 'Automatically move completed projects to archive after specified days',
        unit: 'days'
      },
      weeklyReview: {
        title: 'Weekly review reminders',
        description: 'Get notified to perform weekly reviews',
        days: {
          sunday: 'Sunday',
          monday: 'Monday',
          friday: 'Friday'
        }
      },
      projectTemplate: {
        title: 'Default project template',
        description: 'Template to use when creating new projects',
        options: {
          basic: 'Basic',
          detailed: 'Detailed',
          agile: 'Agile'
        }
      }
    },
    // Data & Storage settings
    dataStorage: {
      title: 'Data & Storage',
      description: 'Manage your data, backups, and storage settings',
      stats: {
        projects: 'Projects',
        areas: 'Areas',
        totalItems: 'Total Items',
        backups: 'Backups'
      },
      actions: {
        exportData: 'Export Data',
        importData: 'Import Data',
        createBackup: 'Create Backup'
      }
    },
    // Advanced settings
    advanced: {
      title: 'Advanced',
      description: 'Shortcuts, paths and other advanced feature configuration',
      shortcuts: {
        title: 'Shortcut Management',
        description: 'Customize application shortcuts and keyboard operations',
        configure: 'Configure Shortcuts'
      },
      workspaceDirectory: {
        title: 'Workspace Directory',
        description: 'Storage location for application data and resources',
        readonly: 'Read Only',
        notSet: 'Not Set'
      },
      actions: {
        resetSettings: 'Reset Settings'
      }
    },
    // About information
    about: {
      title: 'About PaoLife',
      description: 'Application information and credits',
      info: {
        version: 'Version:',
        build: 'Build:',
        electron: 'Electron:',
        nodejs: 'Node.js:',
        license: 'License:'
      }
    },
    // Status labels
    status: {
      enabled: 'Enabled',
      disabled: 'Disabled'
    }
  },

  // First Time Setup
  firstTimeSetup: {
    title: 'Welcome to PaoLife',
    description: "Let's set up some basics to get you started",
    step1: {
      title: 'Step 1: Set Username',
      description: 'Please enter your username, this will be used to personalize your experience',
      usernameLabel: 'Username',
      usernamePlaceholder: 'Enter your username',
      nextButton: 'Next'
    },
    step2: {
      title: 'Step 2: Choose Workspace Directory',
      description: 'Choose a directory to store your notes and files',
      directoryLabel: 'Workspace Directory',
      directoryPlaceholder: 'Enter directory path or click browse...',
      browseButton: 'Browse',
      browsing: 'Selecting...',
      useDefault: 'Use Default Directory',
      clear: 'Clear',
      selected: 'Selected: {path}',
      previousButton: 'Previous',
      completeButton: 'Complete Setup'
    },
    notifications: {
      setupComplete: 'Setup Complete',
      welcomeMessage: 'Welcome to PaoLife, {username}!',
      setupFailed: 'Setup Failed',
      initializationError: 'Unable to initialize workspace directory, please try again',
      useDefaultDirectory: 'Use Default Directory',
      browserEnvironmentMessage: 'Using default workspace directory in browser environment: {directory}',
      directoryPickerFailed: 'Unable to open directory picker, using default directory: {directory}',
      usernameRequired: 'Please enter username',
      usernameEmpty: 'Username cannot be empty',
      directoryRequired: 'Please select workspace directory',
      directoryNeeded: 'A directory is needed to store your files',
      fileSystemInitFailed: 'File system initialization failed'
    },
    dialog: {
      selectDirectoryTitle: 'Select Workspace Directory',
      selectDirectoryButton: 'Select This Directory'
    }
  },

  // Pages translations
  pages: {
    projects: {
      title: 'Projects',
      description: 'Outcomes with specific deadlines and clear completion criteria.',
      newProject: 'New Project',
      searchPlaceholder: 'Search projects...',
      allAreas: 'All Areas',
      allStatus: 'All Status',
      sortBy: 'Sort by',
      viewMode: 'View Mode',
      grid: 'Grid',
      list: 'List',
      // Notification messages
      notifications: {
        createSuccess: 'Project Created Successfully',
        createSuccessMessage: 'Project "{name}" has been created',
        createFailed: 'Failed to Create Project',
        createFailedMessage: 'An unknown error occurred while creating the project',
        deleteConfirmTitle: 'Delete Project',
        deleteConfirmMessage: 'Are you sure you want to delete "{name}"? This action cannot be undone.',
        deleteSuccess: 'Project Deleted Successfully',
        deleteSuccessMessage: 'Project "{name}" has been deleted',
        deleteFailed: 'Failed to Delete Project',
        deleteFailedMessage: 'An unknown error occurred while deleting the project',
        archiveConfirmTitle: 'Archive Project',
        archiveConfirmMessage: 'Archive "{name}"? You can restore it from the archive later.',
        archiveSuccess: 'Project Archived',
        archiveSuccessMessage: '"{name}" has been successfully archived. You can find it in the archive page.',
        archiveFailed: 'Archive Failed',
        archiveFailedMessage: 'An error occurred while archiving the project. Please try again.'
      },
      kanban: 'Kanban',
      noProjects: 'No projects',
      createFirst: 'Create your first project',
      filters: {
        status: {
          notStarted: 'Not Started',
          inProgress: 'In Progress',
          atRisk: 'At Risk',
          paused: 'Paused',
          completed: 'Completed'
        },
        sort: {
          updated: 'Last Updated',
          name: 'Name',
          deadline: 'Deadline',
          progress: 'Progress'
        }
      },
      detail: {
        // Navigation
        navigation: {
          backToProjects: '← Back to Projects',
          backToArea: '← Back to {areaName}',
          backToAreaDefault: '← Back to Area'
        },
        // Error page
        notFound: {
          title: 'Project Not Found',
          description: 'The project you\'re looking for doesn\'t exist or has been deleted.',
          backButton: 'Back to Projects'
        },
        // Archived banner
        archivedBanner: {
          title: 'You are viewing an archived project',
          description: 'This project is in read-only mode. You can view all historical information but cannot make edits.'
        },
        // Project info labels
        projectInfo: {
          description: 'Description',
          goal: 'Project Goal',
          deliverable: 'Final Deliverable'
        },
        // Progress statistics
        progress: {
          overall: 'Overall Progress',
          totalTasks: 'Total Tasks',
          completed: 'Completed',
          overdue: 'Overdue',
          dueSoon: 'Due Soon'
        },
        // Countdown
        countdown: {
          title: 'Countdown',
          daysOverdue: 'Days Overdue',
          dueToday: 'Due Today',
          dayLeft: 'Day Left',
          daysLeft: 'Days Left'
        },
        // Date labels
        dates: {
          created: 'Created',
          deadline: 'Deadline',
          updated: 'Updated',
          area: 'Area'
        },
        // Action buttons
        actions: {
          title: 'Actions',
          editProject: 'Edit Project',
          archiveProject: 'Archive Project',
          deleteProject: 'Delete Project'
        },
        // Task management
        tasks: {
          title: 'Project Tasks',
          description: 'Manage tasks and subtasks for this project',
          hideFilters: 'Hide Filters',
          showFilters: 'Show Filters',
          addTask: 'Add Task',
          noTasks: 'No tasks yet',
          noTasksHint: 'Add your first task to get started',
          // Task detail panel
          taskDetails: 'Task Details',
          taskTitle: 'Task Title',
          enterTaskTitle: 'Enter task title...',
          taskDescription: 'Description',
          addTaskDescription: 'Add task description...',
          priority: 'Priority',
          priorityNone: 'None',
          priorityLow: 'Low',
          priorityMedium: 'Medium',
          priorityHigh: 'High',
          priorityCritical: 'Critical',
          status: 'Status',
          statusTodo: 'To Do',
          statusInProgress: 'In Progress',
          statusBlocked: 'Blocked',
          statusReview: 'Review',
          statusDone: 'Done',
          deadline: 'Deadline',
          estimatedHours: 'Estimated Hours',
          actualHours: 'Actual Hours',
          project: 'Project',
          noProject: 'No Project',
          area: 'Area',
          noArea: 'No Area',
          blockedBy: 'Blocked By',
          describeBlocking: 'Describe what\'s blocking this task...',
          subtask: 'Subtask',
          // Task filter
          filter: {
            searchPlaceholder: 'Search tasks by name or description...',
            filters: 'Filters',
            filterOptions: 'Filter Options',
            allStatus: 'All Status',
            allPriority: 'All Priority',
            createdDate: 'Created Date',
            name: 'Name',
            dueDate: 'Due Date',
            progress: 'Progress',
            allDates: 'All Dates',
            overdue: 'Overdue',
            dueToday: 'Due Today',
            dueThisWeek: 'Due This Week',
            noDeadline: 'No Deadline',
            showCompleted: 'Show Completed Tasks',
            clearAll: 'Clear All Filters',
            sort: 'Sort',
            sortBy: 'Sort By',
            sortAscending: 'Sort Ascending',
            sortDescending: 'Sort Descending'
          },
          // Hierarchy controls
          hierarchyControls: 'Hierarchy Controls',
          hierarchyStats: '{tasks} tasks, {levels} levels',
          expandAll: 'Expand All',
          collapseAll: 'Collapse All',
          level1: 'Level 1',
          level2: 'Level 2',
          // Task item labels
          overdue: 'Overdue',
          due: 'Due',
          est: 'Est',
          addSubtask: 'Add subtask',
          subtasks: 'Subtasks',
          subtaskCount: 'subtask',
          // Hierarchy control stats
          completion: 'Completion',
          expanded: 'Expanded',
          maxDepth: 'Max Depth'
        },
        // Notes functionality
        notes: {
          title: 'Project Notes',
          edit: 'Edit',
          cancel: 'Cancel',
          save: 'Save Notes',
          placeholder: 'Add project notes, meeting minutes, or important information...',
          empty: 'No notes yet. Click Edit to add project notes.'
        },
        // Quick actions
        quickActions: {
          title: 'Quick Actions',
          addTask: 'Add Task',
          linkResource: 'Link Resource',
          viewAnalytics: 'View Analytics'
        },
        // Confirmation dialogs
        confirmDialogs: {
          deleteProject: {
            title: 'Delete Project',
            description: 'Are you sure you want to delete "{name}"? This action cannot be undone.',
            confirm: 'Delete',
            cancel: 'Cancel'
          },
          archiveProject: {
            title: 'Archive Project',
            description: 'Archive "{name}"? You can restore it later from the archive.',
            confirm: 'Archive',
            cancel: 'Cancel'
          },
          batchCompleteTask: {
            title: 'Batch Complete Tasks',
            description: 'Mark this task and all its {count} subtasks as completed?',
            confirm: 'Yes',
            cancel: 'No'
          }
        },
        // Notification messages
        notifications: {
          taskCompleted: 'Tasks Completed',
          taskCompletedMessage: 'Successfully completed {count} tasks',
          updateFailed: 'Update Failed',
          updateFailedMessage: 'Some task status updates failed, please try again',
          operationFailed: 'Operation Failed',
          operationFailedMessage: 'Task status update failed, please try again'
        },
        // Create task dialog
        createTaskDialog: {
          title: {
            create: 'Create New Task',
            edit: 'Edit Task',
            createSubtask: 'Create Subtask'
          },
          description: {
            create: 'Create a new task to track your work.',
            createSubtask: 'Create a subtask under the selected task.'
          },
          fields: {
            content: 'Task Content',
            contentRequired: 'Task Content *',
            contentPlaceholder: 'What needs to be done?',
            description: 'Description',
            descriptionPlaceholder: 'Additional details about this task...',
            priority: 'Priority',
            priorityPlaceholder: 'Select priority...',
            deadline: 'Deadline',
            project: 'Project',
            projectPlaceholder: 'Select project...',
            noProject: 'No project',
            area: 'Area',
            areaPlaceholder: 'Select area...',
            noArea: 'No area',
            repeat: 'Repeat',
            repeatPlaceholder: 'Select repeat pattern...',
            inheritedFromParent: 'inherited from parent'
          },
          buttons: {
            cancel: 'Cancel',
            create: 'Create Task',
            update: 'Update Task',
            creating: 'Creating...',
            updating: 'Updating...'
          },
          priority: {
            none: 'None',
            low: 'Low',
            medium: 'Medium',
            high: 'High',
            urgent: 'Urgent'
          },
          repeat: {
            none: 'No repeat',
            daily: 'Daily',
            weekly: 'Weekly',
            monthly: 'Monthly'
          }
        },
        // Project deliverables
        deliverables: {
          title: 'Project Deliverables',
          description: 'Track and manage project outcomes and deliverables',
          addDeliverable: 'Add Deliverable',
          noDeliverables: 'No deliverables yet',
          noDeliverablesHint: 'Add your first deliverable to start tracking project outcomes',
          // Create deliverable dialog
          createDialog: {
            title: 'Create New Deliverable',
            description: 'Define a project outcome or deliverable to track progress and completion.',
            titleLabel: 'Title',
            titleRequired: 'Title *',
            titlePlaceholder: 'e.g., User Manual Documentation, MVP Release, Performance Metrics',
            descriptionLabel: 'Description',
            descriptionPlaceholder: 'Detailed description of what needs to be delivered...',
            typeLabel: 'Type',
            statusLabel: 'Status',
            priorityLabel: 'Priority',
            plannedDateLabel: 'Planned Date',
            deadlineLabel: 'Deadline',
            contentLabel: 'Additional Details',
            contentPlaceholder: 'Acceptance criteria, requirements, notes...',
            cancel: 'Cancel',
            create: 'Create Deliverable',
            creating: 'Creating...'
          },
          status: {
            planned: 'Planned',
            notStarted: 'Not Started',
            inProgress: 'In Progress',
            review: 'Under Review',
            completed: 'Completed',
            onHold: 'On Hold',
            cancelled: 'Cancelled'
          },
          statusDescriptions: {
            planned: 'Not yet started',
            inProgress: 'Currently being worked on',
            review: 'Under review or testing',
            completed: 'Work finished, ready for delivery',
            cancelled: 'Cancelled or no longer needed'
          },
          priority: {
            low: 'Low',
            medium: 'Medium',
            high: 'High',
            critical: 'Critical'
          },
          type: {
            document: 'Document',
            software: 'Software',
            report: 'Report',
            presentation: 'Presentation',
            prototype: 'Prototype',
            feature: 'Feature',
            milestone: 'Milestone',
            deliverable: 'Deliverable',
            other: 'Other'
          },
          typeDescriptions: {
            document: 'Written deliverable like reports, specifications',
            software: 'Software applications or systems',
            report: 'Analysis reports and documentation',
            presentation: 'Presentations and demos',
            prototype: 'Prototypes and proof of concepts',
            other: 'Other types of deliverables'
          },
          actions: {
            edit: 'Edit',
            delete: 'Delete',
            markComplete: 'Mark Complete',
            markInProgress: 'Mark In Progress'
          },
          due: 'Due',
          created: 'Created'
        },
        // Linked resources
        resources: {
          title: 'Linked Resources',
          description: 'Manage resources and documents related to this project',
          linkResource: 'Link Resource',
          noResources: 'No linked resources',
          noResourcesHint: 'Link resources for quick access to project-related documents',
          // Link resource dialog
          linkDialog: {
            titleProject: 'Link Resources to Project',
            titleArea: 'Link Resources to Area',
            descriptionProject: 'Select resources from your knowledge base to link to this project. Linked resources will appear in the project details and create bidirectional connections.',
            descriptionArea: 'Select resources from your knowledge base to link to this area. Linked resources will appear in the area details and create bidirectional connections.',
            searchPlaceholder: 'Search resources...',
            noResourcesFound: 'No resources found',
            noResourcesFoundHint: 'Try adjusting your search criteria or check your knowledge base',
            selectedCount: '{count} resources selected',
            linkSelected: 'Link Selected Resources',
            linking: 'Linking...',
            cancel: 'Cancel',
            showFileSystem: 'Show File System Resources',
            hideFileSystem: 'Hide File System Resources',
            fileSystemResources: 'File System Resources',
            databaseResources: 'Database Resources',
            selectAll: 'Select all ({count} {files})',
            file: 'file',
            files: 'files'
          },
          actions: {
            open: 'Open',
            unlink: 'Unlink'
          },
          confirmDialogs: {
            unlinkResource: {
              title: 'Unlink Resource',
              message: 'Are you sure you want to unlink "{name}" from this {type}?',
              confirmText: 'Unlink',
              cancelText: 'Cancel'
            }
          },
          linkedOn: 'Linked on',
          notifications: {
            linkSuccess: 'Resources linked successfully',
            linkSuccessMessage: '{count} resource{plural} linked to {type}',
            linkError: 'Failed to link resources',
            unlinkSuccess: 'Resource unlinked successfully',
            unlinkSuccessMessage: 'Resource "{name}" has been unlinked',
            unlinkError: 'Failed to unlink resource',
            fileNotFound: 'File not found',
            fileNotFoundMessage: 'The resource file could not be found at: {path}'
          }
        },
        projectKPI: {
          title: 'Project KPIs',
          description: 'Track and manage project key performance indicators',
          batchRecord: 'Batch Record',
          addKPI: 'Add KPI',
          noKPIs: 'No KPIs',
          noKPIsDescription: 'Start creating KPIs to track project progress',
          createFirst: 'Create First KPI',
          createFirstKPI: 'Create your first KPI to start tracking',
          noKPIsToTrack: 'No KPIs to track',
          createKPIsFirst: 'Please create some KPIs first',
          loadError: 'Failed to load KPIs',
          updateKPI: 'Update {name}',
          recordNewValue: 'Record new value',
          newValue: 'New Value',
          currentValue: 'Current value: {value}',
          noteOptional: 'Note (Optional)',
          addNote: 'Add note...',
          record: 'Record',
          recording: 'Recording...',
          editKPI: 'Edit KPI',
          showHistory: 'Show History',
          hideHistory: 'Hide History',
          progress: 'Progress',
          updateSuccess: '{name} updated successfully',
          updateError: 'Failed to update KPI',
          deleteSuccess: 'Deleted successfully',
          deleteError: 'Failed to delete',
          noHistoryRecords: 'No history records',
          batchRecordTitle: 'Batch Record KPIs',
          batchRecordDescription: 'Record values for all KPIs at once',
          globalNote: 'Global Note',
          globalNotePlaceholder: 'Add a unified note for all KPIs...',
          recentHistory: 'Recent History',
          applyToAll: 'Apply to All KPIs',
          // Batch record related
          batch: {
            selectAtLeastOne: 'Please select at least one KPI to record',
            recordSuccess: 'Successfully recorded {count} KPI values',
            recordPartialSuccess: '{success} records created, {failed} failed',
            recordError: 'Failed to create batch records',
            recording: 'Recording...',
            recordValues: 'Record Values ({count})',
            recordingTime: 'Recording Time',
            kpiRecords: 'KPI Records ({count} enabled)',
            enableAll: 'Enable All',
            disableAll: 'Disable All',
            current: 'Current',
            newValue: 'New Value',
            note: 'Note',
            optionalNote: 'Optional note',
            enterValue: 'Enter value'
          },
          // KPI statistics cards
          stats: {
            totalKPIs: 'Total KPIs',
            onTrack: 'On Track',
            atRisk: 'At Risk',
            avgProgress: 'Avg Progress'
          }
        }
      }
    },
    areas: {
      title: 'Areas',
      description: 'Ongoing responsibilities and standards to maintain over time.',
      newArea: 'New Area',
      searchPlaceholder: 'Search areas...',
      allStatus: 'All Status',
      noAreas: 'No areas',
      createFirst: 'Create your first area',
      filters: {
        status: {
          active: 'Active',
          needsAttention: 'Needs Attention',
          onHold: 'On Hold',
          reviewRequired: 'Review Required'
        },
        sort: {
          updated: 'Last Updated',
          name: 'Name',
          status: 'Status'
        }
      },
      detail: {
        backToAreas: '← Back to Areas',
        actions: 'Actions',
        editArea: 'Edit Area',
        archiveArea: 'Archive Area',
        deleteArea: 'Delete Area',
        notFound: 'Area Not Found',
        notFoundDescription: "The area you're looking for doesn't exist or has been deleted.",
        backToAreasButton: 'Back to Areas',
        areaStatus: 'Area Status',
        standardsChecklist: 'Standards Checklist',
        completed: 'Completed',
        activeHabits: 'Active Habits',
        areaStandard: 'Area Standard',
        areaStandardDescription: 'The level you want to maintain in this area',
        standardsChecklistDescription: 'Track your progress against area standards',
        addItem: 'Add Item',
        noChecklistItems: 'No checklist items yet',
        addChecklistHint: 'Add items to track your area standards',
        areaNotes: 'Area Notes',
        edit: 'Edit',
        // New translation keys
        loading: 'Loading area data...',
        loadingFailed: 'Loading Failed',
        loadingFailedMessage: 'An error occurred while loading area data.',
        deleteConfirmTitle: 'Delete Area',
        deleteConfirmMessage: 'Are you sure you want to delete "{name}"? This action cannot be undone.',
        archiveConfirmTitle: 'Archive Area',
        archiveConfirmMessage: 'Archive "{name}"? You can restore it from the archive later.',
        archiveSuccessTitle: 'Area Archived',
        archiveSuccessMessage: '"{name}" has been successfully archived. You can find it in the archive page.',
        archiveFailedTitle: 'Archive Failed',
        archiveFailedMessage: 'An error occurred while archiving the area. Please try again.',
        projectCreateSuccessTitle: 'Project Created Successfully',
        projectCreateSuccessMessage: 'Project "{name}" has been created and linked to the current area',
        projectCreateFailedTitle: 'Project Creation Failed',
        projectCreateFailedMessage: 'Failed to create project',
        habitDeleteConfirmTitle: 'Delete Habit',
        habitDeleteConfirmMessage: 'Are you sure you want to delete this habit? This action cannot be undone.',
        // Interface text
        returnToAreasList: 'Return to areas list',
        reviewFrequency: 'Review Frequency:',
        createdAt: 'Created:',
        updatedAt: 'Updated:',
        weekly: 'Weekly',
        monthly: 'Monthly',
        quarterly: 'Quarterly',
        statusActive: 'Active',
        statusNeedsAttention: 'Needs Attention',
        statusOnHold: 'On Hold',
        statusReviewRequired: 'Review Required',
        archivedBannerTitle: 'You are viewing an archived area',
        archivedBannerMessage: 'This area is in read-only mode. You can view all historical information but cannot make edits.',
        standardPlaceholder: 'e.g., Maintain monthly savings rate of at least 30% and pay all bills on time.',
        save: 'Save',
        cancel: 'Cancel',
        // Layout areas
        executionArea: 'Execution Area',
        planningResourceArea: 'Planning & Resource Area',
        habitTracker: 'Habit Tracker',
        keyMetrics: 'Key Metrics',
        recurringTasks: 'Recurring Maintenance Tasks',
        activeChecklists: 'Active Checklists',
        relatedProjects: 'Related Projects',
        relatedResources: 'Related Resources',
        checklistTemplates: 'Checklist Templates',
        // Related resources
        resources: {
          title: 'Related Resources',
          description: 'Manage resources and documents related to this area',
          linkResource: 'Link Resource',
          noResources: 'No related resources',
          noResourcesHint: 'Link resources for quick access to area-related documents',
          actions: {
            open: 'Open',
            unlink: 'Unlink'
          },
          confirmDialogs: {
            unlinkResource: {
              title: 'Unlink Resource',
              message: 'Are you sure you want to unlink "{name}" from this {type}?',
              confirmText: 'Unlink',
              cancelText: 'Cancel'
            }
          },
          linkedOn: 'Linked on',
          notifications: {
            linkSuccess: 'Resources linked successfully',
            linkSuccessMessage: '{count} resource{plural} linked to {type}',
            linkError: 'Failed to link resources',
            unlinkSuccess: 'Resource unlinked successfully',
            unlinkSuccessMessage: 'Resource "{name}" has been unlinked',
            unlinkError: 'Failed to unlink resource',
            fileNotFound: 'File not found',
            fileNotFoundMessage: 'The resource file could not be found at: {path}'
          }
        }
      },
      cancel: 'Cancel',
      saveNotes: 'Save Notes',
      notesPlaceholder: 'Add area notes, reflections, or important information...',
      noNotesYet: 'No notes yet. Click Edit to add area notes.',
      areaHabits: 'Area Habits',
      areaHabitsDescription: 'Track daily habits related to this area',
      addHabit: 'Add Habit',
      noHabitsYet: 'No habits yet',
      addFirstHabit: 'Add your first habit to start tracking',
      keyInformation: 'Key Information',
      reviewFrequency: 'Review Frequency',
      created: 'Created',
      lastUpdated: 'Last Updated',
      weekly: 'Weekly',
      quickActions: 'Quick Actions',
      linkProject: 'Link Project',
      viewAnalytics: 'View Analytics',
      relatedProjectsDescription: 'Projects associated with this area',
      confirmDeleteArea:
        'Are you sure you want to delete "{name}"? This action cannot be undone.',
      confirmArchiveArea: 'Archive "{name}"? You can restore it later from the archive.',
      confirmDeleteHabit: 'Are you sure you want to delete this habit?',
      newChecklistItem: 'New checklist item',
      reviewStandardsWeekly: 'Review area standards weekly',
      updateHabitsTracking: 'Update habits tracking',
      assessProgress: 'Assess progress against goals',
      // {{ AURA-X: Add - 添加缺失的领域详情页英文翻译. Approval: 寸止(ID:1738157400). }}
      keyMetrics: 'Key Metrics',
      keyMetricsDescription: 'Record and track important data metrics related to this area',
      addMetric: 'Add Metric',
      createMetric: 'Create New Metric',
      createMetricDescription: 'Add a new key metric to track this area\'s performance',
      metricName: 'Metric Name',
      metricNamePlaceholder: 'e.g.: Weight, Savings Rate, Exercise Duration',
      metricValue: 'Current Value',
      metricValuePlaceholder: '75',
      metricUnit: 'Unit',
      metricUnitPlaceholder: 'kg, %, hours',
      noMetricsYet: 'No metrics added yet',
      addFirstMetric: 'Add your first key metric to start tracking',
      editMetric: 'Edit Metric',
      editMetricDescription: 'Update the metric name, value or unit',
      deleteMetric: 'Delete Metric',
      saveChanges: 'Save Changes',
      // {{ AURA-X: Add - Key Metrics模块国际化英文翻译键. Approval: 寸止(ID:1738157400). }}
      kpiManagement: {
        title: 'Key Metrics',
        description: 'Track and manage your area\'s key performance indicators',
        loadingMetrics: 'Loading metrics...',
        noMetricsYet: 'No metrics yet',
        createFirstMetric: 'Create your first metric to start tracking progress',
        dailyHabits: '🎯 Daily Habits',
        keyMetricsSection: '📊 Key Metrics',
        noMetricsToTrack: 'No metrics to track',
        createMetricsFirst: 'Create metrics first to start recording data',
        batchRecordButton: 'Batch Record',
        addBasic: 'Add Basic',
        setDirections: 'Set Directions',
        overviewTitle: 'KPI Overview',
        overviewDescription: 'Overall overview and current status of key performance indicators',
        setMetricDirections: 'Set Metric Directions',
        setDirectionsDescription: 'Choose whether each metric should increase or decrease to reach its target. The system has made suggestions based on metric names and units.',
        noMetricsFound: 'No metrics found in this area',
        suggestionAvailable: 'Suggestion Available',
        suggested: 'Suggested',
        decrease: 'Decrease',
        increase: 'Increase',
        // New translation keys
        totalMetrics: 'Total Metrics',
        improving: 'Improving',
        needsFocus: 'Needs Focus',
        avgProgress: 'Avg Progress',
        manageHabits: 'Manage Habits',
        addMetric: 'Add Metric',
        noMetrics: 'No Metrics',
        noMetricsDescription: 'Create your first metric to start tracking progress',
        createFirst: 'Create First',
        setupHabits: 'Setup Habits',
        relatedHabits: 'Related Habits',
        dashboard: 'Dashboard',
        trends: 'Trends',
        loadError: 'Load failed',
        createSuccess: 'Created successfully',
        createError: 'Create failed',
        updateSuccess: 'Updated successfully',
        updateError: 'Update failed',
        deleteSuccess: 'Deleted successfully',
        deleteError: 'Delete failed',
        applyAll: 'Apply All',
        cancel: 'Cancel',
        updateDirections: 'Update Directions',
        updating: 'Updating...',
        directionsUpdated: 'Directions Updated',
        successfullyUpdated: 'Successfully updated directions for {count} metrics',
        partialUpdate: 'Partial Update',
        failedToUpdate: '{count} metrics failed to update',
        updateFailed: 'Update Failed',
        failedToUpdateDirections: 'Failed to update metric directions',
        suggestedChanges: '{count} metrics have suggested direction changes',
        kpiChart: {
          title: 'KPI Performance Overview',
          description: 'Visual overview of all key performance indicators',
          kpisCount: '{count} KPIs',
          achieved: 'Achieved',
          onTrack: 'On Track',
          atRisk: 'At Risk',
          behind: 'Behind',
          noTarget: 'No Target',
          avgProgress: 'Avg Progress',
          individualProgress: 'Individual KPI Progress',
          unknown: 'Unknown'
        },
        kpiTrends: {
          title: 'KPI Trends & Analysis',
          description: 'Performance trends and actionable insights',
          kpisCount: '{count} KPIs',
          export: 'Export',
          days7: '7 days',
          days30: '30 days',
          days90: '90 days',
          current: 'Current',
          target: 'Target',
          trend: 'Trend',
          recommendation: '💡 Recommendation',
          keyInsights: '📊 Key Insights',
          improving: 'Improving',
          stable: 'Stable',
          declining: 'Declining',
          improvingDesc: 'KPIs showing positive trends',
          stableDesc: 'KPIs maintaining performance',
          decliningDesc: 'KPIs needing attention',
          noData: 'no-data',
          up: 'Up',
          down: 'Down',
          noHistoricalData: 'No historical data available',
          performanceStable: 'Performance is stable. Continue current approach.',
          excellentImprovement: 'Excellent improvement! Consider scaling successful strategies.',
          goodProgress: 'Good progress. Maintain current momentum.',
          significantDecline: 'Significant decline. Immediate action required.',
          slightDecline: 'Slight decline. Review and adjust strategy.'
        },
        areaAnalytics: {
          title: 'Area Analytics Dashboard',
          description: 'Deep insights into your area performance and trends',
          analyzingData: 'Analyzing your area data...',
          totalMetrics: 'Total Metrics',
          active: 'Active',
          habits: 'Habits',
          avgProgress: 'Avg Progress',
          overallProgress: 'Overall Progress',
          complete: 'Complete',
          topPerformers: 'Top Performers',
          needsAttention: 'Needs Attention',
          noDataAvailable: 'No data available',
          allMetricsOnTrack: 'All metrics on track!',
          review: 'Review',
          weeklyActivityTrends: 'Weekly Activity Trends',
          weeklyTrendsDesc: 'Number of records created each day this week',
          categoryDistribution: 'Category Distribution',
          priorityDistribution: 'Priority Distribution',
          keyInsights: 'Key Insights',
          recommendations: 'Recommendations',
          addMoreDataForInsights: 'Add more data to generate insights',
          greatJobKeepUp: 'Great job! Keep up the current approach.',
          excellentHabitConsistency: 'Excellent habit consistency! {count} habits are performing well.',
          habitFormationNeedsAttention: 'Habit formation needs attention. Consider reducing the number of habits or adjusting targets.',
          focusOnCoreHabits: 'Focus on 1-2 core habits until they become automatic.',
          outstandingPerformance: 'Outstanding performance across all metrics!',
          considerChallengingTargets: 'Consider setting more challenging targets to continue growth.',
          severalMetricsBelowTarget: 'Several metrics are below target. Time for strategy review.',
          identifyTopPriorityMetrics: 'Identify the top 3 priority metrics and focus your efforts there.',
          increaseTrackingFrequency: 'Increase tracking frequency to get better insights.',
          tabs: {
            overview: 'Overview',
            trends: 'Trends',
            distribution: 'Distribution',
            insights: 'Insights'
          }
        },
        tabs: {
          overview: 'Overview',
          records: 'Records',
          dashboard: 'Dashboard',
          charts: 'Charts',
          trends: 'Trends',
          analytics: 'Analytics'
        }
      },
      projectKPI: {
        title: 'Key Performance Indicators',
        description: 'Manage and monitor your key performance indicators',
        addKPI: 'Add KPI',
        overview: 'KPI Overview',
        noKPIsDefined: 'No KPIs defined yet',
        // New translation keys
        totalKPIs: 'Total KPIs',
        onTrack: 'On Track',
        needsAttention: 'Needs Attention',
        avgProgress: 'Avg Progress',
        batchRecord: 'Batch Record',
        noKPIs: 'No KPIs',
        noKPIsDescription: 'Create your first KPI to start tracking project progress',
        createFirst: 'Create First',
        dashboard: 'Dashboard',
        trends: 'Trends',
        loadError: 'Load failed',
        createSuccess: 'Created successfully',
        createError: 'Create failed',
        updateSuccess: 'Updated successfully',
        updateError: 'Update failed',
        deleteSuccess: 'Deleted successfully',
        deleteError: 'Delete failed',
        addKPIsToTrack: 'Add key performance indicators to track project success',
        noKPIsToTrack: 'No KPIs to track',
        createKPIsFirst: 'Create KPIs first to start recording data',
        tabs: {
          overview: 'Overview',
          records: 'Records',
          dashboard: 'Dashboard',
          charts: 'Charts',
          trends: 'Trends'
        },
        // New quick input related translations
        updateKPI: 'Update {name}',
        recordNewValue: 'Record a new value for this KPI',
        newValue: 'New Value',
        currentValue: 'Current: {value}',
        noteOptional: 'Note (optional)',
        addNote: 'Add a note about this update...',
        recording: 'Recording...',
        record: 'Record',
        editKPI: 'Edit KPI',
        showHistory: 'Show History',
        hideHistory: 'Hide History',
        progress: 'Progress',
        recentHistory: 'Recent History',
        noHistoryRecords: 'No history records yet',
        moreRecords: '+{count} more records',
        // Batch record related translations
        batchRecordTitle: 'Batch Record KPI Values',
        batchRecordDescription: 'Record new values for multiple KPIs at once. You can enable/disable individual KPIs and add notes.',
        globalNote: 'Global Note',
        globalNotePlaceholder: 'Enter a note to apply to all enabled KPIs...',
        applyToAll: 'Apply to All'
      },
      habitTracker: {
        dailyHabitTracker: 'Daily Habit Tracker',
        checkIn: 'Check In',
        doneToday: 'Done Today',
        currentStreak: 'Current Streak',
        thisWeek: 'This Week',
        bestStreak: 'Best Streak',
        total: 'Total',
        checkInFailed: 'Check-in Failed',
        failedToRecord: 'Failed to record habit completion'
      },
      editRecord: {
        title: 'Edit Record',
        value: 'Value',
        valuePlaceholder: 'Enter value',
        note: 'Note',
        notePlaceholder: 'Add a note...',
        cancel: 'Cancel',
        saveChanges: 'Save Changes',
        saving: 'Saving...',
        recordUpdated: 'Record Updated',
        recordUpdatedMessage: 'Metric record has been updated successfully',
        failedToUpdate: 'Failed to Update',
        edit: 'Edit',
        delete: 'Delete'
      },
      categories: {
        uncategorized: 'Uncategorized',
        health: 'Health',
        fitness: 'Fitness',
        work: 'Work',
        personal: 'Personal',
        finance: 'Finance',
        learning: 'Learning'
      },
      priorities: {
        low: 'Low',
        medium: 'Medium',
        high: 'High'
      },
      metricHistory: {
        title: 'History',
        titleWithName: 'History - {name}',
        description: 'Recent data points and changes',
        loadingRecords: 'Loading records...',
        noRecordsYet: 'No records yet',
        startRecording: 'Start recording data to see history'
      },
      kpiDashboard: {
        title: 'KPI Dashboard',
        noKPIsToDisplay: 'No KPIs to display',
        addKPIsToSee: 'Add KPIs to see dashboard metrics',
        realTimeOverview: 'Real-time performance overview and key insights',
        healthScore: 'Health Score',
        overallHealthScore: 'Overall KPI Health Score',
        achieved: 'Achieved',
        onTrack: 'On Track',
        atRisk: 'At Risk',
        behind: 'Behind',
        topPerformer: 'Top Performer',
        needsAttention: 'Needs Attention',
        quickActions: '💡 Quick Actions',
        reviewUnderperforming: 'Review {count} underperforming KPI{plural}',
        celebrateAchieved: 'Celebrate {count} achieved goal{plural}!',
        averageProgress: 'Average Progress',
        healthLabels: {
          excellent: 'Excellent',
          good: 'Good',
          fair: 'Fair',
          needsImprovement: 'Needs Improvement',
          noData: 'No Data'
        },
        statusDescriptions: {
          achieved: '100% Complete',
          onTrack: '75%+ Complete',
          atRisk: '50-74% Complete',
          behind: '<50% Complete'
        }
      },
      checklistTemplates: 'Checklist Templates',
      checklistTemplatesDescription: 'Create reusable standard checklist templates',
      createTemplate: 'Create Template',
      createChecklistTemplate: 'Create Checklist Template',
      createTemplateDescription: 'Create a reusable checklist template',
      templateName: 'Template Name',
      templateNamePlaceholder: 'e.g.: Weekly Home Cleaning Checklist',
      templateItems: 'Checklist Items',
      itemPlaceholder: 'Enter checklist item...',
      noItemsYet: 'No items added yet',
      templates: 'Templates',
      noTemplatesYet: 'No templates created yet',
      createFirstTemplate: 'Create your first checklist template',
      items: 'items',
      useTemplate: 'Use This Template',
      editTemplate: 'Edit Template',
      deleteTemplate: 'Delete Template',
      activeChecklists: 'Active Checklists',
      completedOn: 'Completed on',
      editTemplateDescription: 'Modify the template name and items',
      standardPlaceholder: 'e.g.: Maintain monthly savings rate of at least 30% and pay all bills on time.',
      noStandardSet: 'No area standard set yet. Click edit to define success standards for this area.',
      saveStandard: 'Save Standard',
      // Area detail page specific
      checklistTemplateManagement: 'Checklist Template Management'
    },
    dialog: {
      createTitle: 'Create New Area',
      editTitle: 'Edit Area',
      description: 'Areas are ongoing responsibilities and standards to maintain over time.',
      areaName: 'Area Name',
      areaNameRequired: 'Area Name *',
      areaNamePlaceholder: 'Enter area name...',
      descriptionLabel: 'Description',
      descriptionPlaceholder: 'Describe what this area encompasses...',
      standardLabel: 'Standard to Maintain',
      standardPlaceholder: 'What standard or level do you want to maintain in this area?',
      standardExample:
        'Example: "Exercise 3x per week, maintain healthy weight, get 7+ hours sleep"',
      status: 'Status',
      reviewFrequency: 'Review Frequency',
      areaExamples: 'Area Examples',
      cancel: 'Cancel',
      create: 'Create Area',
      update: 'Update Area',
      creating: 'Creating...',
      updating: 'Updating...',
      reviewFrequencyOptions: {
        daily: 'Daily',
        weekly: 'Weekly',
        monthly: 'Monthly',
        quarterly: 'Quarterly'
      }
    },
    card: {
      standard: 'Standard',
      habitCompletion: 'Habit Completion',
      relatedProjects: 'Related Projects',
      activeHabits: 'Active Habits',
      review: 'Review',
      updated: 'Updated',
      habits: 'habits',
      viewDetails: 'View Details',
      editArea: 'Edit Area',
      archiveArea: 'Archive Area',
      deleteArea: 'Delete Area'
    },
    habits: {
      dialog: {
        createTitle: 'Create New Habit',
        editTitle: 'Edit Habit',
        description: 'Build positive habits and track your progress over time.',
        habitName: 'Habit Name',
        habitNameRequired: 'Habit Name *',
        habitNamePlaceholder: 'Enter habit name...',
        descriptionLabel: 'Description',
        descriptionPlaceholder: 'Describe this habit...',
        frequency: 'Frequency',
        target: 'Target (Optional)',
        targetPlaceholder: 'e.g., 30',
        unit: 'Unit (Optional)',
        unitPlaceholder: 'e.g., minutes',
        color: 'Color',
        associatedArea: 'Associated Area',
        selectAreaPlaceholder: 'Select an area...',
        noArea: 'No area',
        habitExamples: 'Habit Examples',
        cancel: 'Cancel',
        create: 'Create Habit',
        update: 'Update Habit',
        creating: 'Creating...',
        updating: 'Updating...',
        frequencyOptions: {
          daily: 'Daily',
          weekly: 'Weekly',
          monthly: 'Monthly'
        },
        frequencyDescriptions: {
          daily: 'Every day',
          weekly: 'Once per week',
          monthly: 'Once per month'
        }
      },
      // Mini habit tracker
      miniTracker: {
        title: 'Habit Tracker',
        description: '{monthName} · Click date to check in',
        addButton: 'Add',
        noHabits: 'No habits yet',
        createFirstHabitHint: 'Click "Add" to start building good habits',
        deleteHabit: 'Delete Habit',
        deleteConfirm: 'Are you sure you want to delete this habit? This action cannot be undone.',
        target: 'Target',
        daily: 'Daily',
        // Tooltip texts
        futureDate: 'Future date',
        noRecord: 'No record',
        completed: 'Completed',
        notCompleted: 'Not completed',
        completedWithValue: 'Completed {value}{unit} / Target {target}{unit} ({progress}%)',
        targetWithUnit: 'Target {target}{unit}',
        clickToModify: 'Click to modify value',
        clickToToggle: 'Click to toggle status'
      },
      // Habit type explanations for create dialog
      typeExplanation: {
        title: 'Habit Type Explanation',
        currentSetting: 'Current setting will create:',
        numericHabit: 'Numeric Habit',
        booleanHabit: 'Boolean Habit',
        numericDescription: 'Target value > 1 and has unit',
        booleanDescription: 'Target value ≤ 1 or no unit',
        numericExample: 'e.g., Target 8 cups, Unit "cups" → Can record specific quantities, supports partial completion display',
        booleanExample: 'e.g., Meditation, Early rising → Simple completed/not completed status',
        numericFeature: 'Supports recording specific quantities, partial completion shows blue, 100% completion shows green',
        booleanFeature: 'Simple completed/not completed status, completion shows green'
      },

      // Resources page additions
      resources: {
        title: 'Resources',
        description: 'Topics or themes of ongoing interest for future reference.',
        exitFocusModeTooltip: 'Exit Focus Mode (F11)',
        enterFocusModeTooltip: 'Enter Focus Mode (F11)',
        exitFocusMode: 'Exit Focus',
        focusMode: 'Focus Mode',
        markdownEditor: 'Markdown Editor',
        selectMarkdownToEdit: 'Please select a Markdown file to edit',
        notMarkdown: 'Current file is not Markdown format',
        chooseMarkdownFromTree: 'Choose a Markdown file from the left tree',
        addToLibraryWithName: 'Add "{name}" to library',
        file: 'File',
        folder: 'Folder',
        createDialog: {
          title: 'Create {type}',
          description: 'Please enter a name for the {type}',
          namePlaceholder: 'Enter {type} name'
        },
        sidebar: {
          title: 'Resources',
          new: 'New',
          newFile: 'New File',
          newFolder: 'New Folder',
          searchPlaceholder: 'Search files...',
          loading: 'Loading files...',
          noMatch: 'No files match your search',
          noFiles: 'No files yet',
          tryDifferentSearch: 'Try a different search term',
          createFirst: 'Create your first file or folder'
        }
      },

      item: {
        frequencyLabels: {
          daily: 'Daily',
          weekly: 'Weekly',
          monthly: 'Monthly'
        },
        editHabit: 'Edit Habit',
        showDetails: 'Show Details',
        hideDetails: 'Hide Details',
        deleteHabit: 'Delete Habit',
        completedToday: 'Completed today!',
        markAsDoneToday: 'Mark as done today',
      },
      tracker: {
        target: 'Target',
        dayStreak: 'day streak',
        thisMonth: 'this month',
        last7Days: 'Last 7 days',
        monthlyProgress: 'Monthly Progress',
        currentStreak: 'Current Streak',
        totalCompleted: 'Total Completed',
        created: 'Created'
      }
    },
    resources: {
      title: 'Resources',
      description: 'Topics or themes of ongoing interest for future reference.',
      addResource: 'Add Resource',
      selectFile: 'Select a file to view its content',
      chooseFromTree: 'Choose a file from the tree on the left',
      folderSelected: 'Folder selected',
      selectFileToView: 'Select a file to view its content',
      links: 'Links',
      graph: 'Graph',
      selectFileToSeeLinks: 'Select a file to see links',
      exitFocusModeTooltip: 'Exit Focus Mode (F11)',
      enterFocusModeTooltip: 'Enter Focus Mode (F11)',
      exitFocusMode: 'Exit Focus',
      focusMode: 'Focus Mode',
      markdownEditor: 'Markdown Editor',
      selectMarkdownToEdit: 'Please select a Markdown file to edit',
      notMarkdown: 'Current file is not Markdown format',
      chooseMarkdownFromTree: 'Choose a Markdown file from the left tree',
      addToLibraryWithName: 'Add "{name}" to library',
      file: 'File',
      // Notification messages
      notifications: {
        wikiLinkSuccess: 'WikiLink Navigation Successful',
        wikiLinkSuccessMessage: 'Opened file: {fileName}',
        wikiLinkFailed: 'WikiLink Navigation Failed',
        wikiLinkFailedMessage: 'An unknown error occurred during navigation',
        openFileFailed: 'Failed to Open File',
        openFileFailedMessage: 'An unknown error occurred while opening the file',
        fileCreated: 'File Created Successfully',
        fileCreatedMessage: 'File "{fileName}" has been created and opened',
        createFileFailed: 'Failed to Create File',
        createFileFailedMessage: 'An unknown error occurred while creating the file',
        saveSuccess: 'Save Successful',
        saveSuccessMessage: 'File "{fileName}" has been saved',
        saveFailed: 'Save Failed',
        saveFailedMessage: 'An unknown error occurred while saving the file',
        cannotSave: 'Cannot Save',
        cannotSaveMessage: 'Please select a file first',
        readFileFailed: 'Failed to Read File',
        readFileFailedMessage: 'Unable to read file content',
        fileExists: 'File Already Exists',
        fileExistsMessage: 'File "{fileName}" already exists, please use a different name',
        folderExists: 'Folder Already Exists',
        folderExistsMessage: 'Folder "{name}" already exists, please use a different name',
        folderCreated: 'Folder Created Successfully',
        folderCreatedMessage: 'Folder "{name}" has been created',
        createFailed: 'Creation Failed',
        createFailedMessage: 'Failed to create {type}',
        selectFile: 'Please Select File',
        selectFileMessage: 'Please first click to select a file in the left file tree, then click the add resource button',
        selectFileNotFolder: 'Only files can be added to the resource library, please select a file instead of a folder',
        resourceAdded: 'Resource Added Successfully',
        resourceAddedMessage: 'File "{fileName}" has been added to the resource library',
        addResourceFailed: 'Failed to Add Resource',
        addResourceFailedMessage: 'An unknown error occurred while adding the resource',
        deleteSuccess: 'Delete Successful',
        deleteSuccessMessage: '{type} "{name}" has been deleted',
        deleteFailed: 'Delete Failed',
        deleteFailedMessage: 'An error occurred during the operation',
        renameSuccess: 'Rename Successful',
        renameSuccessMessage: '{type} "{oldName}" has been renamed to "{newName}"',
        renameFailed: 'Rename Failed',
        renameFailedMessage: 'An error occurred during the operation'
      },
      // Interface text
      unsaved: 'Unsaved',
      saveShortcut: 'Save (Ctrl+S)',
      startWriting: 'Start writing {fileName}...',
      linkStatistics: 'Link Statistics',
      bidirectionalBacklinks: 'Bidirectional Backlinks',
      bidirectionalOutlinks: 'Bidirectional Outlinks',
      linkGraph: '🔗 Link Graph',
      linkGraphRemoved: 'Link graph feature has been removed',
      createTime: 'Created: {time}',
      // File operations
      createNewFile: 'Create New File',
      createNewFileDescription: 'File "{pageName}.md" does not exist, would you like to create this new file?',
      createFile: 'Create File',
      cancel: 'Cancel',
      // Preview related
      previewLoadFailed: 'Preview load failed: {pageName}'
    },

    archive: {
      title: 'Archive',
      description:
        'Inactive items from Projects, Areas, and Resources that are no longer relevant.',
      settings: 'Settings',
      archivedProjects: 'Archived Projects',
      archivedAreas: 'Archived Areas',
      archivedResources: 'Archived Resources',
      completedOrCancelled: 'Completed or cancelled projects',
      noLongerMaintained: 'Areas no longer maintained',
      noLongerRelevant: 'Resources no longer relevant',
      empty: {
        noArchivedItemsYet: 'No archived items yet',
        willAppearWhenArchived: 'Archived items will appear here'
      },
      // Filters and sorting
      filters: {
        type: 'Type',
        reason: 'Reason',
        sort: 'Sort',
        sortBy: {
          dateArchived: 'Date Archived',
          title: 'Title',
          type: 'Type'
        },
        reasonOptions: {
          all: 'All Reasons',
          completed: 'Completed',
          cancelled: 'Cancelled',
          inactive: 'Inactive',
          outdated: 'Outdated',
          manual: 'Manual'
        }
      },
      tabs: {
        items: 'Archived Items',
        settings: 'Settings'
      },

      lastArchivedRecently: 'Last archived recently',
      noArchivedItems: 'No archived items',
      searchPlaceholder: 'Search archived items...',
      management: 'Archive Management',
      managementDescription: 'Manage your archived items and cleanup policies',
      // Settings page translations
      autoArchiveProjects: 'Auto-archive completed projects',
      autoArchiveDescription: 'Automatically move completed projects to archive after 30 days',
      cleanupOldArchives: 'Cleanup old archives',
      cleanupDescription: 'Remove archived items older than specified months',
      archiveNotifications: 'Archive notifications',
      notificationsDescription: 'Get notified when items are automatically archived',
      enabled: 'Enabled',
      disabled: 'Disabled',
      months: 'months',
      cleanup: 'Clean up',
      exportArchiveData: 'Export Archive Data',
      // ArchiveItem component translations
      viewDetails: 'View Details',
      restore: 'Restore',
      deletePermanently: 'Delete Permanently',
      archived: 'Archived',
      originalStatus: 'Original Status',
      lastActivity: 'Last Activity',
      attachments: 'attachments',
      complete: 'complete',
      // Time formatting
      yesterday: 'Yesterday',
      daysAgo: 'days ago',
      weeksAgo: 'weeks ago',
      monthsAgo: 'months ago',
      yearsAgo: 'years ago'
    },
    inbox: {
      title: 'Inbox',
      description: 'Capture and process all incoming information and tasks.',
      quickCapture: 'Quick Capture',
      searchPlaceholder: 'Search items...',
      unprocessed: 'unprocessed',
      allProcessed: 'All processed',
      // Notification messages
      notifications: {
        contentCaptured: 'Content Captured',
        contentCapturedMessage: 'Added to inbox',
        resourceCreated: 'Resource Created Successfully',
        resourceCreatedMessage: 'Created resource file "{fileName}" and added to resource library',
        createFailed: 'Creation Failed',
        createFailedMessage: 'An error occurred while creating resource',
        taskCreated: 'Task Created Successfully',
        taskCreatedMessage: 'Created task "{title}" in "{targetName}"',
        featureInDevelopment: 'Feature In Development',
        featureInDevelopmentMessage: '{actionType} creation feature is under development',
        createContentFailed: 'An error occurred while creating content',
        checklistInDevelopment: 'Checklist creation feature is under development',
        kpiCreated: 'KPI Created Successfully',
        kpiCreatedMessage: 'Created KPI "{name}" in "{targetName}"',
        kpiCreateFailed: 'KPI Creation Failed',
        kpiCreateFailedMessage: 'An error occurred while creating KPI',
        habitCreated: 'Habit Created Successfully',
        habitCreatedMessage: 'Created habit "{name}" in "{targetName}"',
        habitCreateFailed: 'Habit Creation Failed',
        habitCreateFailedMessage: 'An error occurred while creating habit',
        projectCreated: 'Project Created Successfully',
        projectCreatedMessage: 'Created project "{name}"',
        projectCreateFailed: 'An error occurred while creating project',
        areaCreated: 'Area Created Successfully',
        areaCreatedMessage: 'Created area "{name}"',
        areaCreateFailed: 'An error occurred while creating area',
        maintenanceTaskCreated: 'Maintenance Task Created Successfully',
        maintenanceTaskCreatedMessage: 'Created maintenance task in "{targetName}"'
      },
      filters: {
        typeLabel: 'Type',
        statusLabel: 'Status',
        type: {
          allTypes: 'All Types',
          notes: 'Notes',
          tasks: 'Tasks',
          ideas: 'Ideas',
          links: 'Links',
          files: 'Files'
        },
        status: {
          allItems: 'All Items',
          unprocessed: 'Unprocessed',
          processed: 'Processed'
        }
      },
      tabs: {
        inboxItems: 'Inbox Items ({count})',
        unassignedTasks: 'Unassigned Tasks ({count})'
      },
      list: {
        title: 'Inbox Items',
        description: 'Items that need to be processed and categorized',
        loading: 'Loading inbox...'
      },
      unassigned: {
        title: 'Unassigned Tasks',
        description: 'Tasks not associated with any project or area'
      },
      buttons: {
        createTask: 'Create Task'
      },
      dialogs: {
        confirmDeleteItem: 'Are you sure you want to delete this item?'
      },
      item: {
        editPlaceholder: 'Edit content...',
        processedTo: 'Processed to {type}: {name}',
        actions: {
          process: 'Process',
          unprocess: 'Unprocess',
          markDone: 'Mark Done',
          edit: 'Edit',
          delete: 'Delete'
        },
        processToLabel: 'Process to:',
        destinations: {
          project: 'Project',
          area: 'Area',
          resource: 'Resource',
          archive: 'Archive'
        }
      },
      quickCaptureDialog: {
        title: 'Quick Capture',
        description: 'Quickly capture thoughts, tasks, ideas, or any information for later processing.',
        placeholder: "What's on your mind?",
        saveHint: 'Press ⌘+Enter to save quickly',
        typeLabel: 'Type',
        priorityLabel: 'Priority',
        tagsLabel: 'Tags',
        addTagPlaceholder: 'Add tag...',
        addButton: 'Add',
        cancel: 'Cancel',
        capturing: 'Capturing...',
        capture: 'Capture',
        typeDescriptions: {
          note: 'General information or thoughts',
          task: 'Something that needs to be done',
          idea: 'Creative thoughts or concepts',
          link: 'Web links or references',
          file: 'Documents or attachments'
        }
      }
    },
    reviews: {
      title: 'Reviews',
      description: 'Regular review and reflection on your progress.',
      weekly: 'Weekly Review',
      monthly: 'Monthly Review',
      quarterly: 'Quarterly Review',
      weeklyBadge: 'Weekly',
      startReview: 'Start Review',
      schedule: {
        daily: {
          title: 'Daily Review'
        },
        weekly: {
          title: 'Weekly Review'
        },
        monthly: {
          title: 'Monthly Review'
        },
        quarterly: {
          title: 'Quarterly Review'
        },
        today: 'Today',
        dueDay: 'Due {day}',
        dueDate: 'Due {date}',
        dueSunday: 'Due Sunday',
        dueJan31: 'Due Jan 31',
        dueMar31: 'Due Mar 31'
      },
      status: {
        notStarted: 'Not started',
        completed: 'Completed',
        inProgress: 'In progress'
      },
      progress: {
        tasksCompleted: '{completed}/{total} tasks completed'
      },
      templates: {
        title: 'Review Templates',
        description: 'Manage templates for different types of reviews',
        dailyStandup: {
          title: 'Daily Standup',
          description: 'Quick daily check-in'
        },
        weeklyPlanning: {
          title: 'Weekly Planning',
          description: 'Plan upcoming week'
        },
        monthlyReflection: {
          title: 'Monthly Reflection',
          description: 'Deep dive into progress'
        },
        // Template management
        newTemplate: 'New Template',
        import: 'Import',
        export: 'Export',
        duplicate: 'Duplicate',
        edit: 'Edit',
        delete: 'Delete',
        useTemplate: 'Use Template',
        createTemplate: 'Create Template',
        editTemplate: 'Edit Template',
        templateName: 'Template Name',
        templateDescription: 'Template Description',
        templateType: 'Template Type',
        setAsDefault: 'Set as default template',
        sections: 'Template Sections',
        advancedEditor: 'Advanced Editor',
        addSection: 'Add Section',
        sectionTitle: 'Section Title',
        sectionId: 'Section ID',
        sectionDescription: 'Section Description',
        placeholder: 'Placeholder Text',
        required: 'Required Section',
        systemTemplate: 'System Template',
        defaultTemplate: 'Default',
        // New translation keys
        deleteConfirm: 'Are you sure you want to delete this template?',
        copy: 'Copy',
        imported: 'Imported',
        invalidFormat: 'Invalid template file format',
        importFailed: 'Failed to import template. Please check the file format.',
        newSectionTitle: 'New Section',
        newSectionDescription: 'Section description',
        newSectionPlaceholder: 'Enter your thoughts here...',
        namePlaceholder: 'e.g., Weekly Reflection',
        descriptionPlaceholder: 'Describe when and how to use this template...',
        template: 'Template',
        advancedEditorDescription: 'Customize the sections and structure of your review template',
        sectionNumber: 'Section {number}',
        sectionTitlePlaceholder: 'e.g., Key Achievements',
        sectionIdPlaceholder: 'e.g., achievements',
        sectionDescriptionPlaceholder: 'Brief description of what this section is for',
        placeholderPlaceholder: 'Placeholder text to guide users...',
        sectionsCount: '{count} sections',
        systemTemplateIdReadonly: 'System template section IDs cannot be modified',
        // Default section templates
        defaultSections: {
          daily: {
            wins: {
              title: '🎉 Today\'s Wins',
              description: 'What went well today?',
              placeholder: 'List your achievements and positive moments...'
            },
            challenges: {
              title: '⚡ Challenges',
              description: 'What was difficult?',
              placeholder: 'Describe any obstacles or difficulties...'
            },
            learnings: {
              title: '💡 Learnings',
              description: 'What did you learn?',
              placeholder: 'Key insights and lessons learned...'
            },
            tomorrow: {
              title: '🚀 Tomorrow\'s Focus',
              description: 'What will you focus on tomorrow?',
              placeholder: 'Top priorities for tomorrow...'
            }
          },
          weekly: {
            achievements: {
              title: '🎯 Weekly Achievements',
              description: 'What did you accomplish this week?',
              placeholder: 'Major wins and completed goals...'
            },
            challenges: {
              title: '⚡ Challenges & Obstacles',
              description: 'What challenges did you face?',
              placeholder: 'Difficulties and how you handled them...'
            },
            learnings: {
              title: '💡 Key Learnings',
              description: 'What insights did you gain?',
              placeholder: 'Important lessons and realizations...'
            },
            nextWeek: {
              title: '🚀 Next Week\'s Goals',
              description: 'What will you focus on next week?',
              placeholder: 'Priorities and goals for the coming week...'
            }
          },
          monthly: {
            progress: {
              title: '📈 Monthly Progress',
              description: 'How did you progress toward your goals?',
              placeholder: 'Review progress on monthly objectives...'
            },
            highlights: {
              title: '⭐ Month Highlights',
              description: 'What were the standout moments?',
              placeholder: 'Most memorable and impactful events...'
            },
            challenges: {
              title: '🔧 Areas for Improvement',
              description: 'What could be improved?',
              placeholder: 'Challenges faced and improvement opportunities...'
            },
            nextMonth: {
              title: '🎯 Next Month\'s Focus',
              description: 'What are your priorities for next month?',
              placeholder: 'Key goals and focus areas...'
            }
          }
        }
      },
      duration: {
        minutes: '{count} min'
      },
      insights: {
        title: 'Review Insights',
        description: 'Key metrics and trends from your reviews',
        completionRate: 'Review Completion Rate',
        averageTime: 'Average Review Time',
        actionItemsCompleted: 'Action Items Completed'
      },
      recent: {
        title: 'Recent Reviews',
        description: 'Your latest review sessions and outcomes',
        weeklyReviewTitle: 'Weekly Review - Week {week}',
        weeklyReviewContent: 'Completed {projects} projects, identified {areas} new areas for improvement',
        dailyReviewTitle: 'Daily Review - Today',
        dailyReviewContent: 'In progress: Reviewing inbox and planning next actions',
        timeStamp: '{date} • {duration} minutes',
        timeStampSoFar: '{date} • {duration} minutes so far'
      },
      editor: {
        title: 'Review Editor',
        newReview: 'New Review',
        editReview: 'Edit Review',
        basicInfo: 'Basic Information',
        reviewType: 'Review Type',
        period: 'Period',
        reviewTitle: 'Title',
        titlePlaceholder: 'Review title (optional)',
        summary: 'Summary',
        template: 'Template',
        selectTemplate: 'Select a template',
        content: 'Review Content',
        contentDescription: 'Reflect on your achievements, challenges, and learnings. Use "Auto Generate" to populate content based on your data.',
        autoGenerate: 'Auto Generate',
        generating: 'Generating...',
        actionItems: 'Action Items',
        addActionItem: 'Add Action Item',
        actionItemsDescription: 'Track specific actions and commitments from this review',
        noActionItemsTip: 'No action items yet. Click "Add Item" to create one.',
        actionItemPlaceholder: 'Action item description...',
        insights: 'Insights',
        productivity: 'Productivity',
        satisfaction: 'Satisfaction',
        mood: 'Overall Mood',
        energy: 'Energy Level',
        aiAnalysis: 'AI Analysis',
        save: 'Save',
        saving: 'Saving...',
        cancel: 'Cancel',
        status: {
          draft: 'Draft',
          completed: 'Completed',
          archived: 'Archived'
        },
        types: reviewTypeLabels.en,
        summaryPlaceholder: 'Brief summary of this review period...',
        insightsDescription: 'Rate your experience during this review period',
        periodPlaceholder: 'e.g., 2025-W03',
        priorities: {
          low: 'Low',
          medium: 'Medium',
          high: 'High'
        },
        placeholders: {
          selectMood: 'Select mood',
          selectEnergy: 'Select energy level',
          selectProductivity: 'Select productivity level',
          selectSatisfaction: 'Select satisfaction level'
        },
        moodLevels: {
          excellent: 'Excellent',
          good: 'Good',
          neutral: 'Neutral',
          poor: 'Poor',
          terrible: 'Terrible'
        },
        energyLevels: {
          high: 'High',
          medium: 'Medium',
          low: 'Low',
          depleted: 'Depleted'
        },
        productivityLevels: {
          veryHigh: 'Very High',
          high: 'High',
          medium: 'Medium',
          low: 'Low',
          veryLow: 'Very Low'
        },
        satisfactionLevels: {
          verySatisfied: 'Very Satisfied',
          satisfied: 'Satisfied',
          neutral: 'Neutral',
          dissatisfied: 'Dissatisfied',
          veryDissatisfied: 'Very Dissatisfied'
        },
        defaultSections: {
          achievements: 'Achievements & Wins',
          challenges: 'Challenges & Obstacles',
          learnings: 'Key Learnings',
          nextSteps: 'Next Steps'
        },
        fallbackPlaceholders: {
          achievements: 'What did you accomplish? What went well?',
          challenges: "What challenges did you face? What didn't go as planned?",
          learnings: 'What did you learn? What insights did you gain?',
          nextSteps: 'What will you do differently? What are your next actions?'
        }
      },
      list: {
        title: 'Reviews',
        description: 'Manage your periodic reviews and reflections',
        newReview: 'New Review',
        search: 'Search reviews...',
        filterByType: 'Filter by type',
        filterByStatus: 'Filter by status',
        all: 'All',
        selected: '{count} selected',
        export: 'Export',
        compare: 'Compare',
        clear: 'Clear',
        exportSelected: 'Export ({count})',
        noReviews: 'No reviews yet',
        createFirst: 'Create your first review',
        tryAdjustFilters: 'Try adjusting your filters or search query.',
        deleteConfirm: 'Are you sure you want to delete this review?',
        tooltip: {
          export: 'Export review',
          edit: 'Edit review',
          delete: 'Delete review'
        }
      },
      analysis: {
        title: 'Intelligent Analysis',
        description: 'AI-powered insights for your reviews',
        reviewFor: 'review',
        overallScore: 'Overall Performance Score',
        trends: 'Trend Analysis',
        predictions: 'Predictions',
        recommendations: 'Recommendations',
        progress: 'Progress',
        projects: 'Projects',
        habits: 'Habits',
        kpis: 'KPIs',
        projectCompletion: 'Project Completion',
        taskProductivity: 'Task Productivity',
        habitConsistency: 'Habit Consistency',
        nextPeriodProjections: 'Next Period Projections',
        expectedProjects: 'Expected Projects',
        expectedTasks: 'Expected Tasks',
        goalAchievementProbability: 'Goal Achievement Probability',
        riskFactors: 'Risk Factors',
        recommendedFocus: 'Recommended Focus Areas',
        immediateActions: 'Immediate Actions',
        shortTermGoals: 'Short-term Goals',
        longTermStrategy: 'Long-term Strategy',
        thisWeek: 'This week',
        thisMonth: 'This month',
        nextQuarter: 'Next quarter',
        achievements: 'Achievements',
        areasForImprovement: 'Areas for Improvement',
        performanceBreakdown: 'Performance Breakdown',
        noRiskFactors: 'No significant risk factors identified',
        noImmediateActions: 'No immediate actions needed',
        noShortTermRecommendations: 'No short-term recommendations',
        noLongTermRecommendations: 'No long-term recommendations',
        noAchievements: 'No major achievements this period',
        noGaps: 'No significant gaps identified',
        analysisUnavailable: 'Analysis Unavailable',
        unableToGenerate: 'Unable to generate analysis for this period.',
        tryAgain: 'Try Again',
        loadFailed: 'Failed to load analysis',
        loadError: 'Error loading analysis'
      },
      comparison: {
        title: 'Review Comparison',
        description: 'Comparing {count} reviews',
        contentSimilarity: 'Content Similarity',
        progressTrend: 'Progress Trend',
        timeSpan: 'Time Span',
        reviewsCompared: 'Reviews compared',
        overview: 'Overview',
        details: 'Details',
        insights: 'Insights',
        keyDifferences: 'Key Differences',
        commonThemes: 'Common Themes',
        comparisonInsights: 'Comparison Insights',
        contentAnalysis: 'Content Analysis',
        progressAssessment: 'Progress Assessment',
        recommendations: 'Recommendations',
        consistent: 'consistent',
        varied: 'varied',
        showingImprovement: 'showing improvement over time',
        needsAttention: 'indicating areas that need attention',
        maintainingConsistent: 'maintaining consistent performance',
        noDifferences: 'No significant differences found',
        noThemes: 'No common themes identified',
        comparisonUnavailable: 'Comparison Unavailable',
        needAtLeast2: 'Need at least 2 reviews to compare'
      }
    }
  },

  // Components translations
  components: {
    dialog: {
      exitConfirm: {
        title: 'Confirm Exit',
        message: 'Are you sure you want to exit the application?',
        dontShowAgain: 'Don\'t show this again',
        confirm: 'Exit',
        minimizeToTray: 'Minimize to Tray',
        trayHint: 'The application will continue running in the system tray. You can restore the window from the tray menu at any time.'
      }
    },
    deleteTaskDialog: {
      title: 'Delete Task',
      confirmMessage: 'Are you sure you want to delete the task:',
      warning: 'Warning',
      subtaskWarning: 'This task has {count} {subtasks}. Deleting this task will also permanently delete all its subtasks.',
      subtask: 'subtask',
      subtasks: 'subtasks',
      cannotUndo: 'This action cannot be undone.',
      deleteTask: 'Delete Task',
      deleteWithSubtasks: 'Delete Task & {count} {subtasks}'
    },
    taskList: {
      notifications: {
        invalidMove: 'Invalid move',
        circularDependency: 'Cannot move task: would create circular dependency',
        taskMoved: 'Task moved',
        moveFailed: 'Move failed',
        moveFailedMessage: 'Failed to move task. Please try again.',
        taskUpdated: 'Task updated',
        statusChanged: 'Status changed to {status}',
        updateFailed: 'Failed to update task',
        updateFailedMessage: 'Task may not exist in database. Please refresh the page.',
        attributesUpdated: 'Task attributes updated',
        attributesSaved: 'Task properties have been saved successfully',
        saveAttributesFailed: 'Failed to save attributes'
      },
      dragHints: {
        reorderVertical: 'Drag vertically to reorder • horizontally to change level',
        makeSubtask: '→ Make subtask of "{name}"',
        promoteTask: '← Promote to parent level',
        reorderSameLevel: '↕ Reorder at same level'
      }
    },
    projectCard: {
      // Status labels
      status: {
        notStarted: 'Not Started',
        completed: 'Completed',
        inProgress: 'In Progress',
        atRisk: 'At Risk',
        paused: 'Paused'
      },
      // Time related
      deadline: {
        noDeadline: 'No deadline',
        daysOverdue: '{days} days overdue',
        dueToday: 'Due today',
        dueTomorrow: 'Due tomorrow',
        daysLeft: '{days} days left'
      },
      // Action buttons
      actions: {
        editProject: 'Edit Project',
        archiveProject: 'Archive Project',
        deleteProject: 'Delete Project'
      },
      // Label texts
      labels: {
        progress: 'Progress',
        goal: 'Goal',
        deliverable: 'Deliverable',
        started: 'Started',
        updated: 'Updated',
        viewDetails: 'View Details'
      }
    },
    createProjectDialog: {
      title: {
        create: 'Create New Project',
        edit: 'Edit Project'
      },
      description: 'Projects are outcomes with specific deadlines and clear completion criteria.',
      fields: {
        name: 'Project Name',
        namePlaceholder: 'Enter project name',
        description: 'Project Description',
        descriptionPlaceholder: 'Describe the project goals and scope',
        goal: 'Project Goal',
        goalPlaceholder: 'What does this project aim to achieve?',
        deliverable: 'Deliverable',
        deliverablePlaceholder: 'What will be delivered when the project is complete?',
        status: 'Status',
        progress: 'Progress',
        startDate: 'Start Date',
        deadline: 'Deadline',
        area: 'Area',
        selectArea: 'Select area',
        noArea: 'No area'
      },
      deliverableTypes: {
        examples: 'Examples:',
        document: '📄 Document/Report',
        documentExample: 'e.g., [[Project Report.md]]',
        metric: '📊 Quantifiable Metric',
        metricExample: 'e.g., User satisfaction ≥ 95%',
        checklist: '✅ Checklist',
        checklistExample: 'e.g., Multiple deliverables',
        other: '🎯 Other',
        otherExample: 'e.g., Custom deliverable'
      },
      buttons: {
        cancel: 'Cancel',
        create: 'Create Project',
        update: 'Update Project',
        creating: 'Creating...',
        updating: 'Updating...'
      }
    },

    dialogs: {
      confirmDelete: 'Are you sure you want to delete "{name}"? This action cannot be undone.',
      confirmArchive: 'Archive "{name}"? You can restore it later from the archive.',
      confirmRestore: 'Restore "{name}"? It will be moved back to its original location.',
      confirmPermanentDelete: 'Permanently delete "{name}"? This action cannot be undone.',
      exitConfirm: {
        title: 'Confirm Exit',
        message: 'Are you sure you want to exit the application?',
        dontShowAgain: 'Don\'t show this again',
        confirm: 'Exit'
      }
    },
    emptyStates: {
      noTasksToday: 'No tasks due today!',
      enjoyFreeTime: 'Enjoy your free time or plan ahead.',
      noUpcomingDeadlines: 'No upcoming deadlines',
      allProjectsOnTrack: 'All projects are on track!',
      noRecentActivity: 'No recent activity',
      startWorking: 'Start working on your projects and areas!',
      noItemsFound: 'No items match your filters',
      tryAdjustingFilters: 'Try adjusting your search or filters',
      noDataAvailable: 'No data available'
    },
    filters: {
      showCompleted: 'Show completed',
      hideCompleted: 'Hide completed',
      clearFilters: 'Clear filters',
      applyFilters: 'Apply filters'
    },
    forms: {
      required: 'This field is required',
      invalidEmail: 'Please enter a valid email address',
      passwordTooShort: 'Password must be at least 8 characters',
      confirmPassword: 'Password confirmation does not match'
    },
    actions: {
      viewDetails: 'View Details',
      viewAll: 'View All',
      open: 'Open',
      close: 'Close',
      expand: 'Expand',
      collapse: 'Collapse',
      refresh: 'Refresh',
      export: 'Export',
      import: 'Import',
      duplicate: 'Duplicate',
      archive: 'Archive',
      restore: 'Restore',
      move: 'Move',
      copy: 'Copy',
      paste: 'Paste'
    },
    inspirationCapture: {
      title: 'Inspiration Capture',
      shortcut: 'Ctrl+Shift+I',
      placeholder: 'Record your inspiration and ideas...',
      hint: 'Ctrl+Enter to save quickly, ESC to close',
      capture: 'Capture',
      capturing: 'Capturing...',
      success: {
        title: 'Inspiration Captured',
        message: 'Your idea has been saved to inbox'
      },
      error: {
        title: 'Capture Failed',
        message: 'Error occurred while saving inspiration, please try again'
      }
    },
    checklistTemplates: {
      title: 'Checklist Template Library',
      description: 'Reusable standard checklist templates',
      newTemplate: 'New Template',
      noTemplates: 'No checklist templates yet',
      createHint: 'Click "New Template" to create reusable checklists',
      itemsCount: '{count} items',
      usedTimes: 'Used {count} times',
      activeCount: '{count} in progress',
      containsItems: 'Contains items',
      moreItems: '{count} more items...',
      useTemplate: '+ Use',
      notifications: {
        created: 'Checklist Created',
        createdMessage: 'New instance of "{name}" has been added to execution area',
        createFailed: 'Creation Failed',
        createFailedMessage: 'Unable to create checklist instance',
        cannotDelete: 'Cannot Delete',
        hasActiveInstances: 'This template has {count} active instances, please complete or delete them first',
        deleted: 'Deleted',
        deletedMessage: 'Checklist template has been deleted'
      },
      deleteConfirm: {
        title: 'Delete Checklist Template',
        message: 'Are you sure you want to delete template "{name}"? This action cannot be undone.'
      }
    }
  },

  // Enums translations
  enums: {
    status: {
      notStarted: 'Not Started',
      inProgress: 'In Progress',
      atRisk: 'At Risk',
      paused: 'Paused',
      completed: 'Completed',
      cancelled: 'Cancelled',
      active: 'Active',
      needsAttention: 'Needs Attention',
      onHold: 'On Hold',
      reviewRequired: 'Review Required'
    },
    priority: {
      low: 'Low',
      medium: 'Medium',
      high: 'High',
      urgent: 'Urgent'
    },
    types: {
      note: 'Note',
      task: 'Task',
      idea: 'Idea',
      link: 'Link',
      file: 'File',
      project: 'Project',
      area: 'Area',
      resource: 'Resource'
    }
  },

  // Messages translations
  messages: {
    success: {
      saved: 'Saved successfully',
      created: 'Created successfully',
      updated: 'Updated successfully',
      deleted: 'Deleted successfully',
      archived: 'Archived successfully',
      restored: 'Restored successfully',
      imported: 'Imported successfully',
      exported: 'Exported successfully'
    },
    error: {
      saveFailed: 'Save failed',
      createFailed: 'Create failed',
      updateFailed: 'Update failed',
      deleteFailed: 'Delete failed',
      archiveFailed: 'Archive failed',
      restoreFailed: 'Restore failed',
      importFailed: 'Import failed',
      exportFailed: 'Export failed',
      networkError: 'Network error',
      unknownError: 'Unknown error'
    },
    warning: {
      title: 'Warning',
      unsavedChanges: 'You have unsaved changes',
      confirmLeave: 'Are you sure you want to leave?',
      dataWillBeLost: 'Data will be lost'
    },
    info: {
      processing: 'Processing...',
      uploading: 'Uploading...',
      downloading: 'Downloading...',
      syncing: 'Syncing...'
    }
  },

  // Recurring maintenance tasks
  recurringTasks: {
    title: 'Recurring Maintenance Tasks',
    description: 'Repetitive maintenance tasks sorted by due date',
    addTask: 'Add Task',
    noTasks: 'No recurring maintenance tasks yet',
    createFirstTask: 'Click "Add Task" to create your first recurring task',
    deleteTask: 'Delete Recurring Maintenance Task',
    deleteConfirm: 'Are you sure you want to delete task "{title}"? This action cannot be undone.',
    deleteButton: 'Delete',
    cancelButton: 'Cancel',
    taskDeleted: 'Task Deleted',
    taskDeletedMessage: 'Recurring maintenance task "{title}" has been deleted',
    lastCompleted: 'Last',
    weekdays: {
      sunday: 'Sunday',
      monday: 'Monday',
      tuesday: 'Tuesday',
      wednesday: 'Wednesday',
      thursday: 'Thursday',
      friday: 'Friday',
      saturday: 'Saturday'
    },
    // Repeat rules
    repeatRules: {
      daily: 'Daily',
      weekly: 'Weekly',
      monthly: 'Monthly',
      yearly: 'Yearly',
      unknown: 'Unknown'
    },
    // Create dialog
    createDialog: {
      title: 'Create Recurring Maintenance Task',
      description: 'Create a repetitive maintenance task that the system will automatically remind you to complete on time.',
      taskTitle: 'Task Title',
      taskTitleRequired: 'Task Title *',
      taskTitlePlaceholder: 'e.g., House Deep Cleaning',
      taskDescription: 'Task Description',
      taskDescriptionPlaceholder: 'Describe the details of this maintenance task...',
      repeatRule: 'Repeat Rule',
      repeatInterval: 'Repeat Interval',
      selectDate: 'Select Date',
      intervalUnits: {
        days: 'days',
        weeks: 'weeks',
        months: 'months',
        years: 'years'
      },
      cancel: 'Cancel',
      create: 'Create Task',
      creating: 'Creating...',
      taskCreated: 'Task Created',
      taskCreatedMessage: 'Recurring maintenance task "{title}" has been created',
      createFailed: 'Creation Failed',
      createFailedMessage: 'Unable to create recurring maintenance task'
    }
  },

  // Time related
  time: {
    justNow: 'Just now',
    minutesAgo: '{count}m ago',
    hoursAgo: '{count}h ago',
    daysAgo: '{count}d ago',
    weeksAgo: '{count}w ago',
    monthsAgo: '{count} months ago',
    yearsAgo: '{count} years ago',
    due: 'Due',
    dueToday: 'Due today',
    dueTomorrow: 'Due tomorrow',
    daysLeft: '{count} days left',
    overdue: 'Overdue',
    overdueBy: 'Overdue by {count} days'
  }
}

// 翻译数据映射
const translations: Record<Language, TranslationData> = {
  zh: zhTranslations,
  en: enTranslations
}

// 语言提供者组件
interface LanguageProviderProps {
  children: ReactNode
}

export function LanguageProvider({ children }: LanguageProviderProps) {
  // 从localStorage获取保存的语言，默认为中文
  const [language, setLanguageState] = useState<Language>(() => {
    const saved = localStorage.getItem('paolife-language')
    return (saved as Language) || 'zh'
  })

  // 设置语言并保存到localStorage
  const setLanguage = (lang: Language) => {
    setLanguageState(lang)
    localStorage.setItem('paolife-language', lang)
  }

  // 翻译函数
  const t = (key: string, params?: Record<string, string | number>): string => {
    const keys = key.split('.')
    let value: any = translations[language]

    for (const k of keys) {
      value = value?.[k]
    }

    if (typeof value !== 'string') {
      console.warn(`Translation key "${key}" not found for language "${language}"`)
      return key
    }

    // 替换参数
    if (params) {
      return value.replace(/\{(\w+)\}/g, (match: string, paramKey: string) => {
        return params[paramKey]?.toString() || match
      })
    }

    return value
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  )
}

// 使用语言上下文的Hook
export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

