// Cache Infrastructure - 缓存基础设施

// 重新导出共享的缓存实现
pub use crate::shared::utils::Cache;
pub use crate::shared::utils::MemoryCache;

// 缓存键生成器
pub struct CacheKeyGenerator;

impl CacheKeyGenerator {
    pub fn user_key(user_id: &str) -> String {
        format!("user:{}", user_id)
    }

    pub fn project_key(project_id: &str) -> String {
        format!("project:{}", project_id)
    }

    pub fn task_key(task_id: &str) -> String {
        format!("task:{}", task_id)
    }

    pub fn area_key(area_id: &str) -> String {
        format!("area:{}", area_id)
    }

    pub fn search_key(query: &str) -> String {
        format!("search:{}", query)
    }

    pub fn stats_key(entity_type: &str, date: &str) -> String {
        format!("stats:{}:{}", entity_type, date)
    }
}

// 缓存配置
#[derive(Debug, <PERSON>lone)]
pub struct CacheConfig {
    pub default_ttl: u64,
    pub max_size: usize,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            default_ttl: 3600, // 1 hour
            max_size: 1000,
        }
    }
}
