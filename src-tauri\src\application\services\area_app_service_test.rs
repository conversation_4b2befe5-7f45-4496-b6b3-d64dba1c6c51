#[cfg(test)]
mod tests {
    use super::*;
    use crate::domain::entities::{Area, AreaStatus, Project};
    use crate::domain::repositories::{AreaRepository, ProjectRepository};
    use crate::domain::services::AreaDomainService;
    use crate::shared::errors::{AppError, Result};
    use crate::shared::types::{Id, QueryParams, EntityStatus};
    use async_trait::async_trait;
    use std::collections::HashMap;
    use std::sync::{Arc, Mutex};

    // Mock Area Repository
    #[derive(Default)]
    struct MockAreaRepository {
        areas: Arc<Mutex<HashMap<Id, Area>>>,
        names: Arc<Mutex<HashMap<String, Id>>>,
    }

    impl MockAreaRepository {
        fn new() -> Self {
            Self::default()
        }

        fn add_area(&self, area: Area) {
            let mut areas = self.areas.lock().unwrap();
            let mut names = self.names.lock().unwrap();
            names.insert(area.name.clone(), area.id.clone());
            areas.insert(area.id.clone(), area);
        }
    }

    #[async_trait]
    impl AreaRepository for MockAreaRepository {
        async fn find_by_id(&self, id: &Id) -> Result<Option<Area>> {
            let areas = self.areas.lock().unwrap();
            Ok(areas.get(id).cloned())
        }

        async fn find_by_name(&self, name: &str) -> Result<Option<Area>> {
            let names = self.names.lock().unwrap();
            let areas = self.areas.lock().unwrap();
            
            if let Some(id) = names.get(name) {
                Ok(areas.get(id).cloned())
            } else {
                Ok(None)
            }
        }

        async fn save(&self, area: &Area) -> Result<()> {
            self.add_area(area.clone());
            Ok(())
        }

        async fn update(&self, area: &Area) -> Result<()> {
            let mut areas = self.areas.lock().unwrap();
            if areas.contains_key(&area.id) {
                areas.insert(area.id.clone(), area.clone());
                Ok(())
            } else {
                Err(AppError::NotFound("Area not found".to_string()))
            }
        }

        async fn delete(&self, id: &Id) -> Result<()> {
            let mut areas = self.areas.lock().unwrap();
            if let Some(mut area) = areas.get(id).cloned() {
                area.entity_status = EntityStatus::Deleted;
                areas.insert(id.clone(), area);
                Ok(())
            } else {
                Err(AppError::NotFound("Area not found".to_string()))
            }
        }

        async fn find_all_active(&self) -> Result<Vec<Area>> {
            let areas = self.areas.lock().unwrap();
            Ok(areas.values()
                .filter(|a| a.entity_status == EntityStatus::Active)
                .cloned()
                .collect())
        }

        async fn find_by_status(&self, status: &AreaStatus) -> Result<Vec<Area>> {
            let areas = self.areas.lock().unwrap();
            Ok(areas.values()
                .filter(|a| a.status == *status && a.entity_status != EntityStatus::Deleted)
                .cloned()
                .collect())
        }

        async fn find_with_pagination(&self, _params: &QueryParams) -> Result<(Vec<Area>, u64)> {
            let areas = self.find_all_active().await?;
            let total = areas.len() as u64;
            Ok((areas, total))
        }

        async fn count_active_areas(&self) -> Result<u64> {
            let areas = self.find_all_active().await?;
            Ok(areas.len() as u64)
        }

        async fn search(&self, query: &str) -> Result<Vec<Area>> {
            let areas = self.areas.lock().unwrap();
            Ok(areas.values()
                .filter(|a| {
                    (a.name.contains(query) || 
                     a.description.as_ref().map_or(false, |d| d.contains(query))) &&
                    a.entity_status != EntityStatus::Deleted
                })
                .cloned()
                .collect())
        }

        async fn name_exists(&self, name: &str) -> Result<bool> {
            let names = self.names.lock().unwrap();
            Ok(names.contains_key(name))
        }

        async fn get_statistics(&self) -> Result<crate::domain::repositories::AreaStatistics> {
            let areas = self.areas.lock().unwrap();
            let total_areas = areas.len() as u64;
            let active_areas = areas.values().filter(|a| a.entity_status == EntityStatus::Active).count() as u64;

            Ok(crate::domain::repositories::AreaStatistics {
                total_areas,
                active_areas,
                areas_by_status: vec![],
                areas_with_projects: 0,
                areas_with_habits: 0,
                average_projects_per_area: 0.0,
                average_habits_per_area: 0.0,
            })
        }

        async fn get_project_count(&self, _area_id: &Id) -> Result<u64> {
            Ok(0) // Simplified
        }

        async fn get_habit_count(&self, _area_id: &Id) -> Result<u64> {
            Ok(0) // Simplified
        }
    }

    // Mock Project Repository
    #[derive(Default)]
    struct MockProjectRepository {
        projects: Arc<Mutex<HashMap<Id, Project>>>,
    }

    impl MockProjectRepository {
        fn new() -> Self {
            Self::default()
        }
    }

    #[async_trait]
    impl ProjectRepository for MockProjectRepository {
        async fn find_by_id(&self, id: &Id) -> Result<Option<Project>> {
            let projects = self.projects.lock().unwrap();
            Ok(projects.get(id).cloned())
        }

        async fn find_by_area_id(&self, area_id: &Id) -> Result<Vec<Project>> {
            let projects = self.projects.lock().unwrap();
            Ok(projects.values()
                .filter(|p| p.area_id.as_ref() == Some(area_id) && p.entity_status != EntityStatus::Deleted)
                .cloned()
                .collect())
        }

        // Implement other required methods with minimal functionality
        async fn find_by_name(&self, _name: &str) -> Result<Option<Project>> { Ok(None) }
        async fn save(&self, _project: &Project) -> Result<()> { Ok(()) }
        async fn update(&self, _project: &Project) -> Result<()> { Ok(()) }
        async fn delete(&self, _id: &Id) -> Result<()> { Ok(()) }
        async fn find_all_active(&self) -> Result<Vec<Project>> { Ok(vec![]) }
        async fn find_by_status(&self, _status: &crate::domain::entities::ProjectStatus) -> Result<Vec<Project>> { Ok(vec![]) }
        async fn find_by_priority(&self, _priority: &crate::shared::types::Priority) -> Result<Vec<Project>> { Ok(vec![]) }
        async fn find_due_soon(&self, _days: u32) -> Result<Vec<Project>> { Ok(vec![]) }
        async fn find_overdue(&self) -> Result<Vec<Project>> { Ok(vec![]) }
        async fn find_with_pagination(&self, _params: &QueryParams) -> Result<(Vec<Project>, u64)> { Ok((vec![], 0)) }
        async fn count_active_projects(&self) -> Result<u64> { Ok(0) }
        async fn find_by_progress_range(&self, _min_progress: f32, _max_progress: f32) -> Result<Vec<Project>> { Ok(vec![]) }
        async fn search(&self, _query: &str) -> Result<Vec<Project>> { Ok(vec![]) }
        async fn get_statistics(&self) -> Result<crate::domain::repositories::ProjectStatistics> {
            Ok(crate::domain::repositories::ProjectStatistics {
                total_projects: 0,
                active_projects: 0,
                completed_projects: 0,
                overdue_projects: 0,
                average_progress: 0.0,
                projects_by_status: vec![],
                projects_by_priority: vec![],
            })
        }
    }

    fn create_area_app_service() -> AreaAppService {
        let area_repository = Arc::new(MockAreaRepository::new());
        let project_repository = Arc::new(MockProjectRepository::new());
        let domain_service = AreaDomainService::new();
        
        AreaAppService::new(area_repository, project_repository, domain_service)
    }

    #[tokio::test]
    async fn test_create_area_success() {
        let service = create_area_app_service();
        
        let result = service.create_area(
            "Test Area".to_string(),
            Some("A test area".to_string()),
            Some(vec!["Standard 1".to_string(), "Standard 2".to_string()]),
            Some("#FF0000".to_string()),
            Some("icon-test".to_string()),
        ).await;

        assert!(result.is_ok());
        let area = result.unwrap();
        assert_eq!(area.name, "Test Area");
        assert_eq!(area.description, Some("A test area".to_string()));
        assert_eq!(area.standards, vec!["Standard 1".to_string(), "Standard 2".to_string()]);
        assert_eq!(area.color, Some("#FF0000".to_string()));
        assert_eq!(area.icon, Some("icon-test".to_string()));
        assert_eq!(area.status, AreaStatus::Active);
    }

    #[tokio::test]
    async fn test_create_area_duplicate_name() {
        let service = create_area_app_service();
        
        // Create first area
        let _area1 = service.create_area(
            "Test Area".to_string(),
            Some("First area".to_string()),
            None,
            None,
            None,
        ).await.unwrap();

        // Try to create second area with same name
        let result = service.create_area(
            "Test Area".to_string(),
            Some("Second area".to_string()),
            None,
            None,
            None,
        ).await;

        assert!(result.is_err());
        match result.unwrap_err() {
            AppError::ValidationError(msg) => assert_eq!(msg, "Area name already exists"),
            _ => panic!("Expected ValidationError"),
        }
    }

    #[tokio::test]
    async fn test_get_area_by_id_success() {
        let service = create_area_app_service();
        
        let created_area = service.create_area(
            "Test Area".to_string(),
            Some("A test area".to_string()),
            None,
            None,
            None,
        ).await.unwrap();

        let result = service.get_area_by_id(&created_area.id).await;
        assert!(result.is_ok());
        
        let area = result.unwrap();
        assert_eq!(area.id, created_area.id);
        assert_eq!(area.name, "Test Area");
    }

    #[tokio::test]
    async fn test_get_area_by_id_not_found() {
        let service = create_area_app_service();
        
        let result = service.get_area_by_id("nonexistent").await;
        assert!(result.is_err());
        match result.unwrap_err() {
            AppError::NotFound(msg) => assert_eq!(msg, "Area not found"),
            _ => panic!("Expected NotFound error"),
        }
    }

    #[tokio::test]
    async fn test_update_area_success() {
        let service = create_area_app_service();
        
        let created_area = service.create_area(
            "Test Area".to_string(),
            Some("Original description".to_string()),
            None,
            None,
            None,
        ).await.unwrap();

        let result = service.update_area(
            &created_area.id,
            Some("Updated Area".to_string()),
            Some("Updated description".to_string()),
            Some(vec!["New Standard".to_string()]),
            Some("#00FF00".to_string()),
            Some("new-icon".to_string()),
        ).await;

        assert!(result.is_ok());
        let updated_area = result.unwrap();
        assert_eq!(updated_area.name, "Updated Area");
        assert_eq!(updated_area.description, Some("Updated description".to_string()));
        assert_eq!(updated_area.standards, vec!["New Standard".to_string()]);
        assert_eq!(updated_area.color, Some("#00FF00".to_string()));
        assert_eq!(updated_area.icon, Some("new-icon".to_string()));
    }

    #[tokio::test]
    async fn test_update_area_status() {
        let service = create_area_app_service();
        
        let created_area = service.create_area(
            "Test Area".to_string(),
            None,
            None,
            None,
            None,
        ).await.unwrap();

        let result = service.update_area_status(&created_area.id, AreaStatus::Maintenance).await;
        assert!(result.is_ok());
        
        let updated_area = result.unwrap();
        assert_eq!(updated_area.status, AreaStatus::Maintenance);
    }

    #[tokio::test]
    async fn test_activate_area() {
        let service = create_area_app_service();
        
        let created_area = service.create_area(
            "Test Area".to_string(),
            None,
            None,
            None,
            None,
        ).await.unwrap();

        // Set to maintenance first
        let _maintenance_area = service.set_area_maintenance(&created_area.id).await.unwrap();

        // Then activate
        let result = service.activate_area(&created_area.id).await;
        assert!(result.is_ok());
        
        let activated_area = result.unwrap();
        assert_eq!(activated_area.status, AreaStatus::Active);
    }

    #[tokio::test]
    async fn test_set_area_maintenance() {
        let service = create_area_app_service();
        
        let created_area = service.create_area(
            "Test Area".to_string(),
            None,
            None,
            None,
            None,
        ).await.unwrap();

        let result = service.set_area_maintenance(&created_area.id).await;
        assert!(result.is_ok());
        
        let maintenance_area = result.unwrap();
        assert_eq!(maintenance_area.status, AreaStatus::Maintenance);
    }

    #[tokio::test]
    async fn test_set_area_dormant() {
        let service = create_area_app_service();
        
        let created_area = service.create_area(
            "Test Area".to_string(),
            None,
            None,
            None,
            None,
        ).await.unwrap();

        let result = service.set_area_dormant(&created_area.id).await;
        assert!(result.is_ok());
        
        let dormant_area = result.unwrap();
        assert_eq!(dormant_area.status, AreaStatus::Dormant);
    }

    #[tokio::test]
    async fn test_delete_area_success() {
        let service = create_area_app_service();
        
        let created_area = service.create_area(
            "Test Area".to_string(),
            None,
            None,
            None,
            None,
        ).await.unwrap();

        let result = service.delete_area(&created_area.id).await;
        assert!(result.is_ok());

        // Verify area is marked as deleted
        let result = service.get_area_by_id(&created_area.id).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_archive_area() {
        let service = create_area_app_service();
        
        let created_area = service.create_area(
            "Test Area".to_string(),
            None,
            None,
            None,
            None,
        ).await.unwrap();

        let result = service.archive_area(&created_area.id).await;
        assert!(result.is_ok());
        
        let archived_area = result.unwrap();
        assert_eq!(archived_area.entity_status, EntityStatus::Archived);
    }

    #[tokio::test]
    async fn test_area_name_availability() {
        let service = create_area_app_service();
        
        // Check availability of new name
        let result = service.is_area_name_available("New Area").await;
        assert!(result.is_ok());
        assert!(result.unwrap());

        // Create area
        let _area = service.create_area("New Area".to_string(), None, None, None, None).await.unwrap();

        // Check availability of existing name
        let result = service.is_area_name_available("New Area").await;
        assert!(result.is_ok());
        assert!(!result.unwrap());
    }

    #[tokio::test]
    async fn test_search_areas() {
        let service = create_area_app_service();
        
        // Create test areas
        let _area1 = service.create_area("Development Area".to_string(), Some("Software development".to_string()), None, None, None).await.unwrap();
        let _area2 = service.create_area("Marketing Area".to_string(), Some("Product marketing".to_string()), None, None, None).await.unwrap();
        let _area3 = service.create_area("Sales Area".to_string(), Some("Sales activities".to_string()), None, None, None).await.unwrap();

        // Search by name
        let result = service.search_areas("Development").await;
        assert!(result.is_ok());
        let areas = result.unwrap();
        assert_eq!(areas.len(), 1);
        assert_eq!(areas[0].name, "Development Area");

        // Search by description
        let result = service.search_areas("marketing").await;
        assert!(result.is_ok());
        let areas = result.unwrap();
        assert_eq!(areas.len(), 1);
        assert_eq!(areas[0].name, "Marketing Area");
    }

    #[tokio::test]
    async fn test_batch_update_area_status() {
        let service = create_area_app_service();
        
        // Create multiple areas
        let area1 = service.create_area("Area 1".to_string(), None, None, None, None).await.unwrap();
        let area2 = service.create_area("Area 2".to_string(), None, None, None, None).await.unwrap();
        let area3 = service.create_area("Area 3".to_string(), None, None, None, None).await.unwrap();

        let area_ids = vec![area1.id, area2.id, area3.id];
        let result = service.batch_update_area_status(area_ids, AreaStatus::Maintenance).await;
        
        assert!(result.is_ok());
        let updated_areas = result.unwrap();
        assert_eq!(updated_areas.len(), 3);
        
        for area in updated_areas {
            assert_eq!(area.status, AreaStatus::Maintenance);
        }
    }
}
