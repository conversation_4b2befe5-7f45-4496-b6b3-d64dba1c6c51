#[cfg(test)]
mod tests {
    use super::*;
    use crate::domain::entities::{User, UserPreferences};
    use crate::domain::repositories::UserRepository;
    use crate::domain::services::UserDomainService;
    use crate::shared::errors::{AppError, Result};
    use crate::shared::types::{Id, QueryParams, EntityStatus};
    use async_trait::async_trait;
    use std::collections::HashMap;
    use std::sync::{Arc, Mutex};

    // Mock User Repository for testing
    #[derive(Default)]
    struct MockUserRepository {
        users: Arc<Mutex<HashMap<Id, User>>>,
        usernames: Arc<Mutex<HashMap<String, Id>>>,
        emails: Arc<Mutex<HashMap<String, Id>>>,
    }

    impl MockUserRepository {
        fn new() -> Self {
            Self::default()
        }

        fn add_user(&self, user: User) {
            let mut users = self.users.lock().unwrap();
            let mut usernames = self.usernames.lock().unwrap();
            let mut emails = self.emails.lock().unwrap();

            usernames.insert(user.username.clone(), user.id.clone());
            if let Some(ref email) = user.email {
                emails.insert(email.clone(), user.id.clone());
            }
            users.insert(user.id.clone(), user);
        }
    }

    #[async_trait]
    impl UserRepository for MockUserRepository {
        async fn find_by_id(&self, id: &Id) -> Result<Option<User>> {
            let users = self.users.lock().unwrap();
            Ok(users.get(id).cloned())
        }

        async fn find_by_username(&self, username: &str) -> Result<Option<User>> {
            let usernames = self.usernames.lock().unwrap();
            let users = self.users.lock().unwrap();
            
            if let Some(id) = usernames.get(username) {
                Ok(users.get(id).cloned())
            } else {
                Ok(None)
            }
        }

        async fn find_by_email(&self, email: &str) -> Result<Option<User>> {
            let emails = self.emails.lock().unwrap();
            let users = self.users.lock().unwrap();
            
            if let Some(id) = emails.get(email) {
                Ok(users.get(id).cloned())
            } else {
                Ok(None)
            }
        }

        async fn save(&self, user: &User) -> Result<()> {
            self.add_user(user.clone());
            Ok(())
        }

        async fn update(&self, user: &User) -> Result<()> {
            let mut users = self.users.lock().unwrap();
            if users.contains_key(&user.id) {
                users.insert(user.id.clone(), user.clone());
                Ok(())
            } else {
                Err(AppError::NotFound("User not found".to_string()))
            }
        }

        async fn delete(&self, id: &Id) -> Result<()> {
            let mut users = self.users.lock().unwrap();
            if let Some(mut user) = users.get(id).cloned() {
                user.entity_status = EntityStatus::Deleted;
                users.insert(id.clone(), user);
                Ok(())
            } else {
                Err(AppError::NotFound("User not found".to_string()))
            }
        }

        async fn find_all_active(&self) -> Result<Vec<User>> {
            let users = self.users.lock().unwrap();
            Ok(users.values()
                .filter(|u| u.entity_status == EntityStatus::Active)
                .cloned()
                .collect())
        }

        async fn find_with_pagination(&self, _params: &QueryParams) -> Result<(Vec<User>, u64)> {
            let users = self.find_all_active().await?;
            let total = users.len() as u64;
            Ok((users, total))
        }

        async fn username_exists(&self, username: &str) -> Result<bool> {
            let usernames = self.usernames.lock().unwrap();
            Ok(usernames.contains_key(username))
        }

        async fn email_exists(&self, email: &str) -> Result<bool> {
            let emails = self.emails.lock().unwrap();
            Ok(emails.contains_key(email))
        }

        async fn count_active_users(&self) -> Result<u64> {
            let users = self.find_all_active().await?;
            Ok(users.len() as u64)
        }

        async fn find_by_status(&self, status: &str) -> Result<Vec<User>> {
            let users = self.users.lock().unwrap();
            Ok(users.values()
                .filter(|u| match status {
                    "active" => u.entity_status == EntityStatus::Active,
                    "inactive" => u.entity_status == EntityStatus::Inactive,
                    _ => false,
                })
                .cloned()
                .collect())
        }
    }

    fn create_user_app_service() -> UserAppService {
        let repository = Arc::new(MockUserRepository::new());
        let domain_service = UserDomainService::new();
        UserAppService::new(repository, domain_service)
    }

    #[tokio::test]
    async fn test_create_user_success() {
        let service = create_user_app_service();
        
        let result = service.create_user(
            "testuser".to_string(),
            Some("<EMAIL>".to_string())
        ).await;

        assert!(result.is_ok());
        let user = result.unwrap();
        assert_eq!(user.username, "testuser");
        assert_eq!(user.email, Some("<EMAIL>".to_string()));
        assert_eq!(user.entity_status, EntityStatus::Active);
    }

    #[tokio::test]
    async fn test_create_user_duplicate_username() {
        let service = create_user_app_service();
        
        // Create first user
        let _user1 = service.create_user(
            "testuser".to_string(),
            Some("<EMAIL>".to_string())
        ).await.unwrap();

        // Try to create second user with same username
        let result = service.create_user(
            "testuser".to_string(),
            Some("<EMAIL>".to_string())
        ).await;

        assert!(result.is_err());
        match result.unwrap_err() {
            AppError::ValidationError(msg) => assert_eq!(msg, "Username already exists"),
            _ => panic!("Expected ValidationError"),
        }
    }

    #[tokio::test]
    async fn test_create_user_duplicate_email() {
        let service = create_user_app_service();
        
        // Create first user
        let _user1 = service.create_user(
            "testuser1".to_string(),
            Some("<EMAIL>".to_string())
        ).await.unwrap();

        // Try to create second user with same email
        let result = service.create_user(
            "testuser2".to_string(),
            Some("<EMAIL>".to_string())
        ).await;

        assert!(result.is_err());
        match result.unwrap_err() {
            AppError::ValidationError(msg) => assert_eq!(msg, "Email already exists"),
            _ => panic!("Expected ValidationError"),
        }
    }

    #[tokio::test]
    async fn test_get_user_by_id_success() {
        let service = create_user_app_service();
        
        let created_user = service.create_user(
            "testuser".to_string(),
            Some("<EMAIL>".to_string())
        ).await.unwrap();

        let result = service.get_user_by_id(&created_user.id).await;
        assert!(result.is_ok());
        
        let user = result.unwrap();
        assert_eq!(user.id, created_user.id);
        assert_eq!(user.username, "testuser");
    }

    #[tokio::test]
    async fn test_get_user_by_id_not_found() {
        let service = create_user_app_service();
        
        let result = service.get_user_by_id("nonexistent").await;
        assert!(result.is_err());
        match result.unwrap_err() {
            AppError::NotFound(msg) => assert_eq!(msg, "User not found"),
            _ => panic!("Expected NotFound error"),
        }
    }

    #[tokio::test]
    async fn test_update_user_success() {
        let service = create_user_app_service();
        
        let created_user = service.create_user(
            "testuser".to_string(),
            Some("<EMAIL>".to_string())
        ).await.unwrap();

        let result = service.update_user(
            &created_user.id,
            Some("Test User".to_string()),
            Some("<EMAIL>".to_string()),
            Some("https://avatar.example.com".to_string()),
        ).await;

        assert!(result.is_ok());
        let updated_user = result.unwrap();
        assert_eq!(updated_user.display_name, Some("Test User".to_string()));
        assert_eq!(updated_user.email, Some("<EMAIL>".to_string()));
        assert_eq!(updated_user.avatar_url, Some("https://avatar.example.com".to_string()));
    }

    #[tokio::test]
    async fn test_update_user_preferences() {
        let service = create_user_app_service();
        
        let created_user = service.create_user(
            "testuser".to_string(),
            None
        ).await.unwrap();

        let mut new_preferences = UserPreferences::default();
        new_preferences.theme = "dark".to_string();
        new_preferences.language = "zh-CN".to_string();

        let result = service.update_user_preferences(
            &created_user.id,
            new_preferences.clone(),
        ).await;

        assert!(result.is_ok());
        let updated_user = result.unwrap();
        assert_eq!(updated_user.preferences.theme, "dark");
        assert_eq!(updated_user.preferences.language, "zh-CN");
    }

    #[tokio::test]
    async fn test_activate_deactivate_user() {
        let service = create_user_app_service();
        
        let created_user = service.create_user(
            "testuser".to_string(),
            None
        ).await.unwrap();

        // Deactivate user
        let result = service.deactivate_user(&created_user.id).await;
        assert!(result.is_ok());
        let deactivated_user = result.unwrap();
        assert_eq!(deactivated_user.entity_status, EntityStatus::Inactive);

        // Activate user
        let result = service.activate_user(&created_user.id).await;
        assert!(result.is_ok());
        let activated_user = result.unwrap();
        assert_eq!(activated_user.entity_status, EntityStatus::Active);
    }

    #[tokio::test]
    async fn test_delete_user() {
        let service = create_user_app_service();
        
        let created_user = service.create_user(
            "testuser".to_string(),
            None
        ).await.unwrap();

        let result = service.delete_user(&created_user.id).await;
        assert!(result.is_ok());

        // Verify user is marked as deleted
        let result = service.get_user_by_id(&created_user.id).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_username_availability() {
        let service = create_user_app_service();
        
        // Check availability of new username
        let result = service.is_username_available("newuser").await;
        assert!(result.is_ok());
        assert!(result.unwrap());

        // Create user
        let _user = service.create_user("newuser".to_string(), None).await.unwrap();

        // Check availability of existing username
        let result = service.is_username_available("newuser").await;
        assert!(result.is_ok());
        assert!(!result.unwrap());
    }

    #[tokio::test]
    async fn test_email_availability() {
        let service = create_user_app_service();
        
        // Check availability of new email
        let result = service.is_email_available("<EMAIL>").await;
        assert!(result.is_ok());
        assert!(result.unwrap());

        // Create user
        let _user = service.create_user(
            "testuser".to_string(),
            Some("<EMAIL>".to_string())
        ).await.unwrap();

        // Check availability of existing email
        let result = service.is_email_available("<EMAIL>").await;
        assert!(result.is_ok());
        assert!(!result.unwrap());
    }
}
