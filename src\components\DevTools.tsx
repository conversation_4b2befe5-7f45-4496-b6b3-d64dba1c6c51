// 开发工具组件
// 仅在开发环境显示，提供API切换、状态查看等功能

import { createSignal, Show } from 'solid-js';
import { getApiType, healthCheck } from '../services/apiFactory';

export function DevTools() {
  const [isOpen, setIsOpen] = createSignal(false);
  const [apiStatus, setApiStatus] = createSignal<'unknown' | 'connected' | 'error'>('unknown');
  const [lastCheck, setLastCheck] = createSignal<string>('');

  // 检查API连接状态
  const checkApiStatus = async () => {
    try {
      await healthCheck();
      setApiStatus('connected');
      setLastCheck(new Date().toLocaleTimeString());
    } catch (error) {
      setApiStatus('error');
      setLastCheck(new Date().toLocaleTimeString());
      console.error('API Health Check Failed:', error);
    }
  };

  // 仅在开发环境显示
  const isDev = import.meta.env.DEV;
  if (!isDev) return null;

  return (
    <div class="fixed bottom-4 right-4 z-50">
      {/* 切换按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen())}
        class="bg-gray-800 text-white p-2 rounded-full shadow-lg hover:bg-gray-700 transition-colors"
        title="开发工具"
      >
        🛠️
      </button>

      {/* 开发工具面板 */}
      <Show when={isOpen()}>
        <div class="absolute bottom-12 right-0 bg-white border border-gray-200 rounded-lg shadow-xl p-4 w-80">
          <div class="flex justify-between items-center mb-3">
            <h3 class="font-semibold text-gray-800">开发工具</h3>
            <button
              onClick={() => setIsOpen(false)}
              class="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>

          {/* API 状态 */}
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <span class="text-sm font-medium text-gray-600">API 状态</span>
              <button
                onClick={checkApiStatus}
                class="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200"
              >
                检查连接
              </button>
            </div>
            
            <div class="space-y-1">
              <div class="flex justify-between text-sm">
                <span>类型:</span>
                <span class={`font-medium ${getApiType() === 'mock' ? 'text-orange-600' : 'text-green-600'}`}>
                  {getApiType() === 'mock' ? 'Mock API' : 'Real API'}
                </span>
              </div>
              
              <div class="flex justify-between text-sm">
                <span>状态:</span>
                <span class={`font-medium ${
                  apiStatus() === 'connected' ? 'text-green-600' : 
                  apiStatus() === 'error' ? 'text-red-600' : 'text-gray-500'
                }`}>
                  {apiStatus() === 'connected' ? '已连接' : 
                   apiStatus() === 'error' ? '连接失败' : '未知'}
                </span>
              </div>
              
              <Show when={lastCheck()}>
                <div class="flex justify-between text-xs text-gray-500">
                  <span>最后检查:</span>
                  <span>{lastCheck()}</span>
                </div>
              </Show>
            </div>
          </div>

          {/* 环境信息 */}
          <div class="mb-4">
            <h4 class="text-sm font-medium text-gray-600 mb-2">环境信息</h4>
            <div class="space-y-1 text-xs text-gray-500">
              <div class="flex justify-between">
                <span>模式:</span>
                <span>{import.meta.env.MODE}</span>
              </div>
              <div class="flex justify-between">
                <span>DEV:</span>
                <span>{import.meta.env.DEV ? '是' : '否'}</span>
              </div>
              <div class="flex justify-between">
                <span>MOCK API:</span>
                <span>{import.meta.env.VITE_USE_MOCK_API}</span>
              </div>
              <div class="flex justify-between">
                <span>强制 MOCK:</span>
                <span>{import.meta.env.VITE_FORCE_MOCK_API}</span>
              </div>
            </div>
          </div>

          {/* 快捷操作 */}
          <div>
            <h4 class="text-sm font-medium text-gray-600 mb-2">快捷操作</h4>
            <div class="space-y-2">
              <button
                onClick={() => window.location.reload()}
                class="w-full text-xs bg-gray-100 text-gray-700 px-3 py-2 rounded hover:bg-gray-200"
              >
                🔄 重新加载页面
              </button>
              
              <button
                onClick={() => {
                  localStorage.clear();
                  sessionStorage.clear();
                  window.location.reload();
                }}
                class="w-full text-xs bg-red-100 text-red-700 px-3 py-2 rounded hover:bg-red-200"
              >
                🗑️ 清除缓存并重载
              </button>
            </div>
          </div>
        </div>
      </Show>
    </div>
  );
}
