/**
 * 习惯追踪组件库入口文件
 * 导出所有习惯追踪相关的组件、类型和工具函数
 */

// 主要组件
export { default as HabitTracker } from './HabitTracker'
export { default as HabitList } from './HabitList'
export { default as HabitCard } from './HabitCard'
export { default as HabitDialog } from './HabitDialog'
export { default as HabitStatistics } from './HabitStatistics'
export { default as HabitCalendar } from './HabitCalendar'
export { default as HabitChart } from './HabitChart'

// 数据源适配器
export { 
  DefaultHabitDataSource, 
  createHabitDataSource 
} from './adapters'

// 类型定义
export type {
  // 基础数据类型
  BaseHabit,
  HabitRecord,
  HabitStatistics,
  HabitProgress,
  HabitType,
  HabitFrequency,
  HabitStatus,
  RecordSource,
  
  // 配置类型
  HabitTrackerConfig,
  HabitFilter,
  
  // 事件类型
  HabitEvent,
  HabitEventType,
  HabitEventHandlers,
  
  // API接口类型
  HabitDataSource,
  QueryOptions,
  CreateHabitData,
  UpdateHabitData,
  CreateRecordData,
  
  // 组件Props类型
  HabitTrackerProps,
  HabitListProps,
  HabitCardProps,
  HabitDialogProps,
  HabitStatisticsProps,
  HabitCalendarProps,
  HabitChartProps,
  
  // 工具类型
  DeepPartial,
  RequireFields,
  HabitTypeColorMap
} from './types'

// 常量
export { 
  DEFAULT_HABIT_CONFIG,
  HABIT_TYPES,
  HABIT_FREQUENCIES,
  HABIT_STATUSES,
  PRESET_COLORS,
  HABIT_TYPE_COLORS,
  COMMON_UNITS,
  COMPLETION_GRADES,
  STREAK_MILESTONES,
  CHART_TYPES,
  TIME_RANGES,
  LAYOUT_OPTIONS,
  SORT_OPTIONS,
  FILTER_OPTIONS,
  DEFAULT_VALUES,
  VALIDATION_RULES,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  HINT_MESSAGES
} from './constants'

// 工具函数
export {
  calculateStreak,
  calculateLongestStreak,
  calculateCompletionRate,
  getHabitTypeLabel,
  getHabitFrequencyLabel,
  getHabitTypeColor,
  formatHabitTarget,
  formatRecordValue,
  isHabitCompletedOnDate,
  getHabitValueOnDate,
  generateDateRange,
  formatDateString,
  parseDateString,
  isToday,
  isYesterday,
  getRelativeDateDescription,
  calculateHabitTrend,
  getCompletionGrade,
  calculateHabitSummary,
  generateHabitColor,
  validateHabitData,
  searchHabits,
  sortHabits,
  groupHabits,
  debounce
} from './utils'
