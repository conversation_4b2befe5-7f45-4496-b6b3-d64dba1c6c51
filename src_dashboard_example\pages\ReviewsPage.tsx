import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../components/ui/tabs'
import { useLanguage } from '../contexts/LanguageContext'
import ReviewList from '../components/features/ReviewList'
import ReviewEditor from '../components/features/ReviewEditor'
import ReviewTemplateManager from '../components/features/ReviewTemplateManager'
import type { Review, ReviewTemplate } from '../../../shared/types'

export function ReviewsPage() {
  const { t } = useLanguage()
  const [activeTab, setActiveTab] = useState('reviews')
  const [currentView, setCurrentView] = useState<'list' | 'editor'>('list')
  const [editingReview, setEditingReview] = useState<Review | null>(null)
  const [selectedTemplate, setSelectedTemplate] = useState<ReviewTemplate | null>(null)

  const handleCreateReview = () => {
    setEditingReview(null)
    setCurrentView('editor')
  }

  const handleEditReview = (review: Review) => {
    setEditingReview(review)
    setCurrentView('editor')
  }

  const handleSaveReview = (review: Review) => {
    setCurrentView('list')
    setEditingReview(null)
    // Refresh the list by switching tabs or triggering a reload
  }

  const handleCancelEdit = () => {
    setCurrentView('list')
    setEditingReview(null)
  }

  const handleTemplateSelect = (template: ReviewTemplate) => {
    setSelectedTemplate(template)
    setActiveTab('reviews')
    setCurrentView('editor')
  }

  return (
    <div className="container mx-auto p-6">
      {currentView === 'editor' ? (
        <ReviewEditor
          review={editingReview}
          onSave={handleSaveReview}
          onCancel={handleCancelEdit}
          onClose={handleCancelEdit}
        />
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="reviews">{t('pages.reviews.title')}</TabsTrigger>
            <TabsTrigger value="templates">{t('pages.reviews.templates.title')}</TabsTrigger>
          </TabsList>

          <TabsContent value="reviews" className="space-y-6">
            <ReviewList
              onCreateReview={handleCreateReview}
              onEditReview={handleEditReview}
            />
          </TabsContent>

          <TabsContent value="templates" className="space-y-6">
            <ReviewTemplateManager
              onTemplateSelect={handleTemplateSelect}
            />
          </TabsContent>
        </Tabs>
      )}
    </div>
  )
}

export default ReviewsPage
