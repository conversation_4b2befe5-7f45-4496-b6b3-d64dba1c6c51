// Task Repository Interface - 任务仓储接口

use crate::domain::entities::{Task, TaskStatus};
use crate::shared::errors::Result;
use crate::shared::types::{Id, QueryParams, Priority};
use async_trait::async_trait;

/// 任务仓储接口
#[async_trait]
pub trait TaskRepository: Send + Sync {
    /// 根据ID查找任务
    async fn find_by_id(&self, id: &Id) -> Result<Option<Task>>;
    
    /// 根据标题查找任务
    async fn find_by_title(&self, title: &str) -> Result<Option<Task>>;
    
    /// 保存任务
    async fn save(&self, task: &Task) -> Result<()>;
    
    /// 更新任务
    async fn update(&self, task: &Task) -> Result<()>;
    
    /// 删除任务（软删除）
    async fn delete(&self, id: &Id) -> Result<()>;
    
    /// 查找所有活跃任务
    async fn find_all_active(&self) -> Result<Vec<Task>>;
    
    /// 根据项目ID查找任务
    async fn find_by_project_id(&self, project_id: &Id) -> Result<Vec<Task>>;
    
    /// 根据父任务ID查找子任务
    async fn find_by_parent_task_id(&self, parent_task_id: &Id) -> Result<Vec<Task>>;
    
    /// 根据状态查找任务
    async fn find_by_status(&self, status: &TaskStatus) -> Result<Vec<Task>>;
    
    /// 根据优先级查找任务
    async fn find_by_priority(&self, priority: &Priority) -> Result<Vec<Task>>;
    
    /// 查找即将到期的任务
    async fn find_due_soon(&self, days: u32) -> Result<Vec<Task>>;
    
    /// 查找已逾期的任务
    async fn find_overdue(&self) -> Result<Vec<Task>>;
    
    /// 查找今日任务
    async fn find_today_tasks(&self) -> Result<Vec<Task>>;
    
    /// 分页查询任务
    async fn find_with_pagination(&self, params: &QueryParams) -> Result<(Vec<Task>, u64)>;
    
    /// 获取任务总数
    async fn count_active_tasks(&self) -> Result<u64>;
    
    /// 根据进度范围查找任务
    async fn find_by_progress_range(&self, min_progress: f32, max_progress: f32) -> Result<Vec<Task>>;
    
    /// 搜索任务（按标题和描述）
    async fn search(&self, query: &str) -> Result<Vec<Task>>;
    
    /// 获取任务统计信息
    async fn get_statistics(&self) -> Result<TaskStatistics>;
    
    /// 获取项目的任务完成率
    async fn get_project_completion_rate(&self, project_id: &Id) -> Result<f32>;
    
    /// 查找根任务（没有父任务的任务）
    async fn find_root_tasks(&self) -> Result<Vec<Task>>;
    
    /// 查找任务树（包含所有子任务）
    async fn find_task_tree(&self, root_task_id: &Id) -> Result<Vec<Task>>;
}

/// 任务统计信息
#[derive(Debug, Clone)]
pub struct TaskStatistics {
    pub total_tasks: u64,
    pub active_tasks: u64,
    pub completed_tasks: u64,
    pub overdue_tasks: u64,
    pub today_tasks: u64,
    pub average_progress: f32,
    pub tasks_by_status: Vec<(TaskStatus, u64)>,
    pub tasks_by_priority: Vec<(Priority, u64)>,
    pub completion_rate: f32,
}
