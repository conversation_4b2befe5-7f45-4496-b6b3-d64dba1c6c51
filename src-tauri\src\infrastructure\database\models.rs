// Database Models - 数据库模型

use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct UserModel {
    pub id: String,
    pub username: String,
    pub email: Option<String>,
    pub display_name: Option<String>,
    pub avatar_url: Option<String>,
    pub preferences: String, // JSON string
    pub status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub version: i64,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ProjectModel {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub goals: Option<String>, // JSON string
    pub deliverables: Option<String>, // JSON string
    pub start_date: Option<chrono::DateTime<chrono::Utc>>,
    pub due_date: Option<chrono::DateTime<chrono::Utc>>,
    pub area_id: Option<String>,
    pub status: String,
    pub priority: String,
    pub progress: f64,
    pub tags: Option<String>, // JSON string
    pub entity_status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub version: i64,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct TaskModel {
    pub id: String,
    pub title: String,
    pub description: Option<String>,
    pub project_id: Option<String>,
    pub parent_task_id: Option<String>,
    pub due_date: Option<chrono::DateTime<chrono::Utc>>,
    pub status: String,
    pub priority: String,
    pub progress: f64,
    pub entity_status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub version: i64,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct AreaModel {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub standards: Option<String>, // JSON string
    pub color: Option<String>,
    pub icon: Option<String>,
    pub status: String,
    pub entity_status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub version: i64,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct HabitModel {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub area_id: String,
    pub habit_type: String,
    pub frequency: String, // JSON string
    pub target_value: Option<f64>,
    pub unit: Option<String>,
    pub entity_status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub version: i64,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ResourceModel {
    pub id: String,
    pub title: String,
    pub content: String,
    pub resource_type: String,
    pub file_path: Option<String>,
    pub tags: Option<String>, // JSON string
    pub links: Option<String>, // JSON string
    pub entity_status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub version: i64,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct InboxItemModel {
    pub id: String,
    pub content: String,
    pub item_type: String,
    pub priority: String,
    pub tags: Option<String>, // JSON string
    pub processed: bool,
    pub processed_at: Option<chrono::DateTime<chrono::Utc>>,
    pub entity_status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub version: i64,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ReviewModel {
    pub id: String,
    pub title: String,
    pub content: String,
    pub review_type: String,
    pub period_start: chrono::DateTime<chrono::Utc>,
    pub period_end: chrono::DateTime<chrono::Utc>,
    pub template_id: Option<String>,
    pub entity_status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub version: i64,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct NotificationModel {
    pub id: String,
    pub title: String,
    pub message: String,
    pub notification_type: String,
    pub priority: String,
    pub read: bool,
    pub read_at: Option<chrono::DateTime<chrono::Utc>>,
    pub scheduled_at: Option<chrono::DateTime<chrono::Utc>>,
    pub entity_status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub version: i64,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ChecklistModel {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub items: String, // JSON string
    pub entity_status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub version: i64,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct TemplateModel {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub template_type: String,
    pub content: String,
    pub variables: String, // JSON string
    pub entity_status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub version: i64,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct FileModel {
    pub id: String,
    pub name: String,
    pub path: String,
    pub file_type: String,
    pub size: i64,
    pub mime_type: Option<String>,
    pub checksum: Option<String>,
    pub entity_status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub version: i64,
}
