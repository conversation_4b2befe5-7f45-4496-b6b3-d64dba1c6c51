# PaoLife 可访问性设计规范

## 📋 概述

本文档定义了PaoLife应用的可访问性设计标准，确保所有用户，包括残障用户，都能有效地使用我们的产品。遵循WCAG 2.1 AA级标准。

## 🎯 可访问性原则

### 1. 四大原则 (POUR)

#### 可感知 (Perceivable)
- 信息和界面组件必须以用户能够感知的方式呈现
- 提供文本替代方案
- 确保足够的颜色对比度
- 支持屏幕阅读器

#### 可操作 (Operable)
- 界面组件和导航必须是可操作的
- 支持键盘导航
- 提供足够的操作时间
- 避免引起癫痫的内容

#### 可理解 (Understandable)
- 信息和界面操作必须是可理解的
- 文本内容可读且可理解
- 界面以可预测的方式出现和操作
- 帮助用户避免和纠正错误

#### 健壮 (Robust)
- 内容必须足够健壮，能被各种用户代理解释
- 兼容辅助技术
- 使用语义化的HTML
- 遵循Web标准

### 2. 设计目标

- **包容性**: 为所有能力水平的用户设计
- **独立性**: 用户能够独立完成任务
- **平等性**: 提供平等的用户体验
- **灵活性**: 适应不同的使用方式和偏好

## 🎨 视觉可访问性

### 1. 颜色对比度

#### WCAG 2.1 AA 标准
```css
/* 最低对比度要求 */
--contrast-normal-text: 4.5;    /* 正常文本 */
--contrast-large-text: 3.0;     /* 大文本 (18pt+) */
--contrast-ui-elements: 3.0;    /* UI组件 */
```

#### 颜色对比度检查
```css
/* 符合AA标准的颜色组合 */
.text-primary {
  color: #1f2937;              /* 对比度: 16.94 */
  background-color: #ffffff;
}

.text-secondary {
  color: #4b5563;              /* 对比度: 7.59 */
  background-color: #ffffff;
}

.text-muted {
  color: #6b7280;              /* 对比度: 5.74 */
  background-color: #ffffff;
}

/* 按钮对比度 */
.btn-primary {
  color: #ffffff;              /* 对比度: 5.74 */
  background-color: #2563eb;
}

.btn-secondary {
  color: #374151;              /* 对比度: 12.63 */
  background-color: #f3f4f6;
}
```

#### 对比度测试工具
- WebAIM Contrast Checker
- Colour Contrast Analyser
- Chrome DevTools Accessibility

### 2. 色彩独立性

#### 不依赖颜色传达信息
```html
<!-- ❌ 仅使用颜色 -->
<span style="color: red;">错误</span>

<!-- ✅ 颜色 + 图标 + 文字 -->
<span class="error">
  <svg class="icon-error" aria-hidden="true">...</svg>
  错误：请检查输入内容
</span>
```

#### 状态指示器设计
```css
/* 多重状态指示 */
.status-success {
  color: var(--color-success-700);
  background-color: var(--color-success-50);
  border-left: 4px solid var(--color-success-500);
}

.status-success::before {
  content: "✓";
  margin-right: 8px;
  font-weight: bold;
}

.status-error {
  color: var(--color-error-700);
  background-color: var(--color-error-50);
  border-left: 4px solid var(--color-error-500);
}

.status-error::before {
  content: "⚠";
  margin-right: 8px;
  font-weight: bold;
}
```

### 3. 字体和排版

#### 可读性标准
```css
/* 最小字体大小 */
.text-minimum {
  font-size: 16px;              /* 最小16px */
  line-height: 1.5;             /* 行高至少1.5倍 */
}

/* 大文本标准 */
.text-large {
  font-size: 18px;              /* 18pt = 24px */
  font-weight: bold;
}

/* 段落间距 */
.paragraph {
  margin-bottom: 1em;           /* 段落间距 */
}

/* 字符间距 */
.letter-spacing {
  letter-spacing: 0.12em;       /* 字符间距至少0.12em */
}

/* 单词间距 */
.word-spacing {
  word-spacing: 0.16em;         /* 单词间距至少0.16em */
}
```

#### 字体选择原则
- 选择清晰易读的字体
- 避免装饰性过强的字体
- 确保数字和字母易于区分
- 支持多语言字符

## ⌨️ 键盘可访问性

### 1. 键盘导航

#### Tab 顺序
```html
<!-- 逻辑的Tab顺序 -->
<form>
  <input type="text" tabindex="1" placeholder="姓名">
  <input type="email" tabindex="2" placeholder="邮箱">
  <input type="tel" tabindex="3" placeholder="电话">
  <button type="submit" tabindex="4">提交</button>
</form>
```

#### 焦点指示器
```css
/* 清晰的焦点指示 */
.focusable:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px var(--color-primary-100);
}

/* 移除默认outline，但保留自定义样式 */
.focusable:focus:not(:focus-visible) {
  outline: none;
  box-shadow: none;
}

/* 键盘焦点时显示 */
.focusable:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px var(--color-primary-100);
}
```

### 2. 键盘快捷键

#### 标准快捷键
```typescript
// 键盘快捷键映射
const keyboardShortcuts = {
  // 导航快捷键
  'Alt+1': () => navigateTo('/dashboard'),
  'Alt+2': () => navigateTo('/projects'),
  'Alt+3': () => navigateTo('/tasks'),
  'Alt+4': () => navigateTo('/areas'),
  
  // 操作快捷键
  'Ctrl+N': () => createNew(),
  'Ctrl+S': () => save(),
  'Ctrl+F': () => openSearch(),
  'Escape': () => closeModal(),
  
  // 可访问性快捷键
  'Alt+M': () => toggleMainMenu(),
  'Alt+S': () => skipToContent(),
  'Alt+H': () => showHelp(),
};

// 快捷键处理
document.addEventListener('keydown', (event) => {
  const key = [
    event.ctrlKey && 'Ctrl',
    event.altKey && 'Alt',
    event.shiftKey && 'Shift',
    event.key
  ].filter(Boolean).join('+');
  
  const handler = keyboardShortcuts[key];
  if (handler) {
    event.preventDefault();
    handler();
  }
});
```

#### 快捷键提示
```html
<!-- 快捷键提示 -->
<button title="保存 (Ctrl+S)">
  <span class="sr-only">保存，快捷键Ctrl+S</span>
  <svg aria-hidden="true">...</svg>
  保存
</button>
```

### 3. 跳过链接

#### 跳过导航
```html
<!-- 跳过链接 -->
<a href="#main-content" class="skip-link">
  跳过导航，直接到主内容
</a>

<nav aria-label="主导航">
  <!-- 导航内容 -->
</nav>

<main id="main-content" tabindex="-1">
  <!-- 主要内容 -->
</main>
```

```css
/* 跳过链接样式 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary-600);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}
```

## 🔊 屏幕阅读器支持

### 1. 语义化HTML

#### 正确的HTML结构
```html
<!-- ✅ 语义化结构 -->
<header>
  <h1>PaoLife - 个人生产力管理</h1>
  <nav aria-label="主导航">
    <ul>
      <li><a href="/dashboard">仪表盘</a></li>
      <li><a href="/projects">项目</a></li>
      <li><a href="/tasks">任务</a></li>
    </ul>
  </nav>
</header>

<main>
  <section aria-labelledby="projects-heading">
    <h2 id="projects-heading">我的项目</h2>
    <article>
      <h3>项目名称</h3>
      <p>项目描述...</p>
    </article>
  </section>
</main>

<aside aria-label="侧边栏">
  <h2>快速操作</h2>
  <!-- 侧边栏内容 -->
</aside>

<footer>
  <p>&copy; 2024 PaoLife. 保留所有权利。</p>
</footer>
```

### 2. ARIA 标签和属性

#### 常用ARIA属性
```html
<!-- 标签和描述 -->
<input 
  type="email" 
  id="email"
  aria-label="邮箱地址"
  aria-describedby="email-help"
  aria-required="true"
>
<div id="email-help">请输入有效的邮箱地址</div>

<!-- 状态和属性 -->
<button 
  aria-expanded="false"
  aria-controls="menu"
  aria-haspopup="true"
>
  菜单
</button>

<ul id="menu" aria-hidden="true">
  <li><a href="/profile">个人资料</a></li>
  <li><a href="/settings">设置</a></li>
</ul>

<!-- 实时区域 -->
<div 
  role="alert" 
  aria-live="polite"
  aria-atomic="true"
>
  保存成功
</div>

<!-- 进度指示 -->
<div 
  role="progressbar"
  aria-valuenow="65"
  aria-valuemin="0"
  aria-valuemax="100"
  aria-label="项目完成进度"
>
  65%
</div>
```

#### 复杂组件的ARIA
```html
<!-- 标签页 -->
<div role="tablist" aria-label="项目视图">
  <button 
    role="tab"
    aria-selected="true"
    aria-controls="list-panel"
    id="list-tab"
  >
    列表视图
  </button>
  <button 
    role="tab"
    aria-selected="false"
    aria-controls="grid-panel"
    id="grid-tab"
  >
    网格视图
  </button>
</div>

<div 
  role="tabpanel"
  id="list-panel"
  aria-labelledby="list-tab"
>
  <!-- 列表内容 -->
</div>

<!-- 模态框 -->
<div 
  role="dialog"
  aria-modal="true"
  aria-labelledby="modal-title"
  aria-describedby="modal-description"
>
  <h2 id="modal-title">确认删除</h2>
  <p id="modal-description">此操作无法撤销，确定要删除这个项目吗？</p>
  <button>取消</button>
  <button>删除</button>
</div>
```

### 3. 屏幕阅读器优化

#### 隐藏装饰性内容
```css
/* 对屏幕阅读器隐藏 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 仅对屏幕阅读器显示 */
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}
```

#### 描述性文本
```html
<!-- 为图标添加描述 -->
<button>
  <svg aria-hidden="true">...</svg>
  <span class="sr-only">删除项目</span>
</button>

<!-- 为链接添加上下文 -->
<a href="/project/123">
  查看详情
  <span class="sr-only">关于"网站重设计"项目</span>
</a>
```

## 📱 移动端可访问性

### 1. 触摸目标

#### 最小触摸区域
```css
/* 最小44px触摸目标 */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 扩展小元素的触摸区域 */
.icon-button {
  padding: 12px;
  margin: -12px;
}
```

### 2. 手势替代方案

#### 提供多种操作方式
```html
<!-- 滑动删除 + 按钮删除 -->
<div class="list-item">
  <div class="content">项目内容</div>
  <div class="actions">
    <button aria-label="编辑项目">编辑</button>
    <button aria-label="删除项目">删除</button>
  </div>
</div>
```

### 3. 方向和动作

#### 避免依赖设备方向
```css
/* 响应式设计，不依赖方向 */
@media (orientation: portrait) {
  .layout {
    flex-direction: column;
  }
}

@media (orientation: landscape) {
  .layout {
    flex-direction: row;
  }
}
```

## 🧪 可访问性测试

### 1. 自动化测试

#### 测试工具
```typescript
// 使用 axe-core 进行自动化测试
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

test('页面应该没有可访问性违规', async () => {
  const { container } = render(<App />);
  const results = await axe(container);
  expect(results).toHaveNoViolations();
});
```

#### CI/CD 集成
```yaml
# GitHub Actions 可访问性测试
- name: Run accessibility tests
  run: |
    npm run test:a11y
    npm run lighthouse:a11y
```

### 2. 手动测试

#### 测试检查清单
- [ ] 仅使用键盘导航整个应用
- [ ] 使用屏幕阅读器测试
- [ ] 检查颜色对比度
- [ ] 测试不同缩放级别 (200%)
- [ ] 验证焦点指示器
- [ ] 测试表单验证和错误消息

#### 测试工具
- **屏幕阅读器**: NVDA, JAWS, VoiceOver
- **浏览器扩展**: axe DevTools, WAVE
- **对比度检查**: Colour Contrast Analyser
- **键盘测试**: 禁用鼠标进行测试

### 3. 用户测试

#### 包容性用户测试
- 邀请残障用户参与测试
- 收集真实用户反馈
- 持续改进可访问性
- 建立可访问性反馈渠道

## 📋 可访问性检查清单

### 感知性
- [ ] 所有图像都有替代文本
- [ ] 颜色对比度符合AA标准
- [ ] 不仅依赖颜色传达信息
- [ ] 支持200%缩放而不丢失功能

### 可操作性
- [ ] 所有功能都可通过键盘访问
- [ ] 焦点指示器清晰可见
- [ ] 没有闪烁内容
- [ ] 用户可以控制时间限制

### 可理解性
- [ ] 页面有清晰的标题
- [ ] 导航一致且可预测
- [ ] 表单有清晰的标签和错误消息
- [ ] 语言标记正确

### 健壮性
- [ ] 使用语义化HTML
- [ ] ARIA标签使用正确
- [ ] 兼容辅助技术
- [ ] 代码通过验证

---

**文档版本**: 1.0  
**创建日期**: 2024年12月  
**维护者**: 可访问性团队  
**下次更新**: 根据可访问性标准更新
