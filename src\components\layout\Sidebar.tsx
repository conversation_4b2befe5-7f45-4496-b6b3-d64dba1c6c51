// Sidebar Component - 侧边栏组件
// 应用程序主导航侧边栏

import { JSX, Show, For, createSignal } from 'solid-js';
import { cn } from '@/lib/utils';
import { Button } from '../ui/button';

export interface MenuItem {
  id: string;
  label: string;
  icon: JSX.Element;
  path?: string;
  badge?: string | number;
  children?: MenuItem[];
  disabled?: boolean;
  onClick?: () => void;
}

export interface SidebarProps {
  items: MenuItem[];
  activeItem?: string;
  collapsed?: boolean;
  onItemClick?: (item: MenuItem) => void;
  onToggleCollapse?: () => void;
  user?: {
    name: string;
    avatar?: string;
    email?: string;
  };
  onUserMenuClick?: () => void;
  onSettingsClick?: () => void;
  class?: string;
}

export function Sidebar(props: SidebarProps) {
  const [expandedItems, setExpandedItems] = createSignal<Set<string>>(new Set());

  const toggleExpanded = (itemId: string) => {
    const expanded = expandedItems();
    const newExpanded = new Set(expanded);
    
    if (expanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    
    setExpandedItems(newExpanded);
  };

  const handleItemClick = (item: MenuItem) => {
    if (item.children && item.children.length > 0) {
      toggleExpanded(item.id);
    } else {
      props.onItemClick?.(item);
      item.onClick?.();
    }
  };

  const isExpanded = (itemId: string) => expandedItems().has(itemId);
  const isActive = (itemId: string) => props.activeItem === itemId;

  return (
    <aside class={cn(
      'flex flex-col h-full bg-card border-r border-border transition-all duration-300',
      props.collapsed ? 'w-16' : 'w-64',
      props.class
    )}>
      {/* 侧边栏头部 */}
      <div class="flex items-center justify-between p-4 border-b border-border">
        <Show when={!props.collapsed}>
          <div class="flex items-center space-x-2">
            <div class="h-8 w-8 rounded-lg bg-primary text-primary-foreground flex items-center justify-center font-bold text-sm">
              P
            </div>
            <span class="font-semibold text-lg">泡泡言</span>
          </div>
        </Show>
        
        <Button
          variant="ghost"
          size="icon-sm"
          onClick={props.onToggleCollapse}
          aria-label={props.collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class={cn(
              'transition-transform duration-200',
              props.collapsed && 'rotate-180'
            )}
          >
            <path d="m15 18-6-6 6-6" />
          </svg>
        </Button>
      </div>

      {/* 导航菜单 */}
      <nav class="flex-1 overflow-y-auto p-2">
        <ul class="space-y-1">
          <For each={props.items}>
            {(item) => (
              <SidebarItem
                item={item}
                collapsed={props.collapsed}
                active={isActive(item.id)}
                expanded={isExpanded(item.id)}
                onItemClick={handleItemClick}
                level={0}
              />
            )}
          </For>
        </ul>
      </nav>

      {/* 侧边栏底部 - 用户信息和设置 */}
      <div class="p-2 border-t border-border">
        <Show when={props.user}>
          <UserSection
            user={props.user!}
            collapsed={props.collapsed}
            onUserMenuClick={props.onUserMenuClick}
            onSettingsClick={props.onSettingsClick}
          />
        </Show>
      </div>
    </aside>
  );
}

// 侧边栏菜单项组件
interface SidebarItemProps {
  item: MenuItem;
  collapsed?: boolean;
  active?: boolean;
  expanded?: boolean;
  onItemClick: (item: MenuItem) => void;
  level: number;
}

function SidebarItem(props: SidebarItemProps) {
  const hasChildren = () => props.item.children && props.item.children.length > 0;
  const paddingLeft = () => `${(props.level + 1) * 12}px`;

  return (
    <li>
      <Button
        variant="ghost"
        size="sm"
        disabled={props.item.disabled}
        onClick={() => props.onItemClick(props.item)}
        class={cn(
          'w-full justify-start h-10 px-3 font-normal transition-colors',
          props.active && 'bg-accent text-accent-foreground font-medium',
          props.collapsed && 'px-2 justify-center',
          props.level > 0 && !props.collapsed && 'ml-4'
        )}
        style={!props.collapsed && props.level > 0 ? { 'padding-left': paddingLeft() } : {}}
      >
        {/* 图标 */}
        <div class={cn(
          'h-4 w-4 flex-shrink-0',
          !props.collapsed && 'mr-3'
        )}>
          {props.item.icon}
        </div>

        {/* 标签和徽章 */}
        <Show when={!props.collapsed}>
          <div class="flex-1 flex items-center justify-between min-w-0">
            <span class="truncate">{props.item.label}</span>
            
            <div class="flex items-center space-x-1">
              {/* 徽章 */}
              <Show when={props.item.badge}>
                <span class="inline-flex items-center justify-center px-2 py-1 text-xs font-medium bg-primary text-primary-foreground rounded-full min-w-[20px] h-5">
                  {props.item.badge}
                </span>
              </Show>
              
              {/* 展开/收起图标 */}
              <Show when={hasChildren()}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class={cn(
                    'transition-transform duration-200',
                    props.expanded && 'rotate-90'
                  )}
                >
                  <path d="m9 18 6-6-6-6" />
                </svg>
              </Show>
            </div>
          </div>
        </Show>
      </Button>

      {/* 子菜单 */}
      <Show when={hasChildren() && props.expanded && !props.collapsed}>
        <ul class="mt-1 space-y-1">
          <For each={props.item.children}>
            {(child) => (
              <SidebarItem
                item={child}
                collapsed={props.collapsed}
                active={props.active}
                expanded={false}
                onItemClick={props.onItemClick}
                level={props.level + 1}
              />
            )}
          </For>
        </ul>
      </Show>
    </li>
  );
}

// 用户信息区域组件
interface UserSectionProps {
  user: {
    name: string;
    avatar?: string;
    email?: string;
  };
  collapsed?: boolean;
  onUserMenuClick?: () => void;
  onSettingsClick?: () => void;
}

function UserSection(props: UserSectionProps) {
  const [showUserMenu, setShowUserMenu] = createSignal(false);

  const handleUserClick = () => {
    setShowUserMenu(!showUserMenu());
    props.onUserMenuClick?.();
  };

  return (
    <div class="space-y-1">
      {/* 用户信息按钮 */}
      <Show when={!props.collapsed}>
        <div class="relative">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleUserClick}
            class="w-full justify-start h-10 px-3 font-normal"
          >
            <div class="flex items-center space-x-3 min-w-0 flex-1">
              <Show
                when={props.user.avatar}
                fallback={
                  <div class="h-6 w-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs font-medium flex-shrink-0">
                    {props.user.name.charAt(0).toUpperCase()}
                  </div>
                }
              >
                <img
                  src={props.user.avatar}
                  alt={props.user.name}
                  class="h-6 w-6 rounded-full object-cover flex-shrink-0"
                />
              </Show>
              <div class="text-left min-w-0 flex-1">
                <div class="text-sm font-medium truncate">{props.user.name}</div>
                <Show when={props.user.email}>
                  <div class="text-xs text-muted-foreground truncate">{props.user.email}</div>
                </Show>
              </div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class={cn(
                  'transition-transform duration-200 flex-shrink-0',
                  showUserMenu() && 'rotate-180'
                )}
              >
                <path d="m6 9 6 6 6-6" />
              </svg>
            </div>
          </Button>

          {/* 用户下拉菜单 */}
          <Show when={showUserMenu()}>
            <div class="absolute bottom-full left-0 right-0 mb-2 rounded-md border border-border bg-popover p-1 shadow-md animate-in slide-in-from-bottom-2">
              <button
                type="button"
                onClick={props.onSettingsClick}
                class="flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground"
              >
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                设置
              </button>
              <div class="h-px bg-border my-1" />
              <button type="button" class="flex w-full items-center rounded-sm px-2 py-1.5 text-sm text-destructive hover:bg-destructive hover:text-destructive-foreground">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                退出登录
              </button>
            </div>
          </Show>
        </div>
      </Show>

      {/* 收起状态下的用户头像 */}
      <Show when={props.collapsed}>
        <Button
          variant="ghost"
          size="icon-sm"
          onClick={handleUserClick}
          class="w-full justify-center"
        >
          <Show
            when={props.user.avatar}
            fallback={
              <div class="h-6 w-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs font-medium">
                {props.user.name.charAt(0).toUpperCase()}
              </div>
            }
          >
            <img
              src={props.user.avatar}
              alt={props.user.name}
              class="h-6 w-6 rounded-full object-cover"
            />
          </Show>
        </Button>
      </Show>
    </div>
  );
}

// 默认菜单项配置
export const defaultMenuItems: MenuItem[] = [
  {
    id: 'dashboard',
    label: '仪表盘',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <rect width="7" height="9" x="3" y="3" rx="1" />
        <rect width="7" height="5" x="14" y="3" rx="1" />
        <rect width="7" height="9" x="14" y="12" rx="1" />
        <rect width="7" height="5" x="3" y="16" rx="1" />
      </svg>
    ),
    path: '/dashboard',
  },
  {
    id: 'inbox',
    label: '收件箱',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <polyline points="22,12 18,12 15,21 9,3 6,12 2,12" />
      </svg>
    ),
    path: '/inbox',
    badge: 5,
  },
  {
    id: 'projects',
    label: '项目管理',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M8 6h10" />
        <path d="M6 12h9" />
        <path d="M11 18h7" />
      </svg>
    ),
    path: '/projects',
  },
  {
    id: 'areas',
    label: '领域管理',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M3 3v18h18" />
        <path d="M18.7 8a3 3 0 0 0-5.4 0" />
        <path d="M16.4 12a3 3 0 0 0-5.4 0" />
        <path d="M14.1 16a3 3 0 0 0-5.4 0" />
      </svg>
    ),
    path: '/areas',
  },
  {
    id: 'resources',
    label: '资源库',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
        <polyline points="14,2 14,8 20,8" />
        <line x1="16" y1="13" x2="8" y2="13" />
        <line x1="16" y1="17" x2="8" y2="17" />
        <polyline points="10,9 9,9 8,9" />
      </svg>
    ),
    path: '/resources',
  },
  {
    id: 'archive',
    label: '归档管理',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <rect width="20" height="5" x="2" y="3" rx="1" />
        <path d="M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8" />
        <path d="M10 12h4" />
      </svg>
    ),
    path: '/archive',
  },
  {
    id: 'review',
    label: '复盘总结',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h11a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-4" />
        <path d="M9 7V4a2 2 0 0 1 4 0v3" />
        <circle cx="12" cy="16" r="1" />
      </svg>
    ),
    path: '/review',
  },
  {
    id: 'settings',
    label: '设置',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
        <circle cx="12" cy="12" r="3" />
      </svg>
    ),
    path: '/settings',
  },
];
