/**
 * 习惯追踪数据源适配器
 * 将现有的API接口适配为统一的习惯数据源接口
 */

import type {
  HabitDataSource,
  BaseHabit,
  HabitRecord,
  HabitStatistics,
  HabitProgress,
  CreateHabitData,
  UpdateHabitData,
  CreateRecordData,
  HabitFilter,
  QueryOptions
} from './types'

// 导入现有的API（需要根据实际项目结构调整）
import { apiService } from '../../services/api'

/**
 * 默认习惯数据源适配器
 * 适配现有的数据库API为习惯追踪接口
 */
export class DefaultHabitDataSource implements HabitDataSource {
  async getHabits(areaId: string, filter?: HabitFilter): Promise<BaseHabit[]> {
    try {
      // 暂时返回空数组，等待实际API实现
      console.warn('getHabits not implemented yet')
      return []
    } catch (error) {
      console.error('Failed to get habits:', error)
      throw error
    }
  }

  async getHabit(id: string): Promise<BaseHabit | null> {
    try {
      // 暂时返回null，等待实际API实现
      console.warn('getHabit not implemented yet')
      return null
    } catch (error) {
      console.error('Failed to get habit:', error)
      throw error
    }
  }

  async createHabit(data: CreateHabitData): Promise<BaseHabit> {
    try {
      // 转换数据格式以匹配现有API
      const habitData = {
        name: data.name,
        description: data.description,
        areaId: data.areaId,
        type: data.type,
        frequency: data.frequency,
        target: data.target,
        unit: data.unit,
        color: data.color,
        status: 'active' as const,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      // 暂时返回模拟数据，等待实际API实现
      console.warn('createHabit not implemented yet')
      return {
        ...habitData,
        id: this.generateId(),
        streak: 0,
        longestStreak: 0,
        completionRate: 0
      } as BaseHabit
    } catch (error) {
      console.error('Failed to create habit:', error)
      throw error
    }
  }

  async updateHabit(id: string, data: UpdateHabitData): Promise<BaseHabit> {
    try {
      // 暂时返回模拟数据，等待实际API实现
      console.warn('updateHabit not implemented yet')
      return {
        id,
        ...data,
        updatedAt: new Date()
      } as BaseHabit
    } catch (error) {
      console.error('Failed to update habit:', error)
      throw error
    }
  }

  async deleteHabit(id: string): Promise<void> {
    try {
      // 暂时不执行任何操作，等待实际API实现
      console.warn('deleteHabit not implemented yet')
    } catch (error) {
      console.error('Failed to delete habit:', error)
      throw error
    }
  }

  async getRecords(habitId: string, dateRange?: { start: Date; end: Date }): Promise<HabitRecord[]> {
    try {
      // 暂时返回空数组，等待实际API实现
      console.warn('getRecords not implemented yet')
      return []
    } catch (error) {
      console.error('Failed to get records:', error)
      throw error
    }
  }

  async createRecord(data: CreateRecordData): Promise<HabitRecord> {
    try {
      // 暂时返回模拟数据，等待实际API实现
      console.warn('createRecord not implemented yet')
      const recordData = {
        id: this.generateId(),
        ...data,
        source: 'manual' as const,
        createdAt: new Date()
      }
      return recordData as HabitRecord
    } catch (error) {
      console.error('Failed to create record:', error)
      throw error
    }
  }

  async updateRecord(id: string, data: Partial<CreateRecordData>): Promise<HabitRecord> {
    try {
      // 暂时返回模拟数据，等待实际API实现
      console.warn('updateRecord not implemented yet')
      return {
        id,
        ...data
      } as HabitRecord
    } catch (error) {
      console.error('Failed to update record:', error)
      throw error
    }
  }

  async deleteRecord(id: string): Promise<void> {
    try {
      // 暂时不执行任何操作，等待实际API实现
      console.warn('deleteRecord not implemented yet')
    } catch (error) {
      console.error('Failed to delete record:', error)
      throw error
    }
  }

  async getStatistics(areaId: string): Promise<HabitStatistics> {
    try {
      const habits = await this.getHabits(areaId)
      const activeHabits = habits.filter(h => h.isActive)
      
      // 获取今日记录
      const today = new Date().toISOString().split('T')[0]
      const todayRecords = await Promise.all(
        activeHabits.map(habit => 
          this.getRecords(habit.id, {
            start: new Date(today),
            end: new Date(today)
          })
        )
      )
      
      const completedToday = todayRecords.flat().filter(r => r.completed).length
      
      // 计算平均完成率
      const averageCompletion = habits.length > 0
        ? habits.reduce((sum, h) => sum + h.completionRate, 0) / habits.length
        : 0
      
      // 获取最近7天和30天的数据
      const weeklyProgress = await this.getProgressData(areaId, 7)
      const monthlyProgress = await this.getProgressData(areaId, 30)
      
      // 统计习惯类型分布
      const habitsByType = habits.reduce((acc, habit) => {
        acc[habit.type] = (acc[habit.type] || 0) + 1
        return acc
      }, {} as Record<string, number>)
      
      // 统计频率分布
      const habitsByFrequency = habits.reduce((acc, habit) => {
        acc[habit.frequency] = (acc[habit.frequency] || 0) + 1
        return acc
      }, {} as Record<string, number>)
      
      return {
        totalHabits: habits.length,
        activeHabits: activeHabits.length,
        completedToday,
        averageCompletion,
        totalStreak: Math.max(...habits.map(h => h.streak), 0),
        longestStreak: Math.max(...habits.map(h => h.longestStreak), 0),
        weeklyProgress,
        monthlyProgress,
        habitsByType,
        habitsByFrequency
      }
    } catch (error) {
      console.error('Failed to get statistics:', error)
      throw error
    }
  }

  async getProgress(habitId: string): Promise<HabitProgress> {
    try {
      const habit = await this.getHabit(habitId)
      if (!habit) throw new Error('Habit not found')
      
      // 获取最近的记录
      const records = await this.getRecords(habitId, {
        start: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // 最近一年
        end: new Date()
      })
      
      // 计算当前连击
      const currentStreak = this.calculateCurrentStreak(records)
      
      // 计算各时期进度
      const weekProgress = this.calculatePeriodProgress(records, 7)
      const monthProgress = this.calculatePeriodProgress(records, 30)
      const yearProgress = this.calculatePeriodProgress(records, 365)
      
      // 计算趋势
      const trend = this.calculateTrend(records)
      
      // 获取最后完成日期
      const lastCompleted = records
        .filter(r => r.completed)
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0]
      
      return {
        habitId,
        currentStreak,
        weekProgress,
        monthProgress,
        yearProgress,
        trend,
        lastCompleted: lastCompleted ? new Date(lastCompleted.date) : undefined,
        nextDue: this.calculateNextDue(habit, records)
      }
    } catch (error) {
      console.error('Failed to get progress:', error)
      throw error
    }
  }

  async bulkCreateRecords(records: CreateRecordData[]): Promise<HabitRecord[]> {
    try {
      const createdRecords = await Promise.all(
        records.map(record => this.createRecord(record))
      )
      
      // 批量更新习惯统计
      const habitIds = [...new Set(records.map(r => r.habitId))]
      await Promise.all(habitIds.map(id => this.updateHabitStatistics(id)))
      
      return createdRecords
    } catch (error) {
      console.error('Failed to bulk create records:', error)
      throw error
    }
  }

  async bulkUpdateHabits(updates: Array<{ id: string; data: UpdateHabitData }>): Promise<BaseHabit[]> {
    try {
      return await Promise.all(
        updates.map(update => this.updateHabit(update.id, update.data))
      )
    } catch (error) {
      console.error('Failed to bulk update habits:', error)
      throw error
    }
  }

  // 私有辅助方法
  private convertToBaseHabit(habit: any): BaseHabit {
    return {
      id: habit.id,
      name: habit.name,
      description: habit.description,
      areaId: habit.areaId,
      type: habit.type,
      frequency: habit.frequency,
      target: habit.target || 1,
      unit: habit.unit,
      color: habit.color || '#3b82f6',
      status: habit.status || 'active',
      streak: habit.streak || 0,
      longestStreak: habit.longestStreak || 0,
      completionRate: habit.completionRate || 0,
      createdAt: new Date(habit.createdAt),
      updatedAt: new Date(habit.updatedAt),
      isActive: habit.isActive !== false
    }
  }

  private convertToHabitRecord(record: any): HabitRecord {
    return {
      id: record.id,
      habitId: record.habitId,
      date: record.date,
      completed: record.completed,
      value: record.value,
      note: record.note,
      source: record.source || 'manual',
      createdAt: new Date(record.createdAt)
    }
  }

  private applyFilter(habits: BaseHabit[], filter?: HabitFilter): BaseHabit[] {
    if (!filter) return habits
    
    return habits.filter(habit => {
      if (filter.type && !filter.type.includes(habit.type)) return false
      if (filter.frequency && !filter.frequency.includes(habit.frequency)) return false
      if (filter.status && !filter.status.includes(habit.status)) return false
      if (filter.search) {
        const search = filter.search.toLowerCase()
        if (!habit.name.toLowerCase().includes(search) && 
            !habit.description?.toLowerCase().includes(search)) {
          return false
        }
      }
      return true
    })
  }

  private async updateHabitStatistics(habitId: string): Promise<void> {
    try {
      const records = await this.getRecords(habitId)
      const currentStreak = this.calculateCurrentStreak(records)
      const completionRate = this.calculatePeriodProgress(records, 30)
      const longestStreak = this.calculateLongestStreak(records)
      
      // 暂时不执行任何操作，等待实际API实现
      console.warn('updateHabitStatistics not implemented yet')
    } catch (error) {
      console.error('Failed to update habit statistics:', error)
    }
  }

  private calculateCurrentStreak(records: HabitRecord[]): number {
    const sortedRecords = records
      .filter(r => r.completed)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    
    let streak = 0
    const today = new Date()
    
    for (let i = 0; i < sortedRecords.length; i++) {
      const recordDate = new Date(sortedRecords[i].date)
      const expectedDate = new Date(today)
      expectedDate.setDate(today.getDate() - i)
      
      if (recordDate.toDateString() === expectedDate.toDateString()) {
        streak++
      } else {
        break
      }
    }
    
    return streak
  }

  private calculateLongestStreak(records: HabitRecord[]): number {
    const completedRecords = records
      .filter(r => r.completed)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    
    let maxStreak = 0
    let currentStreak = 0
    let lastDate: Date | null = null
    
    for (const record of completedRecords) {
      const recordDate = new Date(record.date)
      
      if (lastDate) {
        const daysDiff = Math.floor((recordDate.getTime() - lastDate.getTime()) / (1000 * 60 * 60 * 24))
        
        if (daysDiff === 1) {
          currentStreak++
        } else {
          maxStreak = Math.max(maxStreak, currentStreak)
          currentStreak = 1
        }
      } else {
        currentStreak = 1
      }
      
      lastDate = recordDate
    }
    
    return Math.max(maxStreak, currentStreak)
  }

  private calculatePeriodProgress(records: HabitRecord[], days: number): number {
    const endDate = new Date()
    const startDate = new Date(endDate.getTime() - (days - 1) * 24 * 60 * 60 * 1000)
    
    const periodRecords = records.filter(r => {
      const recordDate = new Date(r.date)
      return recordDate >= startDate && recordDate <= endDate
    })
    
    const completedCount = periodRecords.filter(r => r.completed).length
    return days > 0 ? Math.round((completedCount / days) * 100) : 0
  }

  private calculateTrend(records: HabitRecord[]): 'up' | 'down' | 'stable' {
    if (records.length < 14) return 'stable'
    
    const recentWeek = this.calculatePeriodProgress(records.slice(-7), 7)
    const previousWeek = this.calculatePeriodProgress(records.slice(-14, -7), 7)
    
    if (recentWeek > previousWeek * 1.1) return 'up'
    if (recentWeek < previousWeek * 0.9) return 'down'
    return 'stable'
  }

  private calculateNextDue(habit: BaseHabit, records: HabitRecord[]): Date | undefined {
    // 根据习惯频率计算下次到期时间
    const lastRecord = records
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0]
    
    if (!lastRecord) return new Date()
    
    const lastDate = new Date(lastRecord.date)
    const nextDue = new Date(lastDate)
    
    switch (habit.frequency) {
      case 'daily':
        nextDue.setDate(nextDue.getDate() + 1)
        break
      case 'weekly':
        nextDue.setDate(nextDue.getDate() + 7)
        break
      case 'monthly':
        nextDue.setMonth(nextDue.getMonth() + 1)
        break
    }
    
    return nextDue
  }

  private async getProgressData(areaId: string, days: number): Promise<number[]> {
    try {
      const habits = await this.getHabits(areaId)
      const activeHabits = habits.filter(h => h.isActive)
      
      const progressData: number[] = []
      
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        const dateString = date.toISOString().split('T')[0]
        
        const dayRecords = await Promise.all(
          activeHabits.map(habit => 
            this.getRecords(habit.id, {
              start: new Date(dateString),
              end: new Date(dateString)
            })
          )
        )
        
        const completedCount = dayRecords.flat().filter(r => r.completed).length
        const completionRate = activeHabits.length > 0 
          ? Math.round((completedCount / activeHabits.length) * 100)
          : 0
        
        progressData.push(completionRate)
      }
      
      return progressData
    } catch (error) {
      console.error('Failed to get progress data:', error)
      return Array(days).fill(0)
    }
  }
}

/**
 * 创建习惯数据源实例
 */
export function createHabitDataSource(): HabitDataSource {
  return new DefaultHabitDataSource()
}
