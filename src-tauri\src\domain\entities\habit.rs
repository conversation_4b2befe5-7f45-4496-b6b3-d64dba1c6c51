// Habit Entity - 习惯实体

use crate::shared::types::{Id, Metadata, EntityStatus};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Habit {
    pub id: Id,
    pub name: String,
    pub description: Option<String>,
    pub area_id: Id,
    pub habit_type: HabitType,
    pub frequency: HabitFrequency,
    pub target_value: Option<f64>,
    pub unit: Option<String>,
    pub entity_status: EntityStatus,
    pub metadata: Metadata,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum HabitType {
    Boolean,
    Numeric,
    Duration,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HabitFrequency {
    Daily,
    Weekly(u8), // times per week
    Monthly(u8), // times per month
}

impl Habit {
    pub fn new(name: String, area_id: Id, habit_type: HabitType) -> Self {
        Self {
            id: crate::shared::utils::IdGenerator::new_id_with_prefix("habit"),
            name,
            description: None,
            area_id,
            habit_type,
            frequency: HabitFrequency::Daily,
            target_value: None,
            unit: None,
            entity_status: EntityStatus::Active,
            metadata: Metadata::default(),
        }
    }
}
