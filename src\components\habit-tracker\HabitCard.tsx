/**
 * 习惯卡片组件
 * 单个习惯的详细展示和交互
 */

import { createSignal, Show, For } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { Input } from '../ui/input'
import { 
  CheckCircle, 
  Circle, 
  Edit, 
  Trash2, 
  Target,
  Flame,
  Calendar,
  TrendingUp,
  MoreHorizontal,
  Plus,
  Minus
} from 'lucide-solid'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { cn } from '../../lib/utils'

import type { HabitCardProps, HabitRecord } from './types'

export function HabitCard(props: HabitCardProps) {
  const [showValueInput, setShowValueInput] = createSignal(false)
  const [inputValue, setInputValue] = createSignal('')

  // 获取今日记录
  const getTodayRecord = () => {
    const today = props.selectedDate?.toISOString().split('T')[0] || new Date().toISOString().split('T')[0]
    return props.records.find(r => r.date === today)
  }

  // 获取最近7天记录
  const getRecentRecords = () => {
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - i)
      return date.toISOString().split('T')[0]
    }).reverse()

    return last7Days.map(date => {
      const record = props.records.find(r => r.date === date)
      return {
        date,
        completed: record?.completed || false,
        value: record?.value || 0
      }
    })
  }

  // 计算完成率
  const getCompletionRate = () => {
    const recentRecords = getRecentRecords()
    const completedCount = recentRecords.filter(r => r.completed).length
    return Math.round((completedCount / recentRecords.length) * 100)
  }

  // 获取习惯类型颜色
  const getHabitTypeColor = () => {
    switch (props.habit.type) {
      case 'boolean':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'numeric':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'duration':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // 格式化频率
  const formatFrequency = () => {
    switch (props.habit.frequency) {
      case 'daily':
        return 'Daily'
      case 'weekly':
        return 'Weekly'
      case 'monthly':
        return 'Monthly'
      default:
        return props.habit.frequency
    }
  }

  // 处理记录切换
  const handleToggle = () => {
    const today = props.selectedDate?.toISOString().split('T')[0] || new Date().toISOString().split('T')[0]
    const todayRecord = getTodayRecord()
    props.onRecordToggle?.(props.habit.id, today, !todayRecord?.completed)
  }

  // 处理数值设置
  const handleValueSet = () => {
    const today = props.selectedDate?.toISOString().split('T')[0] || new Date().toISOString().split('T')[0]
    const value = parseFloat(inputValue()) || 0
    props.onValueSet?.(props.habit.id, today, value)
    setShowValueInput(false)
    setInputValue('')
  }

  // 快速增减数值
  const handleQuickValue = (delta: number) => {
    const today = props.selectedDate?.toISOString().split('T')[0] || new Date().toISOString().split('T')[0]
    const todayRecord = getTodayRecord()
    const currentValue = todayRecord?.value || 0
    const newValue = Math.max(0, currentValue + delta)
    props.onValueSet?.(props.habit.id, today, newValue)
  }

  const todayRecord = getTodayRecord()
  const recentRecords = getRecentRecords()
  const completionRate = getCompletionRate()

  return (
    <Card class={cn(
      "hover:shadow-md transition-all duration-200",
      props.compact && "p-2",
      todayRecord?.completed && "ring-2 ring-green-200 bg-green-50/30"
    )}>
      <CardHeader class={cn("pb-3", props.compact && "pb-2")}>
        <div class="flex items-start justify-between">
          <div class="flex-1 min-w-0">
            <CardTitle class="flex items-center gap-2 text-lg">
              <div 
                class="w-3 h-3 rounded-full" 
                style={{ backgroundColor: props.habit.color }}
              />
              <span class="truncate">{props.habit.name}</span>
              <Badge variant="outline" class={cn("text-xs", getHabitTypeColor())}>
                {props.habit.type}
              </Badge>
            </CardTitle>
            
            <Show when={props.habit.description && !props.compact}>
              <CardDescription class="mt-1">
                {props.habit.description}
              </CardDescription>
            </Show>
            
            <div class="flex items-center gap-3 mt-2 text-sm text-muted-foreground">
              <span class="flex items-center gap-1">
                <Calendar class="h-3 w-3" />
                {formatFrequency()}
              </span>
              
              <Show when={props.habit.target > 1}>
                <span class="flex items-center gap-1">
                  <Target class="h-3 w-3" />
                  Target: {props.habit.target} {props.habit.unit}
                </span>
              </Show>
              
              <Show when={props.showStreak && props.habit.streak > 0}>
                <span class="flex items-center gap-1">
                  <Flame class="h-3 w-3 text-orange-500" />
                  {props.habit.streak} day streak
                </span>
              </Show>
            </div>
          </div>

          {/* 操作菜单 */}
          <DropdownMenu>
            <DropdownMenuTrigger as={Button} variant="ghost" size="sm" class="w-8 h-8 p-0">
              <MoreHorizontal class="h-4 w-4" />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => props.onEdit?.(props.habit)}>
                <Edit class="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => props.onDelete?.(props.habit.id)}
                class="text-red-600"
              >
                <Trash2 class="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent class={cn("space-y-4", props.compact && "space-y-2")}>
        {/* 今日状态和操作 */}
        <div class="flex items-center gap-3">
          <Show when={props.habit.type === 'boolean'}>
            <Button
              variant={todayRecord?.completed ? "default" : "outline"}
              size="sm"
              class={cn(
                "flex-1",
                todayRecord?.completed && "bg-green-600 hover:bg-green-700"
              )}
              onClick={handleToggle}
            >
              <Show when={todayRecord?.completed} fallback={<Circle class="h-4 w-4 mr-2" />}>
                <CheckCircle class="h-4 w-4 mr-2" />
              </Show>
              {todayRecord?.completed ? 'Completed' : 'Mark Complete'}
            </Button>
          </Show>

          <Show when={props.habit.type === 'numeric'}>
            <div class="flex items-center gap-2 flex-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickValue(-1)}
                disabled={(todayRecord?.value || 0) <= 0}
              >
                <Minus class="h-3 w-3" />
              </Button>
              
              <div class="flex-1 text-center">
                <div class="text-lg font-semibold">
                  {todayRecord?.value || 0}
                  <Show when={props.habit.unit}>
                    <span class="text-sm text-muted-foreground ml-1">
                      {props.habit.unit}
                    </span>
                  </Show>
                </div>
                <div class="text-xs text-muted-foreground">
                  Target: {props.habit.target}
                </div>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickValue(1)}
              >
                <Plus class="h-3 w-3" />
              </Button>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowValueInput(true)}
            >
              Set
            </Button>
          </Show>
        </div>

        {/* 数值输入对话框 */}
        <Show when={showValueInput()}>
          <div class="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <Input
              type="number"
              placeholder="Enter value"
              value={inputValue()}
              onInput={(e) => setInputValue(e.currentTarget.value)}
              class="flex-1"
              min="0"
            />
            <Button size="sm" onClick={handleValueSet}>
              Save
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setShowValueInput(false)}
            >
              Cancel
            </Button>
          </div>
        </Show>

        {/* 进度显示 */}
        <Show when={props.showProgress && !props.compact}>
          <div class="space-y-2">
            <div class="flex items-center justify-between text-sm">
              <span>Weekly Progress</span>
              <span>{completionRate}%</span>
            </div>
            <Progress value={completionRate} class="h-2" />
          </div>
        </Show>

        {/* 最近7天热力图 */}
        <Show when={!props.compact}>
          <div class="space-y-2">
            <div class="text-sm font-medium">Recent Activity</div>
            <div class="flex gap-1">
              <For each={recentRecords}>
                {(record) => (
                  <div class="flex flex-col items-center gap-1">
                    <div
                      class={cn(
                        "w-6 h-6 rounded-sm flex items-center justify-center text-xs font-medium",
                        record.completed 
                          ? "bg-green-500 text-white" 
                          : "bg-gray-200 text-gray-500"
                      )}
                      title={`${record.date}: ${record.completed ? 'Completed' : 'Not completed'}`}
                    >
                      <Show when={props.habit.type === 'numeric' && record.value > 0}>
                        {record.value}
                      </Show>
                      <Show when={props.habit.type === 'boolean'}>
                        <Show when={record.completed} fallback="·">
                          ✓
                        </Show>
                      </Show>
                    </div>
                    <div class="text-xs text-muted-foreground">
                      {new Date(record.date).toLocaleDateString('en-US', { weekday: 'short' })}
                    </div>
                  </div>
                )}
              </For>
            </div>
          </div>
        </Show>

        {/* 统计信息 */}
        <Show when={!props.compact}>
          <div class="grid grid-cols-3 gap-4 pt-2 border-t text-center">
            <div>
              <div class="text-lg font-semibold">{props.habit.streak}</div>
              <div class="text-xs text-muted-foreground">Current Streak</div>
            </div>
            <div>
              <div class="text-lg font-semibold">{props.habit.longestStreak}</div>
              <div class="text-xs text-muted-foreground">Best Streak</div>
            </div>
            <div>
              <div class="text-lg font-semibold">{Math.round(props.habit.completionRate)}%</div>
              <div class="text-xs text-muted-foreground">Completion Rate</div>
            </div>
          </div>
        </Show>
      </CardContent>
    </Card>
  )
}

export default HabitCard
