import * as React from 'react'
import { cn } from '../../lib/utils'
import { Button } from './button'

interface ToastProps {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  onClose: (id: string) => void
}

const toastVariants = {
  success: 'border-green-200 bg-green-50 text-green-800',
  error: 'border-red-200 bg-red-50 text-red-800',
  warning: 'border-yellow-200 bg-yellow-50 text-yellow-800',
  info: 'border-blue-200 bg-blue-50 text-blue-800'
}

const iconVariants = {
  success: '✅',
  error: '❌',
  warning: '⚠️',
  info: 'ℹ️'
}

export function Toast({ id, type, title, message, onClose }: ToastProps) {
  React.useEffect(() => {
    const timer = setTimeout(() => {
      onClose(id)
    }, 5000)

    return () => clearTimeout(timer)
  }, [id, onClose])

  return (
    <div
      className={cn(
        'relative flex items-start gap-3 p-4 rounded-lg border shadow-lg transition-all duration-300 animate-in slide-in-from-right-full',
        toastVariants[type]
      )}
    >
      <div className="flex-shrink-0 text-lg">{iconVariants[type]}</div>

      <div className="flex-1 min-w-0">
        <div className="font-medium text-sm">{title}</div>
        {message && <div className="text-sm opacity-90 mt-1">{message}</div>}
      </div>

      <Button
        variant="ghost"
        size="sm"
        onClick={() => onClose(id)}
        className="flex-shrink-0 h-6 w-6 p-0 hover:bg-black/10"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </Button>
    </div>
  )
}

interface ToastContainerProps {
  notifications: Array<{
    id: string
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message?: string
    timestamp: Date
  }>
  onRemove: (id: string) => void
}

export function ToastContainer({ notifications, onRemove }: ToastContainerProps) {
  return (
    <div className="fixed top-4 right-4 z-50 flex flex-col gap-2 max-w-sm w-full">
      {notifications.map((notification) => (
        <Toast
          key={notification.id}
          id={notification.id}
          type={notification.type}
          title={notification.title}
          message={notification.message}
          onClose={onRemove}
        />
      ))}
    </div>
  )
}
