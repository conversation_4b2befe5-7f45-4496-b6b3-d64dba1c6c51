/**
 * 领域资源关联使用示例
 * 展示如何在领域详情页面中集成资源关联组件
 */

import { createSignal, onMount } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card'
import { Button } from '../../ui/button'
import { Badge } from '../../ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../ui/tabs'
import { 
  FileText, 
  BookOpen,
  Image,
  Link as LinkIcon,
  Calendar,
  TrendingUp
} from 'lucide-solid'

// 导入资源关联组件
import { 
  ResourceAssociation,
  createResourceDataSource,
  type ResourceEventHandlers,
  type ResourceAssociationConfig
} from '../index'

interface AreaResourceExampleProps {
  areaId: string
  areaName?: string
  className?: string
}

export function AreaResourceExample(props: AreaResourceExampleProps) {
  // 状态管理
  const [activeTab, setActiveTab] = createSignal('overview')
  const [resources, setResources] = createSignal([])

  // 创建数据源
  const dataSource = createResourceDataSource('area')

  // 针对个人领域优化的配置
  const componentConfig: ResourceAssociationConfig = {
    enableMarkdownAssociation: true,
    enableLinkManagement: true,
    enableFileUpload: true,
    enableBidirectionalLinks: true,
    showResourcePreview: true,
    showResourceStats: true,
    showRecentResources: true,
    showResourceSearch: true,
    allowCreate: true,
    allowEdit: true,
    allowDelete: true,
    allowBulkOperations: false, // 个人使用通常不需要批量操作
    allowDragDrop: true,
    layout: 'grid', // 网格布局更适合个人资源浏览
    groupBy: 'type',
    sortBy: 'date',
    sortOrder: 'desc',
    maxFileSize: 20 * 1024 * 1024, // 20MB，个人文件通常较小
    allowedFileTypes: [
      'image/*',
      'application/pdf',
      'text/*',
      '.md',
      '.txt',
      '.docx',
      '.xlsx',
      '.pptx'
    ],
    enableThumbnails: true,
    enableLinkPreview: true,
    enableFaviconFetch: true,
    enableWikiLinks: true,
    enableAutoLinking: true,
    theme: 'auto',
    compactMode: false
  }

  // 事件处理器
  const eventHandlers: ResourceEventHandlers = {
    onCreate: (resource) => {
      console.log('Area resource created:', resource)
      // 可以在这里触发领域进度更新
    },
    
    onUpdate: (resource, changes) => {
      console.log('Area resource updated:', resource, changes)
      // 可以在这里更新相关习惯或任务
    },
    
    onDelete: (resourceId) => {
      console.log('Area resource deleted:', resourceId)
      // 可以在这里清理相关关联
    },
    
    onAccess: (resource) => {
      console.log('Area resource accessed:', resource)
      // 可以在这里记录学习或工作时间
    },
    
    onUploadStart: (uploadData) => {
      console.log('Area upload started:', uploadData)
      // 可以在这里显示上传状态
    },
    
    onUploadComplete: (resource) => {
      console.log('Area upload completed:', resource)
      // 可以在这里检查是否需要创建相关任务
      checkResourceIntegration(resource)
    },
    
    onError: (error, context) => {
      console.error('Area resource error:', error, context)
    }
  }

  // 检查资源集成
  const checkResourceIntegration = (resource: any) => {
    // 这里可以实现与习惯、任务系统的集成逻辑
    // 例如：如果上传了学习资料，自动创建学习任务
  }

  // 加载资源数据
  const loadResources = async () => {
    try {
      const data = await dataSource.getResources('area', props.areaId)
      setResources(data)
    } catch (error) {
      console.error('Failed to load area resources:', error)
    }
  }

  onMount(() => {
    loadResources()
  })

  return (
    <div class="space-y-6">
      {/* 页面头部 */}
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <div>
              <CardTitle class="flex items-center gap-2">
                <BookOpen class="h-5 w-5" />
                Area Resources
                <Badge variant="outline">
                  {props.areaName || `Area ${props.areaId}`}
                </Badge>
              </CardTitle>
              <CardDescription>
                Manage personal resources, documents and references for this life area
              </CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 标签页导航 */}
      <Tabs value={activeTab()} onValueChange={setActiveTab}>
        <TabsList class="grid w-full grid-cols-4">
          <TabsTrigger value="overview" class="flex items-center gap-2">
            <TrendingUp class="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="documents" class="flex items-center gap-2">
            <FileText class="h-4 w-4" />
            Documents
          </TabsTrigger>
          <TabsTrigger value="media" class="flex items-center gap-2">
            <Image class="h-4 w-4" />
            Media
          </TabsTrigger>
          <TabsTrigger value="links" class="flex items-center gap-2">
            <LinkIcon class="h-4 w-4" />
            Links
          </TabsTrigger>
        </TabsList>

        {/* 概览标签页 */}
        <TabsContent value="overview" class="space-y-6">
          {/* 快速统计 */}
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent class="p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-muted-foreground">Total Resources</p>
                    <p class="text-2xl font-bold">{resources().length}</p>
                  </div>
                  <FileText class="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent class="p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-muted-foreground">Documents</p>
                    <p class="text-2xl font-bold">
                      {resources().filter(r => r.type === 'markdown').length}
                    </p>
                  </div>
                  <BookOpen class="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent class="p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-muted-foreground">Files</p>
                    <p class="text-2xl font-bold">
                      {resources().filter(r => r.type === 'file').length}
                    </p>
                  </div>
                  <Image class="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent class="p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-muted-foreground">Links</p>
                    <p class="text-2xl font-bold">
                      {resources().filter(r => r.type === 'link').length}
                    </p>
                  </div>
                  <LinkIcon class="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 最近资源 */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Resources</CardTitle>
              <CardDescription>
                Recently added or accessed resources
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div class="space-y-3">
                {resources().slice(0, 5).map(resource => (
                  <div class="flex items-center gap-3 p-3 border rounded-lg">
                    <div class="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center">
                      <FileText class="h-5 w-5 text-gray-600" />
                    </div>
                    <div class="flex-1">
                      <p class="font-medium text-sm">{resource.title}</p>
                      <p class="text-xs text-muted-foreground">
                        {resource.type} • {new Date(resource.updatedAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 文档标签页 */}
        <TabsContent value="documents" class="space-y-6">
          <ResourceAssociation
            entityType="area"
            entityId={props.areaId}
            dataSource={dataSource}
            config={{
              ...componentConfig,
              // 只显示文档相关功能
              enableFileUpload: false,
              enableLinkManagement: false
            }}
            eventHandlers={eventHandlers}
            className={props.className}
          />
        </TabsContent>

        {/* 媒体文件标签页 */}
        <TabsContent value="media" class="space-y-6">
          <ResourceAssociation
            entityType="area"
            entityId={props.areaId}
            dataSource={dataSource}
            config={{
              ...componentConfig,
              // 只显示文件上传功能
              enableMarkdownAssociation: false,
              enableLinkManagement: false,
              allowedFileTypes: ['image/*', 'video/*', 'audio/*'],
              layout: 'grid'
            }}
            eventHandlers={eventHandlers}
            className={props.className}
          />
        </TabsContent>

        {/* 链接标签页 */}
        <TabsContent value="links" class="space-y-6">
          <ResourceAssociation
            entityType="area"
            entityId={props.areaId}
            dataSource={dataSource}
            config={{
              ...componentConfig,
              // 只显示链接管理功能
              enableMarkdownAssociation: false,
              enableFileUpload: false,
              layout: 'list'
            }}
            eventHandlers={eventHandlers}
            className={props.className}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default AreaResourceExample
