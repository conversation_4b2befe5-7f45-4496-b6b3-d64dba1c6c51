// Export all Zustand stores
export { useProjectStore } from './projectStore'
export { useAreaStore } from './areaStore'
export { useTaskStore } from './taskStore'
export { useResourceStore } from './resourceStore'
export { useUIStore } from './uiStore'
// export { useEditorStore } from './editorStore' // Removed

// Export store types for convenience
export type { ProjectStore } from './projectStore'
export type { AreaStore } from './areaStore'
export type { TaskStore } from './taskStore'
export type { ResourceStore } from './resourceStore'
export type { UIStore } from './uiStore'
// export type { EditorStore } from './editorStore' // Removed
