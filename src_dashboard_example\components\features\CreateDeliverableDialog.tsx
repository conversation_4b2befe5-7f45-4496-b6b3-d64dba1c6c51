import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Textarea } from '../ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { useLanguage } from '../../contexts/LanguageContext'

interface CreateDeliverableDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => Promise<void>
}

export function CreateDeliverableDialog({
  isOpen,
  onClose,
  onSubmit
}: CreateDeliverableDialogProps) {
  const { t } = useLanguage()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'document',
    status: 'planned',
    priority: 'medium',
    plannedDate: '',
    deadline: '',
    content: ''
  })

  const deliverableTypes = [
    { value: 'document', label: `📄 ${t('pages.projects.detail.deliverables.type.document')}`, description: t('pages.projects.detail.deliverables.typeDescriptions.document') },
    { value: 'software', label: `💻 ${t('pages.projects.detail.deliverables.type.software')}`, description: t('pages.projects.detail.deliverables.typeDescriptions.software') },
    { value: 'report', label: `📊 ${t('pages.projects.detail.deliverables.type.report')}`, description: t('pages.projects.detail.deliverables.typeDescriptions.report') },
    { value: 'presentation', label: `🎯 ${t('pages.projects.detail.deliverables.type.presentation')}`, description: t('pages.projects.detail.deliverables.typeDescriptions.presentation') },
    { value: 'prototype', label: `🔧 ${t('pages.projects.detail.deliverables.type.prototype')}`, description: t('pages.projects.detail.deliverables.typeDescriptions.prototype') },
    { value: 'other', label: `📦 ${t('pages.projects.detail.deliverables.type.other')}`, description: t('pages.projects.detail.deliverables.typeDescriptions.other') }
  ]

  const statusOptions = [
    { value: 'planned', label: t('pages.projects.detail.deliverables.status.planned'), description: t('pages.projects.detail.deliverables.statusDescriptions.planned') },
    { value: 'inProgress', label: t('pages.projects.detail.deliverables.status.inProgress'), description: t('pages.projects.detail.deliverables.statusDescriptions.inProgress') },
    { value: 'review', label: t('pages.projects.detail.deliverables.status.review'), description: t('pages.projects.detail.deliverables.statusDescriptions.review') },
    { value: 'completed', label: t('pages.projects.detail.deliverables.status.completed'), description: t('pages.projects.detail.deliverables.statusDescriptions.completed') },
    { value: 'cancelled', label: t('pages.projects.detail.deliverables.status.cancelled'), description: t('pages.projects.detail.deliverables.statusDescriptions.cancelled') }
  ]

  const priorityOptions = [
    { value: 'low', label: t('pages.projects.detail.deliverables.priority.low'), color: 'text-green-600' },
    { value: 'medium', label: t('pages.projects.detail.deliverables.priority.medium'), color: 'text-yellow-600' },
    { value: 'high', label: t('pages.projects.detail.deliverables.priority.high'), color: 'text-orange-600' },
    { value: 'critical', label: t('pages.projects.detail.deliverables.priority.critical'), color: 'text-red-600' }
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.title.trim()) return

    setIsSubmitting(true)
    try {
      const deliverableData = {
        title: formData.title.trim(),
        description: formData.description.trim() || undefined,
        type: formData.type,
        status: formData.status,
        priority: formData.priority,
        plannedDate: formData.plannedDate ? new Date(formData.plannedDate) : undefined,
        deadline: formData.deadline ? new Date(formData.deadline) : undefined,
        content: formData.content.trim() || undefined
      }

      await onSubmit(deliverableData)
      onClose()

      // Reset form
      setFormData({
        title: '',
        description: '',
        type: 'document',
        status: 'planned',
        priority: 'medium',
        plannedDate: '',
        deadline: '',
        content: ''
      })
    } catch (error) {
      console.error('Failed to create deliverable:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t('pages.projects.detail.deliverables.createDialog.title')}</DialogTitle>
          <DialogDescription>
            {t('pages.projects.detail.deliverables.createDialog.description')}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">{t('pages.projects.detail.deliverables.createDialog.titleRequired')}</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData((prev) => ({ ...prev, title: e.target.value }))}
              placeholder={t('pages.projects.detail.deliverables.createDialog.titlePlaceholder')}
              required
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">{t('pages.projects.detail.deliverables.createDialog.descriptionLabel')}</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
              placeholder={t('pages.projects.detail.deliverables.createDialog.descriptionPlaceholder')}
              rows={3}
            />
          </div>

          {/* Type and Status */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="type">{t('pages.projects.detail.deliverables.createDialog.typeLabel')}</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, type: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="max-w-xs">
                  {deliverableTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div className="max-w-xs">
                        <div className="font-medium truncate">{type.label}</div>
                        <div className="text-xs text-muted-foreground line-clamp-2">{type.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">{t('pages.projects.detail.deliverables.createDialog.statusLabel')}</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => setFormData((prev) => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="max-w-xs">
                  {statusOptions.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      <div className="max-w-xs">
                        <div className="font-medium truncate">{status.label}</div>
                        <div className="text-xs text-muted-foreground line-clamp-2">{status.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Priority */}
          <div className="space-y-2">
            <Label htmlFor="priority">{t('pages.projects.detail.deliverables.createDialog.priorityLabel')}</Label>
            <Select
              value={formData.priority}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, priority: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {priorityOptions.map((priority) => (
                  <SelectItem key={priority.value} value={priority.value}>
                    <span className={priority.color}>{priority.label}</span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="plannedDate">{t('pages.projects.detail.deliverables.createDialog.plannedDateLabel')}</Label>
              <Input
                id="plannedDate"
                type="date"
                value={formData.plannedDate}
                onChange={(e) => setFormData((prev) => ({ ...prev, plannedDate: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="deadline">{t('pages.projects.detail.deliverables.createDialog.deadlineLabel')}</Label>
              <Input
                id="deadline"
                type="date"
                value={formData.deadline}
                onChange={(e) => setFormData((prev) => ({ ...prev, deadline: e.target.value }))}
              />
            </div>
          </div>

          {/* Content */}
          <div className="space-y-2">
            <Label htmlFor="content">{t('pages.projects.detail.deliverables.createDialog.contentLabel')}</Label>
            <Textarea
              id="content"
              value={formData.content}
              onChange={(e) => setFormData((prev) => ({ ...prev, content: e.target.value }))}
              placeholder={t('pages.projects.detail.deliverables.createDialog.contentPlaceholder')}
              rows={4}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              {t('pages.projects.detail.deliverables.createDialog.cancel')}
            </Button>
            <Button type="submit" disabled={!formData.title.trim() || isSubmitting}>
              {isSubmitting ? t('pages.projects.detail.deliverables.createDialog.creating') : t('pages.projects.detail.deliverables.createDialog.create')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default CreateDeliverableDialog
