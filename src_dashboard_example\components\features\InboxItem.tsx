import { useState } from 'react'
import { Card, CardContent } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Textarea } from '../ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '../ui/tooltip'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { cn } from '../../lib/utils'
import { useLanguage } from '../../contexts/LanguageContext'

export interface InboxItem {
  id: string
  content: string
  type: 'note' | 'task' | 'idea' | 'link' | 'file'
  priority: 'low' | 'medium' | 'high'
  tags: string[]
  processed: boolean
  processedAt?: string
  processedTo?: {
    type: 'project' | 'area' | 'resource' | 'archive'
    id: string
    name: string
  }
  createdAt: string
  updatedAt: string
}

interface InboxItemProps {
  item: InboxItem
  onEdit?: (item: InboxItem) => void
  onDelete?: (itemId: string) => void
  onProcess?: (
    itemId: string,
    destination: { type: 'project' | 'area' | 'resource' | 'processed'; id: string; name: string }
  ) => void
  onProcessCreate?: (
    itemId: string,
    type: 'project' | 'area' | 'resource',
    data: any
  ) => void
  onProcessLink?: (
    itemId: string,
    type: 'project' | 'area',
    targetId: string,
    actionType: string
  ) => void
  onOpenCreateDialog?: (
    type: 'task' | 'kpi' | 'habit' | 'maintenance' | 'checklist',
    data: any,
    itemId: string
  ) => void
  onToggleProcessed?: (itemId: string) => void
  className?: string
  projects?: Array<{ id: string; name: string }>
  areas?: Array<{ id: string; name: string }>
}

export function InboxItem({
  item,
  onEdit,
  onDelete,
  onProcess,
  onProcessCreate,
  onProcessLink,
  onOpenCreateDialog,
  onToggleProcessed,
  className,
  projects = [],
  areas = []
}: InboxItemProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editContent, setEditContent] = useState(item.content)
  const [showProcessOptions, setShowProcessOptions] = useState(false)
  const [showActionDialog, setShowActionDialog] = useState<{
    type: 'project' | 'area'
    targetId: string
    targetName: string
  } | null>(null)
  const { t } = useLanguage()

  const handleSaveEdit = () => {
    if (editContent.trim() && editContent !== item.content) {
      onEdit?.({
        ...item,
        content: editContent.trim(),
        updatedAt: new Date().toISOString()
      })
    }
    setIsEditing(false)
    setEditContent(item.content)
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
    setEditContent(item.content)
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'note':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
            />
          </svg>
        )
      case 'task':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
            />
          </svg>
        )
      case 'idea':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
            />
          </svg>
        )
      case 'link':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
            />
          </svg>
        )
      case 'file':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        )
      default:
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v9a2 2 0 01-2 2h-1M9 7h1"
            />
          </svg>
        )
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'note':
        return 'bg-blue-100 text-blue-800'
      case 'task':
        return 'bg-green-100 text-green-800'
      case 'idea':
        return 'bg-purple-100 text-purple-800'
      case 'link':
        return 'bg-orange-100 text-orange-800'
      case 'file':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // 辅助函数：从内容中提取标题
  const extractTitle = (content: string): string => {
    const firstLine = content.split('\n')[0]
    return firstLine.length > 50 ? firstLine.slice(0, 47) + '...' : firstLine
  }

  // 处理创建新实体
  const handleCreateNew = (type: 'project' | 'area' | 'resource') => {
    const title = extractTitle(item.content)
    const data = {
      name: title,
      description: item.content,
      ...(type === 'project' && { goal: `基于收件箱: ${item.content.slice(0, 50)}...` }),
      ...(type === 'area' && { standard: '基于收件箱内容建立的领域标准' })
    }

    onProcessCreate?.(item.id, type, data)
    setShowProcessOptions(false)
  }

  // 处理选择现有项目
  const handleSelectProject = (projectId: string) => {
    console.log('🔧 [DEBUG] handleSelectProject called with:', projectId)
    const project = projects.find(p => p.id === projectId)
    console.log('🔧 [DEBUG] Found project:', project)
    if (project) {
      setShowActionDialog({
        type: 'project',
        targetId: projectId,
        targetName: project.name
      })
      setShowProcessOptions(false)
    }
  }

  // 处理选择现有领域
  const handleSelectArea = (areaId: string) => {
    console.log('🔧 [DEBUG] handleSelectArea called with:', areaId)
    const area = areas.find(a => a.id === areaId)
    console.log('🔧 [DEBUG] Found area:', area)
    if (area) {
      setShowActionDialog({
        type: 'area',
        targetId: areaId,
        targetName: area.name
      })
      setShowProcessOptions(false)
    }
  }

  // 处理动作选择
  const handleActionSelect = (actionType: string) => {
    console.log('🔧 [DEBUG] handleActionSelect called with:', {
      actionType,
      showActionDialog,
      itemContent: item.content
    })

    if (showActionDialog) {
      // 准备预填充数据
      const title = item.content.split('\n')[0]
      const extractedTitle = title.length > 50 ? title.slice(0, 47) + '...' : title

      let createData = null

      switch (actionType) {
        case 'task':
          createData = {
            title: extractedTitle,
            description: item.content,
            priority: item.priority,
            projectId: showActionDialog.type === 'project' ? showActionDialog.targetId : undefined,
            areaId: showActionDialog.type === 'area' ? showActionDialog.targetId : undefined
          }
          break
        case 'kpi':
          createData = {
            name: extractedTitle,
            value: '0',
            target: '100',
            unit: '',
            frequency: 'monthly',
            direction: 'increase',
            projectId: showActionDialog.type === 'project' ? showActionDialog.targetId : undefined,
            areaId: showActionDialog.type === 'area' ? showActionDialog.targetId : undefined
          }
          break
        case 'habit':
          createData = {
            name: extractedTitle,
            description: item.content,
            targetFrequency: 7,
            areaId: showActionDialog.targetId
          }
          break
        case 'maintenance':
          createData = {
            title: extractedTitle,
            description: item.content,
            repeatRule: 'weekly',
            repeatInterval: 1,
            areaId: showActionDialog.targetId
          }
          break
        case 'checklist':
          createData = {
            name: extractedTitle,
            description: item.content,
            areaId: showActionDialog.targetId
          }
          break
      }

      console.log('🔧 [DEBUG] Prepared createData:', createData)

      // 关闭动作选择对话框
      setShowActionDialog(null)

      // 打开对应的创建对话框
      console.log('🔧 [DEBUG] Calling onOpenCreateDialog with:', { actionType, createData, itemId: item.id })
      onOpenCreateDialog?.(actionType as any, createData, item.id)
    }
  }

  // 处理直接处理
  const handleProcess = (destination: {
    type: 'project' | 'area' | 'resource' | 'processed'
    id: string
    name: string
  }) => {
    onProcess?.(item.id, destination)
    setShowProcessOptions(false)
  }

  return (
    <>
    <Card
      className={cn(
        'group transition-all duration-200',
        item.processed ? 'opacity-60 bg-muted/20' : 'hover:shadow-md',
        className
      )}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {/* Type Icon */}
          <div className={cn('flex-shrink-0 p-2 rounded-lg', getTypeColor(item.type))}>
            {getTypeIcon(item.type)}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            {isEditing ? (
              <div className="space-y-3">
                <Textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  placeholder={t('pages.inbox.item.editPlaceholder')}
                  rows={3}
                  className="resize-none"
                />
                <div className="flex gap-2">
                  <Button size="sm" onClick={handleSaveEdit}>
                    {t('common.save')}
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleCancelEdit}>
                    {t('common.cancel')}
                  </Button>
                </div>
              </div>
            ) : (
              <>
                <p
                  className={cn(
                    'text-sm mb-2',
                    item.processed && 'line-through text-muted-foreground'
                  )}
                >
                  {item.content}
                </p>

                {/* Tags */}
                {item.tags.length > 0 && (
                  <div className="flex items-center gap-2 mb-2">
                    {item.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
                        #{tag}
                      </Badge>
                    ))}
                  </div>
                )}

                {/* Processed Info */}
                {item.processed && item.processedTo && (
                  <div className="text-xs text-muted-foreground mb-2">
                    {t('pages.inbox.item.processedTo', {
                      type: t(`enums.types.${item.processedTo.type}`),
                      name: item.processedTo.name
                    })}
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">
                    {new Date(item.createdAt).toLocaleString()}
                  </div>

                  <TooltipProvider>
                    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      {/* 处理按钮 */}
                      {!item.processed && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setShowProcessOptions(!showProcessOptions)}
                              className="h-7 w-7 p-0 hover:bg-blue-100 hover:text-blue-600"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                              </svg>
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>处理</p>
                          </TooltipContent>
                        </Tooltip>
                      )}

                      {/* 取消处理按钮 */}
                      {item.processed && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onToggleProcessed?.(item.id)}
                              className="h-7 w-7 p-0 hover:bg-orange-100 hover:text-orange-600"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                              </svg>
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>取消处理</p>
                          </TooltipContent>
                        </Tooltip>
                      )}

                      {/* 编辑按钮 */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setIsEditing(true)}
                            className="h-7 w-7 p-0 hover:bg-green-100 hover:text-green-600"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>编辑</p>
                        </TooltipContent>
                      </Tooltip>

                      {/* 删除按钮 */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onDelete?.(item.id)}
                            className="h-7 w-7 p-0 hover:bg-red-100 hover:text-red-600"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>删除</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </TooltipProvider>
                </div>

                {/* Process Options */}
                {showProcessOptions && !item.processed && (
                  <div className="mt-3 p-3 border rounded-lg bg-accent/20">
                    <div className="text-xs font-medium mb-3">{t('pages.inbox.item.processToLabel')}</div>
                    <div className="grid grid-cols-4 gap-3">
                      {/* 项目列 */}
                      <div className="space-y-2">
                        <div className="text-xs font-medium text-center">📋 项目</div>
                        <div className="grid grid-cols-2 gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleCreateNew('project')}
                            className="text-xs h-8"
                          >
                            新建
                          </Button>
                          <Select onValueChange={handleSelectProject} disabled={projects.length === 0}>
                            <SelectTrigger className="h-8 text-xs bg-background border border-input">
                              <SelectValue placeholder="选择" />
                            </SelectTrigger>
                            <SelectContent>
                              {projects.map(project => (
                                <SelectItem key={project.id} value={project.id}>
                                  {project.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      {/* 领域列 */}
                      <div className="space-y-2">
                        <div className="text-xs font-medium text-center">🏠 领域</div>
                        <div className="grid grid-cols-2 gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleCreateNew('area')}
                            className="text-xs h-8"
                          >
                            新建
                          </Button>
                          <Select onValueChange={handleSelectArea} disabled={areas.length === 0}>
                            <SelectTrigger className="h-8 text-xs bg-background border border-input">
                              <SelectValue placeholder="选择" />
                            </SelectTrigger>
                            <SelectContent>
                              {areas.map(area => (
                                <SelectItem key={area.id} value={area.id}>
                                  {area.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      {/* 资源列 */}
                      <div className="space-y-2">
                        <div className="text-xs font-medium text-center">📄 资源</div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCreateNew('resource')}
                          className="w-full text-xs h-8"
                        >
                          创建资源
                        </Button>
                      </div>

                      {/* 直接处理列 */}
                      <div className="space-y-2">
                        <div className="text-xs font-medium text-center">✅ 处理</div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleProcess({ type: 'processed', id: 'processed', name: 'Processed' })
                          }
                          className="w-full text-xs h-8"
                        >
                          直接处理
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>

    {/* 动作选择对话框 */}
    <Dialog open={!!showActionDialog} onOpenChange={(open) => !open && setShowActionDialog(null)}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>选择创建内容</DialogTitle>
          <DialogDescription>
            选择要在 "{showActionDialog?.targetName}" 中创建的内容类型
          </DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-1 gap-3 py-4">
          {showActionDialog?.type === 'project' ? (
            <>
              <Button
                variant="outline"
                onClick={() => handleActionSelect('task')}
                className="justify-start h-12"
              >
                <div className="flex items-center gap-3">
                  <span className="text-lg">✅</span>
                  <div className="text-left">
                    <div className="font-medium">新建任务</div>
                    <div className="text-xs text-muted-foreground">在项目中创建新任务</div>
                  </div>
                </div>
              </Button>
              <Button
                variant="outline"
                onClick={() => handleActionSelect('kpi')}
                className="justify-start h-12"
              >
                <div className="flex items-center gap-3">
                  <span className="text-lg">📊</span>
                  <div className="text-left">
                    <div className="font-medium">新建KPI</div>
                    <div className="text-xs text-muted-foreground">创建项目关键绩效指标</div>
                  </div>
                </div>
              </Button>
            </>
          ) : (
            <>
              <Button
                variant="outline"
                onClick={() => handleActionSelect('habit')}
                className="justify-start h-12"
              >
                <div className="flex items-center gap-3">
                  <span className="text-lg">🔄</span>
                  <div className="text-left">
                    <div className="font-medium">新建习惯</div>
                    <div className="text-xs text-muted-foreground">创建日常习惯追踪</div>
                  </div>
                </div>
              </Button>
              <Button
                variant="outline"
                onClick={() => handleActionSelect('kpi')}
                className="justify-start h-12"
              >
                <div className="flex items-center gap-3">
                  <span className="text-lg">📊</span>
                  <div className="text-left">
                    <div className="font-medium">新建KPI</div>
                    <div className="text-xs text-muted-foreground">创建领域关键指标</div>
                  </div>
                </div>
              </Button>
              <Button
                variant="outline"
                onClick={() => handleActionSelect('maintenance')}
                className="justify-start h-12"
              >
                <div className="flex items-center gap-3">
                  <span className="text-lg">🔧</span>
                  <div className="text-left">
                    <div className="font-medium">新建定期维护任务</div>
                    <div className="text-xs text-muted-foreground">创建重复性维护任务</div>
                  </div>
                </div>
              </Button>
              <Button
                variant="outline"
                onClick={() => handleActionSelect('checklist')}
                className="justify-start h-12"
              >
                <div className="flex items-center gap-3">
                  <span className="text-lg">📋</span>
                  <div className="text-left">
                    <div className="font-medium">新建清单</div>
                    <div className="text-xs text-muted-foreground">创建检查清单模板</div>
                  </div>
                </div>
              </Button>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  </>
  )
}

export default InboxItem
