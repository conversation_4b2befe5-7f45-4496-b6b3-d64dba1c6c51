// Area Value Objects - 领域相关值对象

use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub struct AreaName {
    pub value: String,
}

impl AreaName {
    pub fn new(name: String) -> Result<Self, String> {
        if !name.trim().is_empty() && name.len() <= 100 {
            Ok(Self { value: name })
        } else {
            Err("Area name cannot be empty and must be less than 100 characters".to_string())
        }
    }
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct AreaStandard {
    pub value: String,
}

impl AreaStandard {
    pub fn new(standard: String) -> Result<Self, String> {
        if !standard.trim().is_empty() && standard.len() <= 500 {
            Ok(Self { value: standard })
        } else {
            Err("Area standard cannot be empty and must be less than 500 characters".to_string())
        }
    }
}
