// User Repository Interface - 用户仓储接口

use crate::domain::entities::User;
use crate::shared::errors::Result;
use crate::shared::types::{Id, QueryParams};
use async_trait::async_trait;

/// 用户仓储接口
#[async_trait]
pub trait UserRepository: Send + Sync {
    /// 根据ID查找用户
    async fn find_by_id(&self, id: &Id) -> Result<Option<User>>;
    
    /// 根据用户名查找用户
    async fn find_by_username(&self, username: &str) -> Result<Option<User>>;
    
    /// 根据邮箱查找用户
    async fn find_by_email(&self, email: &str) -> Result<Option<User>>;
    
    /// 保存用户
    async fn save(&self, user: &User) -> Result<()>;
    
    /// 更新用户
    async fn update(&self, user: &User) -> Result<()>;
    
    /// 删除用户（软删除）
    async fn delete(&self, id: &Id) -> Result<()>;
    
    /// 查找所有活跃用户
    async fn find_all_active(&self) -> Result<Vec<User>>;
    
    /// 分页查询用户
    async fn find_with_pagination(&self, params: &QueryParams) -> Result<(Vec<User>, u64)>;
    
    /// 检查用户名是否存在
    async fn username_exists(&self, username: &str) -> Result<bool>;
    
    /// 检查邮箱是否存在
    async fn email_exists(&self, email: &str) -> Result<bool>;
    
    /// 获取用户总数
    async fn count_active_users(&self) -> Result<u64>;
    
    /// 根据状态查找用户
    async fn find_by_status(&self, status: &str) -> Result<Vec<User>>;
}
