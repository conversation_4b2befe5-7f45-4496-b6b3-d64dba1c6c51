/**
 * 通用KPI进度计算器
 * 支持项目KPI和领域指标的双向进度计算
 */

import type { ProjectKPI, AreaMetric } from '../../../shared/types'
import type { BaseKPI, NumericKPI } from '../../../shared/types/kpi'

/**
 * 通用KPI接口 (保持向后兼容)
 */
interface GenericKPI {
  name: string
  value: string
  target?: string
  unit?: string
  direction?: string
}

// 类型转换工具
export function toNumericKPI(kpi: BaseKPI | GenericKPI): NumericKPI {
  return {
    id: 'id' in kpi ? kpi.id : '',
    name: kpi.name,
    value: parseFloat(kpi.value) || 0,
    target: kpi.target ? parseFloat(kpi.target) : undefined,
    unit: kpi.unit,
    direction: (kpi.direction as 'increase' | 'decrease') || 'increase',
    frequency: 'frequency' in kpi ? kpi.frequency : undefined,
    updatedAt: 'updatedAt' in kpi ? kpi.updatedAt : new Date()
  }
}

/**
 * 判断KPI类型：增长型还是减少型
 * 支持ProjectKPI和AreaMetric
 */
export function getKPIDirection(kpi: GenericKPI): 'increase' | 'decrease' {
  // {{ AURA-X: Add - 通用双向KPI方向判断. Approval: 寸止(ID:1738157400). }}
  // 优先使用direction字段
  if (kpi.direction) {
    return kpi.direction as 'increase' | 'decrease'
  }
  
  // 回退到智能判断
  const decreaseKeywords = [
    'weight', '体重', '减肥', 'lose', 'reduce', '减少', '降低', 'lower',
    'cost', '成本', '费用', 'expense', 'debt', '债务', '负债',
    'error', '错误', 'bug', 'defect', '缺陷', 'waste', '浪费',
    'time', '时间', 'duration', '耗时', 'delay', '延迟',
    'risk', '风险', 'issue', '问题', 'complaint', '投诉'
  ]
  
  const decreaseUnits = [
    'kg', 'lb', 'pounds', '斤', '公斤', 
    'min', 'minutes', '分钟', 'hours', '小时',
    'days', '天', 'weeks', '周', 'months', '月'
  ]
  
  const name = kpi.name.toLowerCase()
  const unit = kpi.unit?.toLowerCase() || ''
  
  // 检查名称中是否包含减少型关键词
  const hasDecreaseKeyword = decreaseKeywords.some(keyword => 
    name.includes(keyword.toLowerCase())
  )
  
  // 检查单位是否为减少型
  const hasDecreaseUnit = decreaseUnits.some(u => unit.includes(u))
  
  // 特殊判断：如果名称中包含"增加"、"提高"等词，即使单位是减少型也按增长型处理
  const increaseKeywords = [
    'increase', '增加', '提高', 'improve', 'gain', '获得', 
    'build', '建立', 'grow', '增长', 'boost', '提升',
    'enhance', '增强', 'expand', '扩展'
  ]
  const hasIncreaseKeyword = increaseKeywords.some(keyword => 
    name.includes(keyword.toLowerCase())
  )
  
  if (hasIncreaseKeyword) {
    return 'increase'
  }
  
  return hasDecreaseKeyword || hasDecreaseUnit ? 'decrease' : 'increase'
}

/**
 * 计算KPI进度，支持增长型和减少型指标
 */
export function calculateKPIProgress(kpi: GenericKPI): number {
  if (!kpi.target) return 0
  
  const current = parseFloat(kpi.value)
  const target = parseFloat(kpi.target)
  
  if (isNaN(current) || isNaN(target)) return 0
  
  const direction = getKPIDirection(kpi)
  
  if (direction === 'decrease') {
    // 减少型指标：需要从当前值减少到目标值
    if (target >= current) {
      // 已经达到或超过目标
      return 100
    }
    
    // 计算减少的进度
    // 使用更智能的起始值估算
    let estimatedStart: number
    
    // 如果目标是0，起始值设为当前值的2倍
    if (target === 0) {
      estimatedStart = current * 2
    } else {
      // 否则，起始值设为目标值和当前值之间的合理值
      estimatedStart = Math.max(current * 1.5, target * 2)
    }
    
    const totalReduction = estimatedStart - target
    const currentReduction = estimatedStart - current
    
    if (totalReduction <= 0) return 100
    
    return Math.min((currentReduction / totalReduction) * 100, 100)
  } else {
    // 增长型指标：需要从当前值增长到目标值
    if (target === 0) return 0
    return Math.min((current / target) * 100, 100)
  }
}

/**
 * 获取KPI状态
 */
export function getKPIStatus(progress: number): {
  status: 'achieved' | 'on-track' | 'at-risk' | 'behind'
  color: string
  label: string
} {
  if (progress >= 100) {
    return {
      status: 'achieved',
      color: 'text-green-600',
      label: 'Achieved'
    }
  } else if (progress >= 75) {
    return {
      status: 'on-track',
      color: 'text-blue-600',
      label: 'On Track'
    }
  } else if (progress >= 50) {
    return {
      status: 'at-risk',
      color: 'text-yellow-600',
      label: 'At Risk'
    }
  } else {
    return {
      status: 'behind',
      color: 'text-red-600',
      label: 'Behind'
    }
  }
}

/**
 * 为ProjectKPI计算进度
 */
export function calculateProjectKPIProgress(kpi: ProjectKPI): number {
  return calculateKPIProgress(kpi as GenericKPI)
}

/**
 * 为AreaMetric计算进度
 */
export function calculateAreaMetricProgress(metric: AreaMetric): number {
  return calculateKPIProgress(metric as GenericKPI)
}

/**
 * 获取进度显示文本
 */
export function getProgressText(kpi: GenericKPI): string {
  const direction = getKPIDirection(kpi)
  const current = parseFloat(kpi.value)
  const target = kpi.target ? parseFloat(kpi.target) : null
  
  if (!target) {
    return `${current}${kpi.unit ? ` ${kpi.unit}` : ''}`
  }
  
  if (direction === 'decrease') {
    return `${current} → ${target}${kpi.unit ? ` ${kpi.unit}` : ''}`
  } else {
    return `${current} / ${target}${kpi.unit ? ` ${kpi.unit}` : ''}`
  }
}

/**
 * 获取进度条颜色
 */
export function getProgressBarColor(progress: number): string {
  if (progress >= 100) return 'bg-green-500'
  if (progress >= 75) return 'bg-blue-500'
  if (progress >= 50) return 'bg-yellow-500'
  return 'bg-red-500'
}

/**
 * 批量计算KPI统计
 */
export function calculateKPIStatistics(kpis: GenericKPI[]): {
  total: number
  achieved: number
  onTrack: number
  atRisk: number
  behind: number
  averageProgress: number
  withTargets: number
} {
  const kpisWithTargets = kpis.filter(kpi => kpi.target)
  
  if (kpisWithTargets.length === 0) {
    return {
      total: kpis.length,
      achieved: 0,
      onTrack: 0,
      atRisk: 0,
      behind: 0,
      averageProgress: 0,
      withTargets: 0
    }
  }
  
  let achieved = 0
  let onTrack = 0
  let atRisk = 0
  let behind = 0
  let totalProgress = 0
  
  kpisWithTargets.forEach(kpi => {
    const progress = calculateKPIProgress(kpi)
    totalProgress += progress
    
    if (progress >= 100) achieved++
    else if (progress >= 75) onTrack++
    else if (progress >= 50) atRisk++
    else behind++
  })
  
  return {
    total: kpis.length,
    achieved,
    onTrack,
    atRisk,
    behind,
    averageProgress: totalProgress / kpisWithTargets.length,
    withTargets: kpisWithTargets.length
  }
}

/**
 * 生成KPI改进建议
 */
export function generateKPIRecommendations(kpis: GenericKPI[]): string[] {
  const stats = calculateKPIStatistics(kpis)
  const recommendations: string[] = []
  
  if (stats.behind > 0) {
    recommendations.push(`${stats.behind} KPIs are significantly behind target. Consider reviewing your approach.`)
  }
  
  if (stats.atRisk > 0) {
    recommendations.push(`${stats.atRisk} KPIs are at risk. Focus on these to prevent further decline.`)
  }
  
  if (stats.achieved > 0) {
    recommendations.push(`Great job! ${stats.achieved} KPIs have achieved their targets.`)
  }
  
  const noTargets = stats.total - stats.withTargets
  if (noTargets > 0) {
    recommendations.push(`Set targets for ${noTargets} KPIs to enable better tracking.`)
  }
  
  if (recommendations.length === 0) {
    recommendations.push('Keep up the good work! All KPIs are on track.')
  }
  
  return recommendations
}

export default {
  getKPIDirection,
  calculateKPIProgress,
  getKPIStatus,
  calculateProjectKPIProgress,
  calculateAreaMetricProgress,
  getProgressText,
  getProgressBarColor,
  calculateKPIStatistics,
  generateKPIRecommendations
}
