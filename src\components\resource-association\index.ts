/**
 * 资源关联组件库入口文件
 * 导出所有资源关联相关的组件、类型和工具函数
 */

// 主要组件
export { default as ResourceAssociation } from './ResourceAssociation'
export { default as ResourceList } from './ResourceList'
export { default as FileUpload } from './FileUpload'
export { default as LinkCreate } from './LinkCreate'
export { default as MarkdownAssociation } from './MarkdownAssociation'
export { default as BidirectionalLinks } from './BidirectionalLinks'
export { default as ResourcePreview } from './ResourcePreview'
export { default as ResourceSearch } from './ResourceSearch'

// 数据源适配器
export { 
  ProjectResourceDataSource, 
  AreaResourceDataSource, 
  createResourceDataSource 
} from './adapters'

// 类型定义
export type {
  // 基础数据类型
  UnifiedResource,
  MarkdownResource,
  LinkResource,
  FileResource,
  ReferenceResource,
  BidirectionalLink,
  ResourceStatistics,
  ResourceType,
  LinkType,
  EntityType,
  UploadStatus,
  
  // 配置类型
  ResourceAssociationConfig,
  ResourceFilter,
  
  // 事件类型
  ResourceEvent,
  ResourceEventType,
  ResourceEventHandlers,
  
  // API接口类型
  ResourceDataSource,
  QueryOptions,
  FileUploadData,
  LinkCreateData,
  MarkdownAssociationData,
  
  // 组件Props类型
  ResourceAssociationProps,
  ResourceListProps,
  FileUploadProps,
  LinkCreateProps,
  MarkdownAssociationProps,
  BidirectionalLinksProps,
  
  // 工具类型
  DeepPartial,
  RequireFields,
  ResourceTypeColorMap
} from './types'

// 常量
export { DEFAULT_RESOURCE_CONFIG } from './types'

// 工具函数
export {
  getResourceIcon,
  getResourceTypeColor,
  formatFileSize,
  formatDate,
  validateUrl,
  extractDomain,
  generateResourceId,
  calculateResourceStats
} from './utils'

// 预设配置
export {
  MARKDOWN_FILE_EXTENSIONS,
  SUPPORTED_FILE_TYPES,
  DEFAULT_UPLOAD_LIMITS,
  LINK_VALIDATION_PATTERNS,
  RESOURCE_TYPE_ICONS,
  RESOURCE_TYPE_COLORS
} from './constants'
