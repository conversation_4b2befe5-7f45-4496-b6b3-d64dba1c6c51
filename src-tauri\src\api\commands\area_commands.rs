// Area Commands - 领域相关的 Tauri 命令

use crate::api::dto::*;
use crate::application::services::AreaAppService;
use crate::shared::errors::AppError;
use crate::shared::types::{Id, QueryParams, Pagination};
use std::sync::Arc;
use tauri::State;

/// 创建领域
#[tauri::command]
pub async fn create_area(
    request: CreateAreaRequest,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<AreaResponse>, String> {
    // 验证请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    // 调用应用服务
    match area_service.create_area(
        request.name,
        request.description,
        request.standards,
        request.color,
        request.icon,
    ).await {
        Ok(area) => Ok(ApiResponse::success(AreaResponse::from(area))),
        Err(e) => Ok(ApiResponse::error(format!("Failed to create area: {}", e))),
    }
}

/// 根据ID获取领域
#[tauri::command]
pub async fn get_area_by_id(
    id: Id,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<AreaResponse>, String> {
    match area_service.get_area_by_id(&id).await {
        Ok(area) => Ok(ApiResponse::success(AreaResponse::from(area))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Area not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to get area: {}", e))),
    }
}

/// 根据名称获取领域
#[tauri::command]
pub async fn get_area_by_name(
    name: String,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<AreaResponse>, String> {
    match area_service.get_area_by_name(&name).await {
        Ok(area) => Ok(ApiResponse::success(AreaResponse::from(area))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Area not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to get area: {}", e))),
    }
}

/// 更新领域信息
#[tauri::command]
pub async fn update_area(
    id: Id,
    request: UpdateAreaRequest,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<AreaResponse>, String> {
    // 验证请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    // 调用应用服务
    match area_service.update_area(
        &id,
        request.name,
        request.description,
        request.standards,
        request.color,
        request.icon,
    ).await {
        Ok(area) => Ok(ApiResponse::success(AreaResponse::from(area))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Area not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to update area: {}", e))),
    }
}

/// 更新领域状态
#[tauri::command]
pub async fn update_area_status(
    id: Id,
    request: UpdateAreaStatusRequest,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<AreaResponse>, String> {
    // 验证请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    match area_service.update_area_status(&id, request.get_status()).await {
        Ok(area) => Ok(ApiResponse::success(AreaResponse::from(area))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Area not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to update area status: {}", e))),
    }
}

/// 激活领域
#[tauri::command]
pub async fn activate_area(
    id: Id,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<AreaResponse>, String> {
    match area_service.activate_area(&id).await {
        Ok(area) => Ok(ApiResponse::success(AreaResponse::from(area))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Area not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to activate area: {}", e))),
    }
}

/// 设置领域为维护状态
#[tauri::command]
pub async fn set_area_maintenance(
    id: Id,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<AreaResponse>, String> {
    match area_service.set_area_maintenance(&id).await {
        Ok(area) => Ok(ApiResponse::success(AreaResponse::from(area))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Area not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to set area maintenance: {}", e))),
    }
}

/// 设置领域为休眠状态
#[tauri::command]
pub async fn set_area_dormant(
    id: Id,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<AreaResponse>, String> {
    match area_service.set_area_dormant(&id).await {
        Ok(area) => Ok(ApiResponse::success(AreaResponse::from(area))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Area not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to set area dormant: {}", e))),
    }
}

/// 删除领域
#[tauri::command]
pub async fn delete_area(
    id: Id,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<EmptyResponse>, String> {
    match area_service.delete_area(&id).await {
        Ok(_) => Ok(ApiResponse::success(EmptyResponse)),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Area not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to delete area: {}", e))),
    }
}

/// 获取所有活跃领域
#[tauri::command]
pub async fn get_active_areas(
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<Vec<AreaResponse>>, String> {
    match area_service.get_active_areas().await {
        Ok(areas) => {
            let area_responses: Vec<AreaResponse> = areas.into_iter()
                .map(AreaResponse::from)
                .collect();
            Ok(ApiResponse::success(area_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get active areas: {}", e))),
    }
}

/// 根据状态获取领域
#[tauri::command]
pub async fn get_areas_by_status(
    status: String,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<Vec<AreaResponse>>, String> {
    let status_request = UpdateAreaStatusRequest { status };
    if let Err(e) = status_request.validate() {
        return Ok(ApiResponse::error(e));
    }

    match area_service.get_areas_by_status(&status_request.get_status()).await {
        Ok(areas) => {
            let area_responses: Vec<AreaResponse> = areas.into_iter()
                .map(AreaResponse::from)
                .collect();
            Ok(ApiResponse::success(area_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get areas by status: {}", e))),
    }
}

/// 分页获取领域列表
#[tauri::command]
pub async fn get_areas_with_pagination(
    pagination: PaginationRequest,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<AreaListResponse>, String> {
    // 验证分页参数
    if let Err(e) = pagination.validate() {
        return Ok(ApiResponse::error(e));
    }

    let query_params = QueryParams {
        pagination: Some(Pagination {
            page: pagination.page,
            size: pagination.size,
        }),
        ..Default::default()
    };

    match area_service.get_areas_with_pagination(&query_params).await {
        Ok((areas, total)) => {
            let area_responses: Vec<AreaResponse> = areas.into_iter()
                .map(AreaResponse::from)
                .collect();
            
            let response = AreaListResponse {
                areas: area_responses,
                total,
                page: pagination.page,
                size: pagination.size,
            };
            
            Ok(ApiResponse::success(response))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get areas: {}", e))),
    }
}

/// 搜索领域
#[tauri::command]
pub async fn search_areas(
    request: SearchRequest,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<Vec<AreaResponse>>, String> {
    // 验证搜索请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    match area_service.search_areas(&request.query).await {
        Ok(areas) => {
            let area_responses: Vec<AreaResponse> = areas.into_iter()
                .map(AreaResponse::from)
                .collect();
            Ok(ApiResponse::success(area_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to search areas: {}", e))),
    }
}

/// 获取领域统计信息
#[tauri::command]
pub async fn get_area_statistics(
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<AreaStatisticsResponse>, String> {
    match area_service.get_area_statistics().await {
        Ok(stats) => Ok(ApiResponse::success(AreaStatisticsResponse::from(stats))),
        Err(e) => Ok(ApiResponse::error(format!("Failed to get area statistics: {}", e))),
    }
}

/// 获取领域的项目数量
#[tauri::command]
pub async fn get_area_project_count(
    id: Id,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<u64>, String> {
    match area_service.get_area_project_count(&id).await {
        Ok(count) => Ok(ApiResponse::success(count)),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Area not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to get area project count: {}", e))),
    }
}

/// 获取领域的习惯数量
#[tauri::command]
pub async fn get_area_habit_count(
    id: Id,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<u64>, String> {
    match area_service.get_area_habit_count(&id).await {
        Ok(count) => Ok(ApiResponse::success(count)),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Area not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to get area habit count: {}", e))),
    }
}

/// 获取领域详细信息
#[tauri::command]
pub async fn get_area_details(
    id: Id,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<AreaDetailsResponse>, String> {
    match area_service.get_area_details(&id).await {
        Ok(details) => Ok(ApiResponse::success(AreaDetailsResponse::from(details))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Area not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to get area details: {}", e))),
    }
}

/// 检查领域名称是否可用
#[tauri::command]
pub async fn is_area_name_available(
    name: String,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<AreaNameAvailabilityResponse>, String> {
    match area_service.is_area_name_available(&name).await {
        Ok(available) => {
            let response = AreaNameAvailabilityResponse {
                available,
                name,
            };
            Ok(ApiResponse::success(response))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to check area name availability: {}", e))),
    }
}

/// 归档领域
#[tauri::command]
pub async fn archive_area(
    id: Id,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<AreaResponse>, String> {
    match area_service.archive_area(&id).await {
        Ok(area) => Ok(ApiResponse::success(AreaResponse::from(area))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Area not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to archive area: {}", e))),
    }
}

/// 获取需要关注的领域
#[tauri::command]
pub async fn get_areas_needing_attention(
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<Vec<AreaResponse>>, String> {
    match area_service.get_areas_needing_attention().await {
        Ok(areas) => {
            let area_responses: Vec<AreaResponse> = areas.into_iter()
                .map(AreaResponse::from)
                .collect();
            Ok(ApiResponse::success(area_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get areas needing attention: {}", e))),
    }
}

/// 批量更新领域状态
#[tauri::command]
pub async fn batch_update_area_status(
    request: BatchUpdateAreaStatusRequest,
    area_service: State<'_, Arc<AreaAppService>>,
) -> Result<ApiResponse<Vec<AreaResponse>>, String> {
    // 验证请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    match area_service.batch_update_area_status(request.area_ids, request.get_status()).await {
        Ok(areas) => {
            let area_responses: Vec<AreaResponse> = areas.into_iter()
                .map(AreaResponse::from)
                .collect();
            Ok(ApiResponse::success(area_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to batch update area status: {}", e))),
    }
}
