/**
 * 习惯统计组件
 * 展示习惯追踪的统计信息和图表
 */

import { createMemo, For, Show } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { 
  Target, 
  TrendingUp, 
  Calendar,
  Flame,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-solid'
import { cn } from '../../lib/utils'

import type { HabitStatisticsProps, HabitStatistics } from './types'

export function HabitStatistics(props: HabitStatisticsProps) {
  // 计算趋势
  const getTrend = (data: number[]) => {
    if (data.length < 2) return 'stable'
    const recent = data.slice(-3).reduce((sum, val) => sum + val, 0) / 3
    const previous = data.slice(-6, -3).reduce((sum, val) => sum + val, 0) / 3
    
    if (recent > previous * 1.1) return 'up'
    if (recent < previous * 0.9) return 'down'
    return 'stable'
  }

  // 获取趋势图标和颜色
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return { icon: TrendingUp, color: 'text-green-500' }
      case 'down':
        return { icon: TrendingUp, color: 'text-red-500 rotate-180' }
      default:
        return { icon: Activity, color: 'text-gray-500' }
    }
  }

  // 计算周进度趋势
  const weeklyTrend = createMemo(() => getTrend(props.statistics.weeklyProgress))
  const monthlyTrend = createMemo(() => getTrend(props.statistics.monthlyProgress))

  // 获取最活跃的习惯类型
  const mostActiveType = createMemo(() => {
    const types = props.statistics.habitsByType
    return Object.entries(types).reduce((max, [type, count]) => 
      count > (types[max] || 0) ? type : max, 'boolean'
    )
  })

  // 计算完成率等级
  const getCompletionGrade = (rate: number) => {
    if (rate >= 90) return { grade: 'A+', color: 'text-green-600 bg-green-100' }
    if (rate >= 80) return { grade: 'A', color: 'text-green-600 bg-green-100' }
    if (rate >= 70) return { grade: 'B', color: 'text-blue-600 bg-blue-100' }
    if (rate >= 60) return { grade: 'C', color: 'text-yellow-600 bg-yellow-100' }
    if (rate >= 50) return { grade: 'D', color: 'text-orange-600 bg-orange-100' }
    return { grade: 'F', color: 'text-red-600 bg-red-100' }
  }

  const completionGrade = createMemo(() => getCompletionGrade(props.statistics.averageCompletion))

  return (
    <div class={cn("space-y-6", props.class)}>
      {/* 主要统计卡片 */}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* 总体完成率 */}
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-muted-foreground">Completion Rate</p>
                <div class="flex items-center gap-2 mt-1">
                  <p class="text-2xl font-bold">{Math.round(props.statistics.averageCompletion)}%</p>
                  <Badge class={cn("text-xs", completionGrade().color)}>
                    {completionGrade().grade}
                  </Badge>
                </div>
              </div>
              <Target class="h-8 w-8 text-blue-500" />
            </div>
            <Progress value={props.statistics.averageCompletion} class="mt-3" />
          </CardContent>
        </Card>

        {/* 活跃习惯 */}
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-muted-foreground">Active Habits</p>
                <p class="text-2xl font-bold">{props.statistics.activeHabits}</p>
                <p class="text-xs text-muted-foreground mt-1">
                  of {props.statistics.totalHabits} total
                </p>
              </div>
              <Calendar class="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        {/* 今日完成 */}
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-muted-foreground">Today's Progress</p>
                <p class="text-2xl font-bold">{props.statistics.completedToday}</p>
                <p class="text-xs text-muted-foreground mt-1">
                  {props.statistics.activeHabits > 0 
                    ? Math.round((props.statistics.completedToday / props.statistics.activeHabits) * 100)
                    : 0}% complete
                </p>
              </div>
              <Activity class="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        {/* 最长连击 */}
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-muted-foreground">Longest Streak</p>
                <p class="text-2xl font-bold">{props.statistics.longestStreak}</p>
                <p class="text-xs text-muted-foreground mt-1">
                  Current: {props.statistics.totalStreak} days
                </p>
              </div>
              <Flame class="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 详细统计 */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 习惯类型分布 */}
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <PieChart class="h-5 w-5" />
              Habit Types
            </CardTitle>
            <CardDescription>Distribution by habit type</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <For each={Object.entries(props.statistics.habitsByType)}>
              {([type, count]) => {
                const percentage = props.statistics.totalHabits > 0 
                  ? Math.round((count / props.statistics.totalHabits) * 100) 
                  : 0
                
                const typeColors = {
                  boolean: 'bg-blue-500',
                  numeric: 'bg-green-500',
                  duration: 'bg-purple-500'
                }
                
                return (
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <div class={cn("w-3 h-3 rounded-full", typeColors[type as keyof typeof typeColors])} />
                      <span class="capitalize">{type}</span>
                      <Show when={type === mostActiveType()}>
                        <Badge variant="secondary" class="text-xs">Most Used</Badge>
                      </Show>
                    </div>
                    <div class="flex items-center gap-2">
                      <span class="text-sm text-muted-foreground">{count}</span>
                      <span class="text-sm font-medium">{percentage}%</span>
                    </div>
                  </div>
                )
              }}
            </For>
          </CardContent>
        </Card>

        {/* 频率分布 */}
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <BarChart3 class="h-5 w-5" />
              Frequency Distribution
            </CardTitle>
            <CardDescription>Habits by frequency type</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <For each={Object.entries(props.statistics.habitsByFrequency)}>
              {([frequency, count]) => {
                const percentage = props.statistics.totalHabits > 0 
                  ? Math.round((count / props.statistics.totalHabits) * 100) 
                  : 0
                
                const frequencyColors = {
                  daily: 'bg-emerald-500',
                  weekly: 'bg-blue-500',
                  monthly: 'bg-amber-500'
                }
                
                return (
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <div class={cn("w-3 h-3 rounded-full", frequencyColors[frequency as keyof typeof frequencyColors])} />
                      <span class="capitalize">{frequency}</span>
                    </div>
                    <div class="flex items-center gap-2">
                      <span class="text-sm text-muted-foreground">{count}</span>
                      <span class="text-sm font-medium">{percentage}%</span>
                    </div>
                  </div>
                )
              }}
            </For>
          </CardContent>
        </Card>
      </div>

      {/* 趋势分析 */}
      <Show when={props.showChart}>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 周进度趋势 */}
          <Card>
            <CardHeader>
              <CardTitle class="flex items-center gap-2">
                <Activity class="h-5 w-5" />
                Weekly Trend
                <Show when={weeklyTrend()}>
                  {(() => {
                    const { icon: Icon, color } = getTrendIcon(weeklyTrend())
                    return <Icon class={cn("h-4 w-4", color)} />
                  })()}
                </Show>
              </CardTitle>
              <CardDescription>Last 7 days completion rate</CardDescription>
            </CardHeader>
            <CardContent>
              <div class="space-y-2">
                <For each={props.statistics.weeklyProgress}>
                  {(value, index) => (
                    <div class="flex items-center gap-3">
                      <span class="text-sm text-muted-foreground w-12">
                        Day {index() + 1}
                      </span>
                      <Progress value={value} class="flex-1" />
                      <span class="text-sm font-medium w-12 text-right">
                        {Math.round(value)}%
                      </span>
                    </div>
                  )}
                </For>
              </div>
            </CardContent>
          </Card>

          {/* 月进度趋势 */}
          <Card>
            <CardHeader>
              <CardTitle class="flex items-center gap-2">
                <Calendar class="h-5 w-5" />
                Monthly Trend
                <Show when={monthlyTrend()}>
                  {(() => {
                    const { icon: Icon, color } = getTrendIcon(monthlyTrend())
                    return <Icon class={cn("h-4 w-4", color)} />
                  })()}
                </Show>
              </CardTitle>
              <CardDescription>Last 30 days completion rate</CardDescription>
            </CardHeader>
            <CardContent>
              <div class="space-y-2">
                <For each={props.statistics.monthlyProgress.slice(-4)}>
                  {(value, index) => (
                    <div class="flex items-center gap-3">
                      <span class="text-sm text-muted-foreground w-16">
                        Week {index() + 1}
                      </span>
                      <Progress value={value} class="flex-1" />
                      <span class="text-sm font-medium w-12 text-right">
                        {Math.round(value)}%
                      </span>
                    </div>
                  )}
                </For>
              </div>
            </CardContent>
          </Card>
        </div>
      </Show>

      {/* 成就和里程碑 */}
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Flame class="h-5 w-5" />
            Achievements
          </CardTitle>
          <CardDescription>Your habit tracking milestones</CardDescription>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center p-4 bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
              <div class="text-2xl mb-2">🔥</div>
              <div class="font-semibold">Streak Master</div>
              <div class="text-sm text-muted-foreground">
                {props.statistics.longestStreak} day best streak
              </div>
            </div>
            
            <div class="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg border border-green-200">
              <div class="text-2xl mb-2">🎯</div>
              <div class="font-semibold">Consistency</div>
              <div class="text-sm text-muted-foreground">
                {Math.round(props.statistics.averageCompletion)}% completion rate
              </div>
            </div>
            
            <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-lg border border-blue-200">
              <div class="text-2xl mb-2">📈</div>
              <div class="font-semibold">Active Tracker</div>
              <div class="text-sm text-muted-foreground">
                {props.statistics.activeHabits} active habits
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default HabitStatistics
