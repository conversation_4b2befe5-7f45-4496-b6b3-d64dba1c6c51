// Layout Component - 主布局组件
// 应用程序的主要布局结构

import { JSX, createSignal, Show } from 'solid-js';
import { cn } from '@/lib/utils';
import { Header } from './Header';
import { Sidebar, defaultMenuItems, MenuItem } from './Sidebar';

export interface LayoutProps {
  children: JSX.Element;
  title?: string;
  user?: {
    name: string;
    avatar?: string;
    email?: string;
  };
  menuItems?: MenuItem[];
  activeMenuItem?: string;
  onMenuItemClick?: (item: MenuItem) => void;
  onUserMenuClick?: () => void;
  onSettingsClick?: () => void;
  onNotificationClick?: () => void;
  onSearch?: (query: string) => void;
  notificationCount?: number;
  class?: string;
  fullBleed?: boolean;         // 页面内容满幅
  headerFullBleed?: boolean;   // 头部内容满幅
  contentClass?: string;       // 可覆盖主内容容器的类
}

export function Layout(props: LayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = createSignal(false);
  const [mobileSidebarOpen, setMobileSidebarOpen] = createSignal(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed());
  };

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen());
  };

  const handleMenuItemClick = (item: MenuItem) => {
    // 在移动端点击菜单项后关闭侧边栏
    if (window.innerWidth < 768) {
      setMobileSidebarOpen(false);
    }
    props.onMenuItemClick?.(item);
  };

  return (
    <div class={cn('flex h-screen bg-background', props.class)}>
      {/* 移动端遮罩层 */}
      <Show when={mobileSidebarOpen()}>
        <div
          class="fixed inset-0 z-40 bg-black/50 md:hidden"
          onClick={() => setMobileSidebarOpen(false)}
        />
      </Show>

      {/* 侧边栏 */}
      <div class={cn(
        'fixed inset-y-0 left-0 z-50 transition-transform duration-300 md:relative md:translate-x-0',
        mobileSidebarOpen() ? 'translate-x-0' : '-translate-x-full md:translate-x-0'
      )}>
        <Sidebar
          items={props.menuItems || defaultMenuItems}
          activeItem={props.activeMenuItem}
          collapsed={sidebarCollapsed()}
          onItemClick={handleMenuItemClick}
          onToggleCollapse={toggleSidebar}
          user={props.user}
          onUserMenuClick={props.onUserMenuClick}
          onSettingsClick={props.onSettingsClick}
        />
      </div>

      {/* 主内容区域 */}
      <div class="flex flex-1 flex-col overflow-hidden">
        {/* 头部 */}
        <Header
          title={props.title}
          onMenuToggle={toggleMobileSidebar}
          onNotificationClick={props.onNotificationClick}
          onSearch={props.onSearch}
          notificationCount={props.notificationCount}
          fullBleed={props.headerFullBleed}
        />

        {/* 页面内容 */}
        <main class="flex-1 overflow-hidden bg-background">
          <div class={cn(
            props.fullBleed ? "w-full max-w-none h-full px-0 pt-0 pb-0" : "container mx-auto h-full px-2 pt-0 pb-0 max-w-screen-2xl",
            props.contentClass
        )}>
            {props.children}
          </div>
        </main>
      </div>
    </div>
  );
}

// 页面容器组件
export interface PageContainerProps {
  children: JSX.Element;
  title?: string;
  description?: string;
  actions?: JSX.Element;
  breadcrumbs?: Array<{
    label: string;
    path?: string;
  }>;
  class?: string;
}

export function PageContainer(props: PageContainerProps) {
  return (
    <div class={cn('space-y-6', props.class)}>
      {/* 面包屑导航 */}
      <Show when={props.breadcrumbs && props.breadcrumbs.length > 0}>
        <nav class="flex" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 md:space-x-3">
            {props.breadcrumbs!.map((crumb, index) => (
              <li class="inline-flex items-center">
                <Show when={index > 0}>
                  <svg
                    class="w-3 h-3 text-muted-foreground mx-1"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 6 10"
                  >
                    <path
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="m1 9 4-4-4-4"
                    />
                  </svg>
                </Show>
                <Show
                  when={crumb.path}
                  fallback={
                    <span class="text-sm font-medium text-muted-foreground">
                      {crumb.label}
                    </span>
                  }
                >
                  <a
                    href={crumb.path}
                    class="inline-flex items-center text-sm font-medium text-foreground hover:text-primary transition-colors"
                  >
                    {crumb.label}
                  </a>
                </Show>
              </li>
            ))}
          </ol>
        </nav>
      </Show>

      {/* 页面头部 */}
      <Show when={props.title || props.description || props.actions}>
        <div class="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div class="space-y-1">
            <Show when={props.title}>
              <h1 class="text-3xl font-bold tracking-tight">{props.title}</h1>
            </Show>
            <Show when={props.description}>
              <p class="text-muted-foreground">{props.description}</p>
            </Show>
          </div>
          <Show when={props.actions}>
            <div class="flex items-center space-x-2">
              {props.actions}
            </div>
          </Show>
        </div>
      </Show>

      {/* 页面内容 */}
      <div class="space-y-6">
        {props.children}
      </div>
    </div>
  );
}

// 内容区域组件
export interface ContentAreaProps {
  children: JSX.Element;
  variant?: 'default' | 'card' | 'section';
  class?: string;
}

export function ContentArea(props: ContentAreaProps) {
  const variant = () => props.variant || 'default';

  const variantClasses = () => {
    switch (variant()) {
      case 'card':
        return 'bg-card border border-border rounded-lg p-6 shadow-sm';
      case 'section':
        return 'bg-muted/50 rounded-lg p-6';
      default:
        return '';
    }
  };

  return (
    <div class={cn(variantClasses(), props.class)}>
      {props.children}
    </div>
  );
}

// 空状态组件
export interface EmptyStateProps {
  icon?: JSX.Element;
  title: string;
  description?: string;
  action?: JSX.Element;
  class?: string;
}

export function EmptyState(props: EmptyStateProps) {
  return (
    <div class={cn(
      'flex flex-col items-center justify-center text-center py-12 px-4',
      props.class
    )}>
      <Show when={props.icon}>
        <div class="mb-4 h-12 w-12 text-muted-foreground">
          {props.icon}
        </div>
      </Show>
      <h3 class="text-lg font-semibold mb-2">{props.title}</h3>
      <Show when={props.description}>
        <p class="text-muted-foreground mb-6 max-w-sm">
          {props.description}
        </p>
      </Show>
      <Show when={props.action}>
        {props.action}
      </Show>
    </div>
  );
}

// 加载状态组件
export interface LoadingStateProps {
  message?: string;
  class?: string;
}

export function LoadingState(props: LoadingStateProps) {
  return (
    <div class={cn(
      'flex flex-col items-center justify-center py-12 px-4',
      props.class
    )}>
      <div class="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mb-4" />
      <Show when={props.message}>
        <p class="text-muted-foreground">{props.message}</p>
      </Show>
    </div>
  );
}

// 错误状态组件
export interface ErrorStateProps {
  title?: string;
  message: string;
  action?: JSX.Element;
  class?: string;
}

export function ErrorState(props: ErrorStateProps) {
  return (
    <div class={cn(
      'flex flex-col items-center justify-center text-center py-12 px-4',
      props.class
    )}>
      <div class="mb-4 h-12 w-12 text-destructive">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          class="h-full w-full"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
          />
        </svg>
      </div>
      <h3 class="text-lg font-semibold mb-2">
        {props.title || 'Something went wrong'}
      </h3>
      <p class="text-muted-foreground mb-6 max-w-sm">
        {props.message}
      </p>
      <Show when={props.action}>
        {props.action}
      </Show>
    </div>
  );
}
