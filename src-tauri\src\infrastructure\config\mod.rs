// Infrastructure Configuration - 基础设施配置

use crate::shared::errors::{AppError, Result};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AppConfig {
    pub database: DatabaseConfig,
    pub logging: LoggingConfig,
    pub storage: StorageConfig,
    pub search: SearchConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub timeout_seconds: u64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: String,
    pub file_enabled: bool,
    pub file_path: Option<PathBuf>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageConfig {
    pub data_dir: PathBuf,
    pub backup_dir: PathBuf,
    pub max_file_size: u64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SearchConfig {
    pub index_dir: PathBuf,
    pub max_results: usize,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            database: DatabaseConfig {
                url: "sqlite:data/paolife.db".to_string(),
                max_connections: 10,
                timeout_seconds: 30,
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                file_enabled: true,
                file_path: Some(PathBuf::from("logs/app.log")),
            },
            storage: StorageConfig {
                data_dir: PathBuf::from("data"),
                backup_dir: PathBuf::from("backups"),
                max_file_size: 100 * 1024 * 1024, // 100MB
            },
            search: SearchConfig {
                index_dir: PathBuf::from("data/search_index"),
                max_results: 100,
            },
        }
    }
}

impl AppConfig {
    pub fn load() -> Result<Self> {
        // Try to load from config file, fallback to default
        match std::fs::read_to_string("config.json") {
            Ok(content) => {
                serde_json::from_str(&content)
                    .map_err(|e| AppError::ConfigError(format!("Failed to parse config: {}", e)))
            }
            Err(_) => {
                // Config file doesn't exist, use default and create it
                let config = Self::default();
                config.save()?;
                Ok(config)
            }
        }
    }

    pub fn save(&self) -> Result<()> {
        let content = serde_json::to_string_pretty(self)
            .map_err(|e| AppError::ConfigError(format!("Failed to serialize config: {}", e)))?;
        
        std::fs::write("config.json", content)
            .map_err(|e| AppError::ConfigError(format!("Failed to write config: {}", e)))?;
        
        Ok(())
    }

    pub fn ensure_directories(&self) -> Result<()> {
        let dirs = [
            &self.storage.data_dir,
            &self.storage.backup_dir,
            &self.search.index_dir,
        ];

        for dir in dirs {
            if !dir.exists() {
                std::fs::create_dir_all(dir)
                    .map_err(|e| AppError::ConfigError(format!("Failed to create directory {:?}: {}", dir, e)))?;
            }
        }

        // Create logs directory if logging to file is enabled
        if self.logging.file_enabled {
            if let Some(log_path) = &self.logging.file_path {
                if let Some(parent) = log_path.parent() {
                    std::fs::create_dir_all(parent)
                        .map_err(|e| AppError::ConfigError(format!("Failed to create log directory: {}", e)))?;
                }
            }
        }

        Ok(())
    }
}
