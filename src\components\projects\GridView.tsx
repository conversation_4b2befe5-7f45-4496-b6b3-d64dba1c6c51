import { createSignal, For, Show, type Component } from 'solid-js';
import type { Project } from './types';
import { getStatusDotColor, getRemainingDaysColor, getStatusText } from './types';

interface GridViewProps {
    projects: Project[];
    onSelect: (id: string) => void;
    onEdit: (project: Project) => void;
    onArchive: (project: Project) => void;
    onDelete: (id: string) => void;
}

const ProjectActions: Component<{ onEdit: () => void; onArchive: () => void; onDelete: () => void }> = (props) => {
    const [isOpen, setIsOpen] = createSignal(false);
    let menuRef: HTMLDivElement | undefined;
  
    const handleClickOutside = (e: MouseEvent) => {
      if (menuRef && !menuRef.contains(e.target as Node)) setIsOpen(false);
    };
  
    const toggleMenu = (e: MouseEvent) => {
        e.stopPropagation();
        const wasOpen = isOpen();
        setIsOpen(!wasOpen);
        if (!wasOpen) document.addEventListener('click', handleClickOutside, { once: true });
    };

    return (
        <div class="relative" ref={menuRef}>
            <button onClick={toggleMenu} class="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle></svg>
            </button>
            <Show when={isOpen()}>
                <div class="absolute right-0 top-full mt-2 w-32 bg-white dark:bg-gray-800 rounded-md shadow-lg border dark:border-gray-700 z-10 text-sm">
                    <button onClick={props.onEdit} class="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700">编辑</button>
                    <button onClick={props.onArchive} class="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700">归档</button>
                    <button onClick={props.onDelete} class="w-full text-left px-3 py-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/50">删除</button>
                </div>
            </Show>
        </div>
    );
};

export const ProjectGridView: Component<GridViewProps> = (props) => {
    const calculateProgress = (project: Project) => {
        if (project.tasks.length === 0) return 0;
        const countTasks = (tasks: typeof project.tasks): { total: number, completed: number } => {
            let total = tasks.length;
            let completed = tasks.filter(t => t.completed).length;
            for (const task of tasks) {
                if (task.subtasks.length > 0) {
                    const subCounts = countTasks(task.subtasks);
                    total += subCounts.total;
                    completed += subCounts.completed;
                }
            }
            return { total, completed };
        }
        const { total, completed } = countTasks(project.tasks);
        if (total === 0) return 0;
        return (completed / total) * 100;
    };

    const getRemainingDaysInfo = (dueDate: string | undefined): { text: string; color: string } => {
        if (!dueDate) return { text: '未设截止', color: 'text-gray-500' };
        const today = new Date();
        const due = new Date(dueDate);
        today.setHours(0, 0, 0, 0);
        due.setHours(0, 0, 0, 0);
        const diffDays = Math.ceil((due.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        const color = getRemainingDaysColor(diffDays);
        let text = diffDays < 0 ? `已逾期 ${Math.abs(diffDays)} 天` : diffDays === 0 ? '今天截止' : `剩余 ${diffDays} 天`;
        return { text, color };
    };

    return (
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <For each={props.projects}>
                {project => {
                    const remainingDays = getRemainingDaysInfo(project.dueDate);
                    return (
                        <div onClick={() => props.onSelect(project.id)} class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-xl border border-gray-200 dark:border-gray-700/50 hover:border-blue-500 dark:hover:border-blue-500 transition-all duration-200 cursor-pointer flex flex-col justify-between p-5">
                            <div>
                                <div class="flex justify-between items-start">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center gap-2 mb-2 flex-wrap">
                                            <h3 class="font-bold text-lg text-gray-800 dark:text-gray-100 truncate" title={project.name}>{project.name}</h3>
                                            <Show when={project.areaName}>
                                                <a href="#" onClick={e => e.stopPropagation()} class="text-xs font-medium text-blue-600 dark:text-blue-400 hover:underline flex-shrink-0">{project.areaName}</a>
                                            </Show>
                                            <div class={`w-2 h-2 rounded-full flex-shrink-0 ${getStatusDotColor(project.status)}`} title={getStatusText(project.status)}></div>
                                        </div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">{project.description}</p>
                                    </div>
                                    <div class="flex-shrink-0 ml-2">
                                        <ProjectActions 
                                            onEdit={() => props.onEdit(project)}
                                            onArchive={() => props.onArchive(project)}
                                            onDelete={() => props.onDelete(project.id)}
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex justify-between text-xs text-gray-500 mb-1">
                                    <span>进度</span><span>{calculateProgress(project).toFixed(0)}%</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style={{ width: `${calculateProgress(project)}%` }}></div>
                                </div>
                                <div class="text-xs text-gray-400 dark:text-gray-500 mt-3 flex justify-between items-center" title={`截止日期: ${project.dueDate || 'N/A'}`}>
                                    <span>开始: {project.startDate || 'N/A'}</span>
                                    <span class={`font-medium ${remainingDays.color}`}>{remainingDays.text}</span>
                                </div>
                            </div>
                        </div>
                    );
                }}
            </For>
        </div>
    );
};

