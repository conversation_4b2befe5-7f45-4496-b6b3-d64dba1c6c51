import { Show } from 'solid-js';
import type { Project } from '../../../types/business';

interface OverviewSectionProps {
  project: Project;
}

export default function OverviewSection(props: OverviewSectionProps) {
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  return (
    <div class="space-y-4">
      <div>
        <h3 class="text-2xl font-bold">{props.project.name}</h3>
        <Show when={props.project.description}>
          <p class="text-muted-foreground mt-2">{props.project.description}</p>
        </Show>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="p-4 border rounded-lg">
          <div class="text-sm text-muted-foreground">起止日期</div>
          <div class="mt-1 text-sm">{formatDate(props.project.startDate)} - {formatDate(props.project.dueDate)}</div>
        </div>
        <div class="p-4 border rounded-lg">
          <div class="text-sm text-muted-foreground">进度</div>
          <div class="mt-1 font-medium">{Math.round((props.project.progress || 0) * 100)}%</div>
        </div>
        <div class="p-4 border rounded-lg">
          <div class="text-sm text-muted-foreground">优先级</div>
          <div class="mt-1 font-medium">{props.project.priority}</div>
        </div>
      </div>
    </div>
  );
}

