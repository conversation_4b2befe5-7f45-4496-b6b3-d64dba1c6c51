/**
 * 统一资源关联组件类型定义
 * 为可复用的资源关联组件提供完整的类型支持
 */

import { JSX } from 'solid-js'

// ============================================================================
// 基础数据类型
// ============================================================================

export type ResourceType = 'markdown' | 'link' | 'file' | 'attachment' | 'reference'
export type LinkType = 'wikilink' | 'reference' | 'embed' | 'citation' | 'external'
export type EntityType = 'project' | 'area' | 'task' | 'habit' | 'note'
export type UploadStatus = 'pending' | 'uploading' | 'completed' | 'failed'

// 基础资源数据结构
export interface BaseResource {
  id: string
  title: string
  description?: string
  type: ResourceType
  entityType?: EntityType
  entityId?: string
  createdAt: Date
  updatedAt: Date
  lastAccessedAt?: Date
  accessCount: number
  tags: string[]
  isActive: boolean
}

// Markdown文档资源
export interface MarkdownResource extends BaseResource {
  type: 'markdown'
  filePath: string
  content?: string
  wordCount?: number
  characterCount?: number
  linksCount?: number
  backlinksCount?: number
  isOrphaned?: boolean
  fileHash?: string
}

// 外部链接资源
export interface LinkResource extends BaseResource {
  type: 'link'
  url: string
  domain?: string
  favicon?: string
  previewTitle?: string
  previewDescription?: string
  previewImage?: string
  isValidUrl?: boolean
  lastChecked?: Date
  responseStatus?: number
}

// 文件附件资源
export interface FileResource extends BaseResource {
  type: 'file' | 'attachment'
  filePath: string
  fileName: string
  fileSize: number
  mimeType: string
  fileHash?: string
  uploadStatus: UploadStatus
  uploadProgress?: number
  uploadedBy?: string
  thumbnailPath?: string
  previewAvailable?: boolean
}

// 引用资源
export interface ReferenceResource extends BaseResource {
  type: 'reference'
  sourceType: EntityType
  sourceId: string
  sourcePath?: string
  sourceTitle: string
  referenceType: LinkType
  context: string
  strength: number // 引用强度 0-1
}

// 统一资源类型
export type UnifiedResource = MarkdownResource | LinkResource | FileResource | ReferenceResource

// 双向链接数据
export interface BidirectionalLink {
  id: string
  sourceDocPath: string
  sourceDocTitle?: string
  targetDocPath: string
  targetDocTitle?: string
  linkText: string
  displayText?: string
  linkType: LinkType
  startPosition: number
  endPosition: number
  lineNumber: number
  columnNumber: number
  contextBefore?: string
  contextAfter?: string
  isValid: boolean
  linkStrength: number
  createdAt: Date
  updatedAt: Date
  lastValidated: Date
}

// 文件上传数据
export interface FileUploadData {
  file: File
  fileName?: string
  description?: string
  tags?: string[]
  entityType?: EntityType
  entityId?: string
  targetPath?: string
}

// 链接创建数据
export interface LinkCreateData {
  url: string
  title?: string
  description?: string
  tags?: string[]
  entityType?: EntityType
  entityId?: string
}

// Markdown关联数据
export interface MarkdownAssociationData {
  filePath: string
  title?: string
  description?: string
  tags?: string[]
  entityType?: EntityType
  entityId?: string
  createIfNotExists?: boolean
}

// ============================================================================
// 配置接口
// ============================================================================

// 组件配置选项
export interface ResourceAssociationConfig {
  // 功能开关
  enableMarkdownAssociation?: boolean
  enableLinkManagement?: boolean
  enableFileUpload?: boolean
  enableBidirectionalLinks?: boolean
  
  // 显示选项
  showResourcePreview?: boolean
  showResourceStats?: boolean
  showRecentResources?: boolean
  showResourceSearch?: boolean
  
  // 交互选项
  allowCreate?: boolean
  allowEdit?: boolean
  allowDelete?: boolean
  allowBulkOperations?: boolean
  allowDragDrop?: boolean
  
  // 布局选项
  layout?: 'grid' | 'list' | 'compact' | 'tree'
  groupBy?: 'type' | 'date' | 'entity' | 'tags' | 'none'
  sortBy?: 'name' | 'date' | 'type' | 'access' | 'size'
  sortOrder?: 'asc' | 'desc'
  
  // 文件上传配置
  maxFileSize?: number // bytes
  allowedFileTypes?: string[]
  uploadPath?: string
  enableThumbnails?: boolean
  
  // 链接配置
  enableLinkPreview?: boolean
  enableFaviconFetch?: boolean
  linkValidationTimeout?: number
  
  // Markdown配置
  enableWikiLinks?: boolean
  enableAutoLinking?: boolean
  markdownBasePath?: string
  
  // 样式配置
  className?: string
  theme?: 'light' | 'dark' | 'auto'
  compactMode?: boolean
}

// 资源过滤选项
export interface ResourceFilter {
  type?: ResourceType[]
  entityType?: EntityType
  entityId?: string
  tags?: string[]
  search?: string
  dateRange?: {
    start: Date
    end: Date
  }
  isActive?: boolean
  hasPreview?: boolean
}

// 资源统计信息
export interface ResourceStatistics {
  total: number
  byType: Record<ResourceType, number>
  byEntity: Record<EntityType, number>
  totalSize: number
  recentlyAdded: number
  recentlyAccessed: number
  orphaned: number
  broken: number
}

// ============================================================================
// 事件接口
// ============================================================================

// 资源事件类型
export type ResourceEventType = 
  | 'create'
  | 'update'
  | 'delete'
  | 'access'
  | 'upload-start'
  | 'upload-progress'
  | 'upload-complete'
  | 'upload-error'
  | 'link-create'
  | 'link-update'
  | 'link-validate'

// 资源事件数据
export interface ResourceEvent<T = any> {
  type: ResourceEventType
  resourceId: string
  data: T
  timestamp: Date
}

// 事件回调函数
export interface ResourceEventHandlers {
  onCreate?: (resource: UnifiedResource) => void | Promise<void>
  onUpdate?: (resource: UnifiedResource, changes: Partial<UnifiedResource>) => void | Promise<void>
  onDelete?: (resourceId: string) => void | Promise<void>
  onAccess?: (resource: UnifiedResource) => void | Promise<void>
  onUploadStart?: (uploadData: FileUploadData) => void | Promise<void>
  onUploadProgress?: (resourceId: string, progress: number) => void | Promise<void>
  onUploadComplete?: (resource: FileResource) => void | Promise<void>
  onUploadError?: (error: Error, uploadData: FileUploadData) => void | Promise<void>
  onLinkCreate?: (link: BidirectionalLink) => void | Promise<void>
  onLinkUpdate?: (link: BidirectionalLink) => void | Promise<void>
  onLinkValidate?: (link: BidirectionalLink, isValid: boolean) => void | Promise<void>
  onError?: (error: Error, context?: string) => void
}

// ============================================================================
// API接口
// ============================================================================

// 资源数据源接口
export interface ResourceDataSource {
  // 资源CRUD操作
  getResources(entityType: EntityType, entityId: string, filter?: ResourceFilter): Promise<UnifiedResource[]>
  getResource(id: string): Promise<UnifiedResource | null>
  createResource(data: any): Promise<UnifiedResource>
  updateResource(id: string, data: Partial<any>): Promise<UnifiedResource>
  deleteResource(id: string): Promise<void>
  
  // 文件操作
  uploadFile(uploadData: FileUploadData, onProgress?: (progress: number) => void): Promise<FileResource>
  downloadFile(resourceId: string): Promise<Blob>
  deleteFile(resourceId: string): Promise<void>
  
  // 链接操作
  createLink(linkData: LinkCreateData): Promise<LinkResource>
  validateLink(resourceId: string): Promise<boolean>
  updateLinkPreview(resourceId: string): Promise<LinkResource>
  
  // Markdown操作
  associateMarkdown(data: MarkdownAssociationData): Promise<MarkdownResource>
  getMarkdownContent(resourceId: string): Promise<string>
  updateMarkdownContent(resourceId: string, content: string): Promise<void>
  
  // 双向链接操作
  getBidirectionalLinks(documentPath: string): Promise<BidirectionalLink[]>
  createBidirectionalLink(sourceDoc: string, targetDoc: string, linkData: any): Promise<BidirectionalLink>
  updateBidirectionalLinks(documentPath: string, links: any[]): Promise<void>
  
  // 统计信息
  getStatistics(entityType: EntityType, entityId: string): Promise<ResourceStatistics>
  
  // 搜索
  searchResources(query: string, filter?: ResourceFilter): Promise<UnifiedResource[]>
}

// 查询选项
export interface QueryOptions {
  limit?: number
  offset?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  include?: string[]
}

// ============================================================================
// 组件Props接口
// ============================================================================

// 基础组件Props
export interface BaseComponentProps {
  class?: string
  children?: JSX.Element
}

// 资源关联主组件Props
export interface ResourceAssociationProps extends BaseComponentProps {
  // 必需属性
  entityType: EntityType
  entityId: string
  dataSource: ResourceDataSource
  
  // 可选配置
  config?: Partial<ResourceAssociationConfig>
  eventHandlers?: ResourceEventHandlers
  
  // 初始数据
  initialResources?: UnifiedResource[]
  
  // 样式和主题
  theme?: 'light' | 'dark' | 'auto'
  className?: string
}

// 资源列表组件Props
export interface ResourceListProps extends BaseComponentProps {
  resources: UnifiedResource[]
  config?: Partial<ResourceAssociationConfig>
  onResourceClick?: (resource: UnifiedResource) => void
  onResourceEdit?: (resource: UnifiedResource) => void
  onResourceDelete?: (resourceId: string) => void
  selectedResources?: string[]
  onSelectionChange?: (selectedIds: string[]) => void
}

// 文件上传组件Props
export interface FileUploadProps extends BaseComponentProps {
  onUpload: (uploadData: FileUploadData) => Promise<void>
  config?: Partial<ResourceAssociationConfig>
  accept?: string
  multiple?: boolean
  maxSize?: number
  disabled?: boolean
}

// 链接创建组件Props
export interface LinkCreateProps extends BaseComponentProps {
  onCreateLink: (linkData: LinkCreateData) => Promise<void>
  config?: Partial<ResourceAssociationConfig>
  placeholder?: string
  disabled?: boolean
}

// Markdown关联组件Props
export interface MarkdownAssociationProps extends BaseComponentProps {
  onAssociate: (data: MarkdownAssociationData) => Promise<void>
  config?: Partial<ResourceAssociationConfig>
  basePath?: string
  disabled?: boolean
}

// 双向链接组件Props
export interface BidirectionalLinksProps extends BaseComponentProps {
  documentPath: string
  links: BidirectionalLink[]
  onLinkClick?: (link: BidirectionalLink) => void
  onLinkCreate?: (sourceDoc: string, targetDoc: string) => void
  showBacklinks?: boolean
  showOutlinks?: boolean
}

// ============================================================================
// 工具类型
// ============================================================================

// 深度可选类型
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

// 选择性必需类型
export type RequireFields<T, K extends keyof T> = T & Required<Pick<T, K>>

// 资源类型颜色映射
export interface ResourceTypeColorMap {
  [K in ResourceType]: {
    bg: string
    text: string
    border: string
    icon: string
  }
}

// 导出默认配置
export const DEFAULT_RESOURCE_CONFIG: ResourceAssociationConfig = {
  enableMarkdownAssociation: true,
  enableLinkManagement: true,
  enableFileUpload: true,
  enableBidirectionalLinks: true,
  showResourcePreview: true,
  showResourceStats: true,
  showRecentResources: true,
  showResourceSearch: true,
  allowCreate: true,
  allowEdit: true,
  allowDelete: true,
  allowBulkOperations: false,
  allowDragDrop: true,
  layout: 'list',
  groupBy: 'type',
  sortBy: 'date',
  sortOrder: 'desc',
  maxFileSize: 50 * 1024 * 1024, // 50MB
  allowedFileTypes: ['*'],
  enableThumbnails: true,
  enableLinkPreview: true,
  enableFaviconFetch: true,
  linkValidationTimeout: 5000,
  enableWikiLinks: true,
  enableAutoLinking: true,
  theme: 'auto',
  compactMode: false
}
