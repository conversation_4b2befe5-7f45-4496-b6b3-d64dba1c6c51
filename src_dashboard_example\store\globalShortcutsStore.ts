import { create } from 'zustand'

interface GlobalShortcutsState {
  isInitialized: boolean
  registeredShortcuts: Map<string, string> // accelerator -> action
  initializeGlobalShortcuts: () => Promise<void>
  updateGlobalShortcut: (shortcutId: string, oldKeys: string[], newKeys: string[]) => Promise<void>
  syncAllGlobalShortcuts: () => Promise<void>
}

// Map shortcut IDs to their actions (only for shortcuts that need global registration)
const GLOBAL_SHORTCUT_ACTIONS: Record<string, string> = {
  'inbox-quick-capture': 'inbox-quick-capture',
  'inbox-navigate': 'inbox-navigate'
}

// Convert keys array to Electron accelerator format
function keysToAccelerator(keys: string[]): string {
  return keys.join('+').replace('Ctrl', 'CommandOrControl')
}

export const useGlobalShortcutsStore = create<GlobalShortcutsState>((set, get) => ({
  isInitialized: false,
  registeredShortcuts: new Map(),

  initializeGlobalShortcuts: async () => {
    if (get().isInitialized) return

    try {
      // Get current shortcuts from shortcuts store
      const { useShortcutsStore } = await import('./shortcutsStore')
      const shortcuts = useShortcutsStore.getState().shortcuts

      // Register global shortcuts for inbox actions
      const globalShortcuts = shortcuts.filter(s => 
        GLOBAL_SHORTCUT_ACTIONS.hasOwnProperty(s.id) && s.enabled
      )

      for (const shortcut of globalShortcuts) {
        const accelerator = keysToAccelerator(shortcut.currentKeys)
        const action = GLOBAL_SHORTCUT_ACTIONS[shortcut.id]
        
        if (window.electronAPI?.window?.registerGlobalShortcut) {
          const success = await window.electronAPI.window.registerGlobalShortcut(accelerator, action)
          if (success) {
            get().registeredShortcuts.set(accelerator, action)
            console.log(`Global shortcut registered: ${accelerator} -> ${action}`)
          } else {
            console.warn(`Failed to register global shortcut: ${accelerator}`)
          }
        }
      }

      set({ isInitialized: true })
    } catch (error) {
      console.error('Failed to initialize global shortcuts:', error)
    }
  },

  updateGlobalShortcut: async (shortcutId: string, oldKeys: string[], newKeys: string[]) => {
    if (!GLOBAL_SHORTCUT_ACTIONS.hasOwnProperty(shortcutId)) return

    try {
      const action = GLOBAL_SHORTCUT_ACTIONS[shortcutId]
      
      // Unregister old shortcut if it exists
      if (oldKeys.length > 0) {
        const oldAccelerator = keysToAccelerator(oldKeys)
        if (window.electronAPI?.window?.unregisterGlobalShortcut) {
          await window.electronAPI.window.unregisterGlobalShortcut(oldAccelerator)
          get().registeredShortcuts.delete(oldAccelerator)
          console.log(`Global shortcut unregistered: ${oldAccelerator}`)
        }
      }

      // Register new shortcut
      if (newKeys.length > 0) {
        const newAccelerator = keysToAccelerator(newKeys)
        if (window.electronAPI?.window?.registerGlobalShortcut) {
          const success = await window.electronAPI.window.registerGlobalShortcut(newAccelerator, action)
          if (success) {
            get().registeredShortcuts.set(newAccelerator, action)
            console.log(`Global shortcut updated: ${newAccelerator} -> ${action}`)
          } else {
            console.warn(`Failed to register new global shortcut: ${newAccelerator}`)
          }
        }
      }
    } catch (error) {
      console.error('Failed to update global shortcut:', error)
    }
  },

  syncAllGlobalShortcuts: async () => {
    try {
      // Unregister all current global shortcuts
      if (window.electronAPI?.window?.unregisterAllGlobalShortcuts) {
        await window.electronAPI.window.unregisterAllGlobalShortcuts()
        get().registeredShortcuts.clear()
      }

      // Re-initialize all global shortcuts
      set({ isInitialized: false })
      await get().initializeGlobalShortcuts()
    } catch (error) {
      console.error('Failed to sync global shortcuts:', error)
    }
  }
}))
