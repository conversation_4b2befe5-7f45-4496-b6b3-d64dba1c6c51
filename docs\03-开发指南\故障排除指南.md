# PaoLife 故障排除指南

## 📋 概述

本文档提供了PaoLife项目开发和部署过程中常见问题的解决方案，帮助开发者快速定位和解决问题。

## 🛠️ 开发环境问题

### 1. Rust 相关问题

#### 编译错误
```bash
# 问题：Rust 版本过低
error: package `paolife v0.1.0` cannot be compiled because it requires rustc 1.81.0 or newer

# 解决方案：更新 Rust
rustup update stable
rustc --version  # 验证版本
```

```bash
# 问题：依赖冲突
error: failed to select a version for the requirement `serde = "^1.0"`

# 解决方案：清理并重新构建
cargo clean
rm Cargo.lock
cargo build
```

```bash
# 问题：链接错误
error: linking with `cc` failed: exit status: 1

# 解决方案：安装必要的系统依赖
# Ubuntu/Debian
sudo apt-get install build-essential pkg-config libssl-dev

# macOS
xcode-select --install

# Windows
# 安装 Visual Studio Build Tools
```

#### SQLx 相关问题
```bash
# 问题：数据库连接失败
error: error returned from database: (code: 14) unable to open database file

# 解决方案：检查数据库路径和权限
ls -la data/
mkdir -p data
chmod 755 data
```

```bash
# 问题：迁移失败
error: error returned from database: table already exists

# 解决方案：重置数据库
rm data/paolife.db
sqlx database create
sqlx migrate run
```

### 2. 前端相关问题

#### Node.js 和 pnpm 问题
```bash
# 问题：Node.js 版本不兼容
Error: The engine "node" is incompatible with this module

# 解决方案：使用正确的 Node.js 版本
nvm install 20.10.0
nvm use 20.10.0
node --version
```

```bash
# 问题：pnpm 安装失败
ERR_PNPM_PEER_DEP_ISSUES

# 解决方案：清理缓存并重新安装
pnpm store prune
rm -rf node_modules pnpm-lock.yaml
pnpm install
```

#### TypeScript 类型错误
```typescript
// 问题：类型定义缺失
error TS2307: Cannot find module '@tauri-apps/api/tauri'

// 解决方案：安装类型定义
pnpm add -D @tauri-apps/api
```

```typescript
// 问题：严格模式错误
error TS2322: Type 'string | undefined' is not assignable to type 'string'

// 解决方案：添加类型守卫
if (value !== undefined) {
  // 使用 value
}
// 或使用可选链
const result = value?.toString() ?? '';
```

### 3. Tauri 相关问题

#### 构建失败
```bash
# 问题：WebView2 缺失 (Windows)
Error: WebView2 is not installed

# 解决方案：安装 WebView2
# 下载并安装 Microsoft Edge WebView2 Runtime
# 或在代码中自动下载
```

```bash
# 问题：系统依赖缺失 (Linux)
error: failed to run custom build command for `webkit2gtk-sys`

# 解决方案：安装系统依赖
sudo apt-get install libwebkit2gtk-4.0-dev libgtk-3-dev libayatana-appindicator3-dev
```

#### 权限问题
```bash
# 问题：文件系统权限被拒绝
Error: Permission denied (os error 13)

# 解决方案：配置 Tauri 权限
```

```json
// src-tauri/tauri.conf.json
{
  "tauri": {
    "allowlist": {
      "fs": {
        "all": false,
        "readFile": true,
        "writeFile": true,
        "readDir": true,
        "createDir": true,
        "scope": ["$APPDATA/paolife/**", "$DOCUMENT/**"]
      }
    }
  }
}
```

## 🐛 运行时问题

### 1. 数据库问题

#### 连接池耗尽
```rust
// 问题：连接池耗尽
error: timed out waiting for connection

// 解决方案：优化连接池配置
let pool = SqlitePoolOptions::new()
    .max_connections(20)
    .acquire_timeout(Duration::from_secs(30))
    .idle_timeout(Duration::from_secs(600))
    .connect(&database_url)
    .await?;
```

#### 数据库锁定
```bash
# 问题：数据库被锁定
error: database is locked

# 解决方案：检查并关闭其他连接
lsof data/paolife.db  # Linux/macOS
# 或重启应用程序
```

#### 数据损坏
```bash
# 问题：数据库文件损坏
error: database disk image is malformed

# 解决方案：从备份恢复
cp data/backup/paolife.db.backup data/paolife.db
# 或重建数据库
rm data/paolife.db
cargo run --bin setup-db
```

### 2. 内存和性能问题

#### 内存泄漏
```rust
// 问题：内存使用持续增长
// 解决方案：检查循环引用和未释放的资源

// 使用 Weak 引用打破循环
use std::rc::{Rc, Weak};

struct Parent {
    children: Vec<Rc<Child>>,
}

struct Child {
    parent: Weak<Parent>,  // 使用 Weak 而不是 Rc
}
```

#### 性能瓶颈
```rust
// 问题：查询性能差
// 解决方案：添加索引和优化查询

// 添加数据库索引
CREATE INDEX idx_tasks_project_status ON tasks(project_id, status);

// 使用批量操作
let mut tx = pool.begin().await?;
for chunk in data.chunks(100) {
    // 批量插入
}
tx.commit().await?;
```

### 3. 前端性能问题

#### 组件渲染慢
```typescript
// 问题：大列表渲染性能差
// 解决方案：使用虚拟化

import { VirtualList } from '@tanstack/solid-virtual';

const VirtualizedList = () => {
  return (
    <VirtualList
      count={items.length}
      getScrollElement={() => parentRef}
      estimateSize={() => 50}
      renderItem={({ index }) => <Item data={items[index]} />}
    />
  );
};
```

#### 状态更新频繁
```typescript
// 问题：状态更新过于频繁导致性能问题
// 解决方案：使用防抖和批量更新

import { debounce } from 'lodash-es';

const debouncedUpdate = debounce((value: string) => {
  setSearchTerm(value);
}, 300);

// 或使用 batch 批量更新
import { batch } from 'solid-js';

batch(() => {
  setLoading(false);
  setData(newData);
  setError(null);
});
```

## 🔧 调试技巧

### 1. 后端调试

#### 日志配置
```rust
// src-tauri/src/main.rs
use tracing::{info, warn, error};
use tracing_subscriber;

fn main() {
    // 配置日志
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::DEBUG)
        .with_target(false)
        .init();
    
    info!("Application starting");
    
    tauri::Builder::default()
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

#### 数据库查询调试
```rust
// 启用 SQLx 查询日志
use tracing::Level;

let pool = SqlitePoolOptions::new()
    .connect(&database_url)
    .await?;

// 在查询前添加日志
tracing::debug!("Executing query: {}", query);
let result = sqlx::query(&query).fetch_all(&pool).await?;
tracing::debug!("Query returned {} rows", result.len());
```

#### 性能分析
```rust
// 使用 tokio-console 进行异步性能分析
#[tokio::main]
async fn main() {
    console_subscriber::init();
    // 应用代码
}
```

### 2. 前端调试

#### 开发者工具
```typescript
// 在组件中添加调试信息
const DebugInfo = () => {
  createEffect(() => {
    console.log('State changed:', {
      loading: loading(),
      data: data(),
      error: error(),
    });
  });
  
  return null;
};
```

#### 状态调试
```typescript
// 使用 SolidJS DevTools
import { attachDevtoolsOverlay } from '@solid-devtools/overlay';

if (import.meta.env.DEV) {
  attachDevtoolsOverlay();
}
```

#### API 调用调试
```typescript
// 添加 API 调用拦截器
const apiCall = async (command: string, args?: any) => {
  console.log(`API Call: ${command}`, args);
  const start = performance.now();
  
  try {
    const result = await invoke(command, args);
    const duration = performance.now() - start;
    console.log(`API Success: ${command} (${duration.toFixed(2)}ms)`, result);
    return result;
  } catch (error) {
    const duration = performance.now() - start;
    console.error(`API Error: ${command} (${duration.toFixed(2)}ms)`, error);
    throw error;
  }
};
```

## 📊 监控和诊断

### 1. 应用健康检查

#### 系统资源监控
```rust
// src-tauri/src/health.rs
use sysinfo::{System, SystemExt, ProcessExt};

#[tauri::command]
pub fn get_system_info() -> SystemInfo {
    let mut sys = System::new_all();
    sys.refresh_all();
    
    SystemInfo {
        memory_usage: sys.used_memory(),
        total_memory: sys.total_memory(),
        cpu_usage: sys.global_cpu_info().cpu_usage(),
        disk_usage: get_disk_usage(),
    }
}

fn get_disk_usage() -> u64 {
    // 获取应用数据目录的磁盘使用情况
    std::fs::metadata("data/")
        .map(|m| m.len())
        .unwrap_or(0)
}
```

#### 数据库健康检查
```rust
#[tauri::command]
pub async fn check_database_health(pool: tauri::State<'_, SqlitePool>) -> Result<DatabaseHealth, String> {
    let start = std::time::Instant::now();
    
    // 执行简单查询测试连接
    let result = sqlx::query("SELECT 1").fetch_one(pool.inner()).await;
    let response_time = start.elapsed();
    
    match result {
        Ok(_) => Ok(DatabaseHealth {
            status: "healthy".to_string(),
            response_time_ms: response_time.as_millis() as u64,
        }),
        Err(e) => Err(format!("Database unhealthy: {}", e)),
    }
}
```

### 2. 错误收集

#### 全局错误处理
```typescript
// src/utils/errorHandler.ts
export class ErrorHandler {
  static setup() {
    // 捕获未处理的 Promise 拒绝
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      this.reportError(event.reason);
    });
    
    // 捕获全局错误
    window.addEventListener('error', (event) => {
      console.error('Global error:', event.error);
      this.reportError(event.error);
    });
  }
  
  static reportError(error: any) {
    // 收集错误信息
    const errorInfo = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };
    
    // 发送错误报告
    this.sendErrorReport(errorInfo);
  }
  
  private static sendErrorReport(errorInfo: any) {
    // 实现错误报告发送逻辑
    if (import.meta.env.PROD) {
      // 发送到错误收集服务
    }
  }
}
```

## 🔍 常见问题 FAQ

### Q: 应用启动缓慢怎么办？
A: 检查以下几点：
1. 数据库文件大小和索引优化
2. 启动时的数据预加载量
3. 系统资源使用情况
4. 使用性能分析工具定位瓶颈

### Q: 数据同步失败怎么处理？
A: 按以下步骤排查：
1. 检查网络连接
2. 验证同步服务状态
3. 查看本地数据完整性
4. 检查冲突解决机制

### Q: 内存使用过高怎么优化？
A: 优化策略：
1. 检查是否有内存泄漏
2. 优化大数据集的处理方式
3. 使用虚拟化技术处理大列表
4. 定期清理缓存数据

### Q: 跨平台兼容性问题怎么解决？
A: 处理方法：
1. 使用条件编译处理平台差异
2. 测试所有目标平台
3. 使用平台特定的配置
4. 关注平台特定的依赖

---

**文档版本**: 1.0  
**创建日期**: 2024年12月  
**维护者**: 开发团队  
**下次更新**: 根据问题反馈持续更新
