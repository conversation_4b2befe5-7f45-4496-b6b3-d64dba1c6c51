// User Domain Service - 用户领域服务

use crate::domain::entities::User;
use crate::shared::errors::Result;

pub struct UserDomainService;

impl UserDomainService {
    pub fn new() -> Self {
        Self
    }

    pub fn validate_username(&self, username: &str) -> Result<()> {
        crate::shared::utils::validate_required(username, "username")?;
        crate::shared::utils::validate_length(username, "username", 3, 50)?;
        Ok(())
    }

    pub fn validate_email(&self, email: &str) -> Result<()> {
        if !email.is_empty() {
            crate::shared::utils::validate_email(email)?;
        }
        Ok(())
    }

    pub fn can_update_preferences(&self, _user: &User) -> bool {
        // Business logic for preference updates
        true
    }
}

impl Default for UserDomainService {
    fn default() -> Self {
        Self::new()
    }
}
