/**
 * KPI统计卡片组件
 * 显示KPI的整体统计信息
 */

import { Card, CardContent } from '../ui/card'
import { Badge } from '../ui/badge'
import { Target, TrendingUp, AlertTriangle, BarChart3 } from 'lucide-react'
import { cn } from '../../lib/utils'

import type { KPIStatistics } from './types'

interface KPIStatisticsCardProps {
  statistics: KPIStatistics
  class?: string
}

export function KPIStatisticsCard(props: KPIStatisticsCardProps) {
  const stats = props.statistics

  return (
    <div class={cn("grid grid-cols-1 md:grid-cols-4 gap-4", props.class)}>
      {/* 总指标数 */}
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-muted-foreground">Total KPIs</p>
              <p class="text-2xl font-bold">{stats.total}</p>
            </div>
            <Target class="h-8 w-8 text-blue-500" />
          </div>
        </CardContent>
      </Card>

      {/* 进展良好 */}
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-muted-foreground">On Track</p>
              <p class="text-2xl font-bold text-green-600">
                {stats.achieved + stats.onTrack}
              </p>
              <div class="flex gap-1 mt-1">
                <Badge variant="outline" class="text-xs bg-green-50 text-green-700">
                  {stats.achieved} achieved
                </Badge>
              </div>
            </div>
            <TrendingUp class="h-8 w-8 text-green-500" />
          </div>
        </CardContent>
      </Card>

      {/* 需要关注 */}
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-muted-foreground">Needs Attention</p>
              <p class="text-2xl font-bold text-yellow-600">
                {stats.atRisk + stats.behind}
              </p>
              <div class="flex gap-1 mt-1">
                <Badge variant="outline" class="text-xs bg-red-50 text-red-700">
                  {stats.behind} behind
                </Badge>
              </div>
            </div>
            <AlertTriangle class="h-8 w-8 text-yellow-500" />
          </div>
        </CardContent>
      </Card>

      {/* 平均进度 */}
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-muted-foreground">Avg Progress</p>
              <p class="text-2xl font-bold">{stats.averageProgress}%</p>
              <div class="flex gap-1 mt-1">
                <Badge variant="outline" class="text-xs bg-gray-50 text-gray-700">
                  {stats.noTarget} no target
                </Badge>
              </div>
            </div>
            <BarChart3 class="h-8 w-8 text-purple-500" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default KPIStatisticsCard
