/**
 * 双向链接组件
 * 支持WikiLink语法、反向链接展示、链接预览等功能
 */

import { createSignal, createMemo, For, Show, Switch, Match } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Input } from '../ui/input'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { 
  Link as LinkIcon, 
  ArrowRight,
  ArrowLeft,
  Search,
  ExternalLink,
  Eye,
  Hash,
  FileText,
  Network
} from 'lucide-solid'
import { cn } from '../../lib/utils'

import type { BidirectionalLinksProps, BidirectionalLink } from './types'

// 链接强度颜色映射
function getLinkStrengthColor(strength: number): string {
  if (strength >= 0.8) return 'text-green-600 bg-green-100'
  if (strength >= 0.6) return 'text-blue-600 bg-blue-100'
  if (strength >= 0.4) return 'text-yellow-600 bg-yellow-100'
  return 'text-gray-600 bg-gray-100'
}

// 格式化链接强度
function formatLinkStrength(strength: number): string {
  return `${Math.round(strength * 100)}%`
}

// 格式化日期
function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

// 链接卡片组件
function LinkCard(props: {
  link: BidirectionalLink
  direction: 'outgoing' | 'incoming'
  onLinkClick?: (link: BidirectionalLink) => void
  showContext?: boolean
}) {
  const targetTitle = () => props.direction === 'outgoing' 
    ? props.link.targetDocTitle || props.link.targetDocPath
    : props.link.sourceDocTitle || props.link.sourceDocPath

  const targetPath = () => props.direction === 'outgoing'
    ? props.link.targetDocPath
    : props.link.sourceDocPath

  return (
    <Card class="group hover:shadow-md transition-all cursor-pointer">
      <CardContent class="p-4">
        <div class="flex items-start gap-3">
          {/* 方向图标 */}
          <div class="flex-shrink-0 mt-1">
            <Show when={props.direction === 'outgoing'}>
              <ArrowRight class="h-4 w-4 text-blue-500" />
            </Show>
            <Show when={props.direction === 'incoming'}>
              <ArrowLeft class="h-4 w-4 text-green-500" />
            </Show>
          </div>
          
          {/* 链接信息 */}
          <div 
            class="flex-1 min-w-0"
            onClick={() => props.onLinkClick?.(props.link)}
          >
            <div class="flex items-center gap-2 mb-1">
              <h4 class="font-medium text-sm truncate">{targetTitle()}</h4>
              <Badge 
                variant="outline" 
                class={cn('text-xs', getLinkStrengthColor(props.link.linkStrength))}
              >
                {formatLinkStrength(props.link.linkStrength)}
              </Badge>
              <Show when={!props.link.isValid}>
                <Badge variant="destructive" class="text-xs">
                  Broken
                </Badge>
              </Show>
            </div>
            
            <p class="text-xs text-muted-foreground mb-2 truncate">
              {targetPath()}
            </p>
            
            {/* 链接文本和上下文 */}
            <Show when={props.link.displayText || props.link.linkText}>
              <div class="mb-2">
                <Badge variant="secondary" class="text-xs">
                  {props.link.displayText || props.link.linkText}
                </Badge>
              </div>
            </Show>
            
            <Show when={props.showContext && (props.link.contextBefore || props.link.contextAfter)}>
              <div class="text-xs text-muted-foreground bg-gray-50 p-2 rounded border-l-2 border-gray-200">
                <span class="opacity-60">{props.link.contextBefore}</span>
                <span class="font-medium text-blue-600">
                  {props.link.displayText || props.link.linkText}
                </span>
                <span class="opacity-60">{props.link.contextAfter}</span>
              </div>
            </Show>
            
            {/* 元数据 */}
            <div class="flex items-center gap-3 mt-2 text-xs text-muted-foreground">
              <span>Line {props.link.lineNumber}</span>
              <span>•</span>
              <span>{props.link.linkType}</span>
              <span>•</span>
              <span>{formatDate(props.link.updatedAt)}</span>
            </div>
          </div>
          
          {/* 操作按钮 */}
          <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                props.onLinkClick?.(props.link)
              }}
            >
              <Eye class="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function BidirectionalLinks(props: BidirectionalLinksProps) {
  // 状态管理
  const [searchQuery, setSearchQuery] = createSignal('')
  const [activeTab, setActiveTab] = createSignal<'outgoing' | 'incoming' | 'all'>('all')
  const [showContext, setShowContext] = createSignal(false)

  // 分离出站和入站链接
  const outgoingLinks = createMemo(() => 
    props.links.filter(link => link.sourceDocPath === props.documentPath)
  )

  const incomingLinks = createMemo(() => 
    props.links.filter(link => link.targetDocPath === props.documentPath)
  )

  // 过滤链接
  const filteredLinks = createMemo(() => {
    let links: BidirectionalLink[] = []
    
    switch (activeTab()) {
      case 'outgoing':
        links = outgoingLinks()
        break
      case 'incoming':
        links = incomingLinks()
        break
      case 'all':
        links = props.links
        break
    }
    
    const query = searchQuery().toLowerCase()
    if (query) {
      links = links.filter(link =>
        link.sourceDocTitle?.toLowerCase().includes(query) ||
        link.targetDocTitle?.toLowerCase().includes(query) ||
        link.linkText.toLowerCase().includes(query) ||
        link.displayText?.toLowerCase().includes(query)
      )
    }
    
    return links
  })

  // 统计信息
  const statistics = createMemo(() => ({
    total: props.links.length,
    outgoing: outgoingLinks().length,
    incoming: incomingLinks().length,
    valid: props.links.filter(link => link.isValid).length,
    broken: props.links.filter(link => !link.isValid).length,
    avgStrength: props.links.length > 0 
      ? props.links.reduce((sum, link) => sum + link.linkStrength, 0) / props.links.length 
      : 0
  }))

  if (props.links.length === 0) {
    return (
      <Card class={props.class}>
        <CardContent class="flex items-center justify-center py-8">
          <div class="text-center">
            <Network class="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
            <h3 class="text-lg font-medium mb-2">No links found</h3>
            <p class="text-muted-foreground">
              This document has no bidirectional links yet
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div class={cn("space-y-4", props.class)}>
      {/* 头部统计 */}
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <div>
              <CardTitle class="flex items-center gap-2">
                <Network class="h-5 w-5" />
                Bidirectional Links
                <Badge variant="outline">
                  {statistics().total} links
                </Badge>
              </CardTitle>
              <CardDescription>
                Explore connections between documents
              </CardDescription>
            </div>
            <div class="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowContext(!showContext())}
              >
                {showContext() ? 'Hide' : 'Show'} Context
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600">{statistics().outgoing}</div>
              <div class="text-xs text-muted-foreground">Outgoing</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">{statistics().incoming}</div>
              <div class="text-xs text-muted-foreground">Incoming</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-gray-600">{statistics().valid}</div>
              <div class="text-xs text-muted-foreground">Valid</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-red-600">{statistics().broken}</div>
              <div class="text-xs text-muted-foreground">Broken</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-purple-600">
                {Math.round(statistics().avgStrength * 100)}%
              </div>
              <div class="text-xs text-muted-foreground">Avg Strength</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 搜索和过滤 */}
      <Card>
        <CardContent class="p-4">
          <div class="flex items-center gap-4">
            <div class="relative flex-1">
              <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search links..."
                value={searchQuery()}
                onInput={(e) => setSearchQuery(e.currentTarget.value)}
                class="pl-10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 链接标签页 */}
      <Tabs value={activeTab()} onValueChange={setActiveTab}>
        <TabsList class="grid w-full grid-cols-3">
          <TabsTrigger value="all">
            All ({statistics().total})
          </TabsTrigger>
          <TabsTrigger value="outgoing">
            Outgoing ({statistics().outgoing})
          </TabsTrigger>
          <TabsTrigger value="incoming">
            Incoming ({statistics().incoming})
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab()} class="space-y-3">
          <Show when={filteredLinks().length === 0}>
            <div class="text-center py-8 text-muted-foreground">
              <LinkIcon class="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No links found</p>
            </div>
          </Show>
          
          <Show when={filteredLinks().length > 0}>
            <For each={filteredLinks()}>
              {(link) => {
                const direction = link.sourceDocPath === props.documentPath ? 'outgoing' : 'incoming'
                return (
                  <LinkCard
                    link={link}
                    direction={direction}
                    onLinkClick={props.onLinkClick}
                    showContext={showContext()}
                  />
                )
              }}
            </For>
          </Show>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default BidirectionalLinks
