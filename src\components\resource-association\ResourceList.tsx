/**
 * 资源列表组件
 * 支持多种布局和资源类型的统一展示
 */

import { createMemo, For, Show, Switch, Match } from 'solid-js'
import { Card, CardContent } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Checkbox } from '../ui/checkbox'
import { 
  FileText, 
  Link as LinkIcon, 
  Upload, 
  ExternalLink,
  Eye,
  Edit,
  Trash2,
  Download,
  Share,
  MoreVertical,
  Calendar,
  User,
  Hash,
  FileImage,
  FileVideo,
  FileAudio,
  Archive
} from 'lucide-solid'
import { cn } from '../../lib/utils'

import type { 
  ResourceListProps, 
  UnifiedResource, 
  MarkdownResource,
  LinkResource,
  FileResource,
  ReferenceResource
} from './types'

// 获取资源类型图标
function getResourceIcon(resource: UnifiedResource) {
  switch (resource.type) {
    case 'markdown':
      return FileText
    case 'link':
      return LinkIcon
    case 'file':
    case 'attachment':
      const fileResource = resource as FileResource
      if (fileResource.mimeType?.startsWith('image/')) return FileImage
      if (fileResource.mimeType?.startsWith('video/')) return FileVideo
      if (fileResource.mimeType?.startsWith('audio/')) return FileAudio
      if (fileResource.mimeType?.includes('zip') || fileResource.mimeType?.includes('archive')) return Archive
      return Upload
    case 'reference':
      return ExternalLink
    default:
      return FileText
  }
}

// 获取资源类型颜色
function getResourceTypeColor(type: string) {
  switch (type) {
    case 'markdown':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'link':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'file':
    case 'attachment':
      return 'bg-purple-100 text-purple-800 border-purple-200'
    case 'reference':
      return 'bg-orange-100 text-orange-800 border-orange-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 格式化日期
function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

// 资源卡片组件
function ResourceCard(props: {
  resource: UnifiedResource
  selected: boolean
  onSelect: (selected: boolean) => void
  onResourceClick: (resource: UnifiedResource) => void
  onResourceEdit: (resource: UnifiedResource) => void
  onResourceDelete: (resourceId: string) => void
  allowSelection?: boolean
  allowEdit?: boolean
  allowDelete?: boolean
}) {
  const Icon = getResourceIcon(props.resource)
  
  return (
    <Card class={cn(
      "group relative transition-all hover:shadow-md cursor-pointer",
      props.selected && "ring-2 ring-primary"
    )}>
      <CardContent class="p-4">
        <div class="flex items-start gap-3">
          {/* 选择框 */}
          <Show when={props.allowSelection}>
            <Checkbox
              checked={props.selected}
              onCheckedChange={props.onSelect}
              class="mt-1"
              onClick={(e) => e.stopPropagation()}
            />
          </Show>
          
          {/* 资源图标 */}
          <div class="flex-shrink-0">
            <div class="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center">
              <Icon class="h-5 w-5 text-gray-600" />
            </div>
          </div>
          
          {/* 资源信息 */}
          <div 
            class="flex-1 min-w-0 cursor-pointer"
            onClick={() => props.onResourceClick(props.resource)}
          >
            <div class="flex items-center gap-2 mb-1">
              <h3 class="font-medium text-sm truncate">{props.resource.title}</h3>
              <Badge variant="outline" class={cn('text-xs', getResourceTypeColor(props.resource.type))}>
                {props.resource.type}
              </Badge>
            </div>
            
            <Show when={props.resource.description}>
              <p class="text-xs text-muted-foreground mb-2 line-clamp-2">
                {props.resource.description}
              </p>
            </Show>
            
            {/* 资源特定信息 */}
            <Switch>
              <Match when={props.resource.type === 'link'}>
                {() => {
                  const linkResource = props.resource as LinkResource
                  return (
                    <div class="flex items-center gap-2 text-xs text-muted-foreground">
                      <ExternalLink class="h-3 w-3" />
                      <span class="truncate">{linkResource.domain || linkResource.url}</span>
                    </div>
                  )
                }}
              </Match>
              <Match when={props.resource.type === 'file' || props.resource.type === 'attachment'}>
                {() => {
                  const fileResource = props.resource as FileResource
                  return (
                    <div class="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>{formatFileSize(fileResource.fileSize)}</span>
                      <span>•</span>
                      <span>{fileResource.mimeType}</span>
                    </div>
                  )
                }}
              </Match>
              <Match when={props.resource.type === 'markdown'}>
                {() => {
                  const markdownResource = props.resource as MarkdownResource
                  return (
                    <div class="flex items-center gap-2 text-xs text-muted-foreground">
                      <Show when={markdownResource.wordCount}>
                        <span>{markdownResource.wordCount} words</span>
                        <span>•</span>
                      </Show>
                      <Show when={markdownResource.linksCount}>
                        <span>{markdownResource.linksCount} links</span>
                      </Show>
                    </div>
                  )
                }}
              </Match>
            </Switch>
            
            {/* 标签 */}
            <Show when={props.resource.tags.length > 0}>
              <div class="flex flex-wrap gap-1 mt-2">
                <For each={props.resource.tags.slice(0, 3)}>
                  {(tag) => (
                    <Badge variant="secondary" class="text-xs">
                      {tag}
                    </Badge>
                  )}
                </For>
                <Show when={props.resource.tags.length > 3}>
                  <Badge variant="secondary" class="text-xs">
                    +{props.resource.tags.length - 3}
                  </Badge>
                </Show>
              </div>
            </Show>
            
            {/* 元数据 */}
            <div class="flex items-center gap-4 mt-3 text-xs text-muted-foreground">
              <div class="flex items-center gap-1">
                <Calendar class="h-3 w-3" />
                <span>{formatDate(props.resource.updatedAt)}</span>
              </div>
              <Show when={props.resource.accessCount > 0}>
                <div class="flex items-center gap-1">
                  <Eye class="h-3 w-3" />
                  <span>{props.resource.accessCount}</span>
                </div>
              </Show>
            </div>
          </div>
          
          {/* 操作按钮 */}
          <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
            <div class="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  props.onResourceClick(props.resource)
                }}
              >
                <Eye class="h-3 w-3" />
              </Button>
              <Show when={props.allowEdit}>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    props.onResourceEdit(props.resource)
                  }}
                >
                  <Edit class="h-3 w-3" />
                </Button>
              </Show>
              <Show when={props.allowDelete}>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    props.onResourceDelete(props.resource.id)
                  }}
                >
                  <Trash2 class="h-3 w-3" />
                </Button>
              </Show>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function ResourceList(props: ResourceListProps) {
  // 计算选中状态
  const isSelected = (resourceId: string) => {
    return props.selectedResources?.includes(resourceId) || false
  }

  // 处理选择
  const handleSelect = (resourceId: string, selected: boolean) => {
    if (!props.onSelectionChange) return
    
    const currentSelection = props.selectedResources || []
    if (selected) {
      props.onSelectionChange([...currentSelection, resourceId])
    } else {
      props.onSelectionChange(currentSelection.filter(id => id !== resourceId))
    }
  }

  // 按类型分组资源
  const groupedResources = createMemo(() => {
    if (props.config?.groupBy !== 'type') {
      return { all: props.resources }
    }
    
    return props.resources.reduce((groups, resource) => {
      const type = resource.type
      if (!groups[type]) {
        groups[type] = []
      }
      groups[type].push(resource)
      return groups
    }, {} as Record<string, UnifiedResource[]>)
  })

  if (props.resources.length === 0) {
    return (
      <div class="text-center py-8 text-muted-foreground">
        <FileText class="h-12 w-12 mx-auto mb-4 opacity-50" />
        <p>No resources found</p>
      </div>
    )
  }

  return (
    <div class={cn("space-y-4", props.class)}>
      <Show when={props.config?.groupBy === 'type'}>
        <For each={Object.entries(groupedResources())}>
          {([type, resources]) => (
            <div class="space-y-3">
              <h3 class="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                {type} ({resources.length})
              </h3>
              <div class={cn(
                "space-y-3",
                props.config?.layout === 'grid' && "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 space-y-0"
              )}>
                <For each={resources}>
                  {(resource) => (
                    <ResourceCard
                      resource={resource}
                      selected={isSelected(resource.id)}
                      onSelect={(selected) => handleSelect(resource.id, selected)}
                      onResourceClick={props.onResourceClick || (() => {})}
                      onResourceEdit={props.onResourceEdit || (() => {})}
                      onResourceDelete={props.onResourceDelete || (() => {})}
                      allowSelection={props.config?.allowBulkOperations}
                      allowEdit={props.config?.allowEdit}
                      allowDelete={props.config?.allowDelete}
                    />
                  )}
                </For>
              </div>
            </div>
          )}
        </For>
      </Show>
      
      <Show when={props.config?.groupBy !== 'type'}>
        <div class={cn(
          "space-y-3",
          props.config?.layout === 'grid' && "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 space-y-0"
        )}>
          <For each={props.resources}>
            {(resource) => (
              <ResourceCard
                resource={resource}
                selected={isSelected(resource.id)}
                onSelect={(selected) => handleSelect(resource.id, selected)}
                onResourceClick={props.onResourceClick || (() => {})}
                onResourceEdit={props.onResourceEdit || (() => {})}
                onResourceDelete={props.onResourceDelete || (() => {})}
                allowSelection={props.config?.allowBulkOperations}
                allowEdit={props.config?.allowEdit}
                allowDelete={props.config?.allowDelete}
              />
            )}
          </For>
        </div>
      </Show>
    </div>
  )
}

export default ResourceList
