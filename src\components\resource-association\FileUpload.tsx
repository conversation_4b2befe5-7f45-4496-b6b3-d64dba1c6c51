/**
 * 文件上传组件
 * 支持拖拽上传、多文件上传、进度显示等功能
 */

import { createSignal, createEffect, For, Show } from 'solid-js'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Textarea } from '../ui/textarea'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { 
  Upload, 
  X, 
  File as FileIcon, 
  Image, 
  Video, 
  Music,
  Archive,
  FileText,
  AlertCircle
} from 'lucide-solid'
import { cn } from '../../lib/utils'

import type { FileUploadProps, FileUploadData } from './types'

// 获取文件类型图标
function getFileIcon(file: File) {
  const type = file.type
  if (type.startsWith('image/')) return Image
  if (type.startsWith('video/')) return Video
  if (type.startsWith('audio/')) return Music
  if (type.includes('zip') || type.includes('archive')) return Archive
  if (type.includes('text') || type.includes('document')) return FileText
  return FileIcon
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 验证文件类型
function isFileTypeAllowed(file: File, allowedTypes: string[]): boolean {
  if (allowedTypes.includes('*')) return true
  
  return allowedTypes.some(type => {
    if (type.startsWith('.')) {
      return file.name.toLowerCase().endsWith(type.toLowerCase())
    }
    if (type.includes('/')) {
      return file.type === type
    }
    if (type.includes('*')) {
      const baseType = type.split('/')[0]
      return file.type.startsWith(baseType + '/')
    }
    return false
  })
}

export function FileUpload(props: FileUploadProps) {
  // 状态管理
  const [selectedFiles, setSelectedFiles] = createSignal<File[]>([])
  const [uploadProgress, setUploadProgress] = createSignal<Record<string, number>>({})
  const [uploading, setUploading] = createSignal(false)
  const [dragOver, setDragOver] = createSignal(false)
  const [errors, setErrors] = createSignal<string[]>([])
  
  // 表单数据
  const [description, setDescription] = createSignal('')
  const [tags, setTags] = createSignal('')

  // 配置
  const maxSize = () => props.config?.maxFileSize || 50 * 1024 * 1024 // 50MB
  const allowedTypes = () => props.config?.allowedFileTypes || ['*']
  const multiple = () => props.multiple !== false

  // 处理文件选择
  const handleFileSelect = (files: FileList | null) => {
    if (!files) return
    
    const newFiles: File[] = []
    const newErrors: string[] = []
    
    Array.from(files).forEach(file => {
      // 验证文件大小
      if (file.size > maxSize()) {
        newErrors.push(`${file.name}: File size exceeds ${formatFileSize(maxSize())}`)
        return
      }
      
      // 验证文件类型
      if (!isFileTypeAllowed(file, allowedTypes())) {
        newErrors.push(`${file.name}: File type not allowed`)
        return
      }
      
      newFiles.push(file)
    })
    
    setErrors(newErrors)
    
    if (multiple()) {
      setSelectedFiles(prev => [...prev, ...newFiles])
    } else {
      setSelectedFiles(newFiles.slice(0, 1))
    }
  }

  // 移除文件
  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index))
  }

  // 拖拽处理
  const handleDragOver = (e: DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleDrop = (e: DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    handleFileSelect(e.dataTransfer?.files || null)
  }

  // 上传文件
  const handleUpload = async () => {
    const files = selectedFiles()
    if (files.length === 0) return

    setUploading(true)
    
    try {
      for (const file of files) {
        const uploadData: FileUploadData = {
          file,
          description: description(),
          tags: tags().split(',').map(tag => tag.trim()).filter(Boolean)
        }
        
        await props.onUpload(uploadData)
        
        // 更新进度
        setUploadProgress(prev => ({
          ...prev,
          [file.name]: 100
        }))
      }
      
      // 重置表单
      setSelectedFiles([])
      setDescription('')
      setTags('')
      setUploadProgress({})
      
    } catch (error) {
      console.error('Upload failed:', error)
    } finally {
      setUploading(false)
    }
  }

  return (
    <Dialog open={true} onOpenChange={() => props.onCancel?.()}>
      <DialogContent class="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle class="flex items-center gap-2">
            <Upload class="h-5 w-5" />
            Upload Files
          </DialogTitle>
          <DialogDescription>
            Upload files to associate with this {props.config?.entityType || 'item'}
          </DialogDescription>
        </DialogHeader>

        <div class="space-y-4">
          {/* 拖拽上传区域 */}
          <div
            class={cn(
              "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
              dragOver() ? "border-primary bg-primary/5" : "border-muted-foreground/25",
              props.disabled && "opacity-50 cursor-not-allowed"
            )}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <Upload class="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <div class="space-y-2">
              <p class="text-lg font-medium">
                Drop files here or click to browse
              </p>
              <p class="text-sm text-muted-foreground">
                Maximum file size: {formatFileSize(maxSize())}
              </p>
              <Show when={allowedTypes().length > 0 && !allowedTypes().includes('*')}>
                <p class="text-xs text-muted-foreground">
                  Allowed types: {allowedTypes().join(', ')}
                </p>
              </Show>
            </div>
            
            <input
              type="file"
              multiple={multiple()}
              accept={props.accept}
              class="hidden"
              id="file-input"
              onChange={(e) => handleFileSelect(e.target.files)}
              disabled={props.disabled}
            />
            <label for="file-input">
              <Button variant="outline" class="mt-4" disabled={props.disabled}>
                Browse Files
              </Button>
            </label>
          </div>

          {/* 错误信息 */}
          <Show when={errors().length > 0}>
            <div class="space-y-2">
              <For each={errors()}>
                {(error) => (
                  <div class="flex items-center gap-2 text-sm text-red-600">
                    <AlertCircle class="h-4 w-4" />
                    <span>{error}</span>
                  </div>
                )}
              </For>
            </div>
          </Show>

          {/* 选中的文件列表 */}
          <Show when={selectedFiles().length > 0}>
            <div class="space-y-3">
              <h4 class="text-sm font-medium">Selected Files</h4>
              <div class="space-y-2 max-h-40 overflow-y-auto">
                <For each={selectedFiles()}>
                  {(file, index) => {
                    const Icon = getFileIcon(file)
                    const progress = uploadProgress()[file.name] || 0
                    
                    return (
                      <div class="flex items-center gap-3 p-3 border rounded-lg">
                        <Icon class="h-5 w-5 text-muted-foreground" />
                        <div class="flex-1 min-w-0">
                          <p class="text-sm font-medium truncate">{file.name}</p>
                          <p class="text-xs text-muted-foreground">
                            {formatFileSize(file.size)} • {file.type || 'Unknown type'}
                          </p>
                          <Show when={uploading() && progress > 0}>
                            <Progress value={progress} class="mt-2 h-1" />
                          </Show>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(index())}
                          disabled={uploading()}
                        >
                          <X class="h-4 w-4" />
                        </Button>
                      </div>
                    )
                  }}
                </For>
              </div>
            </div>
          </Show>

          {/* 描述和标签 */}
          <div class="space-y-4">
            <div class="space-y-2">
              <Label for="description">Description (optional)</Label>
              <Textarea
                id="description"
                placeholder="Add a description for these files..."
                value={description()}
                onInput={(e) => setDescription(e.currentTarget.value)}
                rows={2}
              />
            </div>
            
            <div class="space-y-2">
              <Label for="tags">Tags (optional)</Label>
              <Input
                id="tags"
                placeholder="Enter tags separated by commas"
                value={tags()}
                onInput={(e) => setTags(e.currentTarget.value)}
              />
              <Show when={tags()}>
                <div class="flex flex-wrap gap-1">
                  <For each={tags().split(',').map(tag => tag.trim()).filter(Boolean)}>
                    {(tag) => (
                      <Badge variant="secondary" class="text-xs">
                        {tag}
                      </Badge>
                    )}
                  </For>
                </div>
              </Show>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={props.onCancel}
            disabled={uploading()}
          >
            Cancel
          </Button>
          <Button
            onClick={handleUpload}
            disabled={selectedFiles().length === 0 || uploading()}
          >
            {uploading() ? 'Uploading...' : `Upload ${selectedFiles().length} file${selectedFiles().length !== 1 ? 's' : ''}`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default FileUpload
