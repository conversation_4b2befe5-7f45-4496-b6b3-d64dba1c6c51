// Task Repository Implementation - 任务仓储实现

use crate::domain::entities::{Task, TaskStatus};
use crate::domain::repositories::{TaskRepository, TaskStatistics};
use crate::infrastructure::database::{DatabasePool, TaskModel};
use crate::shared::errors::{AppError, Result};
use crate::shared::types::{Id, QueryParams, EntityStatus, Metadata, Priority};
use async_trait::async_trait;

pub struct TaskRepositoryImpl {
    pool: DatabasePool,
}

impl TaskRepositoryImpl {
    pub fn new(pool: DatabasePool) -> Self {
        Self { pool }
    }

    /// 将数据库模型转换为领域实体
    fn model_to_entity(&self, model: TaskModel) -> Result<Task> {
        let status = match model.status.as_str() {
            "todo" => TaskStatus::Todo,
            "in_progress" => TaskStatus::InProgress,
            "completed" => TaskStatus::Completed,
            "cancelled" => TaskStatus::Cancelled,
            _ => TaskStatus::Todo,
        };

        let priority = match model.priority.as_str() {
            "low" => Priority::Low,
            "medium" => Priority::Medium,
            "high" => Priority::High,
            "critical" => Priority::Critical,
            _ => Priority::Medium,
        };

        let entity_status = match model.entity_status.as_str() {
            "active" => EntityStatus::Active,
            "inactive" => EntityStatus::Inactive,
            "deleted" => EntityStatus::Deleted,
            "archived" => EntityStatus::Archived,
            _ => EntityStatus::Active,
        };

        let metadata = Metadata {
            created_at: model.created_at,
            updated_at: model.updated_at,
            created_by: None,
            updated_by: None,
            version: model.version as u64,
        };

        Ok(Task {
            id: model.id,
            title: model.title,
            description: model.description,
            project_id: model.project_id,
            parent_task_id: model.parent_task_id,
            due_date: model.due_date,
            status,
            priority,
            progress: model.progress as f32,
            entity_status,
            metadata,
        })
    }

    /// 将领域实体转换为数据库模型
    fn entity_to_model(&self, task: &Task) -> Result<TaskModel> {
        let status = match task.status {
            TaskStatus::Todo => "todo",
            TaskStatus::InProgress => "in_progress",
            TaskStatus::Completed => "completed",
            TaskStatus::Cancelled => "cancelled",
        };

        let priority = match task.priority {
            Priority::Low => "low",
            Priority::Medium => "medium",
            Priority::High => "high",
            Priority::Critical => "critical",
        };

        let entity_status = match task.entity_status {
            EntityStatus::Active => "active",
            EntityStatus::Inactive => "inactive",
            EntityStatus::Deleted => "deleted",
            EntityStatus::Archived => "archived",
        };

        Ok(TaskModel {
            id: task.id.clone(),
            title: task.title.clone(),
            description: task.description.clone(),
            project_id: task.project_id.clone(),
            parent_task_id: task.parent_task_id.clone(),
            due_date: task.due_date,
            status: status.to_string(),
            priority: priority.to_string(),
            progress: task.progress as f64,
            entity_status: entity_status.to_string(),
            created_at: task.metadata.created_at,
            updated_at: task.metadata.updated_at,
            version: task.metadata.version as i64,
        })
    }
}

#[async_trait]
impl TaskRepository for TaskRepositoryImpl {
    async fn find_by_id(&self, id: &Id) -> Result<Option<Task>> {
        let model = sqlx::query_as::<_, TaskModel>(
            "SELECT * FROM tasks WHERE id = ? AND entity_status != 'deleted'"
        )
        .bind(id)
        .fetch_optional(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find task by id: {}", e)))?;

        match model {
            Some(m) => Ok(Some(self.model_to_entity(m)?)),
            None => Ok(None),
        }
    }

    async fn find_by_title(&self, title: &str) -> Result<Option<Task>> {
        let model = sqlx::query_as::<_, TaskModel>(
            "SELECT * FROM tasks WHERE title = ? AND entity_status != 'deleted'"
        )
        .bind(title)
        .fetch_optional(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find task by title: {}", e)))?;

        match model {
            Some(m) => Ok(Some(self.model_to_entity(m)?)),
            None => Ok(None),
        }
    }

    async fn save(&self, task: &Task) -> Result<()> {
        let model = self.entity_to_model(task)?;
        
        sqlx::query(
            r#"
            INSERT INTO tasks (id, title, description, project_id, parent_task_id, due_date, 
                             status, priority, progress, entity_status, created_at, updated_at, version)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&model.id)
        .bind(&model.title)
        .bind(&model.description)
        .bind(&model.project_id)
        .bind(&model.parent_task_id)
        .bind(&model.due_date)
        .bind(&model.status)
        .bind(&model.priority)
        .bind(&model.progress)
        .bind(&model.entity_status)
        .bind(&model.created_at)
        .bind(&model.updated_at)
        .bind(&model.version)
        .execute(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to save task: {}", e)))?;

        Ok(())
    }

    async fn update(&self, task: &Task) -> Result<()> {
        let model = self.entity_to_model(task)?;
        
        let result = sqlx::query(
            r#"
            UPDATE tasks 
            SET title = ?, description = ?, project_id = ?, parent_task_id = ?, due_date = ?, 
                status = ?, priority = ?, progress = ?, entity_status = ?, updated_at = ?, 
                version = version + 1
            WHERE id = ? AND version = ?
            "#
        )
        .bind(&model.title)
        .bind(&model.description)
        .bind(&model.project_id)
        .bind(&model.parent_task_id)
        .bind(&model.due_date)
        .bind(&model.status)
        .bind(&model.priority)
        .bind(&model.progress)
        .bind(&model.entity_status)
        .bind(chrono::Utc::now())
        .bind(&model.id)
        .bind(&model.version)
        .execute(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to update task: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::DatabaseError("Task not found or version conflict".to_string()));
        }

        Ok(())
    }

    async fn delete(&self, id: &Id) -> Result<()> {
        let result = sqlx::query(
            "UPDATE tasks SET entity_status = 'deleted', updated_at = ? WHERE id = ?"
        )
        .bind(chrono::Utc::now())
        .bind(id)
        .execute(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to delete task: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::NotFound("Task not found".to_string()));
        }

        Ok(())
    }

    async fn find_all_active(&self) -> Result<Vec<Task>> {
        let models = sqlx::query_as::<_, TaskModel>(
            "SELECT * FROM tasks WHERE entity_status = 'active' ORDER BY created_at DESC"
        )
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find active tasks: {}", e)))?;

        let mut tasks = Vec::new();
        for model in models {
            tasks.push(self.model_to_entity(model)?);
        }

        Ok(tasks)
    }

    async fn find_by_project_id(&self, project_id: &Id) -> Result<Vec<Task>> {
        let models = sqlx::query_as::<_, TaskModel>(
            "SELECT * FROM tasks WHERE project_id = ? AND entity_status != 'deleted' ORDER BY created_at DESC"
        )
        .bind(project_id)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find tasks by project: {}", e)))?;

        let mut tasks = Vec::new();
        for model in models {
            tasks.push(self.model_to_entity(model)?);
        }

        Ok(tasks)
    }

    async fn find_by_parent_task_id(&self, parent_task_id: &Id) -> Result<Vec<Task>> {
        let models = sqlx::query_as::<_, TaskModel>(
            "SELECT * FROM tasks WHERE parent_task_id = ? AND entity_status != 'deleted' ORDER BY created_at DESC"
        )
        .bind(parent_task_id)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find subtasks: {}", e)))?;

        let mut tasks = Vec::new();
        for model in models {
            tasks.push(self.model_to_entity(model)?);
        }

        Ok(tasks)
    }

    async fn find_by_status(&self, status: &TaskStatus) -> Result<Vec<Task>> {
        let status_str = match status {
            TaskStatus::Todo => "todo",
            TaskStatus::InProgress => "in_progress",
            TaskStatus::Completed => "completed",
            TaskStatus::Cancelled => "cancelled",
        };

        let models = sqlx::query_as::<_, TaskModel>(
            "SELECT * FROM tasks WHERE status = ? AND entity_status != 'deleted' ORDER BY created_at DESC"
        )
        .bind(status_str)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find tasks by status: {}", e)))?;

        let mut tasks = Vec::new();
        for model in models {
            tasks.push(self.model_to_entity(model)?);
        }

        Ok(tasks)
    }

    async fn find_by_priority(&self, priority: &Priority) -> Result<Vec<Task>> {
        let priority_str = match priority {
            Priority::Low => "low",
            Priority::Medium => "medium",
            Priority::High => "high",
            Priority::Critical => "critical",
        };

        let models = sqlx::query_as::<_, TaskModel>(
            "SELECT * FROM tasks WHERE priority = ? AND entity_status != 'deleted' ORDER BY created_at DESC"
        )
        .bind(priority_str)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find tasks by priority: {}", e)))?;

        let mut tasks = Vec::new();
        for model in models {
            tasks.push(self.model_to_entity(model)?);
        }

        Ok(tasks)
    }

    async fn find_due_soon(&self, days: u32) -> Result<Vec<Task>> {
        let cutoff_date = chrono::Utc::now() + chrono::Duration::days(days as i64);

        let models = sqlx::query_as::<_, TaskModel>(
            r#"
            SELECT * FROM tasks 
            WHERE due_date IS NOT NULL 
              AND due_date <= ? 
              AND status NOT IN ('completed', 'cancelled')
              AND entity_status != 'deleted'
            ORDER BY due_date ASC
            "#
        )
        .bind(cutoff_date)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find tasks due soon: {}", e)))?;

        let mut tasks = Vec::new();
        for model in models {
            tasks.push(self.model_to_entity(model)?);
        }

        Ok(tasks)
    }

    async fn find_overdue(&self) -> Result<Vec<Task>> {
        let now = chrono::Utc::now();

        let models = sqlx::query_as::<_, TaskModel>(
            r#"
            SELECT * FROM tasks 
            WHERE due_date IS NOT NULL 
              AND due_date < ? 
              AND status NOT IN ('completed', 'cancelled')
              AND entity_status != 'deleted'
            ORDER BY due_date ASC
            "#
        )
        .bind(now)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find overdue tasks: {}", e)))?;

        let mut tasks = Vec::new();
        for model in models {
            tasks.push(self.model_to_entity(model)?);
        }

        Ok(tasks)
    }

    async fn find_today_tasks(&self) -> Result<Vec<Task>> {
        let today_start = chrono::Utc::now().date_naive().and_hms_opt(0, 0, 0)
            .map(|dt| chrono::Utc.from_utc_datetime(&dt))
            .unwrap_or_else(|| chrono::Utc::now());
        let today_end = chrono::Utc::now().date_naive().and_hms_opt(23, 59, 59)
            .map(|dt| chrono::Utc.from_utc_datetime(&dt))
            .unwrap_or_else(|| chrono::Utc::now());

        let models = sqlx::query_as::<_, TaskModel>(
            r#"
            SELECT * FROM tasks 
            WHERE due_date IS NOT NULL 
              AND due_date >= ? 
              AND due_date <= ?
              AND entity_status != 'deleted'
            ORDER BY due_date ASC
            "#
        )
        .bind(today_start)
        .bind(today_end)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find today tasks: {}", e)))?;

        let mut tasks = Vec::new();
        for model in models {
            tasks.push(self.model_to_entity(model)?);
        }

        Ok(tasks)
    }

    async fn find_with_pagination(&self, params: &QueryParams) -> Result<(Vec<Task>, u64)> {
        let pagination = params.pagination.as_ref().unwrap_or(&crate::shared::types::Pagination::default());
        let offset = (pagination.page - 1) * pagination.size;

        // 获取总数
        let total = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM tasks WHERE entity_status != 'deleted'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count tasks: {}", e)))? as u64;

        // 获取分页数据
        let models = sqlx::query_as::<_, TaskModel>(
            "SELECT * FROM tasks WHERE entity_status != 'deleted' ORDER BY created_at DESC LIMIT ? OFFSET ?"
        )
        .bind(pagination.size as i64)
        .bind(offset as i64)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find tasks with pagination: {}", e)))?;

        let mut tasks = Vec::new();
        for model in models {
            tasks.push(self.model_to_entity(model)?);
        }

        Ok((tasks, total))
    }

    async fn count_active_tasks(&self) -> Result<u64> {
        let count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM tasks WHERE entity_status = 'active'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count active tasks: {}", e)))?;

        Ok(count as u64)
    }

    async fn find_by_progress_range(&self, min_progress: f32, max_progress: f32) -> Result<Vec<Task>> {
        let models = sqlx::query_as::<_, TaskModel>(
            r#"
            SELECT * FROM tasks 
            WHERE progress >= ? AND progress <= ? 
              AND entity_status != 'deleted'
            ORDER BY progress DESC
            "#
        )
        .bind(min_progress as f64)
        .bind(max_progress as f64)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find tasks by progress range: {}", e)))?;

        let mut tasks = Vec::new();
        for model in models {
            tasks.push(self.model_to_entity(model)?);
        }

        Ok(tasks)
    }

    async fn search(&self, query: &str) -> Result<Vec<Task>> {
        let search_pattern = format!("%{}%", query);

        let models = sqlx::query_as::<_, TaskModel>(
            r#"
            SELECT * FROM tasks 
            WHERE (title LIKE ? OR description LIKE ?)
              AND entity_status != 'deleted'
            ORDER BY 
              CASE WHEN title LIKE ? THEN 1 ELSE 2 END,
              created_at DESC
            "#
        )
        .bind(&search_pattern)
        .bind(&search_pattern)
        .bind(&search_pattern)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to search tasks: {}", e)))?;

        let mut tasks = Vec::new();
        for model in models {
            tasks.push(self.model_to_entity(model)?);
        }

        Ok(tasks)
    }

    async fn get_statistics(&self) -> Result<TaskStatistics> {
        // 获取基础统计
        let total_tasks = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM tasks WHERE entity_status != 'deleted'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count total tasks: {}", e)))? as u64;

        let active_tasks = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM tasks WHERE entity_status = 'active'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count active tasks: {}", e)))? as u64;

        let completed_tasks = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM tasks WHERE status = 'completed' AND entity_status != 'deleted'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count completed tasks: {}", e)))? as u64;

        let overdue_tasks = sqlx::query_scalar::<_, i64>(
            r#"
            SELECT COUNT(*) FROM tasks 
            WHERE due_date IS NOT NULL 
              AND due_date < ? 
              AND status NOT IN ('completed', 'cancelled')
              AND entity_status != 'deleted'
            "#
        )
        .bind(chrono::Utc::now())
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count overdue tasks: {}", e)))? as u64;

        let today_start = chrono::Utc::now().date_naive().and_hms_opt(0, 0, 0)
            .map(|dt| chrono::Utc.from_utc_datetime(&dt))
            .unwrap_or_else(|| chrono::Utc::now());
        let today_end = chrono::Utc::now().date_naive().and_hms_opt(23, 59, 59)
            .map(|dt| chrono::Utc.from_utc_datetime(&dt))
            .unwrap_or_else(|| chrono::Utc::now());

        let today_tasks = sqlx::query_scalar::<_, i64>(
            r#"
            SELECT COUNT(*) FROM tasks 
            WHERE due_date IS NOT NULL 
              AND due_date >= ? 
              AND due_date <= ?
              AND entity_status != 'deleted'
            "#
        )
        .bind(today_start)
        .bind(today_end)
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count today tasks: {}", e)))? as u64;

        let average_progress = sqlx::query_scalar::<_, Option<f64>>(
            "SELECT AVG(progress) FROM tasks WHERE entity_status != 'deleted'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to calculate average progress: {}", e)))?
        .unwrap_or(0.0) as f32;

        let completion_rate = if total_tasks > 0 {
            completed_tasks as f32 / total_tasks as f32
        } else {
            0.0
        };

        // 按状态统计
        let status_stats = sqlx::query_as::<_, (String, i64)>(
            "SELECT status, COUNT(*) FROM tasks WHERE entity_status != 'deleted' GROUP BY status"
        )
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to get status statistics: {}", e)))?;

        let tasks_by_status = status_stats.into_iter().map(|(status, count)| {
            let task_status = match status.as_str() {
                "todo" => TaskStatus::Todo,
                "in_progress" => TaskStatus::InProgress,
                "completed" => TaskStatus::Completed,
                "cancelled" => TaskStatus::Cancelled,
                _ => TaskStatus::Todo,
            };
            (task_status, count as u64)
        }).collect();

        // 按优先级统计
        let priority_stats = sqlx::query_as::<_, (String, i64)>(
            "SELECT priority, COUNT(*) FROM tasks WHERE entity_status != 'deleted' GROUP BY priority"
        )
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to get priority statistics: {}", e)))?;

        let tasks_by_priority = priority_stats.into_iter().map(|(priority, count)| {
            let task_priority = match priority.as_str() {
                "low" => Priority::Low,
                "medium" => Priority::Medium,
                "high" => Priority::High,
                "critical" => Priority::Critical,
                _ => Priority::Medium,
            };
            (task_priority, count as u64)
        }).collect();

        Ok(TaskStatistics {
            total_tasks,
            active_tasks,
            completed_tasks,
            overdue_tasks,
            today_tasks,
            average_progress,
            tasks_by_status,
            tasks_by_priority,
            completion_rate,
        })
    }

    async fn get_project_completion_rate(&self, project_id: &Id) -> Result<f32> {
        let total_tasks = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM tasks WHERE project_id = ? AND entity_status != 'deleted'"
        )
        .bind(project_id)
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count project tasks: {}", e)))?;

        if total_tasks == 0 {
            return Ok(0.0);
        }

        let completed_tasks = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM tasks WHERE project_id = ? AND status = 'completed' AND entity_status != 'deleted'"
        )
        .bind(project_id)
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count completed project tasks: {}", e)))?;

        Ok(completed_tasks as f32 / total_tasks as f32)
    }

    async fn find_root_tasks(&self) -> Result<Vec<Task>> {
        let models = sqlx::query_as::<_, TaskModel>(
            "SELECT * FROM tasks WHERE parent_task_id IS NULL AND entity_status != 'deleted' ORDER BY created_at DESC"
        )
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find root tasks: {}", e)))?;

        let mut tasks = Vec::new();
        for model in models {
            tasks.push(self.model_to_entity(model)?);
        }

        Ok(tasks)
    }

    async fn find_task_tree(&self, root_task_id: &Id) -> Result<Vec<Task>> {
        // 使用递归CTE查询任务树
        let models = sqlx::query_as::<_, TaskModel>(
            r#"
            WITH RECURSIVE task_tree AS (
                SELECT * FROM tasks WHERE id = ? AND entity_status != 'deleted'
                UNION ALL
                SELECT t.* FROM tasks t
                INNER JOIN task_tree tt ON t.parent_task_id = tt.id
                WHERE t.entity_status != 'deleted'
            )
            SELECT * FROM task_tree ORDER BY created_at
            "#
        )
        .bind(root_task_id)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find task tree: {}", e)))?;

        let mut tasks = Vec::new();
        for model in models {
            tasks.push(self.model_to_entity(model)?);
        }

        Ok(tasks)
    }
}
