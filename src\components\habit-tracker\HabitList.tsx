/**
 * 习惯列表组件
 * 展示习惯列表和快速操作功能
 */

import { createSignal, For, Show } from 'solid-js'
import { Card, CardContent } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Target,
  Flame,
  Calendar,
  CheckCircle,
  Circle
} from 'lucide-solid'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { cn } from '../../lib/utils'

import type { HabitListProps, BaseHabit, HabitRecord } from './types'
import HabitCard from './HabitCard'

export function HabitList(props: HabitListProps) {
  const [expandedHabits, setExpandedHabits] = createSignal<Set<string>>(new Set())

  // 获取习惯的今日记录
  const getTodayRecord = (habitId: string) => {
    const today = props.selectedDate?.toISOString().split('T')[0] || new Date().toISOString().split('T')[0]
    return props.records.find(r => r.habitId === habitId && r.date === today)
  }

  // 获取习惯的最近7天记录
  const getRecentRecords = (habitId: string) => {
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - i)
      return date.toISOString().split('T')[0]
    }).reverse()

    return last7Days.map(date => {
      const record = props.records.find(r => r.habitId === habitId && r.date === date)
      return {
        date,
        completed: record?.completed || false,
        value: record?.value || 0
      }
    })
  }

  // 计算习惯完成率
  const getCompletionRate = (habitId: string) => {
    const recentRecords = getRecentRecords(habitId)
    const completedCount = recentRecords.filter(r => r.completed).length
    return Math.round((completedCount / recentRecords.length) * 100)
  }

  // 切换习惯展开状态
  const toggleExpanded = (habitId: string) => {
    setExpandedHabits(prev => {
      const newSet = new Set(prev)
      if (newSet.has(habitId)) {
        newSet.delete(habitId)
      } else {
        newSet.add(habitId)
      }
      return newSet
    })
  }

  // 获取习惯类型颜色
  const getHabitTypeColor = (type: string) => {
    switch (type) {
      case 'boolean':
        return 'bg-blue-100 text-blue-800'
      case 'numeric':
        return 'bg-green-100 text-green-800'
      case 'duration':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // 格式化频率显示
  const formatFrequency = (frequency: string) => {
    switch (frequency) {
      case 'daily':
        return 'Daily'
      case 'weekly':
        return 'Weekly'
      case 'monthly':
        return 'Monthly'
      default:
        return frequency
    }
  }

  if (props.habits.length === 0) {
    return (
      <Card>
        <CardContent class="flex items-center justify-center py-12">
          <div class="text-center">
            <Target class="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
            <h3 class="text-lg font-medium mb-2">No habits yet</h3>
            <p class="text-muted-foreground">
              Create your first habit to start tracking your progress
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div class={cn(
      "space-y-4",
      props.config?.layout === 'grid' && "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 space-y-0",
      props.config?.layout === 'compact' && "space-y-2"
    )}>
      <For each={props.habits}>
        {(habit) => {
          const todayRecord = getTodayRecord(habit.id)
          const recentRecords = getRecentRecords(habit.id)
          const completionRate = getCompletionRate(habit.id)
          const isExpanded = expandedHabits().has(habit.id)

          return (
            <Show when={props.config?.layout === 'compact'} fallback={
              <HabitCard
                habit={habit}
                records={props.records.filter(r => r.habitId === habit.id)}
                selectedDate={props.selectedDate}
                onEdit={props.onHabitEdit}
                onDelete={props.onHabitDelete}
                onRecordToggle={props.onRecordToggle}
                onValueSet={props.onValueSet}
                showProgress={props.config?.showProgress}
                showStreak={props.config?.showStreak}
                compact={props.config?.compactMode}
              />
            }>
              {/* 紧凑模式布局 */}
              <Card class="hover:shadow-md transition-shadow">
                <CardContent class="p-4">
                  <div class="flex items-center gap-3">
                    {/* 完成状态按钮 */}
                    <Button
                      variant="ghost"
                      size="sm"
                      class={cn(
                        "w-8 h-8 p-0 rounded-full",
                        todayRecord?.completed 
                          ? "bg-green-100 text-green-600 hover:bg-green-200" 
                          : "bg-gray-100 text-gray-400 hover:bg-gray-200"
                      )}
                      onClick={() => props.onRecordToggle?.(
                        habit.id, 
                        props.selectedDate?.toISOString().split('T')[0] || new Date().toISOString().split('T')[0],
                        !todayRecord?.completed
                      )}
                    >
                      <Show when={todayRecord?.completed} fallback={<Circle class="h-4 w-4" />}>
                        <CheckCircle class="h-4 w-4" />
                      </Show>
                    </Button>

                    {/* 习惯信息 */}
                    <div class="flex-1 min-w-0">
                      <div class="flex items-center gap-2 mb-1">
                        <h4 class="font-medium truncate">{habit.name}</h4>
                        <Badge variant="outline" class={cn("text-xs", getHabitTypeColor(habit.type))}>
                          {habit.type}
                        </Badge>
                      </div>
                      
                      <div class="flex items-center gap-4 text-sm text-muted-foreground">
                        <span class="flex items-center gap-1">
                          <Calendar class="h-3 w-3" />
                          {formatFrequency(habit.frequency)}
                        </span>
                        
                        <Show when={habit.streak > 0}>
                          <span class="flex items-center gap-1">
                            <Flame class="h-3 w-3 text-orange-500" />
                            {habit.streak} day streak
                          </span>
                        </Show>
                        
                        <span>{completionRate}% this week</span>
                      </div>
                    </div>

                    {/* 最近7天热力图 */}
                    <div class="flex gap-1">
                      <For each={recentRecords}>
                        {(record) => (
                          <div
                            class={cn(
                              "w-3 h-3 rounded-sm",
                              record.completed 
                                ? "bg-green-500" 
                                : "bg-gray-200"
                            )}
                            title={`${record.date}: ${record.completed ? 'Completed' : 'Not completed'}`}
                          />
                        )}
                      </For>
                    </div>

                    {/* 操作菜单 */}
                    <DropdownMenu>
                      <DropdownMenuTrigger as={Button} variant="ghost" size="sm" class="w-8 h-8 p-0">
                        <MoreHorizontal class="h-4 w-4" />
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => props.onHabitEdit?.(habit)}>
                          <Edit class="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => props.onHabitDelete?.(habit.id)}
                          class="text-red-600"
                        >
                          <Trash2 class="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  {/* 展开的详细信息 */}
                  <Show when={isExpanded}>
                    <div class="mt-4 pt-4 border-t space-y-3">
                      <Show when={habit.description}>
                        <p class="text-sm text-muted-foreground">{habit.description}</p>
                      </Show>
                      
                      <div class="space-y-2">
                        <div class="flex items-center justify-between text-sm">
                          <span>Weekly Progress</span>
                          <span>{completionRate}%</span>
                        </div>
                        <Progress value={completionRate} class="h-2" />
                      </div>
                      
                      <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span class="text-muted-foreground">Current Streak:</span>
                          <span class="ml-2 font-medium">{habit.streak} days</span>
                        </div>
                        <div>
                          <span class="text-muted-foreground">Best Streak:</span>
                          <span class="ml-2 font-medium">{habit.longestStreak} days</span>
                        </div>
                      </div>
                    </div>
                  </Show>

                  {/* 展开/收起按钮 */}
                  <Button
                    variant="ghost"
                    size="sm"
                    class="w-full mt-2 h-6 text-xs"
                    onClick={() => toggleExpanded(habit.id)}
                  >
                    {isExpanded ? 'Show Less' : 'Show More'}
                  </Button>
                </CardContent>
              </Card>
            </Show>
          )
        }}
      </For>
    </div>
  )
}

export default HabitList
