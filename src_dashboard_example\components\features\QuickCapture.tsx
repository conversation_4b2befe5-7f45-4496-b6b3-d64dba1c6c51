import { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { cn } from '../../lib/utils'

interface QuickCaptureProps {
  className?: string
  onCapture?: (content: string, type: 'task' | 'note' | 'idea') => void
}

export function QuickCapture({ className, onCapture }: QuickCaptureProps) {
  const [content, setContent] = useState('')
  const [captureType, setCaptureType] = useState<'task' | 'note' | 'idea'>('note')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!content.trim()) return

    setIsSubmitting(true)
    try {
      await onCapture?.(content.trim(), captureType)
      setContent('')
      // Show success feedback
    } catch (error) {
      console.error('Failed to capture:', error)
      // Show error feedback
    } finally {
      setIsSubmitting(false)
    }
  }

  const captureTypes = [
    { value: 'note', label: '笔记', icon: '📝', color: 'bg-blue-100 text-blue-800' },
    { value: 'task', label: '任务', icon: '✅', color: 'bg-green-100 text-green-800' },
    { value: 'idea', label: '想法', icon: '💡', color: 'bg-yellow-100 text-yellow-800' }
  ] as const

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">快速记录</CardTitle>
            <CardDescription>即时记录想法、任务和创意</CardDescription>
          </div>
          <div className="flex gap-1">
            {captureTypes.map((type) => (
              <Button
                key={type.value}
                variant={captureType === type.value ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setCaptureType(type.value)}
                className={cn('h-8 px-2 text-xs', captureType === type.value && 'shadow-sm')}
              >
                <span className="mr-1">{type.icon}</span>
                {type.label}
              </Button>
            ))}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-3">
          <div className="relative">
            <Input
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder={`你在想什么？(${captureTypes.find((t) => t.value === captureType)?.label})`}
              className="pr-20"
              disabled={isSubmitting}
            />
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
              <Badge
                variant="secondary"
                className={cn('text-xs', captureTypes.find((t) => t.value === captureType)?.color)}
              >
                {captureTypes.find((t) => t.value === captureType)?.icon}
                {captureTypes.find((t) => t.value === captureType)?.label}
              </Badge>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="text-xs text-muted-foreground">
              按回车键记录，或使用 ⌘+Enter 快速保存
            </div>
            <Button
              type="submit"
              size="sm"
              disabled={!content.trim() || isSubmitting}
              className="min-w-[80px]"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  <span>保存中...</span>
                </div>
              ) : (
                '记录'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

export default QuickCapture
