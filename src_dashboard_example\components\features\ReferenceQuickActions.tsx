import React, { useState } from 'react'
import { UnifiedReference } from '../../services/unifiedReferenceService'

interface ReferenceQuickActionsProps {
  documentPath: string
  references: UnifiedReference[]
  onRefresh: () => void
  onExport: () => void
  onCreateReference: (type: 'wikilink' | 'project' | 'area') => void
  className?: string
}

/**
 * 引用快捷操作面板
 * 提供常用的引用管理功能
 */
export const ReferenceQuickActions: React.FC<ReferenceQuickActionsProps> = ({
  documentPath,
  references,
  onRefresh,
  onExport,
  onCreateReference,
  className = ''
}) => {
  const [showCreateMenu, setShowCreateMenu] = useState(false)

  // 计算统计信息
  const stats = {
    total: references.length,
    wikilinks: references.filter(ref => ref.referenceType === 'wikilink').length,
    projects: references.filter(ref => ref.sourceType === 'project').length,
    areas: references.filter(ref => ref.sourceType === 'area').length,
    avgStrength: references.length > 0 
      ? references.reduce((sum, ref) => sum + ref.strength, 0) / references.length 
      : 0
  }

  return (
    <div className={`reference-quick-actions ${className}`}>
      {/* 快速统计 */}
      <div className="quick-stats grid grid-cols-4 gap-2 mb-4">
        <div className="stat-card bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
          <div className="text-lg font-bold text-blue-600">{stats.total}</div>
          <div className="text-xs text-blue-500">总引用</div>
        </div>
        <div className="stat-card bg-green-50 border border-green-200 rounded-lg p-3 text-center">
          <div className="text-lg font-bold text-green-600">{stats.wikilinks}</div>
          <div className="text-xs text-green-500">WikiLink</div>
        </div>
        <div className="stat-card bg-purple-50 border border-purple-200 rounded-lg p-3 text-center">
          <div className="text-lg font-bold text-purple-600">{stats.projects + stats.areas}</div>
          <div className="text-xs text-purple-500">项目/领域</div>
        </div>
        <div className="stat-card bg-orange-50 border border-orange-200 rounded-lg p-3 text-center">
          <div className="text-lg font-bold text-orange-600">{(stats.avgStrength * 100).toFixed(0)}%</div>
          <div className="text-xs text-orange-500">平均强度</div>
        </div>
      </div>

      {/* 快捷操作按钮 */}
      <div className="quick-actions space-y-2">
        {/* 刷新按钮 */}
        <button
          type="button"
          onClick={onRefresh}
          className="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          <span>刷新引用</span>
        </button>

        {/* 创建引用按钮 */}
        <div className="relative">
          <button
            type="button"
            onClick={() => setShowCreateMenu(!showCreateMenu)}
            className="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span>创建引用</span>
            <svg className={`w-4 h-4 transform transition-transform ${showCreateMenu ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {/* 创建菜单 */}
          {showCreateMenu && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
              <button
                type="button"
                onClick={() => {
                  onCreateReference('wikilink')
                  setShowCreateMenu(false)
                }}
                className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 transition-colors first:rounded-t-lg"
              >
                <span>📄</span>
                <span>WikiLink 引用</span>
              </button>
              <button
                type="button"
                onClick={() => {
                  onCreateReference('project')
                  setShowCreateMenu(false)
                }}
                className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-green-50 transition-colors"
              >
                <span>📋</span>
                <span>项目引用</span>
              </button>
              <button
                type="button"
                onClick={() => {
                  onCreateReference('area')
                  setShowCreateMenu(false)
                }}
                className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-purple-50 transition-colors last:rounded-b-lg"
              >
                <span>🏷️</span>
                <span>领域引用</span>
              </button>
            </div>
          )}
        </div>

        {/* 导出按钮 */}
        <button
          type="button"
          onClick={onExport}
          className="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <span>导出引用</span>
        </button>

        {/* 分析按钮 */}
        <button
          type="button"
          className="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          title="引用分析功能开发中"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <span>引用分析</span>
        </button>
      </div>

      {/* 快捷提示 */}
      <div className="quick-tips mt-4 p-3 bg-gray-50 rounded-lg">
        <h4 className="text-xs font-medium text-gray-700 mb-2">💡 快捷提示</h4>
        <div className="space-y-1 text-xs text-gray-600">
          <div>• 使用 [[文档名]] 创建 WikiLink</div>
          <div>• 使用 @project:项目名 创建项目引用</div>
          <div>• 使用 #标签名 创建领域标签</div>
          <div>• 点击引用项可以快速跳转</div>
        </div>
      </div>

      {/* 键盘快捷键 */}
      <div className="keyboard-shortcuts mt-3 p-3 bg-blue-50 rounded-lg">
        <h4 className="text-xs font-medium text-blue-700 mb-2">⌨️ 快捷键</h4>
        <div className="space-y-1 text-xs text-blue-600">
          <div className="flex justify-between">
            <span>刷新引用</span>
            <kbd className="px-1 py-0.5 bg-white border border-blue-200 rounded text-xs">Ctrl+R</kbd>
          </div>
          <div className="flex justify-between">
            <span>创建 WikiLink</span>
            <kbd className="px-1 py-0.5 bg-white border border-blue-200 rounded text-xs">[[</kbd>
          </div>
          <div className="flex justify-between">
            <span>项目引用</span>
            <kbd className="px-1 py-0.5 bg-white border border-blue-200 rounded text-xs">@project:</kbd>
          </div>
        </div>
      </div>

      {/* 文档信息 */}
      <div className="document-info mt-3 p-3 bg-gray-50 rounded-lg">
        <h4 className="text-xs font-medium text-gray-700 mb-2">📄 当前文档</h4>
        <div className="text-xs text-gray-600">
          <div className="truncate" title={documentPath}>
            {documentPath.split('/').pop() || documentPath}
          </div>
          <div className="mt-1 text-gray-500">
            {references.length} 个引用关系
          </div>
        </div>
      </div>
    </div>
  )
}

export default ReferenceQuickActions
