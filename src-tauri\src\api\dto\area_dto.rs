// Area DTOs - 领域数据传输对象

use crate::domain::entities::{Area, AreaStatus};
use crate::shared::types::{Id, EntityStatus};
use serde::{Deserialize, Serialize};

/// 创建领域请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateAreaRequest {
    pub name: String,
    pub description: Option<String>,
    pub standards: Option<Vec<String>>,
    pub color: Option<String>,
    pub icon: Option<String>,
}

/// 更新领域请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateAreaRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub standards: Option<Vec<String>>,
    pub color: Option<String>,
    pub icon: Option<String>,
}

/// 更新领域状态请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateAreaStatusRequest {
    pub status: String,
}

/// 批量更新领域状态请求
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct BatchUpdateAreaStatusRequest {
    pub area_ids: Vec<Id>,
    pub status: String,
}

/// 领域响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AreaResponse {
    pub id: Id,
    pub name: String,
    pub description: Option<String>,
    pub standards: Vec<String>,
    pub color: Option<String>,
    pub icon: Option<String>,
    pub status: String,
    pub entity_status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub version: u64,
}

/// 领域详细信息响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AreaDetailsResponse {
    pub area: AreaResponse,
    pub project_count: u64,
    pub habit_count: u64,
    pub health_score: f32,
    pub recent_projects: Vec<crate::api::dto::project_dto::ProjectResponse>,
}

/// 领域列表响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AreaListResponse {
    pub areas: Vec<AreaResponse>,
    pub total: u64,
    pub page: u64,
    pub size: u64,
}

/// 领域统计响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AreaStatisticsResponse {
    pub total_areas: u64,
    pub active_areas: u64,
    pub areas_by_status: Vec<AreaStatusCount>,
    pub areas_with_projects: u64,
    pub areas_with_habits: u64,
    pub average_projects_per_area: f32,
    pub average_habits_per_area: f32,
}

/// 领域状态统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AreaStatusCount {
    pub status: String,
    pub count: u64,
}

/// 领域名称可用性响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AreaNameAvailabilityResponse {
    pub available: bool,
    pub name: String,
}

impl From<Area> for AreaResponse {
    fn from(area: Area) -> Self {
        Self {
            id: area.id,
            name: area.name,
            description: area.description,
            standards: area.standards,
            color: area.color,
            icon: area.icon,
            status: match area.status {
                AreaStatus::Active => "active".to_string(),
                AreaStatus::Maintenance => "maintenance".to_string(),
                AreaStatus::Dormant => "dormant".to_string(),
            },
            entity_status: match area.entity_status {
                EntityStatus::Active => "active".to_string(),
                EntityStatus::Inactive => "inactive".to_string(),
                EntityStatus::Deleted => "deleted".to_string(),
                EntityStatus::Archived => "archived".to_string(),
            },
            created_at: area.metadata.created_at,
            updated_at: area.metadata.updated_at,
            version: area.metadata.version,
        }
    }
}

impl From<crate::application::services::AreaDetails> for AreaDetailsResponse {
    fn from(details: crate::application::services::AreaDetails) -> Self {
        Self {
            area: AreaResponse::from(details.area),
            project_count: details.project_count,
            habit_count: details.habit_count,
            health_score: details.health_score,
            recent_projects: details.recent_projects.into_iter()
                .map(|p| crate::api::dto::project_dto::ProjectResponse::from(p))
                .collect(),
        }
    }
}

impl From<crate::domain::repositories::AreaStatistics> for AreaStatisticsResponse {
    fn from(stats: crate::domain::repositories::AreaStatistics) -> Self {
        Self {
            total_areas: stats.total_areas,
            active_areas: stats.active_areas,
            areas_by_status: stats.areas_by_status.into_iter().map(|(status, count)| {
                AreaStatusCount {
                    status: match status {
                        AreaStatus::Active => "active".to_string(),
                        AreaStatus::Maintenance => "maintenance".to_string(),
                        AreaStatus::Dormant => "dormant".to_string(),
                    },
                    count,
                }
            }).collect(),
            areas_with_projects: stats.areas_with_projects,
            areas_with_habits: stats.areas_with_habits,
            average_projects_per_area: stats.average_projects_per_area,
            average_habits_per_area: stats.average_habits_per_area,
        }
    }
}

/// 验证创建领域请求
impl CreateAreaRequest {
    pub fn validate(&self) -> Result<(), String> {
        if self.name.trim().is_empty() {
            return Err("Area name cannot be empty".to_string());
        }
        
        if self.name.len() > 100 {
            return Err("Area name cannot be longer than 100 characters".to_string());
        }
        
        if let Some(ref description) = self.description {
            if description.len() > 1000 {
                return Err("Area description cannot be longer than 1000 characters".to_string());
            }
        }
        
        if let Some(ref standards) = self.standards {
            if standards.len() > 20 {
                return Err("Cannot have more than 20 standards".to_string());
            }
            
            for standard in standards {
                if standard.len() > 200 {
                    return Err("Standard cannot be longer than 200 characters".to_string());
                }
            }
        }
        
        if let Some(ref color) = self.color {
            if !color.starts_with('#') || color.len() != 7 {
                return Err("Color must be in hex format (#RRGGBB)".to_string());
            }
        }
        
        Ok(())
    }
}

/// 验证更新领域请求
impl UpdateAreaRequest {
    pub fn validate(&self) -> Result<(), String> {
        if let Some(ref name) = self.name {
            if name.trim().is_empty() {
                return Err("Area name cannot be empty".to_string());
            }
            if name.len() > 100 {
                return Err("Area name cannot be longer than 100 characters".to_string());
            }
        }
        
        if let Some(ref description) = self.description {
            if description.len() > 1000 {
                return Err("Area description cannot be longer than 1000 characters".to_string());
            }
        }
        
        if let Some(ref standards) = self.standards {
            if standards.len() > 20 {
                return Err("Cannot have more than 20 standards".to_string());
            }
            
            for standard in standards {
                if standard.len() > 200 {
                    return Err("Standard cannot be longer than 200 characters".to_string());
                }
            }
        }
        
        if let Some(ref color) = self.color {
            if !color.starts_with('#') || color.len() != 7 {
                return Err("Color must be in hex format (#RRGGBB)".to_string());
            }
        }
        
        Ok(())
    }
}

/// 验证更新领域状态请求
impl UpdateAreaStatusRequest {
    pub fn validate(&self) -> Result<(), String> {
        if !["active", "maintenance", "dormant"].contains(&self.status.as_str()) {
            return Err("Invalid area status".to_string());
        }
        Ok(())
    }
    
    pub fn get_status(&self) -> AreaStatus {
        match self.status.as_str() {
            "active" => AreaStatus::Active,
            "maintenance" => AreaStatus::Maintenance,
            "dormant" => AreaStatus::Dormant,
            _ => AreaStatus::Active,
        }
    }
}

/// 验证批量更新领域状态请求
impl BatchUpdateAreaStatusRequest {
    pub fn validate(&self) -> Result<(), String> {
        if self.area_ids.is_empty() {
            return Err("Area IDs cannot be empty".to_string());
        }
        
        if self.area_ids.len() > 50 {
            return Err("Cannot update more than 50 areas at once".to_string());
        }
        
        if !["active", "maintenance", "dormant"].contains(&self.status.as_str()) {
            return Err("Invalid area status".to_string());
        }
        
        Ok(())
    }
    
    pub fn get_status(&self) -> AreaStatus {
        match self.status.as_str() {
            "active" => AreaStatus::Active,
            "maintenance" => AreaStatus::Maintenance,
            "dormant" => AreaStatus::Dormant,
            _ => AreaStatus::Active,
        }
    }
}
