// Shared Types - 共享类型定义
// 跨层使用的通用类型

use serde::{Deserialize, Serialize};
use std::fmt;

// 通用 ID 类型
pub type Id = String;

// 时间戳类型
pub type Timestamp = chrono::DateTime<chrono::Utc>;

// 分页参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Pagination {
    pub page: u32,
    pub size: u32,
    pub total: Option<u64>,
}

impl Default for Pagination {
    fn default() -> Self {
        Self {
            page: 1,
            size: 20,
            total: None,
        }
    }
}

// 排序参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Sort {
    pub field: String,
    pub direction: SortDirection,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SortDirection {
    Asc,
    Desc,
}

impl fmt::Display for SortDirection {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            SortDirection::Asc => write!(f, "ASC"),
            SortDirection::Desc => write!(f, "DESC"),
        }
    }
}

// 过滤参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Filter {
    pub field: String,
    pub operator: FilterOperator,
    pub value: FilterValue,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FilterOperator {
    Equals,
    NotEquals,
    Contains,
    StartsWith,
    EndsWith,
    GreaterThan,
    LessThan,
    GreaterThanOrEqual,
    LessThanOrEqual,
    In,
    NotIn,
    IsNull,
    IsNotNull,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FilterValue {
    String(String),
    Number(f64),
    Boolean(bool),
    Array(Vec<String>),
    Null,
}

// 查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryParams {
    pub pagination: Option<Pagination>,
    pub sort: Option<Vec<Sort>>,
    pub filters: Option<Vec<Filter>>,
    pub search: Option<String>,
}

impl Default for QueryParams {
    fn default() -> Self {
        Self {
            pagination: Some(Pagination::default()),
            sort: None,
            filters: None,
            search: None,
        }
    }
}

// 实体状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum EntityStatus {
    Active,
    Inactive,
    Deleted,
    Archived,
}

impl fmt::Display for EntityStatus {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            EntityStatus::Active => write!(f, "active"),
            EntityStatus::Inactive => write!(f, "inactive"),
            EntityStatus::Deleted => write!(f, "deleted"),
            EntityStatus::Archived => write!(f, "archived"),
        }
    }
}

// 优先级
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum Priority {
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4,
}

impl fmt::Display for Priority {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Priority::Low => write!(f, "low"),
            Priority::Medium => write!(f, "medium"),
            Priority::High => write!(f, "high"),
            Priority::Critical => write!(f, "critical"),
        }
    }
}

impl Default for Priority {
    fn default() -> Self {
        Priority::Medium
    }
}

// 标签
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct Tag {
    pub id: Id,
    pub name: String,
    pub color: Option<String>,
    pub description: Option<String>,
}

// 元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Metadata {
    pub created_at: Timestamp,
    pub updated_at: Timestamp,
    pub created_by: Option<Id>,
    pub updated_by: Option<Id>,
    pub version: u64,
}

impl Default for Metadata {
    fn default() -> Self {
        let now = chrono::Utc::now();
        Self {
            created_at: now,
            updated_at: now,
            created_by: None,
            updated_by: None,
            version: 1,
        }
    }
}
