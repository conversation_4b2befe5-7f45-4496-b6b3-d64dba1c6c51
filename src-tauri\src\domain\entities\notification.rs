// Notification Entity - 通知实体

use crate::shared::types::{Id, Metadata, EntityStatus, Priority};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Notification {
    pub id: Id,
    pub title: String,
    pub message: String,
    pub notification_type: NotificationType,
    pub priority: Priority,
    pub read: bool,
    pub read_at: Option<chrono::DateTime<chrono::Utc>>,
    pub scheduled_at: Option<chrono::DateTime<chrono::Utc>>,
    pub entity_status: EntityStatus,
    pub metadata: Metadata,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum NotificationType {
    TaskDue,
    ProjectDeadline,
    HabitReminder,
    ReviewReminder,
    System,
}

impl Notification {
    pub fn new(title: String, message: String, notification_type: NotificationType) -> Self {
        Self {
            id: crate::shared::utils::IdGenerator::new_id_with_prefix("notif"),
            title,
            message,
            notification_type,
            priority: Priority::default(),
            read: false,
            read_at: None,
            scheduled_at: None,
            entity_status: EntityStatus::Active,
            metadata: Metadata::default(),
        }
    }

    pub fn mark_read(&mut self) {
        self.read = true;
        self.read_at = Some(chrono::Utc::now());
        self.metadata.updated_at = chrono::Utc::now();
        self.metadata.version += 1;
    }
}
