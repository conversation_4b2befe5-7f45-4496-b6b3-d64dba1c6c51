// Shared Utils - 共享工具函数
// 跨层使用的通用工具和辅助函数

use crate::shared::types::Id;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

pub mod date_utils;
pub mod string_utils;
pub mod validation;
pub mod crypto;

// 重新导出工具模块
pub use date_utils::*;
pub use string_utils::*;
pub use validation::*;
pub use crypto::*;

// ID 生成器
pub struct IdGenerator;

impl IdGenerator {
    /// 生成新的 UUID
    pub fn new_id() -> Id {
        uuid::Uuid::new_v4().to_string()
    }
    
    /// 生成带前缀的 ID
    pub fn new_id_with_prefix(prefix: &str) -> Id {
        format!("{}_{}", prefix, uuid::Uuid::new_v4().to_string())
    }
    
    /// 生成短 ID (8位)
    pub fn new_short_id() -> String {
        uuid::Uuid::new_v4().to_string()[..8].to_string()
    }
}

// 配置管理器
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    pub database: DatabaseConfig,
    pub app: AppConfig,
    pub logging: LoggingConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub timeout: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    pub name: String,
    pub version: String,
    pub environment: String,
    pub data_dir: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: String,
    pub file_enabled: bool,
    pub file_path: Option<String>,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            database: DatabaseConfig {
                url: "sqlite:data/paolife.db".to_string(),
                max_connections: 10,
                timeout: 30,
            },
            app: AppConfig {
                name: "PaoLife".to_string(),
                version: "0.1.0".to_string(),
                environment: "development".to_string(),
                data_dir: "data".to_string(),
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                file_enabled: true,
                file_path: Some("logs/app.log".to_string()),
            },
        }
    }
}

// 事件发布器
pub trait EventPublisher {
    fn publish<T>(&self, event: T) -> crate::shared::errors::Result<()>
    where
        T: Serialize + Send + Sync + 'static;
}

// 缓存接口
#[async_trait::async_trait]
pub trait Cache {
    async fn get<T>(&self, key: &str) -> crate::shared::errors::Result<Option<T>>
    where
        T: for<'de> Deserialize<'de> + Send + Sync;
        
    async fn set<T>(&self, key: &str, value: &T, ttl: Option<u64>) -> crate::shared::errors::Result<()>
    where
        T: Serialize + Send + Sync;
        
    async fn delete(&self, key: &str) -> crate::shared::errors::Result<()>;
    
    async fn exists(&self, key: &str) -> crate::shared::errors::Result<bool>;
}

// 内存缓存实现
pub struct MemoryCache {
    data: std::sync::RwLock<HashMap<String, (String, Option<DateTime<Utc>>)>>,
}

impl MemoryCache {
    pub fn new() -> Self {
        Self {
            data: std::sync::RwLock::new(HashMap::new()),
        }
    }
    
    fn is_expired(&self, expiry: Option<DateTime<Utc>>) -> bool {
        if let Some(expiry) = expiry {
            Utc::now() > expiry
        } else {
            false
        }
    }
}

#[async_trait::async_trait]
impl Cache for MemoryCache {
    async fn get<T>(&self, key: &str) -> crate::shared::errors::Result<Option<T>>
    where
        T: for<'de> Deserialize<'de> + Send + Sync,
    {
        let data = self.data.read().unwrap();
        if let Some((value, expiry)) = data.get(key) {
            if !self.is_expired(*expiry) {
                let deserialized: T = serde_json::from_str(value)?;
                Ok(Some(deserialized))
            } else {
                Ok(None)
            }
        } else {
            Ok(None)
        }
    }
    
    async fn set<T>(&self, key: &str, value: &T, ttl: Option<u64>) -> crate::shared::errors::Result<()>
    where
        T: Serialize + Send + Sync,
    {
        let serialized = serde_json::to_string(value)?;
        let expiry = ttl.map(|seconds| Utc::now() + chrono::Duration::seconds(seconds as i64));
        
        let mut data = self.data.write().unwrap();
        data.insert(key.to_string(), (serialized, expiry));
        Ok(())
    }
    
    async fn delete(&self, key: &str) -> crate::shared::errors::Result<()> {
        let mut data = self.data.write().unwrap();
        data.remove(key);
        Ok(())
    }
    
    async fn exists(&self, key: &str) -> crate::shared::errors::Result<bool> {
        let data = self.data.read().unwrap();
        if let Some((_, expiry)) = data.get(key) {
            Ok(!self.is_expired(*expiry))
        } else {
            Ok(false)
        }
    }
}

impl Default for MemoryCache {
    fn default() -> Self {
        Self::new()
    }
}
