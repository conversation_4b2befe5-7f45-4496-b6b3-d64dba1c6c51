export type ProjectStatus = 'not-started' | 'in-progress' | 'at-risk' | 'paused' | 'completed' | 'archived';
export type TaskPriority = 'low' | 'medium' | 'high';
export type ResourceType = 'link' | 'markdown' | 'file';
export type KpiType = 'growth' | 'reduction';

export interface Task {
    id: string;
    title: string;
    completed: boolean;
    priority: TaskPriority;
    dueDate?: string;
    subtasks: Task[];
}

export interface KPI {
    id: string;
    name: string;
    type: KpiType;
    currentValue: number;
    targetValue: number;
    unit: string;
}

export interface Resource {
    id: string;
    name: string;
    type: ResourceType;
    url: string;
}

export interface Project {
    id: string;
    name: string;
    description: string;
    status: ProjectStatus;
    areaId: string;
    areaName?: string; 
    startDate?: string;
    dueDate?: string;
    tasks: Task[];
    resources: Resource[]; 
    milestones?: any[]; 
    kpis: KPI[]; 
    statusHistory: { status: ProjectStatus; changedAt: string }[];
    updatedAt: string;
}

export interface Area {
    id: string;
    name: string;
}

export interface SortBy {
    field: 'name' | 'dueDate' | 'updatedAt' | 'progress';
    direction: 'asc' | 'desc';
}

export interface FilterState {
    status: 'all' | ProjectStatus;
    areaId: 'all' | string;
}

export const getStatusStyles = (status: ProjectStatus): string => {
    const styles: Record<ProjectStatus, string> = {
        'not-started': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
        'in-progress': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
        'at-risk': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        'paused': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
        'completed': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        'archived': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
    };
    return styles[status] || styles['not-started'];
};

export const getStatusText = (status: ProjectStatus): string => {
    const textMap: Record<ProjectStatus, string> = {
        'not-started': '未开始', 'in-progress': '进行中', 'at-risk': '有风险',
        'paused': '已暂停', 'completed': '已完成', 'archived': '已归档',
    };
    return textMap[status] || status;
};

export const getRemainingDaysColor = (days: number): string => {
    if (days < 0) return 'text-red-600 dark:text-red-400';
    if (days >= 0 && days <= 7) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-gray-500 dark:text-gray-400';
};

export const getStatusDotColor = (status: ProjectStatus): string => {
    const colors: Record<ProjectStatus, string> = {
        'not-started': 'bg-gray-400', 'in-progress': 'bg-blue-500', 'at-risk': 'bg-red-500',
        'paused': 'bg-yellow-500', 'completed': 'bg-green-500', 'archived': 'bg-purple-500',
    };
    return colors[status] || 'bg-gray-400';
};

