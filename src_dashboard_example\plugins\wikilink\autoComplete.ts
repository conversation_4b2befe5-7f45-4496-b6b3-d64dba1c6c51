/**
 * WikiLink 自动补全功能
 * 提供智能提示和自动补全
 */

import { $prose } from '@milkdown/kit/utils'
import { Plugin, PluginKey } from '@milkdown/kit/prose/state'
import { Decoration, DecorationSet } from '@milkdown/kit/prose/view'

export interface AutoCompleteItem {
  id: string
  type: 'document' | 'project' | 'area'
  title: string
  path?: string
  description?: string
  icon: string
  score: number
}

export interface AutoCompleteConfig {
  onSearch: (query: string, type: string) => Promise<AutoCompleteItem[]>
  onSelect: (item: AutoCompleteItem) => void
  maxResults: number
  minQueryLength: number
}

const autoCompleteKey = new PluginKey('autoComplete')

/**
 * 自动补全插件
 */
export const autoCompletePlugin = $prose((config: AutoCompleteConfig) => {
  let currentSuggestions: AutoCompleteItem[] = []
  let suggestionElement: HTMLElement | null = null
  let selectedIndex = 0

  return new Plugin({
    key: autoCompleteKey,
    
    state: {
      init() {
        return {
          active: false,
          query: '',
          type: 'document',
          position: 0
        }
      },
      
      apply(tr, value) {
        // 检查是否有选择变化
        if (tr.selectionSet) {
          const { from } = tr.selection
          const text = tr.doc.textBetween(Math.max(0, from - 20), from)
          
          // 检测 WikiLink 输入
          const wikiLinkMatch = text.match(/\[\[([^\]]*?)$/)
          if (wikiLinkMatch) {
            return {
              active: true,
              query: wikiLinkMatch[1],
              type: 'document',
              position: from
            }
          }
          
          // 检测项目引用输入
          const projectMatch = text.match(/@project:([^\s]*?)$/)
          if (projectMatch) {
            return {
              active: true,
              query: projectMatch[1],
              type: 'project',
              position: from
            }
          }
          
          // 检测任务引用输入
          const taskMatch = text.match(/@task:([^\s]*?)$/)
          if (taskMatch) {
            return {
              active: true,
              query: taskMatch[1],
              type: 'task',
              position: from
            }
          }
          
          // 检测领域引用输入
          const areaMatch = text.match(/@area:([^\s]*?)$/)
          if (areaMatch) {
            return {
              active: true,
              query: areaMatch[1],
              type: 'area',
              position: from
            }
          }
          
          // 检测领域标签输入
          const tagMatch = text.match(/#([^\s#]*?)$/)
          if (tagMatch) {
            return {
              active: true,
              query: tagMatch[1],
              type: 'tag',
              position: from
            }
          }
          
          // 没有匹配，关闭自动补全
          return {
            active: false,
            query: '',
            type: 'document',
            position: 0
          }
        }
        
        return value
      }
    },
    
    view(editorView) {
      return {
        update: async (view, prevState) => {
          const state = autoCompleteKey.getState(view.state)
          const prevAutoCompleteState = autoCompleteKey.getState(prevState)
          
          // 状态变化时更新建议
          if (state?.active !== prevAutoCompleteState?.active || 
              state?.query !== prevAutoCompleteState?.query ||
              state?.type !== prevAutoCompleteState?.type) {
            
            if (state?.active && state.query.length >= config.minQueryLength) {
              console.log('🔍 搜索自动补全建议:', { query: state.query, type: state.type })
              
              try {
                currentSuggestions = await config.onSearch(state.query, state.type)
                selectedIndex = 0
                showSuggestions(view, state.position)
              } catch (error) {
                console.error('❌ 搜索建议失败:', error)
                hideSuggestions()
              }
            } else {
              hideSuggestions()
            }
          }
        },
        
        destroy() {
          hideSuggestions()
        }
      }
    },
    
    props: {
      handleKeyDown(view, event) {
        const state = autoCompleteKey.getState(view.state)
        
        if (!state?.active || currentSuggestions.length === 0) {
          return false
        }
        
        switch (event.key) {
          case 'ArrowDown':
            event.preventDefault()
            selectedIndex = Math.min(selectedIndex + 1, currentSuggestions.length - 1)
            updateSuggestionSelection()
            return true
            
          case 'ArrowUp':
            event.preventDefault()
            selectedIndex = Math.max(selectedIndex - 1, 0)
            updateSuggestionSelection()
            return true
            
          case 'Enter':
          case 'Tab':
            event.preventDefault()
            if (currentSuggestions[selectedIndex]) {
              selectSuggestion(view, currentSuggestions[selectedIndex], state)
            }
            return true
            
          case 'Escape':
            event.preventDefault()
            hideSuggestions()
            return true
            
          default:
            return false
        }
      }
    }
  })

  /**
   * 显示建议列表
   */
  function showSuggestions(view: any, position: number) {
    hideSuggestions()
    
    if (currentSuggestions.length === 0) return
    
    // 创建建议元素
    suggestionElement = document.createElement('div')
    suggestionElement.className = 'auto-complete-suggestions'
    suggestionElement.style.cssText = `
      position: absolute;
      background: white;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      max-height: 200px;
      overflow-y: auto;
      z-index: 1000;
      min-width: 250px;
    `
    
    // 添加建议项
    currentSuggestions.forEach((item, index) => {
      const itemElement = document.createElement('div')
      itemElement.className = `suggestion-item ${index === selectedIndex ? 'selected' : ''}`
      itemElement.style.cssText = `
        padding: 8px 12px;
        cursor: pointer;
        border-bottom: 1px solid #f1f5f9;
        display: flex;
        align-items: center;
        gap: 8px;
        ${index === selectedIndex ? 'background-color: #f8fafc;' : ''}
      `
      
      itemElement.innerHTML = `
        <span style="font-size: 16px;">${item.icon}</span>
        <div style="flex: 1;">
          <div style="font-weight: 500; color: #1e293b;">${item.title}</div>
          ${item.description ? `<div style="font-size: 12px; color: #64748b;">${item.description}</div>` : ''}
        </div>
        <div style="font-size: 11px; color: #94a3b8; text-transform: uppercase;">${item.type}</div>
      `
      
      itemElement.addEventListener('click', () => {
        const state = autoCompleteKey.getState(view.state)
        if (state) {
          selectSuggestion(view, item, state)
        }
      })
      
      suggestionElement!.appendChild(itemElement)
    })
    
    // 计算位置
    const coords = view.coordsAtPos(position)
    suggestionElement.style.left = `${coords.left}px`
    suggestionElement.style.top = `${coords.bottom + 4}px`
    
    // 添加到 DOM
    document.body.appendChild(suggestionElement)
  }
  
  /**
   * 隐藏建议列表
   */
  function hideSuggestions() {
    if (suggestionElement) {
      suggestionElement.remove()
      suggestionElement = null
    }
    currentSuggestions = []
    selectedIndex = 0
  }
  
  /**
   * 更新选中状态
   */
  function updateSuggestionSelection() {
    if (!suggestionElement) return
    
    const items = suggestionElement.querySelectorAll('.suggestion-item')
    items.forEach((item, index) => {
      if (index === selectedIndex) {
        item.classList.add('selected')
        ;(item as HTMLElement).style.backgroundColor = '#f8fafc'
      } else {
        item.classList.remove('selected')
        ;(item as HTMLElement).style.backgroundColor = ''
      }
    })
  }
  
  /**
   * 选择建议项
   */
  function selectSuggestion(view: any, item: AutoCompleteItem, state: any) {
    console.log('✅ 选择自动补全项:', item)
    
    // 计算替换范围
    const { from } = view.state.selection
    let replaceFrom = from
    let replaceTo = from
    
    // 根据类型计算替换范围
    switch (state.type) {
      case 'document':
        // WikiLink: [[query -> [[title]]
        replaceFrom = from - state.query.length - 2 // 减去 [[ 和 query
        replaceTo = from
        break
        
      case 'project':
        // 项目引用: @project:query -> @project:title
        replaceFrom = from - state.query.length - 9 // 减去 @project: 和 query
        replaceTo = from
        break
        
      case 'task':
        // 任务引用: @task:query -> @task:title
        replaceFrom = from - state.query.length - 6 // 减去 @task: 和 query
        replaceTo = from
        break
        
      case 'area':
        // 领域引用: @area:query -> @area:title
        replaceFrom = from - state.query.length - 6 // 减去 @area: 和 query
        replaceTo = from
        break
        
      case 'tag':
        // 标签: #query -> #title
        replaceFrom = from - state.query.length - 1 // 减去 # 和 query
        replaceTo = from
        break
    }
    
    // 生成替换文本
    let replacementText = ''
    switch (state.type) {
      case 'document':
        replacementText = `[[${item.title}]]`
        break
      case 'project':
        replacementText = `@project:${item.title}`
        break
      case 'task':
        replacementText = `@task:${item.title}`
        break
      case 'area':
        replacementText = `@area:${item.title}`
        break
      case 'tag':
        replacementText = `#${item.title}`
        break
    }
    
    // 执行替换
    const tr = view.state.tr.replaceWith(
      replaceFrom,
      replaceTo,
      view.state.schema.text(replacementText)
    )
    
    view.dispatch(tr)
    
    // 隐藏建议
    hideSuggestions()
    
    // 触发选择回调
    config.onSelect(item)
  }
})

export default autoCompletePlugin
