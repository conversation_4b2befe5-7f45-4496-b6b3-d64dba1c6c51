import type { ExtendedTask } from '../store/taskStore'

/**
 * 生成测试用的任务数据，用于验证无限层级子任务系统的性能
 */
export function generateTestTasks(
  projectId: string,
  options: {
    rootTaskCount?: number // 根任务数量
    maxDepth?: number // 最大层级深度
    childrenPerLevel?: number // 每级子任务数量
    totalTaskLimit?: number // 总任务数量限制
  } = {}
): ExtendedTask[] {
  const {
    rootTaskCount = 5,
    maxDepth = 4,
    childrenPerLevel = 3,
    totalTaskLimit = 100
  } = options

  const tasks: ExtendedTask[] = []
  let taskCounter = 1

  // 生成任务ID
  const generateTaskId = () => `test-task-${taskCounter++}`

  // 生成随机优先级
  const getRandomPriority = () => {
    const priorities = ['low', 'medium', 'high', 'critical', null]
    return priorities[Math.floor(Math.random() * priorities.length)]
  }

  // 生成随机状态
  const getRandomStatus = () => {
    const statuses = ['todo', 'in_progress', 'blocked', 'review', 'done']
    return statuses[Math.floor(Math.random() * statuses.length)]
  }

  // 生成随机日期
  const getRandomDate = () => {
    const now = new Date()
    const futureDate = new Date(now.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000) // 30天内
    return Math.random() > 0.7 ? futureDate : null // 70% 概率有截止日期
  }

  // 递归生成子任务
  function generateSubtasks(
    parentId: string,
    currentDepth: number,
    parentTitle: string
  ): void {
    if (currentDepth >= maxDepth || tasks.length >= totalTaskLimit) {
      return
    }

    const childCount = Math.floor(Math.random() * childrenPerLevel) + 1
    
    for (let i = 0; i < childCount && tasks.length < totalTaskLimit; i++) {
      const taskId = generateTaskId()
      const isCompleted = Math.random() > 0.7 // 30% 概率已完成
      
      const task: ExtendedTask = {
        id: taskId,
        content: `${parentTitle} - Subtask ${i + 1} (Level ${currentDepth + 1})`,
        description: `This is a test subtask at level ${currentDepth + 1}. Parent: ${parentTitle}`,
        completed: isCompleted,
        priority: getRandomPriority(),
        deadline: getRandomDate(),
        parentId: parentId,
        repeatRule: null,
        projectId: projectId,
        areaId: null,
        sourceResourceId: null,
        sourceText: null,
        sourceContext: null,
        createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // 7天内创建
        updatedAt: new Date(),
        
        // 扩展属性
        status: getRandomStatus(),
        estimatedHours: Math.random() > 0.5 ? Math.floor(Math.random() * 8) + 1 : undefined,
        actualHours: Math.random() > 0.6 ? Math.floor(Math.random() * 6) + 1 : undefined,
        progress: isCompleted ? 100 : Math.floor(Math.random() * 80),
        blockedBy: Math.random() > 0.9 ? 'Waiting for external dependency' : undefined
      }

      tasks.push(task)

      // 递归生成子任务（50% 概率）
      if (Math.random() > 0.5) {
        generateSubtasks(taskId, currentDepth + 1, task.content)
      }
    }
  }

  // 生成根任务
  for (let i = 0; i < rootTaskCount && tasks.length < totalTaskLimit; i++) {
    const taskId = generateTaskId()
    const isCompleted = Math.random() > 0.8 // 20% 概率已完成
    
    const rootTask: ExtendedTask = {
      id: taskId,
      content: `Root Task ${i + 1}: ${getTaskTitle(i)}`,
      description: `This is a root level test task for performance testing. Task number ${i + 1}.`,
      completed: isCompleted,
      priority: getRandomPriority(),
      deadline: getRandomDate(),
      parentId: null,
      repeatRule: null,
      projectId: projectId,
      areaId: null,
      sourceResourceId: null,
      sourceText: null,
      sourceContext: null,
      createdAt: new Date(Date.now() - Math.random() * 14 * 24 * 60 * 60 * 1000), // 14天内创建
      updatedAt: new Date(),
      
      // 扩展属性
      status: getRandomStatus(),
      estimatedHours: Math.random() > 0.3 ? Math.floor(Math.random() * 16) + 4 : undefined,
      actualHours: Math.random() > 0.5 ? Math.floor(Math.random() * 12) + 2 : undefined,
      progress: isCompleted ? 100 : Math.floor(Math.random() * 90),
      blockedBy: Math.random() > 0.95 ? 'Waiting for approval' : undefined
    }

    tasks.push(rootTask)

    // 生成子任务
    generateSubtasks(taskId, 0, rootTask.content)
  }

  return tasks
}

// 生成有意义的任务标题
function getTaskTitle(index: number): string {
  const titles = [
    'User Authentication System',
    'Database Migration',
    'API Documentation',
    'Frontend Redesign',
    'Performance Optimization',
    'Security Audit',
    'Mobile App Development',
    'Testing Framework Setup',
    'CI/CD Pipeline',
    'Code Review Process'
  ]
  
  return titles[index % titles.length]
}

/**
 * 生成简单的测试任务（用于快速测试）
 */
export function generateSimpleTestTasks(projectId: string, count: number = 10): ExtendedTask[] {
  const tasks: ExtendedTask[] = []
  
  for (let i = 0; i < count; i++) {
    const task: ExtendedTask = {
      id: `simple-task-${i + 1}`,
      content: `Simple Test Task ${i + 1}`,
      description: `Description for test task ${i + 1}`,
      completed: Math.random() > 0.7,
      priority: i % 2 === 0 ? 'medium' : 'high',
      deadline: Math.random() > 0.5 ? new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) : null,
      parentId: null,
      repeatRule: null,
      projectId: projectId,
      areaId: null,
      sourceResourceId: null,
      sourceText: null,
      sourceContext: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      status: 'todo',
      progress: 0
    }
    
    tasks.push(task)
  }
  
  return tasks
}

/**
 * 生成深层嵌套的测试任务（用于测试极限情况）
 */
export function generateDeepNestedTasks(projectId: string, depth: number = 10): ExtendedTask[] {
  const tasks: ExtendedTask[] = []
  let parentId: string | null = null
  
  for (let i = 0; i < depth; i++) {
    const taskId = `deep-task-${i + 1}`
    
    const task: ExtendedTask = {
      id: taskId,
      content: `Deep Nested Task Level ${i + 1}`,
      description: `This task is at nesting level ${i + 1}`,
      completed: false,
      priority: 'medium',
      deadline: null,
      parentId: parentId,
      repeatRule: null,
      projectId: projectId,
      areaId: null,
      sourceResourceId: null,
      sourceText: null,
      sourceContext: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      status: 'todo',
      progress: 0
    }
    
    tasks.push(task)
    parentId = taskId // 下一个任务的父任务
  }
  
  return tasks
}

/**
 * 清理测试数据
 */
export function cleanupTestTasks(tasks: ExtendedTask[]): string[] {
  return tasks
    .filter(task => task.id.startsWith('test-task-') || task.id.startsWith('simple-task-') || task.id.startsWith('deep-task-'))
    .map(task => task.id)
}
