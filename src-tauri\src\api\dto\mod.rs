// API DTOs - API 数据传输对象

use serde::{Deserialize, Serialize};

// 具体的 DTO 模块
pub mod user_dto;
pub mod project_dto;
pub mod task_dto;
pub mod area_dto;

// 重新导出所有 DTO
pub use user_dto::*;
pub use project_dto::*;
pub use task_dto::*;
pub use area_dto::*;

// API 特定的请求和响应类型

/// 通用分页请求
#[derive(Debu<PERSON>, Clone, Serialize, Deserialize)]
pub struct PaginationRequest {
    pub page: u64,
    pub size: u64,
}

impl Default for PaginationRequest {
    fn default() -> Self {
        Self {
            page: 1,
            size: 20,
        }
    }
}

impl PaginationRequest {
    pub fn validate(&self) -> Result<(), String> {
        if self.page == 0 {
            return Err("Page number must be greater than 0".to_string());
        }

        if self.size == 0 {
            return Err("Page size must be greater than 0".to_string());
        }

        if self.size > 100 {
            return Err("Page size cannot be greater than 100".to_string());
        }

        Ok(())
    }
}

/// 通用搜索请求
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SearchRequest {
    pub query: String,
    pub pagination: Option<PaginationRequest>,
}

impl SearchRequest {
    pub fn validate(&self) -> Result<(), String> {
        if self.query.trim().is_empty() {
            return Err("Search query cannot be empty".to_string());
        }

        if self.query.len() > 200 {
            return Err("Search query cannot be longer than 200 characters".to_string());
        }

        if let Some(ref pagination) = self.pagination {
            pagination.validate()?;
        }

        Ok(())
    }
}

/// 通用 API 响应包装器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            timestamp: chrono::Utc::now(),
        }
    }

    pub fn error(error: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(error),
            timestamp: chrono::Utc::now(),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HealthCheckResponse {
    pub status: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub version: String,
    pub database_status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub error: String,
    pub code: u32,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl ErrorResponse {
    pub fn new(error: String, code: u32) -> Self {
        Self {
            error,
            code,
            timestamp: chrono::Utc::now(),
        }
    }
}

/// 空响应（用于删除等操作）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmptyResponse;
