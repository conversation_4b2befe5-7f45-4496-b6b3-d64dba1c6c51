// Card Component - 卡片组件
// 基于设计规范的统一卡片组件

import { JSX, splitProps, mergeProps, Show } from 'solid-js';
import { Dynamic } from 'solid-js/web';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';

// 卡片变体样式
const cardVariants = cva(
  'rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200',
  {
    variants: {
      variant: {
        default: 'border-border',
        elevated: 'border-border shadow-md hover:shadow-lg',
        outlined: 'border-2 border-border shadow-none',
        ghost: 'border-transparent shadow-none bg-transparent',
      },
      size: {
        default: 'p-6',
        sm: 'p-4',
        lg: 'p-8',
        xl: 'p-10',
      },
      interactive: {
        true: 'cursor-pointer hover:shadow-md hover:scale-[1.02] active:scale-[0.98]',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      interactive: false,
    },
  }
);

export interface CardProps extends JSX.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'xl';
  interactive?: boolean;
  children?: JSX.Element;
}

export function Card(props: CardProps) {
  const merged = mergeProps(
    {
      variant: 'default' as const,
      size: 'default' as const,
      interactive: false,
    },
    props
  );

  const [local, others] = splitProps(merged, [
    'variant',
    'size',
    'interactive',
    'class',
    'children',
  ]);

  return (
    <div
      class={cn(
        cardVariants({
          variant: local.variant,
          size: local.size,
          interactive: local.interactive,
        }),
        local.class
      )}
      {...others}
    >
      {local.children}
    </div>
  );
}

// 卡片头部组件
export interface CardHeaderProps extends JSX.HTMLAttributes<HTMLDivElement> {
  children?: JSX.Element;
}

export function CardHeader(props: CardHeaderProps) {
  const [local, others] = splitProps(props, ['class', 'children']);

  return (
    <div
      class={cn('flex flex-col space-y-1.5 p-6', local.class)}
      {...others}
    >
      {local.children}
    </div>
  );
}

// 卡片标题组件
export interface CardTitleProps extends JSX.HTMLAttributes<HTMLHeadingElement> {
  children?: JSX.Element;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

export function CardTitle(props: CardTitleProps) {
  const merged = mergeProps({ as: 'h3' as const }, props);
  const [local, others] = splitProps(merged, ['class', 'children', 'as']);

  return (
    <Dynamic
      component={local.as}
      class={cn(
        'text-2xl font-semibold leading-none tracking-tight',
        local.class
      )}
      {...others}
    >
      {local.children}
    </Dynamic>
  );
}

// 卡片描述组件
export interface CardDescriptionProps extends JSX.HTMLAttributes<HTMLParagraphElement> {
  children?: JSX.Element;
}

export function CardDescription(props: CardDescriptionProps) {
  const [local, others] = splitProps(props, ['class', 'children']);

  return (
    <p
      class={cn('text-sm text-muted-foreground', local.class)}
      {...others}
    >
      {local.children}
    </p>
  );
}

// 卡片内容组件
export interface CardContentProps extends JSX.HTMLAttributes<HTMLDivElement> {
  children?: JSX.Element;
}

export function CardContent(props: CardContentProps) {
  const [local, others] = splitProps(props, ['class', 'children']);

  return (
    <div class={cn('p-6 pt-0', local.class)} {...others}>
      {local.children}
    </div>
  );
}

// 卡片底部组件
export interface CardFooterProps extends JSX.HTMLAttributes<HTMLDivElement> {
  children?: JSX.Element;
}

export function CardFooter(props: CardFooterProps) {
  const [local, others] = splitProps(props, ['class', 'children']);

  return (
    <div
      class={cn('flex items-center p-6 pt-0', local.class)}
      {...others}
    >
      {local.children}
    </div>
  );
}

// 统计卡片组件
export interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon?: JSX.Element;
  trend?: {
    value: number;
    direction: 'up' | 'down' | 'stable';
    period?: string;
  };
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
  class?: string;
}

export function StatsCard(props: StatsCardProps) {
  const merged = mergeProps({ variant: 'default' as const }, props);

  const trendColor = () => {
    if (!merged.trend) return '';
    switch (merged.trend.direction) {
      case 'up':
        return 'text-success';
      case 'down':
        return 'text-destructive';
      case 'stable':
        return 'text-muted-foreground';
      default:
        return '';
    }
  };

  const variantStyles = () => {
    switch (merged.variant) {
      case 'success':
        return 'border-l-4 border-l-success';
      case 'warning':
        return 'border-l-4 border-l-warning';
      case 'error':
        return 'border-l-4 border-l-destructive';
      case 'info':
        return 'border-l-4 border-l-info';
      default:
        return '';
    }
  };

  return (
    <Card class={cn(variantStyles(), merged.class)}>
      <CardContent class="p-6">
        <div class="flex items-center justify-between space-y-0 pb-2">
          <CardDescription>{merged.title}</CardDescription>
          <Show when={merged.icon}>
            <div class="h-4 w-4 text-muted-foreground">
              {merged.icon}
            </div>
          </Show>
        </div>
        <div class="space-y-1">
          <div class="text-2xl font-bold">{merged.value}</div>
          <Show when={merged.description || merged.trend}>
            <div class="flex items-center space-x-2 text-xs text-muted-foreground">
              <Show when={merged.trend}>
                <span class={cn('flex items-center', trendColor())}>
                  <Show when={merged.trend!.direction === 'up'}>
                    <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 17l9.2-9.2M17 17V7H7" />
                    </svg>
                  </Show>
                  <Show when={merged.trend!.direction === 'down'}>
                    <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 7l-9.2 9.2M7 7v10h10" />
                    </svg>
                  </Show>
                  <Show when={merged.trend!.direction === 'stable'}>
                    <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                    </svg>
                  </Show>
                  {Math.abs(merged.trend!.value)}%
                </span>
              </Show>
              <Show when={merged.description}>
                <span>{merged.description}</span>
              </Show>
              <Show when={merged.trend?.period}>
                <span>vs {merged.trend!.period}</span>
              </Show>
            </div>
          </Show>
        </div>
      </CardContent>
    </Card>
  );
}

// 项目卡片组件
export interface ProjectCardProps {
  project: {
    id: string;
    name: string;
    description?: string;
    progress: number;
    status: string;
    startDate?: string;
    dueDate?: string;
    updatedAt?: string;
    priority: string;
    tags?: string[];
  };
  onClick?: (id: string) => void;
  onEdit?: (project: any) => void;
  class?: string;
}

export function ProjectCard(props: ProjectCardProps) {
  const handleClick = () => {
    props.onClick?.(props.project.id);
  };

  const handleEdit = (e: Event) => {
    e.stopPropagation();
    props.onEdit?.(props.project);
  };

  const getPriorityLabel = (priority: string) => {
    const priorityMap: Record<string, string> = {
      'low': '低',
      'medium': '中',
      'high': '高',
      'critical': '紧急',
    };
    return priorityMap[priority] || priority;
  };

  const getStatusLabel = (status: string) => {
    const statusMap: Record<string, string> = {
      'not_started': '未开始',
      'in_progress': '进行中',
      'completed': '已完成',
      'on_hold': '暂停',
      'at_risk': '有风险',
    };
    return statusMap[status] || status;
  };

  const priorityColor = () => {
    switch (props.project.priority) {
      case 'critical':
        return 'bg-red-100 text-red-700';
      case 'high':
        return 'bg-orange-100 text-orange-700';
      case 'medium':
        return 'bg-yellow-100 text-yellow-700';
      case 'low':
        return 'bg-green-100 text-green-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const statusColor = () => {
    switch (props.project.status) {
      case 'completed':
        return 'text-green-600';
      case 'in_progress':
        return 'text-blue-600';
      case 'at_risk':
        return 'text-red-600';
      case 'on_hold':
        return 'text-yellow-600';
      case 'not_started':
        return 'text-gray-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const calculateRemainingDays = (dueDate?: string) => {
    if (!dueDate) return null;
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const remainingDays = () => calculateRemainingDays(props.project.dueDate);

  return (
    <Card
      interactive={!!props.onClick}
      onClick={handleClick}
      class={cn('hover:shadow-md transition-shadow', props.class)}
    >
      <CardHeader class="pb-3">
        <div class="flex items-start justify-between">
          <div class="space-y-1 flex-1">
            <CardTitle class="text-lg">{props.project.name}</CardTitle>
            <Show when={props.project.description}>
              <CardDescription class="line-clamp-2">
                {props.project.description}
              </CardDescription>
            </Show>
          </div>
          <Show when={props.onEdit}>
            <button
              type="button"
              onClick={handleEdit}
              class="p-1 rounded-md hover:bg-muted text-muted-foreground hover:text-foreground transition-colors"
              aria-label="编辑项目"
            >
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>
          </Show>
        </div>
      </CardHeader>
      
      <CardContent class="space-y-3">
        {/* 状态和优先级 */}
        <div class="flex items-center justify-between text-sm">
          <div class="flex items-center space-x-2">
            <span class={cn('font-medium', statusColor())}>
              {getStatusLabel(props.project.status)}
            </span>
            <span class="text-muted-foreground">•</span>
            <span class={cn('px-2 py-1 rounded-full text-xs font-medium', priorityColor())}>
              {getPriorityLabel(props.project.priority)}
            </span>
          </div>
        </div>

        {/* 进度条 */}
        <div class="space-y-2">
          <div class="flex justify-between text-sm">
            <span class="text-muted-foreground">进度</span>
            <span class="font-medium">{Math.round(props.project.progress * 100)}%</span>
          </div>
          <div class="w-full bg-muted rounded-full h-2 relative overflow-hidden">
            <div
              class="bg-primary h-full rounded-full transition-all duration-300 absolute top-0 left-0"
              classList={{
                'w-0': props.project.progress === 0,
                'w-1/4': props.project.progress > 0 && props.project.progress <= 0.25,
                'w-2/4': props.project.progress > 0.25 && props.project.progress <= 0.5,
                'w-3/4': props.project.progress > 0.5 && props.project.progress <= 0.75,
                'w-full': props.project.progress > 0.75,
              }}
            />
          </div>
        </div>

        {/* 时间信息 */}
        <div class="space-y-1 text-xs text-muted-foreground">
          <Show when={props.project.startDate}>
            <div class="flex justify-between">
              <span>开始时间:</span>
              <span>{formatDate(props.project.startDate)}</span>
            </div>
          </Show>
          <Show when={props.project.dueDate}>
            <div class="flex justify-between">
              <span>截止时间:</span>
              <span class={remainingDays() !== null && remainingDays()! < 0 ? 'text-red-600' : ''}>
                {formatDate(props.project.dueDate)}
              </span>
            </div>
          </Show>
          <Show when={remainingDays() !== null}>
            <div class="flex justify-between">
              <span>剩余天数:</span>
              <span class={remainingDays()! < 0 ? 'text-red-600' : remainingDays()! <= 3 ? 'text-yellow-600' : ''}>
                {remainingDays()! < 0 ? `逾期 ${Math.abs(remainingDays()!)} 天` : `${remainingDays()} 天`}
              </span>
            </div>
          </Show>
          <div class="flex justify-between">
            <span>更新时间:</span>
            <span>{formatDate(props.project.updatedAt)}</span>
          </div>
        </div>

        {/* 标签 */}
        <Show when={props.project.tags && props.project.tags.length > 0}>
          <div class="flex flex-wrap gap-1">
            <For each={props.project.tags?.slice(0, 3)}>
              {(tag) => (
                <span class="px-2 py-1 bg-secondary text-secondary-foreground rounded text-xs">
                  {tag}
                </span>
              )}
            </For>
            <Show when={props.project.tags && props.project.tags.length > 3}>
              <span class="px-2 py-1 bg-muted text-muted-foreground rounded text-xs">
                +{props.project.tags!.length - 3}
              </span>
            </Show>
          </div>
        </Show>
      </CardContent>
    </Card>
  );
}
