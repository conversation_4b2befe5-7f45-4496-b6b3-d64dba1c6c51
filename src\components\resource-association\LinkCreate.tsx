/**
 * 链接创建组件
 * 支持外部链接添加、预览获取、验证等功能
 */

import { createSignal, createEffect, Show } from 'solid-js'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Textarea } from '../ui/textarea'
import { Badge } from '../ui/badge'
import { Card, CardContent } from '../ui/card'
import { 
  Link as LinkIcon, 
  ExternalLink, 
  Globe,
  Image,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-solid'
import { cn } from '../../lib/utils'

import type { LinkCreateProps, LinkCreateData } from './types'

// URL验证正则
const URL_REGEX = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/

// 获取域名
function getDomain(url: string): string {
  try {
    return new URL(url).hostname.replace('www.', '')
  } catch {
    return ''
  }
}

// 链接预览数据
interface LinkPreview {
  title?: string
  description?: string
  image?: string
  favicon?: string
  domain: string
  isValid: boolean
}

export function LinkCreate(props: LinkCreateProps) {
  // 状态管理
  const [url, setUrl] = createSignal('')
  const [title, setTitle] = createSignal('')
  const [description, setDescription] = createSignal('')
  const [tags, setTags] = createSignal('')
  const [preview, setPreview] = createSignal<LinkPreview | null>(null)
  const [loading, setLoading] = createSignal(false)
  const [validating, setValidating] = createSignal(false)
  const [errors, setErrors] = createSignal<string[]>([])

  // 验证URL
  const isValidUrl = (urlString: string): boolean => {
    return URL_REGEX.test(urlString)
  }

  // 获取链接预览
  const fetchLinkPreview = async (urlString: string) => {
    if (!isValidUrl(urlString)) return

    setValidating(true)
    try {
      // 模拟API调用获取链接预览
      // 在实际实现中，这里应该调用后端API
      const domain = getDomain(urlString)
      
      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockPreview: LinkPreview = {
        title: `Preview for ${domain}`,
        description: `This is a preview description for the link from ${domain}`,
        image: undefined,
        favicon: `https://www.google.com/s2/favicons?domain=${domain}`,
        domain,
        isValid: true
      }
      
      setPreview(mockPreview)
      
      // 自动填充标题和描述（如果用户没有手动输入）
      if (!title() && mockPreview.title) {
        setTitle(mockPreview.title)
      }
      if (!description() && mockPreview.description) {
        setDescription(mockPreview.description)
      }
      
    } catch (error) {
      console.error('Failed to fetch link preview:', error)
      setPreview({
        domain: getDomain(urlString),
        isValid: false
      })
    } finally {
      setValidating(false)
    }
  }

  // URL变化时的处理
  createEffect(() => {
    const urlValue = url()
    setErrors([])
    
    if (urlValue && isValidUrl(urlValue)) {
      // 防抖处理
      const timeoutId = setTimeout(() => {
        fetchLinkPreview(urlValue)
      }, 500)
      
      return () => clearTimeout(timeoutId)
    } else if (urlValue) {
      setErrors(['Please enter a valid URL'])
      setPreview(null)
    } else {
      setPreview(null)
    }
  })

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: string[] = []
    
    if (!url()) {
      newErrors.push('URL is required')
    } else if (!isValidUrl(url())) {
      newErrors.push('Please enter a valid URL')
    }
    
    if (!title()) {
      newErrors.push('Title is required')
    }
    
    setErrors(newErrors)
    return newErrors.length === 0
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!validateForm()) return

    setLoading(true)
    try {
      const linkData: LinkCreateData = {
        url: url(),
        title: title(),
        description: description() || undefined,
        tags: tags().split(',').map(tag => tag.trim()).filter(Boolean)
      }
      
      await props.onCreateLink(linkData)
      
      // 重置表单
      setUrl('')
      setTitle('')
      setDescription('')
      setTags('')
      setPreview(null)
      
    } catch (error) {
      console.error('Failed to create link:', error)
      setErrors(['Failed to create link. Please try again.'])
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={true} onOpenChange={() => props.onCancel?.()}>
      <DialogContent class="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle class="flex items-center gap-2">
            <LinkIcon class="h-5 w-5" />
            Add External Link
          </DialogTitle>
          <DialogDescription>
            Add an external link to associate with this {props.config?.entityType || 'item'}
          </DialogDescription>
        </DialogHeader>

        <div class="space-y-4">
          {/* URL输入 */}
          <div class="space-y-2">
            <Label for="url">URL *</Label>
            <div class="relative">
              <Input
                id="url"
                type="url"
                placeholder="https://example.com"
                value={url()}
                onInput={(e) => setUrl(e.currentTarget.value)}
                class={cn(errors().some(e => e.includes('URL')) && 'border-red-500')}
                disabled={props.disabled}
              />
              <Show when={validating()}>
                <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <Loader2 class="h-4 w-4 animate-spin text-muted-foreground" />
                </div>
              </Show>
            </div>
          </div>

          {/* 链接预览 */}
          <Show when={preview()}>
            <Card class="border-l-4 border-l-blue-500">
              <CardContent class="p-4">
                <div class="flex items-start gap-3">
                  <Show when={preview()?.favicon}>
                    <img 
                      src={preview()!.favicon} 
                      alt="Favicon"
                      class="w-6 h-6 rounded"
                      onError={(e) => e.currentTarget.style.display = 'none'}
                    />
                  </Show>
                  <Show when={!preview()?.favicon}>
                    <Globe class="h-6 w-6 text-muted-foreground" />
                  </Show>
                  
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center gap-2 mb-1">
                      <p class="font-medium text-sm">{preview()?.domain}</p>
                      <Show when={preview()?.isValid}>
                        <CheckCircle class="h-4 w-4 text-green-500" />
                      </Show>
                      <Show when={preview()?.isValid === false}>
                        <AlertCircle class="h-4 w-4 text-red-500" />
                      </Show>
                    </div>
                    
                    <Show when={preview()?.title}>
                      <p class="text-sm font-medium mb-1">{preview()!.title}</p>
                    </Show>
                    
                    <Show when={preview()?.description}>
                      <p class="text-xs text-muted-foreground line-clamp-2">
                        {preview()!.description}
                      </p>
                    </Show>
                  </div>
                  
                  <Show when={preview()?.image}>
                    <img 
                      src={preview()!.image} 
                      alt="Preview"
                      class="w-16 h-16 object-cover rounded"
                    />
                  </Show>
                </div>
              </CardContent>
            </Card>
          </Show>

          {/* 标题 */}
          <div class="space-y-2">
            <Label for="title">Title *</Label>
            <Input
              id="title"
              placeholder="Enter link title"
              value={title()}
              onInput={(e) => setTitle(e.currentTarget.value)}
              class={cn(errors().some(e => e.includes('Title')) && 'border-red-500')}
              disabled={props.disabled}
            />
          </div>

          {/* 描述 */}
          <div class="space-y-2">
            <Label for="description">Description (optional)</Label>
            <Textarea
              id="description"
              placeholder="Add a description for this link..."
              value={description()}
              onInput={(e) => setDescription(e.currentTarget.value)}
              rows={3}
              disabled={props.disabled}
            />
          </div>

          {/* 标签 */}
          <div class="space-y-2">
            <Label for="tags">Tags (optional)</Label>
            <Input
              id="tags"
              placeholder="Enter tags separated by commas"
              value={tags()}
              onInput={(e) => setTags(e.currentTarget.value)}
              disabled={props.disabled}
            />
            <Show when={tags()}>
              <div class="flex flex-wrap gap-1">
                <For each={tags().split(',').map(tag => tag.trim()).filter(Boolean)}>
                  {(tag) => (
                    <Badge variant="secondary" class="text-xs">
                      {tag}
                    </Badge>
                  )}
                </For>
              </div>
            </Show>
          </div>

          {/* 错误信息 */}
          <Show when={errors().length > 0}>
            <div class="space-y-2">
              <For each={errors()}>
                {(error) => (
                  <div class="flex items-center gap-2 text-sm text-red-600">
                    <AlertCircle class="h-4 w-4" />
                    <span>{error}</span>
                  </div>
                )}
              </For>
            </div>
          </Show>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={props.onCancel}
            disabled={loading()}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading() || !url() || !title() || props.disabled}
          >
            {loading() ? (
              <>
                <Loader2 class="h-4 w-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <ExternalLink class="h-4 w-4 mr-2" />
                Add Link
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default LinkCreate
