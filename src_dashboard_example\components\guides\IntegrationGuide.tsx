/**
 * 集成指南组件
 * 提供详细的迁移和集成步骤
 */

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { 
  CheckCircle, 
  Circle, 
  ArrowRight, 
  Code, 
  FileText, 
  Zap,
  AlertTriangle,
  Info,
  Copy,
  ExternalLink
} from 'lucide-react'
import { cn } from '../../lib/utils'

// 集成步骤定义
const INTEGRATION_STEPS = [
  {
    id: 'setup',
    title: '环境设置',
    description: '配置功能开关和开发环境',
    status: 'completed',
    tasks: [
      '安装必要的依赖包',
      '配置功能开关系统',
      '设置开发环境变量',
      '初始化迁移配置'
    ]
  },
  {
    id: 'types',
    title: '类型系统迁移',
    description: '更新类型定义和接口',
    status: 'completed',
    tasks: [
      '创建统一的KPI类型定义',
      '更新现有组件的类型引用',
      '添加类型守卫和转换函数',
      '验证类型兼容性'
    ]
  },
  {
    id: 'components',
    title: '组件重构',
    description: '逐步替换现有组件',
    status: 'in-progress',
    tasks: [
      '创建通用KPI组件',
      '实现自适应组件选择器',
      '重构项目KPI管理组件',
      '重构领域KPI管理组件'
    ]
  },
  {
    id: 'integration',
    title: '系统集成',
    description: '集成新组件到现有页面',
    status: 'pending',
    tasks: [
      '更新项目详情页',
      '更新领域详情页',
      '配置路由和导航',
      '测试功能完整性'
    ]
  },
  {
    id: 'optimization',
    title: '性能优化',
    description: '启用性能优化功能',
    status: 'pending',
    tasks: [
      '启用智能缓存',
      '实现虚拟化列表',
      '优化数据库查询',
      '添加性能监控'
    ]
  },
  {
    id: 'testing',
    title: '测试验证',
    description: '全面测试和验证',
    status: 'pending',
    tasks: [
      '单元测试覆盖',
      '集成测试验证',
      '性能基准测试',
      '用户体验测试'
    ]
  }
]

// 代码示例
const CODE_EXAMPLES = {
  basicUsage: `// 基本使用 - 自适应组件
import { AdaptiveKPIComponent } from '@/components/adaptive/AdaptiveKPIManagement'

function ProjectDetailPage({ projectId }: { projectId: string }) {
  return (
    <div>
      <h1>Project Details</h1>
      <AdaptiveKPIComponent 
        type="project" 
        id={projectId}
        showArchitectureInfo={true}
        allowArchitectureSwitch={true}
      />
    </div>
  )
}`,

  featureFlags: `// 功能开关使用
import { useFeatureFlags } from '@/config/featureFlags'

function MyComponent() {
  const { isEnabled, updateFlags } = useFeatureFlags()
  
  const useNewArchitecture = isEnabled('useRefactoredKPIManagement')
  
  const toggleArchitecture = () => {
    updateFlags({ 
      useRefactoredKPIManagement: !useNewArchitecture 
    })
  }
  
  return (
    <div>
      <p>Using: {useNewArchitecture ? 'New' : 'Legacy'} Architecture</p>
      <button onClick={toggleArchitecture}>Toggle</button>
    </div>
  )
}`,

  directUsage: `// 直接使用重构组件
import RefactoredKPIManagement from '@/components/features/RefactoredKPIManagement'

function ProjectPage({ projectId }: { projectId: string }) {
  return (
    <RefactoredKPIManagement 
      projectId={projectId}
      className="space-y-6"
    />
  )
}`,

  migration: `// 迁移现有组件
// 旧代码
import KPIManagement from '@/components/features/KPIManagement'

// 新代码 - 渐进式迁移
import { AdaptiveProjectKPIManagement } from '@/components/adaptive/AdaptiveKPIManagement'

// 或者直接使用新组件
import RefactoredKPIManagement from '@/components/features/RefactoredKPIManagement'`
}

export function IntegrationGuide() {
  const [activeTab, setActiveTab] = useState('steps')
  const [selectedStep, setSelectedStep] = useState('setup')
  const [copiedCode, setCopiedCode] = useState<string | null>(null)

  const copyToClipboard = (code: string, key: string) => {
    navigator.clipboard.writeText(code)
    setCopiedCode(key)
    setTimeout(() => setCopiedCode(null), 2000)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'in-progress':
        return <Circle className="h-5 w-5 text-blue-600 animate-pulse" />
      case 'pending':
        return <Circle className="h-5 w-5 text-gray-400" />
      default:
        return <Circle className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'in-progress':
        return 'bg-blue-100 text-blue-800'
      case 'pending':
        return 'bg-gray-100 text-gray-600'
      default:
        return 'bg-gray-100 text-gray-600'
    }
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-6 w-6" />
            KPI架构集成指南
          </CardTitle>
          <CardDescription>
            详细的迁移步骤和代码示例，帮助您顺利集成新的KPI架构
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="steps">迁移步骤</TabsTrigger>
          <TabsTrigger value="examples">代码示例</TabsTrigger>
          <TabsTrigger value="best-practices">最佳实践</TabsTrigger>
          <TabsTrigger value="troubleshooting">故障排除</TabsTrigger>
        </TabsList>

        <TabsContent value="steps" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 步骤列表 */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">迁移进度</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {INTEGRATION_STEPS.map((step, index) => (
                    <div
                      key={step.id}
                      className={cn(
                        "flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors",
                        selectedStep === step.id ? "bg-primary/10" : "hover:bg-muted/50"
                      )}
                      onClick={() => setSelectedStep(step.id)}
                    >
                      {getStatusIcon(step.status)}
                      <div className="flex-1">
                        <div className="font-medium">{step.title}</div>
                        <div className="text-sm text-muted-foreground">
                          Step {index + 1}
                        </div>
                      </div>
                      <Badge className={getStatusColor(step.status)}>
                        {step.status}
                      </Badge>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>

            {/* 步骤详情 */}
            <div className="lg:col-span-2">
              {INTEGRATION_STEPS.map((step) => (
                selectedStep === step.id && (
                  <Card key={step.id}>
                    <CardHeader>
                      <div className="flex items-center gap-3">
                        {getStatusIcon(step.status)}
                        <div>
                          <CardTitle>{step.title}</CardTitle>
                          <CardDescription>{step.description}</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <h4 className="font-medium">任务清单:</h4>
                        {step.tasks.map((task, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <span className="text-sm">{task}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="examples" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {Object.entries(CODE_EXAMPLES).map(([key, code]) => (
              <Card key={key}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </CardTitle>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(code, key)}
                    >
                      {copiedCode === key ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <pre className="bg-muted p-4 rounded-lg overflow-x-auto text-sm">
                    <code>{code}</code>
                  </pre>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="best-practices" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-yellow-600" />
                  性能最佳实践
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                  <div>
                    <div className="font-medium">使用懒加载</div>
                    <div className="text-sm text-muted-foreground">
                      通过React.lazy()延迟加载组件
                    </div>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                  <div>
                    <div className="font-medium">启用缓存</div>
                    <div className="text-sm text-muted-foreground">
                      使用智能缓存减少API调用
                    </div>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                  <div>
                    <div className="font-medium">虚拟化列表</div>
                    <div className="text-sm text-muted-foreground">
                      对大量数据使用虚拟滚动
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5 text-blue-600" />
                  代码质量
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                  <div>
                    <div className="font-medium">类型安全</div>
                    <div className="text-sm text-muted-foreground">
                      使用TypeScript严格模式
                    </div>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                  <div>
                    <div className="font-medium">错误处理</div>
                    <div className="text-sm text-muted-foreground">
                      实现统一的错误边界
                    </div>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                  <div>
                    <div className="font-medium">测试覆盖</div>
                    <div className="text-sm text-muted-foreground">
                      保持高测试覆盖率
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="troubleshooting" className="space-y-6">
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-600" />
                  常见问题
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border-l-4 border-yellow-500 pl-4">
                  <div className="font-medium">组件不显示或报错</div>
                  <div className="text-sm text-muted-foreground mt-1">
                    检查功能开关是否正确配置，确保相关依赖已安装
                  </div>
                </div>
                <div className="border-l-4 border-blue-500 pl-4">
                  <div className="font-medium">类型错误</div>
                  <div className="text-sm text-muted-foreground mt-1">
                    确保导入了正确的类型定义，检查类型兼容性
                  </div>
                </div>
                <div className="border-l-4 border-red-500 pl-4">
                  <div className="font-medium">性能问题</div>
                  <div className="text-sm text-muted-foreground mt-1">
                    启用缓存和虚拟化，检查是否有内存泄漏
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5 text-blue-600" />
                  获取帮助
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2">
                  <ExternalLink className="h-4 w-4" />
                  <span>查看详细文档</span>
                </div>
                <div className="flex items-center gap-2">
                  <ExternalLink className="h-4 w-4" />
                  <span>提交问题报告</span>
                </div>
                <div className="flex items-center gap-2">
                  <ExternalLink className="h-4 w-4" />
                  <span>联系技术支持</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default IntegrationGuide
