import { createEffect, createMemo, createSignal, For, onCleanup, onMount, Show, Component } from 'solid-js';
import { createStore, produce } from 'solid-js/store';
import { createVirtualizer } from '@tanstack/solid-virtual';
import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

// --- 类型定义 (由 tauri-specta v2 生成) ---
type InboxItemType = 'note' | 'task' | 'idea' | 'link' | 'file';
type Priority = 'low' | 'medium' | 'high' | 'urgent';

interface Tag {
  id: string;
  name: string;
  color: string;
}

interface InboxItem {
  id: string;
  content: string;
  type: InboxItemType;
  status: 'processed' | 'unprocessed';
  priority: Priority;
  tags: Tag[];
  createdAt: string;
}

// --- 模拟 Tauri IPC 调用 ---
// 提示: 在真实应用中，请移除此部分并使用 from '@tauri-apps/api/core' 导入 invoke
const mockDatabase = {
  items: Array.from({ length: 100 }, (_, i) => ({
    id: `item-${i + 1}`,
    content: `这是第 ${i + 1} 条收件箱内容。这是一个关于 [SolidJS](https://solidjs.com) 的笔记，需要尽快处理。`,
    type: (['note', 'task', 'idea', 'link', 'file'] as InboxItemType[])[i % 5],
    status: i % 10 === 0 ? 'processed' : 'unprocessed',
    priority: (['low', 'medium', 'high', 'urgent'] as Priority[])[i % 4],
    tags: [{ id: `tag-${i % 3}`, name: ['灵感', '工作', '学习'][i % 3], color: ['#5E5CE6', '#34C759', '#FF9500'][i % 3] }],
    createdAt: new Date(Date.now() - i * 3600000).toISOString(),
  })),
  tags: [
      { id: 'tag-1', name: '灵感', color: '#5E5CE6' },
      { id: 'tag-2', name: '工作', color: '#34C759' },
      { id: 'tag-3', name: '学习', color: '#FF9500' },
      { id: 'tag-4', name: '项目A', color: '#FF3B30' },
  ]
};

const invoke = async <T,>(cmd: string, args?: any): Promise<T> => {
  console.log(`[Mock Invoke] CMD: ${cmd}`, args);
  await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 400)); // 模拟网络延迟

  switch (cmd) {
    case 'inbox:list':
      const { filter, q } = args;
      let items = mockDatabase.items;
      if (filter?.type && filter.type !== 'all') {
        items = items.filter(item => item.type === filter.type);
      }
      if (filter?.status && filter.status !== 'all') {
        items = items.filter(item => item.status === filter.status);
      }
      if (q) {
        items = items.filter(item => item.content.includes(q) || item.tags.some(t => t.name.includes(q)));
      }
      return { items: items.slice(0, 50), hasMore: items.length > 50 } as T; // 仅模拟第一页
    case 'inbox:create':
      const newItem = { ...args.data, id: `item-${Date.now()}`, createdAt: new Date().toISOString() };
      mockDatabase.items.unshift(newItem);
      return newItem as T;
    case 'inbox:bulk_action':
       console.log(`执行批量操作: ${args.action} on IDs:`, args.ids);
       args.ids.forEach((id: string) => {
           const item = mockDatabase.items.find(i => i.id === id);
           if(item) {
               if(args.action === 'mark_processed') item.status = 'processed';
               if(args.action === 'delete') mockDatabase.items = mockDatabase.items.filter(i => i.id !== id);
           }
       });
       return { success: true } as T;
    case 'tags:suggest':
      return mockDatabase.tags.filter(t => t.name.toLowerCase().includes(args.query.toLowerCase())) as T;
    default:
      throw new Error(`Unknown mock command: ${cmd}`);
  }
};

// --- i18n 模拟 ---
const i18n = { t: (key: string, params?: object) => { /* 实际应为 i18n 库的实现 */ 
    const dict: Record<string, string> = {
        'inbox.title': '收件箱',
        'inbox.captureNew': '快速捕获',
        'inbox.unprocessed': '未处理',
        'inbox.todayProgress': '今日处理进度',
        // ... more keys
    };
    return dict[key] || key;
} };

// --- 实用工具 ---
const cn = (...inputs: (string | undefined | null | boolean)[]) => twMerge(clsx(inputs));

// --- 模拟 solid-ui (shadcn 风格) 组件 ---
// 在真实项目中，这些会从你的组件库中导入

const Button: Component<any> = (props) => {
    const variants = {
        primary: 'bg-blue-600 text-white hover:bg-blue-700',
        secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600',
        destructive: 'bg-red-600 text-white hover:bg-red-700',
        ghost: 'hover:bg-gray-200 dark:hover:bg-gray-700',
    };
    return <button {...props} class={cn("px-4 py-2 text-sm font-medium rounded-lg focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 transition-colors disabled:opacity-50", variants[props.variant || 'primary'], props.class)} />;
};

const Card: Component<any> = (props) => <div {...props} class={cn("bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl shadow-sm p-6", props.class)} />;
const Input: Component<any> = (props) => <input {...props} class={cn("w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500", props.class)} />;
const Select: Component<{ options: {value: string, label: string}[], class?: string, value: any, onChange: any }> = (props) => (
    <select {...props} class={cn("px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500", props.class)}>
        <For each={props.options}>{opt => <option value={opt.value}>{opt.label}</option>}</For>
    </select>
);
const Badge: Component<any> = (props) => <span {...props} class={cn("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium", props.class)} style={{ 'background-color': props.color, color: '#fff' }} />;
const Checkbox: Component<any> = (props) => (
    <div class="flex items-center">
        <input type="checkbox" {...props} class={cn("h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500", props.class)} />
    </div>
);
const Dialog: Component<any> = (props) => (
    <Show when={props.isOpen}>
        <div class="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center" onClick={props.onClose}>
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl w-full max-w-lg m-4" onClick={e => e.stopPropagation()}>
                <div class="p-6 border-b dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{props.title}</h3>
                </div>
                <div class="p-6">{props.children}</div>
                <Show when={props.footer}>
                    <div class="p-6 pt-0 flex justify-end gap-2">{props.footer}</div>
                </Show>
            </div>
        </div>
    </Show>
);

// --- 页面特定子组件 ---

const SkeletonLoader = () => (
    <div class="space-y-4">
        <For each={Array(5)}>{() =>
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl p-4 animate-pulse">
                <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-4"></div>
                <div class="flex items-center space-x-2">
                    <div class="h-5 w-16 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                    <div class="h-5 w-20 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                </div>
            </div>
        }</For>
    </div>
);

const EmptyState: Component<{ onCapture: () => void }> = (props) => (
    <div data-testid="inbox-empty-state" class="text-center py-20">
        <div class="text-5xl mb-4">🎉</div>
        <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-200">收件箱已清空！</h3>
        <p class="text-gray-500 dark:text-gray-400 mt-2 mb-6">所有想法都已整理。按 'a' 随时捕获新内容。</p>
        <Button onClick={props.onCapture}>快速捕获</Button>
    </div>
);

const ErrorState: Component<{ onRetry: () => void }> = (props) => (
    <div data-testid="inbox-error-state" class="text-center py-20">
         <div class="text-5xl mb-4">😥</div>
        <h3 class="text-xl font-semibold text-red-600">数据加载失败</h3>
        <p class="text-gray-500 dark:text-gray-400 mt-2 mb-6">请检查网络或 Tauri 后端连接，然后重试。</p>
        <Button onClick={props.onRetry} variant="secondary">重试</Button>
    </div>
);

const InboxItemCard: Component<{ item: InboxItem, isSelected: boolean, onToggleSelect: (id: string) => void }> = (props) => {
    const priorityClasses: Record<Priority, string> = {
        low: 'border-l-4 border-gray-400',
        medium: 'border-l-4 border-blue-500',
        high: 'border-l-4 border-yellow-500',
        urgent: 'border-l-4 border-red-500',
    };

    return (
        <div data-testid={`inbox-item-${props.item.id}`} class={cn("bg-white dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-700 rounded-2xl p-4 flex items-start gap-4 transition-all hover:shadow-md hover:-translate-y-0.5", priorityClasses[props.item.priority], props.isSelected && 'ring-2 ring-blue-500')}>
            <Checkbox checked={props.isSelected} onChange={() => props.onToggleSelect(props.item.id)} />
            <div class="flex-1">
                <p class="text-gray-800 dark:text-gray-200 mb-3">{props.item.content.substring(0, 150)}</p>
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <For each={props.item.tags}>
                            {tag => <Badge color={tag.color}>{tag.name}</Badge>}
                        </For>
                    </div>
                    <span class="text-xs text-gray-500 dark:text-gray-400">{new Date(props.item.createdAt).toLocaleDateString()}</span>
                </div>
            </div>
        </div>
    );
};

// --- 主页面组件 ---

export default function InboxPage() {
    const [store, setStore] = createStore<{
        items: InboxItem[];
        isLoading: boolean;
        error: string | null;
        selectedItemIds: Set<string>;
        filters: { type: InboxItemType | 'all'; status: 'all' | 'processed' | 'unprocessed'; query: string };
        isCaptureDialogOpen: boolean;
    }>({
        items: [],
        isLoading: true,
        error: null,
        selectedItemIds: new Set(),
        filters: { type: 'all', status: 'unprocessed', query: '' },
        isCaptureDialogOpen: false,
    });
    
    let scrollContainerRef: HTMLDivElement | undefined;
    let searchInputRef: HTMLInputElement | undefined;

    // 数据获取逻辑
    const fetchData = async () => {
        setStore('isLoading', true);
        setStore('error', null);
        try {
            const result = await invoke<{ items: InboxItem[] }>('inbox:list', { filter: { type: store.filters.type, status: store.filters.status }, q: store.filters.query });
            setStore('items', result.items);
        } catch (e: any) {
            setStore('error', e.message || "An unknown error occurred.");
        } finally {
            setStore('isLoading', false);
        }
    };

    // 搜索去抖
    let debounceTimer: NodeJS.Timeout;
    const debouncedFetch = () => {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(fetchData, 300);
    };

    createEffect(() => {
        // 监听过滤器变化
        store.filters;
        debouncedFetch();
    });

    onMount(fetchData);

    // 虚拟列表
    const rowVirtualizer = createVirtualizer({
        count: () => store.items.length,
        getScrollElement: () => scrollContainerRef,
        estimateSize: () => 120, // 估算每个卡片的高度
        overscan: 5,
    });

    // 批量选择逻辑
    const toggleSelection = (id: string) => {
        setStore('selectedItemIds', produce(ids => {
            if (ids.has(id)) {
                ids.delete(id);
            } else {
                ids.add(id);
            }
        }));
    };

    const toggleSelectAll = () => {
        const allSelected = store.selectedItemIds.size === store.items.length;
        setStore('selectedItemIds', produce(ids => {
            ids.clear();
            if (!allSelected) {
                store.items.forEach(item => ids.add(item.id));
            }
        }));
    }

    const selectedCount = createMemo(() => store.selectedItemIds.size);

    // 批量操作
    const handleBulkAction = async (action: 'mark_processed' | 'delete') => {
        const ids = Array.from(store.selectedItemIds);
        if(ids.length === 0) return;

        await invoke('inbox:bulk_action', { action, ids });
        
        // UI乐观更新
        if(action === 'delete') {
            setStore('items', items => items.filter(item => !ids.includes(item.id)));
        } else if (action === 'mark_processed') {
            setStore('items', item => ids.includes(item.id), 'status', 'processed');
        }
        setStore('selectedItemIds', new Set());
    };

    // 快捷键
    const handleKeyDown = (e: KeyboardEvent) => {
        if (document.activeElement?.tagName === 'INPUT' || document.activeElement?.tagName === 'TEXTAREA') return;

        if (e.key === 'a') {
            e.preventDefault();
            setStore('isCaptureDialogOpen', true);
        }
        if (e.key === '/') {
            e.preventDefault();
            searchInputRef?.focus();
        }
        if (e.key === 'Delete' || e.key === 'Backspace') {
            if(selectedCount() > 0) {
                 e.preventDefault();
                 handleBulkAction('delete');
            }
        }
    };

    onMount(() => {
        window.addEventListener('keydown', handleKeyDown);
        onCleanup(() => window.removeEventListener('keydown', handleKeyDown));
    });

    return (
        <div class="h-screen w-screen bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100 flex flex-col p-4 sm:p-6 lg:p-8 gap-6">
            {/* 1. 页眉 */}
            <header class="flex items-center justify-between">
                <h1 class="text-3xl font-bold">{i18n.t('inbox.title')}</h1>
                <Button onClick={() => setStore('isCaptureDialogOpen', true)} data-testid="add-new-button">
                    <span class="mr-2">+</span>
                    {i18n.t('inbox.captureNew')} (a)
                </Button>
            </header>
            
            {/* TODO: 2. 进度面板 */}
            {/* <ProgressPanel /> */}

            {/* 3. 工具栏 */}
            <div class="flex flex-wrap items-center gap-4">
                <Select
                    class="min-w-[120px]"
                    options={[
                        { value: 'all', label: '所有类型' },
                        { value: 'note', label: '笔记' },
                        { value: 'task', label: '任务' },
                        { value: 'idea', label: '想法' },
                        { value: 'link', label: '链接' },
                        { value: 'file', label: '文件' },
                    ]}
                    value={store.filters.type}
                    onChange={(e) => setStore('filters', 'type', e.currentTarget.value as any)}
                />
                <Select
                    class="min-w-[120px]"
                    options={[
                        { value: 'unprocessed', label: '未处理' },
                        { value: 'processed', label: '已处理' },
                        { value: 'all', label: '全部' },
                    ]}
                    value={store.filters.status}
                    onChange={(e) => setStore('filters', 'status', e.currentTarget.value as any)}
                />
                <div class="relative flex-grow max-w-xs">
                    <Input
                        ref={searchInputRef}
                        type="search"
                        placeholder="搜索内容与标签... (/)"
                        value={store.filters.query}
                        onInput={(e) => setStore('filters', 'query', e.currentTarget.value)}
                        class="pl-10"
                    />
                    <svg class="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>
                </div>
                <span class="text-sm text-gray-500 dark:text-gray-400">{store.items.length} 个结果</span>
            </div>

            {/* 4. 批量操作工具栏 */}
            <Show when={selectedCount() > 0}>
                <div class="bg-blue-100 dark:bg-blue-900/50 p-3 rounded-xl flex items-center justify-between gap-4">
                     <div class="flex items-center gap-3">
                        <Checkbox id="select-all" checked={store.selectedItemIds.size > 0 && store.selectedItemIds.size === store.items.length} onChange={toggleSelectAll} />
                        <label for="select-all" class="text-sm font-medium text-blue-800 dark:text-blue-200">已选择 {selectedCount()} 项</label>
                    </div>
                    <div class="flex items-center gap-2">
                        <Button variant="secondary" onClick={() => handleBulkAction('mark_processed')}>标记为已处理</Button>
                        <Button variant="secondary">移动到...</Button>
                        <Button variant="destructive" onClick={() => handleBulkAction('delete')}>删除 (Del)</Button>
                    </div>
                </div>
            </Show>

            {/* 5. 列表区域 */}
            <main ref={scrollContainerRef} class="flex-1 overflow-y-auto" data-testid="inbox-list-container">
                <Show when={!store.isLoading && !store.error && store.items.length > 0}
                    fallback={
                        <Show when={store.isLoading}><SkeletonLoader /></Show>
                    }
                >
                    <div style={{ height: `${rowVirtualizer.getTotalSize()}px`, width: '100%', position: 'relative' }}>
                        <For each={rowVirtualizer.getVirtualItems()}>
                            {virtualItem => (
                                <div
                                    class="absolute top-0 left-0 w-full"
                                    style={{
                                        height: `${virtualItem.size}px`,
                                        transform: `translateY(${virtualItem.start}px)`,
                                        padding: '8px'
                                    }}
                                >
                                    <InboxItemCard
                                        item={store.items[virtualItem.index]}
                                        isSelected={store.selectedItemIds.has(store.items[virtualItem.index].id)}
                                        onToggleSelect={toggleSelection}
                                    />
                                </div>
                            )}
                        </For>
                    </div>
                </Show>
                
                <Show when={!store.isLoading && store.error}>
                    <ErrorState onRetry={fetchData} />
                </Show>

                <Show when={!store.isLoading && !store.error && store.items.length === 0}>
                    <EmptyState onCapture={() => setStore('isCaptureDialogOpen', true)} />
                </Show>
            </main>

            {/* 6. 快速捕获 Dialog */}
            <QuickCaptureDialog 
                isOpen={store.isCaptureDialogOpen}
                onClose={() => setStore('isCaptureDialogOpen', false)}
                onSuccess={() => {
                    setStore('isCaptureDialogOpen', false);
                    fetchData(); // 刷新列表
                }}
            />
        </div>
    );
}

// 快速捕获对话框组件
const QuickCaptureDialog: Component<{ isOpen: boolean, onClose: () => void, onSuccess: () => void }> = (props) => {
    const [content, setContent] = createSignal('');
    const [type, setType] = createSignal<InboxItemType>('note');
    const [priority, setPriority] = createSignal<Priority>('medium');
    const [isSubmitting, setIsSubmitting] = createSignal(false);

    const handleSubmit = async (e: Event) => {
        e.preventDefault();
        if(!content().trim()) return;

        setIsSubmitting(true);
        try {
            await invoke('inbox:create', { data: {
                content: content(),
                type: type(),
                priority: priority(),
                tags: [], // TODO: TagInput component
                status: 'unprocessed'
            }});
            // TODO: show toast success
            props.onSuccess();
            setContent(''); // Reset form
        } catch (error) {
            // TODO: show toast error
            console.error(error);
        } finally {
            setIsSubmitting(false);
        }
    };
    
    // Shift+Enter 提交
    const handleKeyDown = (e: KeyboardEvent) => {
        if(e.key === 'Enter' && e.shiftKey) {
            e.preventDefault();
            handleSubmit(e);
        }
    }

    return (
        <Dialog isOpen={props.isOpen} onClose={props.onClose} title="快速捕获">
            <form onSubmit={handleSubmit} class="space-y-4">
                <textarea
                    rows="5"
                    placeholder="捕获你的想法、任务、笔记..."
                    class="w-full p-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={content()}
                    onInput={e => setContent(e.currentTarget.value)}
                    onKeyDown={handleKeyDown}
                />
                {/* TODO: 替换为带自动补全的 TagInput */}
                <Input placeholder="添加标签..." />
                <div class="grid grid-cols-2 gap-4">
                    <Select
                        options={[
                            { value: 'note', label: '笔记' },
                            { value: 'task', label: '任务' },
                            { value: 'idea', label: '想法' },
                        ]}
                        value={type()}
                        onChange={e => setType(e.currentTarget.value as any)}
                    />
                    <Select
                        options={[
                            { value: 'low', label: '低优先级' },
                            { value: 'medium', label: '中等优先级' },
                            { value: 'high', label: '高优先级' },
                            { value: 'urgent', label: '紧急' },
                        ]}
                        value={priority()}
                        onChange={e => setPriority(e.currentTarget.value as any)}
                    />
                </div>
            </form>
            <div slot="footer" class="p-6 pt-0 flex justify-end items-center gap-4">
                 <span class="text-xs text-gray-500">Shift + Enter 提交</span>
                <Button variant="secondary" onClick={props.onClose}>取消</Button>
                <Button type="submit" onClick={handleSubmit} disabled={!content().trim() || isSubmitting()}>
                    {isSubmitting() ? '提交中...' : '创建'}
                </Button>
            </div>
        </Dialog>
    );
};