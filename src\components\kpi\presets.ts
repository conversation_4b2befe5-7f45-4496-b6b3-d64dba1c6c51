/**
 * KPI预设配置
 * 提供常用的KPI模板和配置选项
 */

import type { KPITemplate, KPIConfig } from './types'

// 默认单位选项
export const DEFAULT_UNITS = [
  '', // 无单位
  '%', // 百分比
  '个', // 数量
  '人', // 人数
  '次', // 次数
  '分', // 评分
  '元', // 金额
  'kg', // 重量
  'h', // 小时
  'min', // 分钟
  'days', // 天数
  'ms', // 毫秒
  'MB', // 存储
  'GB', // 存储
  'L', // 升
  '步', // 步数
  '杯', // 杯数
  '页', // 页数
  'km', // 公里
  '°C', // 温度
]

// 频率选项
export const FREQUENCY_OPTIONS = [
  { 
    value: 'daily', 
    label: 'Daily', 
    description: 'Track every day',
    icon: 'Calendar'
  },
  { 
    value: 'weekly', 
    label: 'Weekly', 
    description: 'Track once per week',
    icon: 'Calendar'
  },
  { 
    value: 'monthly', 
    label: 'Monthly', 
    description: 'Track once per month',
    icon: 'Calendar'
  },
  { 
    value: 'quarterly', 
    label: 'Quarterly', 
    description: 'Track once per quarter',
    icon: 'Calendar'
  }
]

// 方向选项
export const DIRECTION_OPTIONS = [
  {
    value: 'increase',
    label: 'Increase',
    description: 'Higher values are better',
    icon: 'TrendingUp',
    color: 'text-green-600'
  },
  {
    value: 'decrease',
    label: 'Decrease', 
    description: 'Lower values are better',
    icon: 'TrendingDown',
    color: 'text-red-600'
  }
]

// 项目KPI模板
export const PROJECT_KPI_TEMPLATES: KPITemplate[] = [
  // 开发相关
  {
    name: 'Bug修复数',
    description: '本周修复的bug数量',
    unit: '个',
    target: 10,
    direction: 'increase',
    frequency: 'weekly',
    category: 'development'
  },
  {
    name: '代码覆盖率',
    description: '单元测试代码覆盖率',
    unit: '%',
    target: 80,
    direction: 'increase',
    frequency: 'weekly',
    category: 'development'
  },
  {
    name: '响应时间',
    description: 'API平均响应时间',
    unit: 'ms',
    target: 200,
    direction: 'decrease',
    frequency: 'daily',
    category: 'performance'
  },
  {
    name: '错误率',
    description: '系统错误率',
    unit: '%',
    target: 1,
    direction: 'decrease',
    frequency: 'daily',
    category: 'quality'
  },
  
  // 业务相关
  {
    name: '新增用户',
    description: '新注册用户数量',
    unit: '人',
    target: 100,
    direction: 'increase',
    frequency: 'weekly',
    category: 'business'
  },
  {
    name: '用户满意度',
    description: '用户满意度评分',
    unit: '分',
    target: 4.5,
    direction: 'increase',
    frequency: 'monthly',
    category: 'business'
  },
  {
    name: '销售额',
    description: '月度销售额',
    unit: '元',
    target: 10000,
    direction: 'increase',
    frequency: 'monthly',
    category: 'business'
  },
  {
    name: '转化率',
    description: '访客到客户的转化率',
    unit: '%',
    target: 5,
    direction: 'increase',
    frequency: 'weekly',
    category: 'business'
  },
  
  // 运营相关
  {
    name: '页面访问量',
    description: '网站页面访问量',
    unit: '次',
    target: 1000,
    direction: 'increase',
    frequency: 'daily',
    category: 'operations'
  },
  {
    name: '加载时间',
    description: '页面平均加载时间',
    unit: 'ms',
    target: 500,
    direction: 'decrease',
    frequency: 'daily',
    category: 'performance'
  },
  {
    name: '完成率',
    description: '项目任务完成率',
    unit: '%',
    target: 100,
    direction: 'increase',
    frequency: 'weekly',
    category: 'project'
  }
]

// 领域指标模板
export const AREA_METRIC_TEMPLATES: KPITemplate[] = [
  // 健康相关
  {
    name: '体重',
    description: '每日体重记录',
    unit: 'kg',
    target: 65,
    direction: 'decrease',
    frequency: 'daily',
    category: 'health'
  },
  {
    name: '运动时长',
    description: '每日运动时间',
    unit: 'min',
    target: 30,
    direction: 'increase',
    frequency: 'daily',
    category: 'health'
  },
  {
    name: '步数',
    description: '每日步行步数',
    unit: '步',
    target: 10000,
    direction: 'increase',
    frequency: 'daily',
    category: 'health'
  },
  {
    name: '睡眠时长',
    description: '每日睡眠时间',
    unit: 'h',
    target: 8,
    direction: 'increase',
    frequency: 'daily',
    category: 'health'
  },
  {
    name: '水分摄入',
    description: '每日饮水量',
    unit: 'L',
    target: 2,
    direction: 'increase',
    frequency: 'daily',
    category: 'health'
  },
  
  // 学习相关
  {
    name: '学习时长',
    description: '每日学习时间',
    unit: 'h',
    target: 2,
    direction: 'increase',
    frequency: 'daily',
    category: 'learning'
  },
  {
    name: '阅读页数',
    description: '每日阅读页数',
    unit: '页',
    target: 20,
    direction: 'increase',
    frequency: 'daily',
    category: 'learning'
  },
  {
    name: '练习题数',
    description: '每日完成的练习题数量',
    unit: '题',
    target: 10,
    direction: 'increase',
    frequency: 'daily',
    category: 'learning'
  },
  
  // 生活相关
  {
    name: '冥想时长',
    description: '每日冥想时间',
    unit: 'min',
    target: 15,
    direction: 'increase',
    frequency: 'daily',
    category: 'lifestyle'
  },
  {
    name: '屏幕时间',
    description: '每日屏幕使用时间',
    unit: 'h',
    target: 4,
    direction: 'decrease',
    frequency: 'daily',
    category: 'lifestyle'
  },
  {
    name: '咖啡摄入',
    description: '每日咖啡摄入量',
    unit: '杯',
    target: 2,
    direction: 'decrease',
    frequency: 'daily',
    category: 'lifestyle'
  },
  {
    name: '社交时间',
    description: '每周社交活动时间',
    unit: 'h',
    target: 5,
    direction: 'increase',
    frequency: 'weekly',
    category: 'lifestyle'
  },
  
  // 工作相关
  {
    name: '专注时长',
    description: '每日专注工作时间',
    unit: 'h',
    target: 6,
    direction: 'increase',
    frequency: 'daily',
    category: 'work'
  },
  {
    name: '会议时间',
    description: '每日会议时间',
    unit: 'h',
    target: 2,
    direction: 'decrease',
    frequency: 'daily',
    category: 'work'
  },
  {
    name: '任务完成数',
    description: '每日完成的任务数量',
    unit: '个',
    target: 5,
    direction: 'increase',
    frequency: 'daily',
    category: 'work'
  }
]

// 项目KPI配置
export const PROJECT_KPI_CONFIG: KPIConfig = {
  type: 'project',
  templates: PROJECT_KPI_TEMPLATES,
  allowedUnits: DEFAULT_UNITS,
  defaultFrequency: 'weekly',
  defaultDirection: 'increase',
  validationRules: {
    nameRequired: true,
    nameMaxLength: 100,
    valueRequired: true,
    valueMin: 0,
    targetRequired: false,
    unitMaxLength: 20,
    noteMaxLength: 500
  }
}

// 领域指标配置
export const AREA_METRIC_CONFIG: KPIConfig = {
  type: 'area',
  templates: AREA_METRIC_TEMPLATES,
  allowedUnits: DEFAULT_UNITS,
  defaultFrequency: 'daily',
  defaultDirection: 'increase',
  validationRules: {
    nameRequired: true,
    nameMaxLength: 100,
    valueRequired: true,
    valueMin: 0,
    targetRequired: false,
    unitMaxLength: 20,
    noteMaxLength: 500
  }
}

// 根据类型获取配置
export function getKPIConfig(type: 'project' | 'area'): KPIConfig {
  return type === 'project' ? PROJECT_KPI_CONFIG : AREA_METRIC_CONFIG
}

// 根据分类获取模板
export function getTemplatesByCategory(
  type: 'project' | 'area', 
  category?: string
): KPITemplate[] {
  const config = getKPIConfig(type)
  
  if (!category) {
    return config.templates
  }
  
  return config.templates.filter(template => template.category === category)
}

// 获取所有分类
export function getCategories(type: 'project' | 'area'): string[] {
  const config = getKPIConfig(type)
  const categories = new Set(config.templates.map(t => t.category).filter(Boolean))
  return Array.from(categories).sort()
}

// 搜索模板
export function searchTemplates(
  type: 'project' | 'area',
  query: string
): KPITemplate[] {
  const config = getKPIConfig(type)
  const lowerQuery = query.toLowerCase()
  
  return config.templates.filter(template =>
    template.name.toLowerCase().includes(lowerQuery) ||
    template.description?.toLowerCase().includes(lowerQuery) ||
    template.category?.toLowerCase().includes(lowerQuery)
  )
}
