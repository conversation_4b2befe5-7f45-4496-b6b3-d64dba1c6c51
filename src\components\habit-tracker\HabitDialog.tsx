/**
 * 习惯创建/编辑对话框
 * 支持创建新习惯和编辑现有习惯
 */

import { createSignal, createEffect, Show } from 'solid-js'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input, Textarea } from '../ui/Input'
import { Label } from '../ui/label'
import { Badge } from '../ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { cn } from '../../lib/utils'

import type { HabitDialogProps, CreateHabitData, UpdateHabitData, HabitType, HabitFrequency } from './types'

// 预设颜色选项
const PRESET_COLORS = [
  '#3b82f6', // blue
  '#10b981', // emerald
  '#f59e0b', // amber
  '#ef4444', // red
  '#8b5cf6', // violet
  '#06b6d4', // cyan
  '#84cc16', // lime
  '#f97316', // orange
  '#ec4899', // pink
  '#6b7280'  // gray
]

// 习惯类型选项
const HABIT_TYPES: Array<{ value: HabitType; label: string; description: string }> = [
  {
    value: 'boolean',
    label: 'Yes/No',
    description: 'Simple completion tracking (e.g., Did I exercise today?)'
  },
  {
    value: 'numeric',
    label: 'Number',
    description: 'Track specific values (e.g., 8 glasses of water, 30 minutes)'
  },
  {
    value: 'duration',
    label: 'Duration',
    description: 'Track time spent (e.g., 30 minutes of reading)'
  }
]

// 频率选项
const FREQUENCY_OPTIONS: Array<{ value: HabitFrequency; label: string }> = [
  { value: 'daily', label: 'Daily' },
  { value: 'weekly', label: 'Weekly' },
  { value: 'monthly', label: 'Monthly' }
]

export function HabitDialog(props: HabitDialogProps) {
  // 表单状态
  const [formData, setFormData] = createSignal<CreateHabitData>({
    name: '',
    description: '',
    areaId: props.areaId,
    type: 'boolean',
    frequency: 'daily',
    target: 1,
    unit: '',
    color: PRESET_COLORS[0]
  })
  
  const [errors, setErrors] = createSignal<Record<string, string>>({})
  const [loading, setLoading] = createSignal(false)

  // 初始化表单数据
  createEffect(() => {
    if (props.mode === 'edit' && props.habit) {
      setFormData({
        name: props.habit.name,
        description: props.habit.description || '',
        areaId: props.habit.areaId,
        type: props.habit.type,
        frequency: props.habit.frequency,
        target: props.habit.target,
        unit: props.habit.unit || '',
        color: props.habit.color
      })
    }
  })

  // 更新表单字段
  const updateField = (field: keyof CreateHabitData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // 清除相关错误
    if (errors()[field]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}
    const data = formData()

    if (!data.name.trim()) {
      newErrors.name = 'Habit name is required'
    }

    if (data.type === 'numeric' || data.type === 'duration') {
      if (!data.target || data.target <= 0) {
        newErrors.target = 'Target value must be greater than 0'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 提交表单
  const handleSubmit = async (e: Event) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setLoading(true)
    try {
      const data = formData()
      
      // 清理数据
      const submitData: CreateHabitData | UpdateHabitData = {
        ...data,
        name: data.name.trim(),
        description: data.description?.trim() || undefined,
        unit: data.unit?.trim() || undefined
      }

      await props.onSubmit(submitData)
    } catch (error) {
      console.error('Failed to submit habit:', error)
    } finally {
      setLoading(false)
    }
  }

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      areaId: props.areaId,
      type: 'boolean',
      frequency: 'daily',
      target: 1,
      unit: '',
      color: PRESET_COLORS[0]
    })
    setErrors({})
  }

  // 关闭对话框
  const handleClose = () => {
    if (!loading()) {
      resetForm()
      props.onCancel()
    }
  }

  return (
    <Dialog open={props.open} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent class="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle class="flex items-center gap-2">
            <div
              class="w-8 h-8 rounded-lg flex items-center justify-center text-white font-semibold"
              style={{ backgroundColor: formData().color }}
            >
              H
            </div>
            {props.mode === 'create' ? 'Create New Habit' : 'Edit Habit'}
          </DialogTitle>
          <DialogDescription>
            {props.mode === 'create' 
              ? 'Set up a new habit to track your progress'
              : 'Update your habit settings'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} class="space-y-6">
          {/* 基本信息 */}
          <div class="space-y-4">
            <div class="space-y-2">
              <Label for="name">Habit Name *</Label>
              <Input
                id="name"
                placeholder="e.g., Drink 8 glasses of water"
                value={formData().name}
                onInput={(e) => updateField('name', e.currentTarget.value)}
                class={cn(errors().name && 'border-red-500')}
                required
              />
              <Show when={errors().name}>
                <p class="text-sm text-red-500">{errors().name}</p>
              </Show>
            </div>

            <div class="space-y-2">
              <Label for="description">Description (optional)</Label>
              <Textarea
                id="description"
                placeholder="Add more details about this habit..."
                value={formData().description}
                onInput={(e) => updateField('description', e.currentTarget.value)}
                rows={2}
              />
            </div>
          </div>

          {/* 习惯类型 */}
          <div class="space-y-3">
            <Label>Habit Type</Label>
            <div class="grid gap-3">
              {HABIT_TYPES.map(type => (
                <div
                  class={cn(
                    "p-3 border rounded-lg cursor-pointer transition-colors",
                    formData().type === type.value 
                      ? "border-blue-500 bg-blue-50" 
                      : "border-gray-200 hover:border-gray-300"
                  )}
                  onClick={() => updateField('type', type.value)}
                >
                  <div class="flex items-center gap-2 mb-1">
                    <div class={cn(
                      "w-4 h-4 rounded-full border-2",
                      formData().type === type.value 
                        ? "border-blue-500 bg-blue-500" 
                        : "border-gray-300"
                    )}>
                      <Show when={formData().type === type.value}>
                        <div class="w-2 h-2 bg-white rounded-full m-0.5" />
                      </Show>
                    </div>
                    <span class="font-medium">{type.label}</span>
                  </div>
                  <p class="text-sm text-muted-foreground ml-6">{type.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* 频率和目标 */}
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="frequency">Frequency</Label>
              <Select value={formData().frequency} onValueChange={(value) => updateField('frequency', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select frequency" />
                </SelectTrigger>
                <SelectContent>
                  {FREQUENCY_OPTIONS.map(option => (
                    <SelectItem value={option.value}>{option.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Show when={formData().type === 'numeric' || formData().type === 'duration'}>
              <div class="space-y-2">
                <Label for="target">Target Value *</Label>
                <Input
                  id="target"
                  type="number"
                  placeholder="e.g., 8"
                  value={formData().target}
                  onInput={(e) => updateField('target', parseInt(e.currentTarget.value) || 0)}
                  class={cn(errors().target && 'border-red-500')}
                  min="1"
                  required
                />
                <Show when={errors().target}>
                  <p class="text-sm text-red-500">{errors().target}</p>
                </Show>
              </div>
            </Show>
          </div>

          {/* 单位 */}
          <Show when={formData().type === 'numeric' || formData().type === 'duration'}>
            <div class="space-y-2">
              <Label for="unit">Unit (optional)</Label>
              <Input
                id="unit"
                placeholder="e.g., glasses, minutes, pages"
                value={formData().unit}
                onInput={(e) => updateField('unit', e.currentTarget.value)}
              />
            </div>
          </Show>

          {/* 颜色选择 */}
          <div class="space-y-3">
            <Label>Color</Label>
            <div class="flex flex-wrap gap-2">
              {PRESET_COLORS.map(color => (
                <button
                  type="button"
                  class={cn(
                    "w-8 h-8 rounded-full border-2 transition-all",
                    formData().color === color 
                      ? "border-gray-400 scale-110" 
                      : "border-gray-200 hover:border-gray-300"
                  )}
                  style={{ backgroundColor: color }}
                  onClick={() => updateField('color', color)}
                />
              ))}
            </div>
          </div>

          {/* 预览 */}
          <div class="p-4 bg-gray-50 rounded-lg">
            <div class="text-sm font-medium mb-2">Preview</div>
            <div class="flex items-center gap-3">
              <div 
                class="w-4 h-4 rounded-full" 
                style={{ backgroundColor: formData().color }}
              />
              <span class="font-medium">{formData().name || 'Habit Name'}</span>
              <Badge variant="outline" class="text-xs">
                {formData().type}
              </Badge>
              <Badge variant="secondary" class="text-xs">
                {formData().frequency}
              </Badge>
              <Show when={formData().target > 1}>
                <span class="text-sm text-muted-foreground">
                  Target: {formData().target} {formData().unit}
                </span>
              </Show>
            </div>
          </div>
        </form>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={loading()}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading() || !formData().name.trim()}
          >
            {loading() ? 'Saving...' : (props.mode === 'create' ? 'Create Habit' : 'Update Habit')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default HabitDialog
