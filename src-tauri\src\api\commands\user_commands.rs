// User Commands - 用户相关的 Tauri 命令

use crate::api::dto::*;
use crate::application::services::UserAppService;
use crate::shared::errors::AppError;
use crate::shared::types::{Id, QueryParams, Pagination};
use std::sync::Arc;
use tauri::State;

/// 创建用户
#[tauri::command]
pub async fn create_user(
    request: CreateUserRequest,
    user_service: State<'_, Arc<UserAppService>>,
) -> Result<ApiResponse<UserResponse>, String> {
    // 验证请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    // 调用应用服务
    match user_service.create_user(request.username, request.email).await {
        Ok(user) => Ok(ApiResponse::success(UserResponse::from(user))),
        Err(e) => Ok(ApiResponse::error(format!("Failed to create user: {}", e))),
    }
}

/// 根据ID获取用户
#[tauri::command]
pub async fn get_user_by_id(
    id: Id,
    user_service: State<'_, Arc<UserAppService>>,
) -> Result<ApiResponse<UserResponse>, String> {
    match user_service.get_user_by_id(&id).await {
        Ok(user) => Ok(ApiResponse::success(UserResponse::from(user))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("User not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to get user: {}", e))),
    }
}

/// 根据用户名获取用户
#[tauri::command]
pub async fn get_user_by_username(
    username: String,
    user_service: State<'_, Arc<UserAppService>>,
) -> Result<ApiResponse<UserResponse>, String> {
    match user_service.get_user_by_username(&username).await {
        Ok(user) => Ok(ApiResponse::success(UserResponse::from(user))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("User not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to get user: {}", e))),
    }
}

/// 更新用户信息
#[tauri::command]
pub async fn update_user(
    id: Id,
    request: UpdateUserRequest,
    user_service: State<'_, Arc<UserAppService>>,
) -> Result<ApiResponse<UserResponse>, String> {
    // 验证请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    // 调用应用服务
    match user_service.update_user(
        &id,
        request.display_name,
        request.email,
        request.avatar_url,
    ).await {
        Ok(user) => Ok(ApiResponse::success(UserResponse::from(user))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("User not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to update user: {}", e))),
    }
}

/// 更新用户偏好设置
#[tauri::command]
pub async fn update_user_preferences(
    id: Id,
    request: UpdateUserPreferencesRequest,
    user_service: State<'_, Arc<UserAppService>>,
) -> Result<ApiResponse<UserResponse>, String> {
    let preferences = request.into();
    
    match user_service.update_user_preferences(&id, preferences).await {
        Ok(user) => Ok(ApiResponse::success(UserResponse::from(user))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("User not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to update preferences: {}", e))),
    }
}

/// 激活用户
#[tauri::command]
pub async fn activate_user(
    id: Id,
    user_service: State<'_, Arc<UserAppService>>,
) -> Result<ApiResponse<UserResponse>, String> {
    match user_service.activate_user(&id).await {
        Ok(user) => Ok(ApiResponse::success(UserResponse::from(user))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("User not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to activate user: {}", e))),
    }
}

/// 停用用户
#[tauri::command]
pub async fn deactivate_user(
    id: Id,
    user_service: State<'_, Arc<UserAppService>>,
) -> Result<ApiResponse<UserResponse>, String> {
    match user_service.deactivate_user(&id).await {
        Ok(user) => Ok(ApiResponse::success(UserResponse::from(user))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("User not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to deactivate user: {}", e))),
    }
}

/// 删除用户
#[tauri::command]
pub async fn delete_user(
    id: Id,
    user_service: State<'_, Arc<UserAppService>>,
) -> Result<ApiResponse<EmptyResponse>, String> {
    match user_service.delete_user(&id).await {
        Ok(_) => Ok(ApiResponse::success(EmptyResponse)),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("User not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to delete user: {}", e))),
    }
}

/// 获取所有活跃用户
#[tauri::command]
pub async fn get_active_users(
    user_service: State<'_, Arc<UserAppService>>,
) -> Result<ApiResponse<Vec<UserResponse>>, String> {
    match user_service.get_active_users().await {
        Ok(users) => {
            let user_responses: Vec<UserResponse> = users.into_iter()
                .map(UserResponse::from)
                .collect();
            Ok(ApiResponse::success(user_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get active users: {}", e))),
    }
}

/// 分页获取用户列表
#[tauri::command]
pub async fn get_users_with_pagination(
    pagination: PaginationRequest,
    user_service: State<'_, Arc<UserAppService>>,
) -> Result<ApiResponse<UserListResponse>, String> {
    // 验证分页参数
    if let Err(e) = pagination.validate() {
        return Ok(ApiResponse::error(e));
    }

    let query_params = QueryParams {
        pagination: Some(Pagination {
            page: pagination.page,
            size: pagination.size,
        }),
        ..Default::default()
    };

    match user_service.get_users_with_pagination(&query_params).await {
        Ok((users, total)) => {
            let user_responses: Vec<UserResponse> = users.into_iter()
                .map(UserResponse::from)
                .collect();
            
            let response = UserListResponse {
                users: user_responses,
                total,
                page: pagination.page,
                size: pagination.size,
            };
            
            Ok(ApiResponse::success(response))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get users: {}", e))),
    }
}

/// 搜索用户
#[tauri::command]
pub async fn search_users(
    request: SearchRequest,
    user_service: State<'_, Arc<UserAppService>>,
) -> Result<ApiResponse<Vec<UserResponse>>, String> {
    // 验证搜索请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    match user_service.search_users(&request.query).await {
        Ok(users) => {
            let user_responses: Vec<UserResponse> = users.into_iter()
                .map(UserResponse::from)
                .collect();
            Ok(ApiResponse::success(user_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to search users: {}", e))),
    }
}

/// 获取用户统计信息
#[tauri::command]
pub async fn get_user_statistics(
    user_service: State<'_, Arc<UserAppService>>,
) -> Result<ApiResponse<UserStatisticsResponse>, String> {
    match user_service.get_user_statistics().await {
        Ok(stats) => Ok(ApiResponse::success(UserStatisticsResponse::from(stats))),
        Err(e) => Ok(ApiResponse::error(format!("Failed to get user statistics: {}", e))),
    }
}

/// 检查用户名是否可用
#[tauri::command]
pub async fn is_username_available(
    username: String,
    user_service: State<'_, Arc<UserAppService>>,
) -> Result<ApiResponse<UsernameAvailabilityResponse>, String> {
    match user_service.is_username_available(&username).await {
        Ok(available) => {
            let response = UsernameAvailabilityResponse {
                available,
                username,
            };
            Ok(ApiResponse::success(response))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to check username availability: {}", e))),
    }
}

/// 检查邮箱是否可用
#[tauri::command]
pub async fn is_email_available(
    email: String,
    user_service: State<'_, Arc<UserAppService>>,
) -> Result<ApiResponse<EmailAvailabilityResponse>, String> {
    match user_service.is_email_available(&email).await {
        Ok(available) => {
            let response = EmailAvailabilityResponse {
                available,
                email,
            };
            Ok(ApiResponse::success(response))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to check email availability: {}", e))),
    }
}

/// 重置用户偏好设置
#[tauri::command]
pub async fn reset_user_preferences(
    id: Id,
    user_service: State<'_, Arc<UserAppService>>,
) -> Result<ApiResponse<UserResponse>, String> {
    match user_service.reset_user_preferences(&id).await {
        Ok(user) => Ok(ApiResponse::success(UserResponse::from(user))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("User not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to reset preferences: {}", e))),
    }
}

/// 归档用户
#[tauri::command]
pub async fn archive_user(
    id: Id,
    user_service: State<'_, Arc<UserAppService>>,
) -> Result<ApiResponse<UserResponse>, String> {
    match user_service.archive_user(&id).await {
        Ok(user) => Ok(ApiResponse::success(UserResponse::from(user))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("User not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to archive user: {}", e))),
    }
}
