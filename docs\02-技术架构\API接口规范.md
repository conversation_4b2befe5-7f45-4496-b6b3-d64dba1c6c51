# PaoLife API接口规范

## 📋 概述

本文档定义了PaoLife应用的完整API接口规范，基于Tauri命令系统实现前后端通信。所有接口遵循统一的设计原则和错误处理机制。

## 🎯 设计原则

### 1. 接口设计原则
- **RESTful风格**: 遵循REST设计理念
- **统一响应格式**: 所有接口返回统一的响应结构
- **类型安全**: 使用TypeScript类型定义确保类型安全
- **错误处理**: 统一的错误码和错误信息
- **参数验证**: 严格的输入参数验证

### 2. 命名规范
- **命令名称**: 使用snake_case格式，如`get_projects`
- **参数名称**: 使用camelCase格式，如`projectId`
- **响应字段**: 使用camelCase格式，保持与前端一致

### 3. 响应格式
```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    total?: number;
    page?: number;
    pageSize?: number;
    hasMore?: boolean;
  };
}
```

## 🔧 通用类型定义

### 基础类型
```typescript
// 分页参数
interface PaginationParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 过滤参数
interface FilterParams {
  search?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  tags?: string[];
}

// 实体ID类型
type EntityId = string;

// 日期时间类型
type DateTime = string; // ISO 8601 格式
type Date = string;     // YYYY-MM-DD 格式
```

### 状态枚举
```typescript
// 项目状态
enum ProjectStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  AT_RISK = 'at_risk',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ARCHIVED = 'archived'
}

// 任务状态
enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  WAITING = 'waiting',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// 优先级
enum Priority {
  LOW = 1,
  MEDIUM = 2,
  NORMAL = 3,
  HIGH = 4,
  URGENT = 5
}
```

## 🏠 仪表盘API

### 获取仪表盘数据
```typescript
// 命令: get_dashboard_data
interface DashboardRequest {
  dateRange?: {
    start: Date;
    end: Date;
  };
}

interface DashboardResponse {
  stats: {
    projects: {
      total: number;
      inProgress: number;
      completed: number;
      completionRate: number;
    };
    tasks: {
      total: number;
      active: number;
      completed: number;
      overdue: number;
      dueToday: number;
    };
    areas: {
      total: number;
      active: number;
    };
    habits: {
      total: number;
      todayCompleted: number;
      todayCompletionRate: number;
    };
    resources: {
      total: number;
      recentAdded: number;
    };
    inbox: {
      unprocessed: number;
      todayAdded: number;
    };
  };
  todayTasks: TaskSummary[];
  upcomingProjects: ProjectSummary[];
  activeAreas: AreaSummary[];
  recentActivity: ActivitySummary[];
}

interface TaskSummary {
  id: EntityId;
  title: string;
  status: TaskStatus;
  priority: Priority;
  dueDate?: Date;
  projectId?: EntityId;
  projectName?: string;
  isOverdue: boolean;
}

interface ProjectSummary {
  id: EntityId;
  name: string;
  status: ProjectStatus;
  progress: number;
  deadline?: Date;
  daysRemaining?: number;
  tasksCompleted: number;
  tasksTotal: number;
}

interface AreaSummary {
  id: EntityId;
  name: string;
  habitCompletionRate: number;
  recentHabits: HabitRecord[];
}

interface ActivitySummary {
  id: EntityId;
  type: 'project' | 'task' | 'area' | 'habit';
  entityId: EntityId;
  entityName: string;
  action: string;
  timestamp: DateTime;
}
```

### 快速捕获
```typescript
// 命令: quick_capture
interface QuickCaptureRequest {
  content: string;
  tags?: string[];
  priority?: Priority;
  type?: 'note' | 'task' | 'idea' | 'link';
}

interface QuickCaptureResponse {
  id: EntityId;
  processedTags: string[];
  suggestedActions?: {
    type: 'create_project' | 'add_to_project' | 'create_task';
    suggestion: string;
  }[];
}
```

## 📋 项目管理API

### 获取项目列表
```typescript
// 命令: get_projects
interface GetProjectsRequest extends PaginationParams, FilterParams {
  status?: ProjectStatus[];
  areaId?: EntityId;
  includeArchived?: boolean;
}

interface GetProjectsResponse {
  projects: Project[];
  total: number;
}

interface Project {
  id: EntityId;
  name: string;
  description?: string;
  status: ProjectStatus;
  progress: number;
  priority: Priority;
  startDate?: Date;
  deadline?: Date;
  estimatedHours?: number;
  actualHours: number;
  areaId?: EntityId;
  areaName?: string;
  createdBy: EntityId;
  createdAt: DateTime;
  updatedAt: DateTime;
  archivedAt?: DateTime;
  tasksCount: number;
  completedTasksCount: number;
  kpisCount: number;
  deliverablesCount: number;
}
```

### 创建项目
```typescript
// 命令: create_project
interface CreateProjectRequest {
  name: string;
  description?: string;
  areaId?: EntityId;
  priority?: Priority;
  startDate?: Date;
  deadline?: Date;
  estimatedHours?: number;
  templateId?: EntityId;
  templateConfig?: Record<string, any>;
}

interface CreateProjectResponse {
  project: Project;
  createdTasks?: Task[];
  createdKpis?: ProjectKpi[];
  createdDeliverables?: Deliverable[];
}
```

### 更新项目
```typescript
// 命令: update_project
interface UpdateProjectRequest {
  id: EntityId;
  name?: string;
  description?: string;
  status?: ProjectStatus;
  priority?: Priority;
  startDate?: Date;
  deadline?: Date;
  estimatedHours?: number;
  areaId?: EntityId;
}

interface UpdateProjectResponse {
  project: Project;
  statusChanged: boolean;
  progressUpdated: boolean;
}
```

### 获取项目详情
```typescript
// 命令: get_project_detail
interface GetProjectDetailRequest {
  id: EntityId;
  includeTasks?: boolean;
  includeKpis?: boolean;
  includeDeliverables?: boolean;
  includeResources?: boolean;
}

interface GetProjectDetailResponse {
  project: Project;
  tasks?: Task[];
  kpis?: ProjectKpi[];
  deliverables?: Deliverable[];
  resources?: ResourceLink[];
  recentActivity?: ActivitySummary[];
}
```

### 删除项目
```typescript
// 命令: delete_project
interface DeleteProjectRequest {
  id: EntityId;
  archiveOnly?: boolean;
  deleteReason?: string;
}

interface DeleteProjectResponse {
  success: boolean;
  archived: boolean;
  affectedTasks: number;
  affectedResources: number;
}
```

## 📝 任务管理API

### 获取任务列表
```typescript
// 命令: get_tasks
interface GetTasksRequest extends PaginationParams, FilterParams {
  projectId?: EntityId;
  areaId?: EntityId;
  status?: TaskStatus[];
  priority?: Priority[];
  assignedTo?: EntityId;
  dueDateFrom?: Date;
  dueDateTo?: Date;
  parentTaskId?: EntityId;
  includeSubtasks?: boolean;
}

interface GetTasksResponse {
  tasks: Task[];
  total: number;
}

interface Task {
  id: EntityId;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: Priority;
  parentTaskId?: EntityId;
  projectId?: EntityId;
  projectName?: string;
  areaId?: EntityId;
  areaName?: string;
  assignedTo?: EntityId;
  dueDate?: Date;
  estimatedMinutes?: number;
  actualMinutes: number;
  completionPercentage: number;
  sortOrder: number;
  createdBy: EntityId;
  createdAt: DateTime;
  updatedAt: DateTime;
  completedAt?: DateTime;
  tags: string[];
  subtasksCount: number;
  completedSubtasksCount: number;
  isOverdue: boolean;
}
```

### 创建任务
```typescript
// 命令: create_task
interface CreateTaskRequest {
  title: string;
  description?: string;
  projectId?: EntityId;
  areaId?: EntityId;
  parentTaskId?: EntityId;
  priority?: Priority;
  dueDate?: Date;
  estimatedMinutes?: number;
  tags?: string[];
}

interface CreateTaskResponse {
  task: Task;
  parentUpdated?: boolean;
  projectProgressUpdated?: boolean;
}
```

### 更新任务
```typescript
// 命令: update_task
interface UpdateTaskRequest {
  id: EntityId;
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: Priority;
  dueDate?: Date;
  estimatedMinutes?: number;
  actualMinutes?: number;
  completionPercentage?: number;
  parentTaskId?: EntityId;
  tags?: string[];
}

interface UpdateTaskResponse {
  task: Task;
  statusChanged: boolean;
  parentUpdated?: boolean;
  projectProgressUpdated?: boolean;
}
```

### 批量操作任务
```typescript
// 命令: batch_update_tasks
interface BatchUpdateTasksRequest {
  taskIds: EntityId[];
  updates: {
    status?: TaskStatus;
    priority?: Priority;
    projectId?: EntityId;
    areaId?: EntityId;
    tags?: string[];
  };
}

interface BatchUpdateTasksResponse {
  updatedTasks: Task[];
  affectedProjects: EntityId[];
  errors?: {
    taskId: EntityId;
    error: string;
  }[];
}
```

## 🏠 领域管理API

### 获取领域列表
```typescript
// 命令: get_areas
interface GetAreasRequest extends PaginationParams, FilterParams {
  isActive?: boolean;
  includeArchived?: boolean;
}

interface GetAreasResponse {
  areas: Area[];
  total: number;
}

interface Area {
  id: EntityId;
  name: string;
  description?: string;
  standard?: string;
  iconName?: string;
  colorHex?: string;
  sortOrder: number;
  isActive: boolean;
  createdBy: EntityId;
  createdAt: DateTime;
  updatedAt: DateTime;
  archivedAt?: DateTime;
  projectsCount: number;
  activeProjectsCount: number;
  habitsCount: number;
  activeHabitsCount: number;
  metricsCount: number;
}
```

### 创建领域
```typescript
// 命令: create_area
interface CreateAreaRequest {
  name: string;
  description?: string;
  standard?: string;
  iconName?: string;
  colorHex?: string;
  sortOrder?: number;
}

interface CreateAreaResponse {
  area: Area;
}
```

### 获取领域详情
```typescript
// 命令: get_area_detail
interface GetAreaDetailRequest {
  id: EntityId;
  includeProjects?: boolean;
  includeHabits?: boolean;
  includeMetrics?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

interface GetAreaDetailResponse {
  area: Area;
  projects?: Project[];
  habits?: Habit[];
  metrics?: AreaMetric[];
  habitStats?: {
    totalHabits: number;
    activeHabits: number;
    todayCompletionRate: number;
    weeklyCompletionRate: number;
    streaks: HabitStreak[];
  };
  metricStats?: {
    totalMetrics: number;
    activeMetrics: number;
    recentRecords: AreaMetricRecord[];
  };
}

interface Habit {
  id: EntityId;
  areaId: EntityId;
  name: string;
  description?: string;
  targetFrequency: number;
  frequencyUnit: 'daily' | 'weekly' | 'monthly';
  difficultyLevel: number;
  rewardPoints: number;
  isActive: boolean;
  createdAt: DateTime;
  updatedAt: DateTime;
  currentStreak: number;
  longestStreak: number;
  completionRate: number;
}

interface AreaMetric {
  id: EntityId;
  areaId: EntityId;
  name: string;
  description?: string;
  currentValue: number;
  targetValue?: number;
  unit?: string;
  trackingType: 'manual' | 'habit_based' | 'automatic';
  direction: 'higher_better' | 'lower_better' | 'target_value';
  isActive: boolean;
  createdAt: DateTime;
  updatedAt: DateTime;
  progressPercentage: number;
  trend: 'up' | 'down' | 'stable';
}
```

## 📥 收件箱API

### 获取收件箱项目
```typescript
// 命令: get_inbox_notes
interface GetInboxNotesRequest extends PaginationParams, FilterParams {
  processingStatus?: 'unprocessed' | 'processing' | 'processed' | 'archived';
  noteType?: 'quick_note' | 'idea' | 'task' | 'reference' | 'meeting_note';
  priority?: Priority[];
}

interface GetInboxNotesResponse {
  notes: InboxNote[];
  total: number;
  stats: {
    unprocessed: number;
    processing: number;
    processed: number;
    byType: Record<string, number>;
  };
}

interface InboxNote {
  id: EntityId;
  content: string;
  noteType: 'quick_note' | 'idea' | 'task' | 'reference' | 'meeting_note';
  source: 'manual' | 'email' | 'web_clipper' | 'voice';
  processingStatus: 'unprocessed' | 'processing' | 'processed' | 'archived';
  processedIntoType?: 'project' | 'task' | 'area' | 'resource';
  processedIntoId?: EntityId;
  priority: Priority;
  tags: string[];
  createdBy: EntityId;
  createdAt: DateTime;
  processedAt?: DateTime;
  processedBy?: EntityId;
}
```

### 处理收件箱项目
```typescript
// 命令: process_inbox_note
interface ProcessInboxNoteRequest {
  id: EntityId;
  action: 'convert_to_project' | 'convert_to_task' | 'add_to_project' | 'add_to_area' | 'move_to_resources' | 'archive';
  targetId?: EntityId;
  additionalData?: {
    projectName?: string;
    taskTitle?: string;
    areaId?: EntityId;
    resourcePath?: string;
  };
}

interface ProcessInboxNoteResponse {
  note: InboxNote;
  createdEntity?: {
    type: 'project' | 'task' | 'resource';
    id: EntityId;
    name: string;
  };
  success: boolean;
}
```

## 📚 资源库API

### 获取文档列表
```typescript
// 命令: get_documents
interface GetDocumentsRequest extends PaginationParams {
  path?: string;
  search?: string;
  tags?: string[];
  fileType?: string[];
  modifiedAfter?: DateTime;
  includeOrphaned?: boolean;
}

interface GetDocumentsResponse {
  documents: DocumentMetadata[];
  total: number;
  folders: FolderInfo[];
}

interface DocumentMetadata {
  id: EntityId;
  filePath: string;
  title?: string;
  wordCount: number;
  characterCount: number;
  lastModified: DateTime;
  fileHash: string;
  tags: string[];
  linksCount: number;
  backlinksCount: number;
  isOrphaned: boolean;
  syncStatus: 'synced' | 'modified' | 'conflict' | 'missing';
  createdAt: DateTime;
  updatedAt: DateTime;
}

interface FolderInfo {
  path: string;
  name: string;
  filesCount: number;
  subFoldersCount: number;
  lastModified: DateTime;
}
```

### 获取文档内容
```typescript
// 命令: get_document_content
interface GetDocumentContentRequest {
  filePath: string;
  includeLinks?: boolean;
  includeBacklinks?: boolean;
}

interface GetDocumentContentResponse {
  content: string;
  metadata: DocumentMetadata;
  links?: DocumentLink[];
  backlinks?: DocumentLink[];
  relatedDocuments?: DocumentMetadata[];
}

interface DocumentLink {
  id: EntityId;
  sourcePath: string;
  targetPath: string;
  linkType: 'wikilink' | 'reference' | 'embed' | 'citation';
  displayText?: string;
  contextText?: string;
  lineNumber?: number;
  createdAt: DateTime;
}
```

### 创建/更新文档
```typescript
// 命令: save_document
interface SaveDocumentRequest {
  filePath: string;
  content: string;
  title?: string;
  tags?: string[];
  createDirectories?: boolean;
}

interface SaveDocumentResponse {
  metadata: DocumentMetadata;
  linksExtracted: DocumentLink[];
  tagsExtracted: string[];
  isNewFile: boolean;
}
```

## 🔍 搜索API

### 全文搜索
```typescript
// 命令: search_global
interface SearchGlobalRequest {
  query: string;
  filters?: {
    entityTypes?: ('project' | 'task' | 'area' | 'habit' | 'document' | 'note')[];
    dateRange?: {
      start: Date;
      end: Date;
    };
    tags?: string[];
    status?: string[];
  };
  pagination?: PaginationParams;
  highlight?: boolean;
}

interface SearchGlobalResponse {
  results: SearchResult[];
  total: number;
  suggestions?: string[];
  facets?: {
    entityTypes: Record<string, number>;
    tags: Record<string, number>;
    status: Record<string, number>;
  };
}

interface SearchResult {
  id: EntityId;
  entityType: 'project' | 'task' | 'area' | 'habit' | 'document' | 'note';
  title: string;
  content: string;
  highlights?: string[];
  score: number;
  metadata: {
    path?: string;
    status?: string;
    tags?: string[];
    lastModified?: DateTime;
  };
}
```

### 搜索建议
```typescript
// 命令: get_search_suggestions
interface GetSearchSuggestionsRequest {
  query: string;
  limit?: number;
  types?: ('query' | 'tag' | 'title' | 'auto_complete')[];
}

interface GetSearchSuggestionsResponse {
  suggestions: SearchSuggestion[];
}

interface SearchSuggestion {
  text: string;
  type: 'query' | 'tag' | 'title' | 'auto_complete';
  score: number;
  usageCount: number;
}
```

## 🔔 通知系统API

### 获取通知列表
```typescript
// 命令: get_notifications
interface GetNotificationsRequest extends PaginationParams {
  isRead?: boolean;
  notificationType?: ('info' | 'warning' | 'error' | 'success')[];
  dateFrom?: DateTime;
  dateTo?: DateTime;
}

interface GetNotificationsResponse {
  notifications: Notification[];
  total: number;
  unreadCount: number;
}

interface Notification {
  id: EntityId;
  ruleId?: EntityId;
  title: string;
  message: string;
  notificationType: 'info' | 'warning' | 'error' | 'success';
  isRead: boolean;
  scheduledAt: DateTime;
  sentAt?: DateTime;
  userId: EntityId;
  createdAt: DateTime;
  actionUrl?: string;
  actionText?: string;
}
```

### 标记通知已读
```typescript
// 命令: mark_notifications_read
interface MarkNotificationsReadRequest {
  notificationIds?: EntityId[];
  markAllAsRead?: boolean;
}

interface MarkNotificationsReadResponse {
  markedCount: number;
  remainingUnreadCount: number;
}
```

## ⚙️ 设置API

### 获取用户设置
```typescript
// 命令: get_user_settings
interface GetUserSettingsRequest {
  categories?: string[];
}

interface GetUserSettingsResponse {
  settings: UserSetting[];
  profile: UserProfile;
}

interface UserSetting {
  id: EntityId;
  userId: EntityId;
  settingKey: string;
  settingValue: string;
  settingType: 'string' | 'number' | 'boolean' | 'json';
  createdAt: DateTime;
  updatedAt: DateTime;
}

interface UserProfile {
  id: EntityId;
  username: string;
  email?: string;
  fullName?: string;
  avatarUrl?: string;
  timezone: string;
  language: string;
  isActive: boolean;
  createdAt: DateTime;
  lastLoginAt?: DateTime;
}
```

### 更新用户设置
```typescript
// 命令: update_user_settings
interface UpdateUserSettingsRequest {
  settings: {
    key: string;
    value: string;
    type: 'string' | 'number' | 'boolean' | 'json';
  }[];
  profile?: {
    fullName?: string;
    avatarUrl?: string;
    timezone?: string;
    language?: string;
  };
}

interface UpdateUserSettingsResponse {
  updatedSettings: UserSetting[];
  updatedProfile?: UserProfile;
}
```

## 📊 统计分析API

### 获取项目统计
```typescript
// 命令: get_project_analytics
interface GetProjectAnalyticsRequest {
  projectId?: EntityId;
  dateRange: {
    start: Date;
    end: Date;
  };
  metrics?: ('progress' | 'tasks' | 'time' | 'kpis')[];
}

interface GetProjectAnalyticsResponse {
  projectStats: ProjectStats;
  timeSeriesData: TimeSeriesData[];
  kpiTrends: KpiTrend[];
}

interface ProjectStats {
  totalProjects: number;
  completedProjects: number;
  averageCompletionTime: number;
  totalTasks: number;
  completedTasks: number;
  totalTimeSpent: number;
  averageTaskTime: number;
}

interface TimeSeriesData {
  date: Date;
  projectsCreated: number;
  projectsCompleted: number;
  tasksCompleted: number;
  timeSpent: number;
}

interface KpiTrend {
  kpiId: EntityId;
  kpiName: string;
  values: {
    date: Date;
    value: number;
    target?: number;
  }[];
  trend: 'up' | 'down' | 'stable';
  changePercentage: number;
}
```

## 🚨 错误处理

### 错误码定义
```typescript
enum ErrorCode {
  // 通用错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS = 'RESOURCE_ALREADY_EXISTS',

  // 数据库错误
  DATABASE_ERROR = 'DATABASE_ERROR',
  DATABASE_CONNECTION_ERROR = 'DATABASE_CONNECTION_ERROR',
  CONSTRAINT_VIOLATION = 'CONSTRAINT_VIOLATION',

  // 文件系统错误
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  FILE_ACCESS_DENIED = 'FILE_ACCESS_DENIED',
  FILE_ALREADY_EXISTS = 'FILE_ALREADY_EXISTS',
  INVALID_FILE_PATH = 'INVALID_FILE_PATH',

  // 业务逻辑错误
  INVALID_PROJECT_STATUS = 'INVALID_PROJECT_STATUS',
  TASK_CIRCULAR_DEPENDENCY = 'TASK_CIRCULAR_DEPENDENCY',
  HABIT_ALREADY_COMPLETED_TODAY = 'HABIT_ALREADY_COMPLETED_TODAY',
  TEMPLATE_INSTANTIATION_FAILED = 'TEMPLATE_INSTANTIATION_FAILED',

  // 搜索错误
  SEARCH_INDEX_ERROR = 'SEARCH_INDEX_ERROR',
  INVALID_SEARCH_QUERY = 'INVALID_SEARCH_QUERY',

  // 同步错误
  SYNC_CONFLICT = 'SYNC_CONFLICT',
  BACKUP_FAILED = 'BACKUP_FAILED',
  RESTORE_FAILED = 'RESTORE_FAILED'
}
```

### 错误响应示例
```typescript
// 验证错误示例
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "输入参数验证失败",
    "details": {
      "field": "projectName",
      "constraint": "项目名称不能为空",
      "value": ""
    }
  }
}

// 资源未找到错误示例
{
  "success": false,
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "项目不存在",
    "details": {
      "resourceType": "project",
      "resourceId": "proj_123456"
    }
  }
}

// 业务逻辑错误示例
{
  "success": false,
  "error": {
    "code": "TASK_CIRCULAR_DEPENDENCY",
    "message": "任务不能设置自己为父任务",
    "details": {
      "taskId": "task_123456",
      "parentTaskId": "task_123456"
    }
  }
}
```

## 📝 接口调用示例

### 创建项目示例
```typescript
// 前端调用
import { invoke } from '@tauri-apps/api/tauri';

const createProject = async (projectData: CreateProjectRequest) => {
  try {
    const response = await invoke<ApiResponse<CreateProjectResponse>>('create_project', {
      ...projectData
    });

    if (response.success) {
      console.log('项目创建成功:', response.data.project);
      return response.data;
    } else {
      console.error('项目创建失败:', response.error);
      throw new Error(response.error.message);
    }
  } catch (error) {
    console.error('API调用失败:', error);
    throw error;
  }
};

// 使用示例
const newProject = await createProject({
  name: '新项目',
  description: '项目描述',
  areaId: 'area_123',
  priority: Priority.HIGH,
  deadline: '2024-12-31'
});
```

---

**文档版本**: 1.0
**创建日期**: 2024年12月
**最后更新**: 2024年12月
**维护者**: 技术团队