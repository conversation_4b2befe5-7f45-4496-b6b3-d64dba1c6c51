/**
 * 习惯追踪高级使用示例
 * 展示高级功能和自定义配置
 */

import { createSignal, createEffect, onMount, For, Show } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card'
import { Button } from '../../ui/button'
import { Badge } from '../../ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../ui/tabs'
import { 
  Target, 
  BarChart3,
  Calendar,
  Settings,
  Trophy,
  Flame,
  TrendingUp,
  Users,
  Bell
} from 'lucide-solid'

// 导入习惯追踪组件
import { 
  HabitTracker,
  HabitStatistics,
  HabitChart,
  createHabitDataSource,
  calculateHabitSummary,
  getCompletionGrade,
  type BaseHabit,
  type HabitRecord,
  type HabitEventHandlers,
  type HabitTrackerConfig,
  STREAK_MILESTONES
} from '../index'

interface AdvancedExampleProps {
  areaId: string
  areaName?: string
  userId?: string
  className?: string
}

export function AdvancedExample(props: AdvancedExampleProps) {
  // 状态管理
  const [activeTab, setActiveTab] = createSignal('dashboard')
  const [habits, setHabits] = createSignal<BaseHabit[]>([])
  const [records, setRecords] = createSignal<HabitRecord[]>([])
  const [achievements, setAchievements] = createSignal<any[]>([])
  const [notifications, setNotifications] = createSignal<any[]>([])

  // 创建数据源
  const dataSource = createHabitDataSource()

  // 高级配置
  const advancedConfig: HabitTrackerConfig = {
    // 显示选项
    showStatistics: true,
    showProgress: true,
    showCalendar: true,
    showChart: true,
    showStreak: true,
    
    // 交互选项
    allowCreate: true,
    allowEdit: true,
    allowDelete: true,
    allowBulkOperations: true,
    
    // 布局选项
    layout: 'grid',
    calendarView: 'month',
    chartType: 'heatmap',
    
    // 功能选项
    enableReminders: true,
    enableNotes: true,
    enableStreaks: true,
    enableGoals: true,
    
    // 样式选项
    compactMode: false,
    showColors: true,
    animateProgress: true,
    
    // 数据选项
    maxRecords: 365,
    defaultView: 'today',
    
    // 主题
    theme: 'auto'
  }

  // 高级事件处理器
  const eventHandlers: HabitEventHandlers = {
    onCreate: (habit) => {
      console.log('Habit created:', habit)
      addNotification({
        type: 'success',
        title: 'New Habit Created',
        message: `"${habit.name}" has been added to your routine`,
        timestamp: new Date()
      })
      
      // 分析和建议
      analyzeHabitCreation(habit)
    },
    
    onUpdate: (habit, changes) => {
      console.log('Habit updated:', habit, changes)
      addNotification({
        type: 'info',
        title: 'Habit Updated',
        message: `"${habit.name}" settings have been updated`,
        timestamp: new Date()
      })
    },
    
    onRecord: (record) => {
      console.log('Record created:', record)
      
      if (record.completed) {
        // 检查成就
        checkAchievements(record)
        
        // 更新统计
        updateStatistics()
        
        addNotification({
          type: 'success',
          title: 'Progress Recorded',
          message: 'Great job! Keep building your streak! 🔥',
          timestamp: new Date()
        })
      }
    },
    
    onStreakAchieved: (habitId, streak) => {
      console.log('Streak achieved:', habitId, streak)
      
      const milestone = STREAK_MILESTONES.find(m => m.days === streak)
      if (milestone) {
        addAchievement({
          type: 'streak',
          title: milestone.title,
          description: `Achieved ${streak}-day streak`,
          emoji: milestone.emoji,
          habitId,
          timestamp: new Date()
        })
        
        addNotification({
          type: 'achievement',
          title: 'New Achievement Unlocked!',
          message: `${milestone.emoji} ${milestone.title} - ${streak} days streak!`,
          timestamp: new Date()
        })
      }
    },
    
    onGoalReached: (habitId, progress) => {
      console.log('Goal reached:', habitId, progress)
      
      addAchievement({
        type: 'goal',
        title: 'Goal Achieved',
        description: 'Monthly goal completed',
        emoji: '🎯',
        habitId,
        timestamp: new Date()
      })
    },
    
    onError: (error, context) => {
      console.error('Error:', error, context)
      addNotification({
        type: 'error',
        title: 'Error',
        message: error.message,
        timestamp: new Date()
      })
    }
  }

  // 添加通知
  const addNotification = (notification: any) => {
    setNotifications(prev => [notification, ...prev.slice(0, 9)]) // 保留最近10条
  }

  // 添加成就
  const addAchievement = (achievement: any) => {
    setAchievements(prev => [achievement, ...prev])
  }

  // 分析习惯创建
  const analyzeHabitCreation = (habit: BaseHabit) => {
    const currentHabits = habits()
    
    // 检查是否有相似习惯
    const similarHabits = currentHabits.filter(h => 
      h.type === habit.type || h.frequency === habit.frequency
    )
    
    if (similarHabits.length > 0) {
      addNotification({
        type: 'tip',
        title: 'Tip',
        message: `You have ${similarHabits.length} similar habits. Consider grouping them for better tracking.`,
        timestamp: new Date()
      })
    }
    
    // 建议最佳实践
    if (currentHabits.length === 0) {
      addNotification({
        type: 'tip',
        title: 'Welcome!',
        message: 'Start with small, achievable goals. Consistency is key to building lasting habits.',
        timestamp: new Date()
      })
    }
  }

  // 检查成就
  const checkAchievements = (record: HabitRecord) => {
    const habit = habits().find(h => h.id === record.habitId)
    if (!habit) return
    
    // 检查完美日成就
    const today = record.date
    const todayRecords = records().filter(r => r.date === today && r.completed)
    const activeHabits = habits().filter(h => h.isActive)
    
    if (todayRecords.length === activeHabits.length && activeHabits.length > 0) {
      addAchievement({
        type: 'perfect_day',
        title: 'Perfect Day',
        description: 'Completed all habits today',
        emoji: '⭐',
        timestamp: new Date()
      })
    }
  }

  // 更新统计
  const updateStatistics = () => {
    // 这里可以实现实时统计更新
    console.log('Statistics updated')
  }

  // 加载数据
  const loadData = async () => {
    try {
      const habitsData = await dataSource.getHabits(props.areaId)
      setHabits(habitsData)
      
      // 加载记录
      const allRecords: HabitRecord[] = []
      for (const habit of habitsData) {
        const habitRecords = await dataSource.getRecords(habit.id, {
          start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          end: new Date()
        })
        allRecords.push(...habitRecords)
      }
      setRecords(allRecords)
      
    } catch (error) {
      console.error('Failed to load data:', error)
    }
  }

  // 计算摘要统计
  const summary = () => calculateHabitSummary(habits(), records())
  const completionGrade = () => getCompletionGrade(summary().averageCompletion)

  // 初始化
  onMount(() => {
    loadData()
  })

  return (
    <div class="space-y-6">
      {/* 高级仪表板 */}
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 主要统计 */}
        <div class="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle class="flex items-center gap-2">
                <Target class="h-5 w-5" />
                Habit Dashboard
                <Badge variant="outline">Advanced</Badge>
              </CardTitle>
              <CardDescription>
                Comprehensive habit tracking with analytics and achievements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                  <div class="text-2xl font-bold text-blue-600">{summary().activeHabits}</div>
                  <div class="text-sm text-muted-foreground">Active Habits</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-green-600">{summary().todayCompletion}%</div>
                  <div class="text-sm text-muted-foreground">Today's Progress</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold text-orange-600">{summary().bestStreak}</div>
                  <div class="text-sm text-muted-foreground">Best Streak</div>
                </div>
                <div class="text-center">
                  <div class={`text-2xl font-bold ${completionGrade().color.split(' ')[0]}`}>
                    {completionGrade().grade}
                  </div>
                  <div class="text-sm text-muted-foreground">Overall Grade</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 通知面板 */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle class="flex items-center gap-2">
                <Bell class="h-5 w-5" />
                Notifications
                <Badge variant="secondary">{notifications().length}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent class="space-y-3 max-h-60 overflow-y-auto">
              <Show when={notifications().length === 0}>
                <p class="text-sm text-muted-foreground text-center py-4">
                  No notifications
                </p>
              </Show>
              
              <For each={notifications().slice(0, 5)}>
                {(notification) => (
                  <div class="p-2 bg-gray-50 rounded-lg">
                    <div class="flex items-center gap-2 mb-1">
                      <div class={`w-2 h-2 rounded-full ${
                        notification.type === 'success' ? 'bg-green-500' :
                        notification.type === 'error' ? 'bg-red-500' :
                        notification.type === 'achievement' ? 'bg-yellow-500' :
                        'bg-blue-500'
                      }`} />
                      <span class="text-xs font-medium">{notification.title}</span>
                    </div>
                    <p class="text-xs text-muted-foreground">{notification.message}</p>
                  </div>
                )}
              </For>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 标签页内容 */}
      <Tabs value={activeTab()} onValueChange={setActiveTab}>
        <TabsList class="grid w-full grid-cols-4">
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="achievements">Achievements</TabsTrigger>
          <TabsTrigger value="social">Social</TabsTrigger>
        </TabsList>

        {/* 仪表板 */}
        <TabsContent value="dashboard">
          <HabitTracker
            areaId={props.areaId}
            dataSource={dataSource}
            config={advancedConfig}
            eventHandlers={eventHandlers}
            className={props.className}
          />
        </TabsContent>

        {/* 分析 */}
        <TabsContent value="analytics">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <HabitChart
              habits={habits()}
              records={records()}
              type="heatmap"
              timeRange="month"
              showLegend={true}
              height={300}
            />
            
            <HabitChart
              habits={habits()}
              records={records()}
              type="progress"
              timeRange="month"
              showLegend={true}
              height={300}
            />
          </div>
        </TabsContent>

        {/* 成就 */}
        <TabsContent value="achievements">
          <Card>
            <CardHeader>
              <CardTitle class="flex items-center gap-2">
                <Trophy class="h-5 w-5" />
                Achievements
                <Badge variant="secondary">{achievements().length}</Badge>
              </CardTitle>
              <CardDescription>
                Your habit tracking milestones and accomplishments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Show when={achievements().length === 0}>
                <div class="text-center py-8 text-muted-foreground">
                  <Trophy class="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No achievements yet</p>
                  <p class="text-sm">Complete habits to unlock achievements!</p>
                </div>
              </Show>
              
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <For each={achievements()}>
                  {(achievement) => (
                    <Card class="border-l-4 border-l-yellow-500">
                      <CardContent class="p-4">
                        <div class="flex items-center gap-3">
                          <div class="text-2xl">{achievement.emoji}</div>
                          <div>
                            <h4 class="font-medium">{achievement.title}</h4>
                            <p class="text-sm text-muted-foreground">
                              {achievement.description}
                            </p>
                            <p class="text-xs text-muted-foreground mt-1">
                              {achievement.timestamp.toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </For>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 社交功能 */}
        <TabsContent value="social">
          <Card>
            <CardHeader>
              <CardTitle class="flex items-center gap-2">
                <Users class="h-5 w-5" />
                Social Features
                <Badge variant="outline">Coming Soon</Badge>
              </CardTitle>
              <CardDescription>
                Share progress and compete with friends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div class="text-center py-8 text-muted-foreground">
                <Users class="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Social features coming soon!</p>
                <p class="text-sm">Share your streaks and compete with friends</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default AdvancedExample
