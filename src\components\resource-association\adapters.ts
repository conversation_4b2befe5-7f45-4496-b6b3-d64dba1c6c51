/**
 * 资源关联数据源适配器
 * 将现有的API接口适配为统一的资源数据源接口
 */

import type {
  ResourceDataSource,
  UnifiedResource,
  MarkdownResource,
  LinkResource,
  FileResource,
  ReferenceResource,
  BidirectionalLink,
  ResourceStatistics,
  ResourceFilter,
  FileUploadData,
  LinkCreateData,
  MarkdownAssociationData,
  QueryOptions,
  EntityType
} from './types'

// 导入现有的API
// 注意：这些导入路径需要根据实际项目结构调整
import { databaseApi } from '../../lib/api'
import { fileService } from '../../services/fileService'
import { linkService } from '../../services/linkService'
import { markdownService } from '../../services/markdownService'

/**
 * 项目资源数据源适配器
 */
export class ProjectResourceDataSource implements ResourceDataSource {
  async getResources(entityType: EntityType, entityId: string, filter?: ResourceFilter): Promise<UnifiedResource[]> {
    try {
      const resources: UnifiedResource[] = []
      
      // 获取Markdown文档
      if (!filter?.type || filter.type.includes('markdown')) {
        const markdownDocs = await databaseApi.getProjectDocuments(entityId)
        resources.push(...markdownDocs.map(this.convertToMarkdownResource))
      }
      
      // 获取外部链接
      if (!filter?.type || filter.type.includes('link')) {
        const links = await databaseApi.getProjectLinks(entityId)
        resources.push(...links.map(this.convertToLinkResource))
      }
      
      // 获取文件附件
      if (!filter?.type || filter.type.includes('file') || filter?.type.includes('attachment')) {
        const files = await databaseApi.getProjectFiles(entityId)
        resources.push(...files.map(this.convertToFileResource))
      }
      
      // 应用过滤器
      return this.applyFilter(resources, filter)
    } catch (error) {
      console.error('Failed to get project resources:', error)
      throw error
    }
  }

  async getResource(id: string): Promise<UnifiedResource | null> {
    try {
      // 尝试从不同类型的资源中查找
      const [markdownDoc, link, file] = await Promise.allSettled([
        databaseApi.getProjectDocument(id),
        databaseApi.getProjectLink(id),
        databaseApi.getProjectFile(id)
      ])
      
      if (markdownDoc.status === 'fulfilled' && markdownDoc.value) {
        return this.convertToMarkdownResource(markdownDoc.value)
      }
      
      if (link.status === 'fulfilled' && link.value) {
        return this.convertToLinkResource(link.value)
      }
      
      if (file.status === 'fulfilled' && file.value) {
        return this.convertToFileResource(file.value)
      }
      
      return null
    } catch (error) {
      console.error('Failed to get resource:', error)
      throw error
    }
  }

  async createResource(data: any): Promise<UnifiedResource> {
    // 这个方法通常不直接使用，而是通过具体的创建方法
    throw new Error('Use specific create methods instead')
  }

  async updateResource(id: string, data: Partial<any>): Promise<UnifiedResource> {
    try {
      // 首先确定资源类型
      const resource = await this.getResource(id)
      if (!resource) {
        throw new Error('Resource not found')
      }
      
      switch (resource.type) {
        case 'markdown':
          const updatedDoc = await databaseApi.updateProjectDocument(id, data)
          return this.convertToMarkdownResource(updatedDoc)
        case 'link':
          const updatedLink = await databaseApi.updateProjectLink(id, data)
          return this.convertToLinkResource(updatedLink)
        case 'file':
        case 'attachment':
          const updatedFile = await databaseApi.updateProjectFile(id, data)
          return this.convertToFileResource(updatedFile)
        default:
          throw new Error('Unsupported resource type')
      }
    } catch (error) {
      console.error('Failed to update resource:', error)
      throw error
    }
  }

  async deleteResource(id: string): Promise<void> {
    try {
      const resource = await this.getResource(id)
      if (!resource) {
        throw new Error('Resource not found')
      }
      
      switch (resource.type) {
        case 'markdown':
          await databaseApi.deleteProjectDocument(id)
          break
        case 'link':
          await databaseApi.deleteProjectLink(id)
          break
        case 'file':
        case 'attachment':
          await databaseApi.deleteProjectFile(id)
          // 同时删除物理文件
          await fileService.deleteFile((resource as FileResource).filePath)
          break
        default:
          throw new Error('Unsupported resource type')
      }
    } catch (error) {
      console.error('Failed to delete resource:', error)
      throw error
    }
  }

  async uploadFile(uploadData: FileUploadData, onProgress?: (progress: number) => void): Promise<FileResource> {
    try {
      const uploadResult = await fileService.uploadFile(uploadData.file, {
        onProgress,
        targetPath: uploadData.targetPath,
        generateThumbnail: true
      })
      
      const fileData = {
        projectId: uploadData.entityId,
        fileName: uploadData.fileName || uploadData.file.name,
        filePath: uploadResult.filePath,
        fileSize: uploadData.file.size,
        mimeType: uploadData.file.type,
        description: uploadData.description,
        tags: uploadData.tags || [],
        thumbnailPath: uploadResult.thumbnailPath,
        fileHash: uploadResult.fileHash
      }
      
      const createdFile = await databaseApi.createProjectFile(fileData)
      return this.convertToFileResource(createdFile)
    } catch (error) {
      console.error('Failed to upload file:', error)
      throw error
    }
  }

  async downloadFile(resourceId: string): Promise<Blob> {
    try {
      const resource = await this.getResource(resourceId) as FileResource
      if (!resource || (resource.type !== 'file' && resource.type !== 'attachment')) {
        throw new Error('Resource is not a file')
      }
      
      return await fileService.downloadFile(resource.filePath)
    } catch (error) {
      console.error('Failed to download file:', error)
      throw error
    }
  }

  async deleteFile(resourceId: string): Promise<void> {
    await this.deleteResource(resourceId)
  }

  async createLink(linkData: LinkCreateData): Promise<LinkResource> {
    try {
      // 获取链接预览信息
      const previewData = await linkService.fetchLinkPreview(linkData.url)
      
      const linkRecord = {
        projectId: linkData.entityId,
        url: linkData.url,
        title: linkData.title || previewData.title || 'Untitled Link',
        description: linkData.description || previewData.description,
        tags: linkData.tags || [],
        domain: this.extractDomain(linkData.url),
        favicon: previewData.favicon,
        previewTitle: previewData.title,
        previewDescription: previewData.description,
        previewImage: previewData.image,
        isValidUrl: previewData.isValid
      }
      
      const createdLink = await databaseApi.createProjectLink(linkRecord)
      return this.convertToLinkResource(createdLink)
    } catch (error) {
      console.error('Failed to create link:', error)
      throw error
    }
  }

  async validateLink(resourceId: string): Promise<boolean> {
    try {
      const resource = await this.getResource(resourceId) as LinkResource
      if (!resource || resource.type !== 'link') {
        throw new Error('Resource is not a link')
      }
      
      const isValid = await linkService.validateUrl(resource.url)
      
      // 更新验证状态
      await this.updateResource(resourceId, {
        isValidUrl: isValid,
        lastChecked: new Date()
      })
      
      return isValid
    } catch (error) {
      console.error('Failed to validate link:', error)
      throw error
    }
  }

  async updateLinkPreview(resourceId: string): Promise<LinkResource> {
    try {
      const resource = await this.getResource(resourceId) as LinkResource
      if (!resource || resource.type !== 'link') {
        throw new Error('Resource is not a link')
      }
      
      const previewData = await linkService.fetchLinkPreview(resource.url)
      
      const updatedLink = await this.updateResource(resourceId, {
        previewTitle: previewData.title,
        previewDescription: previewData.description,
        previewImage: previewData.image,
        favicon: previewData.favicon,
        isValidUrl: previewData.isValid,
        lastChecked: new Date()
      })
      
      return updatedLink as LinkResource
    } catch (error) {
      console.error('Failed to update link preview:', error)
      throw error
    }
  }

  async associateMarkdown(data: MarkdownAssociationData): Promise<MarkdownResource> {
    try {
      let markdownDoc
      
      if (data.createIfNotExists) {
        // 创建新文档
        markdownDoc = await markdownService.createDocument({
          filePath: data.filePath,
          title: data.title || this.extractTitleFromPath(data.filePath),
          content: '# ' + (data.title || this.extractTitleFromPath(data.filePath)) + '\n\n'
        })
      } else {
        // 关联现有文档
        markdownDoc = await markdownService.getDocumentByPath(data.filePath)
        if (!markdownDoc) {
          throw new Error('Document not found')
        }
      }
      
      // 创建项目关联
      const associationData = {
        projectId: data.entityId,
        documentPath: data.filePath,
        title: data.title || markdownDoc.title,
        description: data.description,
        tags: data.tags || []
      }
      
      const createdAssociation = await databaseApi.createProjectDocumentAssociation(associationData)
      return this.convertToMarkdownResource(createdAssociation)
    } catch (error) {
      console.error('Failed to associate markdown:', error)
      throw error
    }
  }

  async getMarkdownContent(resourceId: string): Promise<string> {
    try {
      const resource = await this.getResource(resourceId) as MarkdownResource
      if (!resource || resource.type !== 'markdown') {
        throw new Error('Resource is not a markdown document')
      }
      
      return await markdownService.getDocumentContent(resource.filePath)
    } catch (error) {
      console.error('Failed to get markdown content:', error)
      throw error
    }
  }

  async updateMarkdownContent(resourceId: string, content: string): Promise<void> {
    try {
      const resource = await this.getResource(resourceId) as MarkdownResource
      if (!resource || resource.type !== 'markdown') {
        throw new Error('Resource is not a markdown document')
      }
      
      await markdownService.updateDocumentContent(resource.filePath, content)
      
      // 更新统计信息
      const stats = markdownService.analyzeContent(content)
      await this.updateResource(resourceId, {
        wordCount: stats.wordCount,
        characterCount: stats.characterCount,
        linksCount: stats.linksCount
      })
    } catch (error) {
      console.error('Failed to update markdown content:', error)
      throw error
    }
  }

  async getBidirectionalLinks(documentPath: string): Promise<BidirectionalLink[]> {
    try {
      return await markdownService.getBidirectionalLinks(documentPath)
    } catch (error) {
      console.error('Failed to get bidirectional links:', error)
      throw error
    }
  }

  async createBidirectionalLink(sourceDoc: string, targetDoc: string, linkData: any): Promise<BidirectionalLink> {
    try {
      return await markdownService.createBidirectionalLink(sourceDoc, targetDoc, linkData)
    } catch (error) {
      console.error('Failed to create bidirectional link:', error)
      throw error
    }
  }

  async updateBidirectionalLinks(documentPath: string, links: any[]): Promise<void> {
    try {
      await markdownService.updateBidirectionalLinks(documentPath, links)
    } catch (error) {
      console.error('Failed to update bidirectional links:', error)
      throw error
    }
  }

  async getStatistics(entityType: EntityType, entityId: string): Promise<ResourceStatistics> {
    try {
      const resources = await this.getResources(entityType, entityId)
      
      const stats: ResourceStatistics = {
        total: resources.length,
        byType: {
          markdown: 0,
          link: 0,
          file: 0,
          attachment: 0,
          reference: 0
        },
        byEntity: {
          project: 0,
          area: 0,
          task: 0,
          habit: 0,
          note: 0
        },
        totalSize: 0,
        recentlyAdded: 0,
        recentlyAccessed: 0,
        orphaned: 0,
        broken: 0
      }
      
      const now = new Date()
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      
      resources.forEach(resource => {
        // 按类型统计
        stats.byType[resource.type]++
        
        // 按实体类型统计
        if (resource.entityType) {
          stats.byEntity[resource.entityType]++
        }
        
        // 文件大小统计
        if (resource.type === 'file' || resource.type === 'attachment') {
          stats.totalSize += (resource as FileResource).fileSize
        }
        
        // 最近添加
        if (resource.createdAt > oneDayAgo) {
          stats.recentlyAdded++
        }
        
        // 最近访问
        if (resource.lastAccessedAt && resource.lastAccessedAt > oneDayAgo) {
          stats.recentlyAccessed++
        }
        
        // 孤立资源（无标签、无描述、访问次数为0）
        if (resource.tags.length === 0 && !resource.description && resource.accessCount === 0) {
          stats.orphaned++
        }
        
        // 损坏的链接
        if (resource.type === 'link' && (resource as LinkResource).isValidUrl === false) {
          stats.broken++
        }
      })
      
      return stats
    } catch (error) {
      console.error('Failed to get statistics:', error)
      throw error
    }
  }

  async searchResources(query: string, filter?: ResourceFilter): Promise<UnifiedResource[]> {
    try {
      // 实现搜索逻辑
      const allResources = await this.getResources(filter?.entityType || 'project', filter?.entityId || '', filter)
      
      const lowerQuery = query.toLowerCase()
      return allResources.filter(resource =>
        resource.title.toLowerCase().includes(lowerQuery) ||
        resource.description?.toLowerCase().includes(lowerQuery) ||
        resource.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
      )
    } catch (error) {
      console.error('Failed to search resources:', error)
      throw error
    }
  }

  // 私有辅助方法
  private convertToMarkdownResource(doc: any): MarkdownResource {
    return {
      id: doc.id,
      title: doc.title,
      description: doc.description,
      type: 'markdown',
      entityType: 'project',
      entityId: doc.projectId,
      createdAt: new Date(doc.createdAt),
      updatedAt: new Date(doc.updatedAt),
      lastAccessedAt: doc.lastAccessedAt ? new Date(doc.lastAccessedAt) : undefined,
      accessCount: doc.accessCount || 0,
      tags: doc.tags || [],
      isActive: doc.isActive !== false,
      filePath: doc.filePath,
      content: doc.content,
      wordCount: doc.wordCount,
      characterCount: doc.characterCount,
      linksCount: doc.linksCount,
      backlinksCount: doc.backlinksCount,
      isOrphaned: doc.isOrphaned,
      fileHash: doc.fileHash
    }
  }

  private convertToLinkResource(link: any): LinkResource {
    return {
      id: link.id,
      title: link.title,
      description: link.description,
      type: 'link',
      entityType: 'project',
      entityId: link.projectId,
      createdAt: new Date(link.createdAt),
      updatedAt: new Date(link.updatedAt),
      lastAccessedAt: link.lastAccessedAt ? new Date(link.lastAccessedAt) : undefined,
      accessCount: link.accessCount || 0,
      tags: link.tags || [],
      isActive: link.isActive !== false,
      url: link.url,
      domain: link.domain,
      favicon: link.favicon,
      previewTitle: link.previewTitle,
      previewDescription: link.previewDescription,
      previewImage: link.previewImage,
      isValidUrl: link.isValidUrl,
      lastChecked: link.lastChecked ? new Date(link.lastChecked) : undefined,
      responseStatus: link.responseStatus
    }
  }

  private convertToFileResource(file: any): FileResource {
    return {
      id: file.id,
      title: file.title || file.fileName,
      description: file.description,
      type: file.type || 'file',
      entityType: 'project',
      entityId: file.projectId,
      createdAt: new Date(file.createdAt),
      updatedAt: new Date(file.updatedAt),
      lastAccessedAt: file.lastAccessedAt ? new Date(file.lastAccessedAt) : undefined,
      accessCount: file.accessCount || 0,
      tags: file.tags || [],
      isActive: file.isActive !== false,
      filePath: file.filePath,
      fileName: file.fileName,
      fileSize: file.fileSize,
      mimeType: file.mimeType,
      fileHash: file.fileHash,
      uploadStatus: file.uploadStatus || 'completed',
      uploadProgress: file.uploadProgress,
      uploadedBy: file.uploadedBy,
      thumbnailPath: file.thumbnailPath,
      previewAvailable: file.previewAvailable
    }
  }

  private applyFilter(resources: UnifiedResource[], filter?: ResourceFilter): UnifiedResource[] {
    if (!filter) return resources
    
    return resources.filter(resource => {
      if (filter.type && !filter.type.includes(resource.type)) return false
      if (filter.entityType && resource.entityType !== filter.entityType) return false
      if (filter.entityId && resource.entityId !== filter.entityId) return false
      if (filter.tags && !filter.tags.some(tag => resource.tags.includes(tag))) return false
      if (filter.isActive !== undefined && resource.isActive !== filter.isActive) return false
      if (filter.dateRange) {
        const date = resource.updatedAt
        if (date < filter.dateRange.start || date > filter.dateRange.end) return false
      }
      return true
    })
  }

  private extractDomain(url: string): string {
    try {
      return new URL(url).hostname.replace('www.', '')
    } catch {
      return ''
    }
  }

  private extractTitleFromPath(filePath: string): string {
    const fileName = filePath.split('/').pop() || ''
    return fileName.replace(/\.[^/.]+$/, '').replace(/[-_]/g, ' ')
  }
}

/**
 * 领域资源数据源适配器
 */
export class AreaResourceDataSource extends ProjectResourceDataSource {
  // 领域资源数据源可以继承项目资源数据源的大部分功能
  // 只需要重写特定的方法来适配领域相关的API
  
  async getResources(entityType: EntityType, entityId: string, filter?: ResourceFilter): Promise<UnifiedResource[]> {
    // 适配领域相关的API调用
    // 实现类似于项目资源的逻辑，但调用领域相关的API
    return super.getResources(entityType, entityId, filter)
  }
}

/**
 * 数据源工厂函数
 */
export function createResourceDataSource(entityType: EntityType): ResourceDataSource {
  switch (entityType) {
    case 'project':
      return new ProjectResourceDataSource()
    case 'area':
      return new AreaResourceDataSource()
    default:
      return new ProjectResourceDataSource()
  }
}
