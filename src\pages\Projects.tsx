import { createSignal, createMemo, For, Show, onMount, type Component } from 'solid-js';
import { createStore, produce } from 'solid-js/store';
import { ProjectGridView } from '../components/projects/GridView';
import { ProjectListView } from '../components/projects/ListView';
import { ProjectKanbanView } from '../components/projects/KanbanView';
import { ProjectModal } from '../components/projects/ProjectModal';
import { ArchiveConfirmModal } from '../components/projects/ArchiveConfirmModal';

import type { Project, ProjectStatus, SortBy, FilterState, Area, Task, KPI, Resource } from '../components/projects/types';
import { getStatusText } from '../components/projects/types';

// --- MOCK DATA & STORE ---
const createInitialData = (): Project[] => [
    { 
        id: 'proj-101', name: '凤凰项目 - 2025Q4', description: '一个关键的业务流程重构项目，旨在提升效率。', 
        status: 'in-progress', areaId: 'area-1', startDate: '2025-08-01', dueDate: '2025-10-31', 
        tasks: [
            { id: 't1-1', title: '阶段一：调研与分析', completed: true, priority: 'high', dueDate: '2025-08-15', subtasks: [
                { id: 't1-1-1', title: '用户访谈', completed: true, priority: 'high', subtasks: [] },
                { id: 't1-1-2', title: '竞品分析报告', completed: true, priority: 'medium', subtasks: [] }
            ]},
            { id: 't1-2', title: '阶段二：设计与原型', completed: true, priority: 'high', dueDate: '2025-09-10', subtasks: [] },
            { id: 't1-3', title: '阶段三：开发与测试', completed: false, priority: 'high', dueDate: '2025-10-20', subtasks: [
                 { id: 't1-3-1', title: '前端开发', completed: false, priority: 'high', subtasks: [] },
                 { id: 't1-3-2', title: '后端接口开发', completed: true, priority: 'high', subtasks: [] },
                 { id: 't1-3-3', title: '集成测试', completed: false, priority: 'medium', subtasks: [] }
            ]},
        ], 
        kpis: [
            { id: 'kpi-1', name: '处理效率提升', type: 'growth', currentValue: 15, targetValue: 20, unit: '%' },
            { id: 'kpi-2', name: '系统错误率', type: 'reduction', currentValue: 3, targetValue: 1, unit: '%' }
        ],
        resources: [
            { id: 'res-1', name: '项目需求文档.md', type: 'markdown', url: '#' },
            { id: 'res-2', name: 'Figma 原型链接', type: 'link', url: 'https://figma.com' },
            { id: 'res-3', name: '会议纪要.pdf', type: 'file', url: '#' }
        ],
        statusHistory: [{ status: 'not-started', changedAt: '2025-08-01' }, { status: 'in-progress', changedAt: '2025-08-15' }], 
        updatedAt: new Date().toISOString() 
    },
    // Add more mock data if needed
    { id: 'proj-102', name: '移动端 App v3.0', description: '为我们的旗舰 App 开发下一个主要版本。', status: 'at-risk', areaId: 'area-2', startDate: '2025-07-15', dueDate: '2025-09-15', tasks: Array.from({length: 10}, (_,i) => ({id:`t${i}`, title:`功能点 ${i+1}`, completed: i < 8, priority: 'medium', subtasks: []})), kpis: [], resources: [], statusHistory: [{ status: 'in-progress', changedAt: '2025-07-15' }], updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() },
    { id: 'proj-103', name: '设计系统迭代', description: '更新和维护公司内部的设计规范与组件库。', status: 'not-started', areaId: '', startDate: '2025-09-10', dueDate: '2025-12-20', tasks: [], kpis:[], resources: [], statusHistory: [{ status: 'not-started', changedAt: '2025-09-01' }], updatedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString() },
    { id: 'proj-104', name: 'API 网关性能优化', description: '解决当前 API 的性能瓶颈问题。', status: 'paused', areaId: 'area-3', startDate: '2025-06-01', dueDate: '2025-09-30', tasks: Array.from({length: 5}, (_,i) => ({id:`t${i}`, title:`优化任务 ${i+1}`, completed: i < 1, priority: 'low', subtasks: []})), kpis:[], resources: [], statusHistory: [{ status: 'in-progress', changedAt: '2025-06-01' }, { status: 'paused', changedAt: '2025-08-20' }], updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString() },
];
const MOCK_AREAS: Area[] = [
    { id: 'area-1', name: '核心业务' }, { id: 'area-2', name: '产品研发' }, { id: 'area-3', name: '技术基建' },
];
const PROJECT_STATUSES: ProjectStatus[] = ['not-started', 'in-progress', 'at-risk', 'paused', 'completed'];


const Projects: Component = () => {
    const [projects, setProjects] = createStore<Project[]>(createInitialData());
    const [view, setView] = createSignal<'grid' | 'list' | 'kanban'>('grid');
    const [filters, setFilters] = createStore<FilterState>({ status: 'all', areaId: 'all' });
    const [sortBy, setSortBy] = createSignal<SortBy>({ field: 'updatedAt', direction: 'desc' });
    const [isModalOpen, setIsModalOpen] = createSignal(false);
    const [editingProject, setEditingProject] = createSignal<Project | null>(null);
    const [activeProjectId, setActiveProjectId] = createSignal<string | null>(null);
    const [projectToArchive, setProjectToArchive] = createSignal<Project | null>(null);

    onMount(() => {
        const savedView = localStorage.getItem('projectView') as 'grid' | 'list' | 'kanban';
        if (savedView) setView(savedView);
    });

    const countTasks = (tasks: Task[]): { total: number, completed: number } => {
        let total = tasks.length;
        let completed = tasks.filter(t => t.completed).length;
        for (const task of tasks) {
            if (task.subtasks.length > 0) {
                const subCounts = countTasks(task.subtasks);
                total += subCounts.total;
                completed += subCounts.completed;
            }
        }
        return { total, completed };
    }

    const calculateProgress = (project: Project) => {
        const { total, completed } = countTasks(project.tasks);
        if (total === 0) return 0;
        return (completed / total) * 100;
    };

    const filteredAndSortedProjects = createMemo(() => {
        const areaMap = new Map(MOCK_AREAS.map(area => [area.id, area.name]));
        const today = new Date().toISOString().split('T')[0];
        
        const enrichedProjects = projects.map(p => {
            let newStatus = p.status;
            if (p.dueDate && p.dueDate < today && p.status !== 'completed' && p.status !== 'archived') newStatus = 'at-risk';
            if (calculateProgress(p) === 100 && p.status !== 'completed' && p.status !== 'archived') newStatus = 'completed';
            return { ...p, status: newStatus, areaName: areaMap.get(p.areaId) };
        });

        let filtered = enrichedProjects.filter(p => 
            (filters.status === 'all' ? p.status !== 'archived' : p.status === filters.status) &&
            (filters.areaId === 'all' || p.areaId === filters.areaId)
        );

        const sortDir = sortBy().direction === 'asc' ? 1 : -1;
        return filtered.sort((a, b) => {
            switch (sortBy().field) {
                case 'name': return a.name.localeCompare(b.name) * sortDir;
                case 'dueDate': return (new Date(a.dueDate || 0).getTime() - new Date(b.dueDate || 0).getTime()) * sortDir;
                case 'progress': return (calculateProgress(a) - calculateProgress(b)) * sortDir;
                case 'updatedAt': return (new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()) * sortDir;
                default: return 0;
            }
        });
    });



    const handleSetView = (newView: 'grid' | 'list' | 'kanban') => {
        setView(newView);
        localStorage.setItem('projectView', newView);
    };

    const handleOpenModal = (project: Project | null) => {
        setEditingProject(project);
        setIsModalOpen(true);
    };

    const handleSaveProject = (projectData: Project) => {
        if (editingProject()) {
            const index = projects.findIndex(p => p.id === projectData.id);
            if(index > -1) {
                setProjects(index, produce(p => {
                    Object.assign(p, projectData, { updatedAt: new Date().toISOString() });
                    if (p.status !== projectData.status) p.statusHistory.push({ status: projectData.status, changedAt: new Date().toISOString() });
                }));
            }
        } else {
            setProjects(produce(ps => ps.unshift({ ...projectData, id: `proj-${Date.now()}`, updatedAt: new Date().toISOString(), statusHistory: [{ status: projectData.status, changedAt: new Date().toISOString() }] })));
        }
        setIsModalOpen(false);
        setEditingProject(null);
    };
    
    const handleUpdateProjectStatus = (projectId: string, newStatus: ProjectStatus) => {
        const index = projects.findIndex(p => p.id === projectId);
        if (index > -1) {
            setProjects(index, produce(p => {
                if(p.status !== newStatus) {
                    p.status = newStatus;
                    p.updatedAt = new Date().toISOString();
                    p.statusHistory.push({ status: newStatus, changedAt: new Date().toISOString() });
                }
            }));
        }
    };

    const handleDeleteProject = (projectId: string) => {
        setProjects(prev => prev.filter(p => p.id !== projectId));
        if (activeProjectId() === projectId) setActiveProjectId(null);
    };

    // 导航到项目详情页面
    const handleSelectProject = (projectId: string) => {
        window.history.pushState({}, '', `/projects/${projectId}`);
        window.dispatchEvent(new Event('routechange'));
    };

    const handleArchiveProject = (project: Project) => {
        setProjectToArchive(project);
    };

    const confirmAndArchiveAction = () => {
        if (projectToArchive()) {
            handleUpdateProjectStatus(projectToArchive()!.id, 'archived');
            if (activeProjectId() === projectToArchive()!.id) setActiveProjectId(null);
            setProjectToArchive(null);
        }
    };
    
    return (
        <div class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen font-sans">
            <div class="flex h-screen">
                <main class="p-4 sm:p-6 lg:p-8 w-full flex flex-col max-h-screen">
                    <header class="mb-6 flex-shrink-0">
                        <div class="flex flex-col xl:flex-row justify-between xl:items-center gap-4 p-3 bg-white dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700/50">
                            {/* Filter & Sort Controls */}
                            <div class="flex flex-wrap items-center gap-4 text-sm">
                                <select onChange={e => setFilters('status', e.currentTarget.value as any)} class="bg-gray-100 dark:bg-gray-700 rounded-md px-3 py-1.5 border-transparent focus:ring-2 focus:ring-blue-500">
                                    <option value="all">所有状态</option>
                                    <For each={PROJECT_STATUSES}>{status => <option value={status}>{getStatusText(status)}</option>}</For>
                                </select>
                                <select onChange={e => setFilters('areaId', e.currentTarget.value)} class="bg-gray-100 dark:bg-gray-700 rounded-md px-3 py-1.5 border-transparent focus:ring-2 focus:ring-blue-500">
                                    <option value="all">所有领域</option>
                                    <For each={MOCK_AREAS}>{area => <option value={area.id}>{area.name}</option>}</For>
                                </select>
                                <div class="flex items-center gap-2">
                                    <span class="text-gray-500 dark:text-gray-400">排序:</span>
                                    <select value={sortBy().field} onChange={e => setSortBy(s => ({ ...s, field: e.currentTarget.value as any }))} class="bg-gray-100 dark:bg-gray-700 rounded-md px-3 py-1.5 border-transparent focus:ring-2 focus:ring-blue-500">
                                        <option value="updatedAt">最后更新</option><option value="name">名称</option><option value="dueDate">截止日期</option><option value="progress">进度</option>
                                    </select>
                                    <button onClick={() => setSortBy(s => ({...s, direction: s.direction === 'asc' ? 'desc' : 'asc'}))} class="p-1.5 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700">
                                        <Show when={sortBy().direction === 'desc'} fallback={<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 5v14m7-7-7-7-7 7"/></svg>}>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 5v14m7-7-7 7-7-7"/></svg>
                                        </Show>
                                    </button>
                                </div>
                            </div>
                            {/* View Switcher & New Project Button */}
                            <div class="flex items-center gap-4">
                                <div class="flex items-center gap-1 bg-gray-100 dark:bg-gray-700 p-1 rounded-lg">
                                    <button onClick={() => handleSetView('grid')} class={`px-3 py-1 text-sm rounded-md transition-colors ${view() === 'grid' ? 'bg-white dark:bg-gray-600 shadow-sm' : 'hover:bg-gray-200 dark:hover:bg-gray-600/50'}`}>网格</button>
                                    <button onClick={() => handleSetView('list')} class={`px-3 py-1 text-sm rounded-md transition-colors ${view() === 'list' ? 'bg-white dark:bg-gray-600 shadow-sm' : 'hover:bg-gray-200 dark:hover:bg-gray-600/50'}`}>列表</button>
                                    <button onClick={() => handleSetView('kanban')} class={`px-3 py-1 text-sm rounded-md transition-colors ${view() === 'kanban' ? 'bg-white dark:bg-gray-600 shadow-sm' : 'hover:bg-gray-200 dark:hover:bg-gray-600/50'}`}>看板</button>
                                </div>
                                <button onClick={() => handleOpenModal(null)} class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-semibold flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                                    新建项目
                                </button>
                            </div>
                        </div>
                    </header>
                    
                    <div class="flex-1 min-h-0">
                        <Show when={filteredAndSortedProjects().length > 0} fallback={<div class="text-center py-16 text-gray-500">无匹配项目。</div>}>
                            <div classList={{ hidden: view() !== 'grid' }}><ProjectGridView projects={filteredAndSortedProjects()} onSelect={handleSelectProject} onEdit={handleOpenModal} onArchive={handleArchiveProject} onDelete={handleDeleteProject} /></div>
                            <div classList={{ hidden: view() !== 'list' }}><ProjectListView projects={filteredAndSortedProjects()} onSelect={handleSelectProject} onEdit={handleOpenModal} onArchive={handleArchiveProject} onDelete={handleDeleteProject} /></div>
                            <div classList={{ hidden: view() !== 'kanban', "h-full": view() === 'kanban' }}><ProjectKanbanView projects={filteredAndSortedProjects()} onUpdateStatus={handleUpdateProjectStatus} onSelect={handleSelectProject} onEdit={handleOpenModal} onArchive={handleArchiveProject} onDelete={handleDeleteProject} /></div>
                        </Show>
                    </div>
                </main>
            </div>

            <Show when={isModalOpen()}><ProjectModal isOpen={isModalOpen()} onClose={() => setIsModalOpen(false)} onSave={handleSaveProject} project={editingProject()} getAreas={() => MOCK_AREAS} /></Show>
            <Show when={projectToArchive()}><ArchiveConfirmModal isOpen={!!projectToArchive()} onClose={() => setProjectToArchive(null)} onConfirm={confirmAndArchiveAction} /></Show>
        </div>
    );
};

export default Projects;

