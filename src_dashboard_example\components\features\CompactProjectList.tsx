import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { Plus, FolderOpen, ExternalLink } from 'lucide-react'
import { useProjectStore } from '../../store/projectStore'
import { useAreaStore } from '../../store/areaStore'
import { useTaskStore } from '../../store/taskStore'
import { useLanguage } from '../../contexts/LanguageContext'
import { Link } from 'react-router-dom'
import { cn } from '../../lib/utils'

interface CompactProjectListProps {
  areaId: string
  onCreateProject?: () => void
  className?: string
}

export function CompactProjectList({ areaId, onCreateProject, className }: CompactProjectListProps) {
  const { t } = useLanguage()
  const { projects } = useProjectStore()
  const { areas } = useAreaStore()
  const { tasks } = useTaskStore()

  // 从领域Store精确获取领域名称
  const area = areas.find(a => a.id === areaId)
  const areaName = area?.name || '领域'

  // 获取该领域的项目（只显示进行中的）
  const areaProjects = projects ? projects.filter(
    project => project.areaId === areaId &&
    !project.archived &&
    project.status !== 'Completed'
  ) : []

  // 获取项目状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Not Started':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'In Progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'On Hold':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'Completed':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // 获取项目状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'Not Started':
        return '未开始'
      case 'In Progress':
        return '进行中'
      case 'On Hold':
        return '暂停'
      case 'Completed':
        return '已完成'
      default:
        return status
    }
  }

  // {{ AURA-X: Fix - 修复项目进度计算，使用实际任务完成情况. Approval: 寸止(ID:1738157400). }}
  // 计算项目进度（基于任务完成情况）
  const getProjectProgress = (projectId: string): number => {
    // 获取项目的所有任务
    const projectTasks = tasks.filter(task => task.projectId === projectId)

    // 如果没有任务，返回项目本身的进度值
    if (projectTasks.length === 0) {
      const project = projects.find(p => p.id === projectId)
      return project?.progress || 0
    }

    // 计算基于任务完成情况的进度
    const completedTasks = projectTasks.filter(task => task.completed)
    return Math.round((completedTasks.length / projectTasks.length) * 100)
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FolderOpen className="h-5 w-5" />
              关联项目
            </CardTitle>
            <CardDescription>
              该领域下的进行中项目
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={onCreateProject}>
            <Plus className="h-4 w-4 mr-2" />
            新建项目
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {areaProjects.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <FolderOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">暂无进行中的项目</p>
            <p className="text-xs mt-1">点击"新建项目"开始第一个项目</p>
          </div>
        ) : (
          <div className="space-y-3">
            {areaProjects.map((project) => {
              const progress = getProjectProgress(project.id)

              return (
                <div
                  key={project.id}
                  className="group p-3 rounded-lg border hover:bg-accent/50 transition-colors"
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Link
                          to={`/projects/${project.id}`}
                          state={{ from: 'area', areaId, areaName }}
                          className="text-sm font-medium hover:text-primary transition-colors truncate"
                        >
                          {project.name}
                        </Link>
                        <ExternalLink className="h-3 w-3 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>

                      {project.description && (
                        <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                          {project.description}
                        </p>
                      )}
                    </div>

                    <Badge
                      variant="outline"
                      className={cn('text-xs ml-2', getStatusColor(project.status || 'Not Started'))}
                    >
                      {getStatusText(project.status || 'Not Started')}
                    </Badge>
                  </div>

                  {/* 进度条 */}
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-muted-foreground">进度</span>
                      <span className="font-medium">{progress}%</span>
                    </div>
                    <Progress value={progress} className="h-1.5" />
                  </div>

                  {/* 项目元信息 */}
                  <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                    <div className="flex items-center gap-3">
                      {project.priority && (
                        <div className="flex items-center gap-1">
                          <div className={`w-2 h-2 rounded-full ${
                            project.priority === 'high' ? 'bg-red-500' :
                            project.priority === 'medium' ? 'bg-yellow-500' :
                            project.priority === 'low' ? 'bg-green-500' : 'bg-gray-300'
                          }`} />
                          <span className="capitalize">{project.priority}</span>
                        </div>
                      )}

                      {project.dueDate && (
                        <div>
                          截止: {new Date(project.dueDate).toLocaleDateString()}
                        </div>
                      )}
                    </div>

                    <div>
                      创建于 {new Date(project.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default CompactProjectList
