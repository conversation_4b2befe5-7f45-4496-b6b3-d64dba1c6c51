/**
 * 指标方向批量更新组件
 * 用于为现有指标批量设置方向（增长型/减少型）
 */

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '../ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { TrendingUp, Settings, CheckCircle, AlertCircle } from 'lucide-react'
import { cn } from '../../lib/utils'
import { databaseApi } from '../../lib/api'
import { useUIStore } from '../../store/uiStore'
import { useLanguage } from '../../contexts/LanguageContext'
import { getKPIDirection } from '../../lib/kpiProgressCalculator'
import type { AreaMetric } from '../../../../shared/types'

interface MetricDirectionBatchUpdateProps {
  areaId: string
  onUpdate?: () => void
  className?: string
}

interface MetricWithDirection extends AreaMetric {
  currentDirection: 'increase' | 'decrease'
  suggestedDirection: 'increase' | 'decrease'
  needsUpdate: boolean
}

export function MetricDirectionBatchUpdate({ 
  areaId, 
  onUpdate, 
  className 
}: MetricDirectionBatchUpdateProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [metrics, setMetrics] = useState<MetricWithDirection[]>([])
  const [loading, setLoading] = useState(false)
  const [updating, setUpdating] = useState(false)
  const { addNotification } = useUIStore()
  const { t } = useLanguage()

  useEffect(() => {
    if (isOpen) {
      loadMetrics()
    }
  }, [isOpen, areaId])

  const loadMetrics = async () => {
    setLoading(true)
    try {
      const result = await databaseApi.getAreaMetrics(areaId)
      if (result.success) {
        const areaMetrics = result.data || []
        
        const metricsWithDirection: MetricWithDirection[] = areaMetrics.map(metric => {
          const extendedMetric = metric as any
          const currentDirection = extendedMetric.direction || 'increase'
          const suggestedDirection = getKPIDirection(metric as any)
          
          return {
            ...metric,
            currentDirection,
            suggestedDirection,
            needsUpdate: currentDirection !== suggestedDirection
          }
        })
        
        setMetrics(metricsWithDirection)
      }
    } catch (error) {
      console.error('Failed to load metrics:', error)
      addNotification({
        type: 'error',
        title: t('pages.areas.detail.kpiManagement.updateFailed'),
        message: t('pages.areas.detail.kpiManagement.noMetricsFound')
      })
    } finally {
      setLoading(false)
    }
  }

  const updateMetricDirection = (metricId: string, direction: 'increase' | 'decrease') => {
    setMetrics(prev => prev.map(metric => 
      metric.id === metricId 
        ? { 
            ...metric, 
            currentDirection: direction,
            needsUpdate: direction !== metric.suggestedDirection
          }
        : metric
    ))
  }

  const applyAllSuggestions = () => {
    setMetrics(prev => prev.map(metric => ({
      ...metric,
      currentDirection: metric.suggestedDirection,
      needsUpdate: false
    })))
  }

  const handleBatchUpdate = async () => {
    setUpdating(true)
    try {
      const updatePromises = metrics.map(async (metric) => {
        const extendedMetric = metric as any
        if (extendedMetric.direction !== metric.currentDirection) {
          return databaseApi.updateAreaMetric({
            id: metric.id,
            updates: {
              direction: metric.currentDirection
            }
          })
        }
        return Promise.resolve({ success: true })
      })

      const results = await Promise.all(updatePromises)
      const failedUpdates = results.filter(r => !r.success).length

      if (failedUpdates === 0) {
        addNotification({
          type: 'success',
          title: t('pages.areas.detail.kpiManagement.directionsUpdated'),
          message: t('pages.areas.detail.kpiManagement.successfullyUpdated', { count: metrics.length })
        })
        setIsOpen(false)
        onUpdate?.()
      } else {
        addNotification({
          type: 'warning',
          title: t('pages.areas.detail.kpiManagement.partialUpdate'),
          message: t('pages.areas.detail.kpiManagement.failedToUpdate', { count: failedUpdates })
        })
      }
    } catch (error) {
      console.error('Failed to update metric directions:', error)
      addNotification({
        type: 'error',
        title: t('pages.areas.detail.kpiManagement.updateFailed'),
        message: t('pages.areas.detail.kpiManagement.failedToUpdateDirections')
      })
    } finally {
      setUpdating(false)
    }
  }

  const needsUpdateCount = metrics.filter(m => m.needsUpdate).length
  const hasChanges = metrics.some(m => {
    const extendedMetric = m as any
    return extendedMetric.direction !== m.currentDirection
  })

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className={className}>
          <Settings className="h-4 w-4 mr-2" />
          {t('pages.areas.detail.kpiManagement.setDirections')}
          {needsUpdateCount > 0 && (
            <Badge variant="secondary" className="ml-2">
              {needsUpdateCount}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            {t('pages.areas.detail.kpiManagement.setMetricDirections')}
          </DialogTitle>
          <DialogDescription>
            {t('pages.areas.detail.kpiManagement.setDirectionsDescription')}
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-sm text-muted-foreground">{t('pages.areas.detail.kpiManagement.loadingMetrics')}</p>
          </div>
        ) : (
          <div className="space-y-4">
            {metrics.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                <p>{t('pages.areas.detail.kpiManagement.noMetricsFound')}</p>
              </div>
            ) : (
              <>
                {needsUpdateCount > 0 && (
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-blue-600" />
                      <span className="text-sm text-blue-800">
                        {t('pages.areas.detail.kpiManagement.suggestedChanges', { count: needsUpdateCount })}
                      </span>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={applyAllSuggestions}
                      className="text-blue-600 border-blue-300 hover:bg-blue-100"
                    >
                      {t('pages.areas.detail.kpiManagement.applyAll')}
                    </Button>
                  </div>
                )}

                <div className="space-y-3">
                  {metrics.map((metric) => (
                    <Card key={metric.id} className="border-l-4 border-l-blue-400">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="font-medium">{metric.name}</h3>
                              {metric.needsUpdate && (
                                <Badge variant="outline" className="text-xs text-orange-600 border-orange-300">
                                  {t('pages.areas.detail.kpiManagement.suggestionAvailable')}
                                </Badge>
                              )}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Current: {metric.value}
                              {metric.target && ` → Target: ${metric.target}`}
                              {metric.unit && ` ${metric.unit}`}
                            </div>
                            {metric.needsUpdate && (
                              <div className="text-xs text-orange-600 mt-1">
                                {t('pages.areas.detail.kpiManagement.suggested')}: {metric.suggestedDirection === 'decrease' ? `📉 ${t('pages.areas.detail.kpiManagement.decrease')}` : `📈 ${t('pages.areas.detail.kpiManagement.increase')}`}
                              </div>
                            )}
                          </div>
                          
                          <div className="ml-4">
                            <Select
                              value={metric.currentDirection}
                              onValueChange={(value: 'increase' | 'decrease') => 
                                updateMetricDirection(metric.id, value)
                              }
                            >
                              <SelectTrigger className="w-32">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="increase">
                                  <div className="flex items-center gap-2">
                                    <TrendingUp className="h-3 w-3 text-green-600" />
                                    <span>{t('pages.areas.detail.kpiManagement.increase')}</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="decrease">
                                  <div className="flex items-center gap-2">
                                    <TrendingUp className="h-3 w-3 text-blue-600 rotate-180" />
                                    <span>{t('pages.areas.detail.kpiManagement.decrease')}</span>
                                  </div>
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </>
            )}
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            {t('pages.areas.detail.kpiManagement.cancel')}
          </Button>
          <Button
            onClick={handleBatchUpdate}
            disabled={!hasChanges || updating}
          >
            {updating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {t('pages.areas.detail.kpiManagement.updating')}
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                {t('pages.areas.detail.kpiManagement.updateDirections')}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default MetricDirectionBatchUpdate
