import { create } from 'zustand'

interface NavigationState {
  isCollapsed: boolean
  setIsCollapsed: (collapsed: boolean) => void
  toggleCollapsed: () => void
}

export const useNavigationStore = create<NavigationState>((set) => ({
  isCollapsed: false,
  setIsCollapsed: (collapsed: boolean) => set({ isCollapsed: collapsed }),
  toggleCollapsed: () => set((state) => ({ isCollapsed: !state.isCollapsed }))
}))
