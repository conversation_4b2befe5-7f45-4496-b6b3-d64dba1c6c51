# 习惯追踪组件库

一个功能完整的习惯追踪组件库，帮助用户建立和维持良好的生活习惯。

## ✨ 特性

- 🎯 **多种习惯类型** - 支持布尔型、数值型和时长型习惯
- 📊 **丰富的统计** - 完成率、连击天数、趋势分析
- 📅 **日历视图** - 直观的日历界面和热力图
- 📈 **图表分析** - 多种图表类型展示进度趋势
- 🔥 **连击系统** - 激励用户保持连续性
- 🏆 **成就系统** - 里程碑奖励和进度激励
- 🎨 **高度可定制** - 丰富的配置选项和主题支持
- 📱 **响应式设计** - 适配各种屏幕尺寸

## 🚀 快速开始

### 基本使用

```tsx
import { HabitTracker, createHabitDataSource } from '@/components/habit-tracker'

function MyComponent() {
  const dataSource = createHabitDataSource()
  
  return (
    <HabitTracker
      areaId="area-123"
      dataSource={dataSource}
    />
  )
}
```

### 完整配置示例

```tsx
import { 
  HabitTracker, 
  createHabitDataSource,
  type HabitEventHandlers,
  type HabitTrackerConfig 
} from '@/components/habit-tracker'

function AdvancedHabitTracker({ areaId }: { areaId: string }) {
  const dataSource = createHabitDataSource()
  
  const config: HabitTrackerConfig = {
    showStatistics: true,
    showProgress: true,
    showCalendar: true,
    showChart: true,
    allowCreate: true,
    allowEdit: true,
    layout: 'grid',
    chartType: 'line',
    enableStreaks: true,
    enableGoals: true
  }
  
  const eventHandlers: HabitEventHandlers = {
    onCreate: (habit) => console.log('Habit created:', habit),
    onRecord: (record) => console.log('Progress recorded:', record),
    onStreakAchieved: (habitId, streak) => {
      console.log(`🔥 ${streak}-day streak achieved!`)
    }
  }
  
  return (
    <HabitTracker
      areaId={areaId}
      dataSource={dataSource}
      config={config}
      eventHandlers={eventHandlers}
    />
  )
}
```

## 📦 组件说明

### HabitTracker - 主要组件

完整的习惯追踪界面，包含所有功能模块。

**Props:**
- `areaId: string` - 领域ID
- `dataSource: HabitDataSource` - 数据源接口
- `config?: HabitTrackerConfig` - 组件配置
- `eventHandlers?: HabitEventHandlers` - 事件处理器

### HabitList - 习惯列表

展示习惯列表和快速操作功能。

### HabitCard - 习惯卡片

单个习惯的详细展示和交互。

### HabitDialog - 习惯对话框

习惯创建和编辑对话框。

### HabitStatistics - 统计组件

展示习惯追踪的统计信息和图表。

### HabitCalendar - 日历组件

提供日历视图的习惯追踪界面。

### HabitChart - 图表组件

提供多种图表类型展示习惯数据。

## ⚙️ 配置选项

### HabitTrackerConfig

```tsx
interface HabitTrackerConfig {
  // 显示选项
  showStatistics?: boolean        // 显示统计信息
  showProgress?: boolean          // 显示进度条
  showCalendar?: boolean          // 显示日历视图
  showChart?: boolean             // 显示图表
  showStreak?: boolean            // 显示连击信息
  
  // 交互选项
  allowCreate?: boolean           // 允许创建习惯
  allowEdit?: boolean             // 允许编辑习惯
  allowDelete?: boolean           // 允许删除习惯
  allowBulkOperations?: boolean   // 允许批量操作
  
  // 布局选项
  layout?: 'grid' | 'list' | 'compact' | 'calendar'
  calendarView?: 'month' | 'week' | 'year'
  chartType?: 'line' | 'bar' | 'heatmap' | 'progress'
  
  // 功能选项
  enableReminders?: boolean       // 启用提醒
  enableNotes?: boolean           // 启用笔记
  enableStreaks?: boolean         // 启用连击系统
  enableGoals?: boolean           // 启用目标设置
  
  // 样式选项
  compactMode?: boolean           // 紧凑模式
  showColors?: boolean            // 显示颜色
  animateProgress?: boolean       // 动画效果
  
  // 主题
  theme?: 'light' | 'dark' | 'auto'
}
```

## 🎯 习惯类型

### Boolean 类型
简单的完成/未完成追踪
```tsx
{
  type: 'boolean',
  target: 1,
  // 示例：每日锻炼、喝水提醒
}
```

### Numeric 类型
数值型目标追踪
```tsx
{
  type: 'numeric',
  target: 8,
  unit: 'glasses',
  // 示例：喝8杯水、做50个俯卧撑
}
```

### Duration 类型
时长型目标追踪
```tsx
{
  type: 'duration',
  target: 30,
  unit: 'minutes',
  // 示例：阅读30分钟、冥想15分钟
}
```

## 🔌 数据源接口

### 创建数据源

```tsx
import { createHabitDataSource } from '@/components/habit-tracker'

const dataSource = createHabitDataSource()
```

### 自定义数据源

```tsx
import { HabitDataSource } from '@/components/habit-tracker'

class CustomHabitDataSource implements HabitDataSource {
  async getHabits(areaId: string): Promise<BaseHabit[]> {
    // 实现获取习惯列表
  }
  
  async createHabit(data: CreateHabitData): Promise<BaseHabit> {
    // 实现创建习惯
  }
  
  async createRecord(data: CreateRecordData): Promise<HabitRecord> {
    // 实现记录习惯
  }
  
  // ... 其他方法
}
```

## 🎊 事件处理

```tsx
const eventHandlers: HabitEventHandlers = {
  onCreate: (habit) => {
    console.log('New habit created:', habit.name)
    // 发送通知、分析等
  },
  
  onRecord: (record) => {
    if (record.completed) {
      console.log('Great job! 🎉')
      // 显示鼓励信息
    }
  },
  
  onStreakAchieved: (habitId, streak) => {
    console.log(`🔥 Amazing! ${streak}-day streak!`)
    // 显示成就通知
  },
  
  onGoalReached: (habitId, progress) => {
    console.log('🎯 Goal reached!')
    // 庆祝动画、奖励等
  }
}
```

## 📊 统计和分析

### 获取统计信息
```tsx
import { calculateHabitSummary, getCompletionGrade } from '@/components/habit-tracker'

const summary = calculateHabitSummary(habits, records)
const grade = getCompletionGrade(summary.averageCompletion)

console.log(`Overall grade: ${grade.grade} (${grade.description})`)
```

### 计算连击
```tsx
import { calculateStreak, calculateLongestStreak } from '@/components/habit-tracker'

const currentStreak = calculateStreak(records)
const longestStreak = calculateLongestStreak(records)
```

## 🎨 主题和样式

### 自动主题
```tsx
<HabitTracker
  config={{
    theme: 'auto', // 跟随系统主题
    showColors: true,
    animateProgress: true
  }}
/>
```

### 自定义样式
```tsx
<HabitTracker
  className="custom-habit-tracker"
  config={{
    compactMode: true,
    layout: 'compact'
  }}
/>
```

## 🔧 工具函数

```tsx
import { 
  formatHabitTarget,
  formatRecordValue,
  isHabitCompletedOnDate,
  generateHabitColor,
  validateHabitData
} from '@/components/habit-tracker'

// 格式化目标显示
const targetText = formatHabitTarget(habit) // "8 glasses"

// 检查日期完成状态
const isCompleted = isHabitCompletedOnDate(habitId, date, records)

// 生成习惯颜色
const color = generateHabitColor(index)

// 验证习惯数据
const errors = validateHabitData(habitData)
```

## 🚀 最佳实践

1. **从简单开始**：建议用户从2-3个简单习惯开始
2. **设置现实目标**：避免设置过高的初始目标
3. **保持一致性**：每天同一时间记录习惯
4. **庆祝小胜利**：及时反馈和鼓励很重要
5. **定期回顾**：每周查看进度和调整目标

## 📝 类型定义

完整的TypeScript类型定义请参考 `types.ts` 文件。

## 🎯 使用场景

- **个人习惯管理**：日常习惯追踪和管理
- **健康生活应用**：健身、饮食、睡眠追踪
- **学习进度管理**：读书、练习、学习计划
- **工作效率提升**：工作习惯和时间管理
- **团队目标管理**：团队习惯和目标追踪
