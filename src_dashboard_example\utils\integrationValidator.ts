/**
 * 集成验证工具
 * 验证KPI架构重构的集成完整性
 */

import { featureFlagManager } from '../config/featureFlags'
import type { FeatureFlags } from '../config/featureFlags'

// 验证结果类型
export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  suggestions: string[]
  score: number
}

// 组件验证结果
export interface ComponentValidation {
  component: string
  exists: boolean
  hasCorrectImports: boolean
  hasCorrectTypes: boolean
  errors: string[]
}

// 集成验证器类
export class IntegrationValidator {
  private flags: FeatureFlags

  constructor() {
    this.flags = featureFlagManager.getFlags()
  }

  /**
   * 验证整体集成状态
   */
  async validateIntegration(): Promise<ValidationResult> {
    const errors: string[] = []
    const warnings: string[] = []
    const suggestions: string[] = []

    // 验证功能开关配置
    const flagValidation = this.validateFeatureFlags()
    errors.push(...flagValidation.errors)
    warnings.push(...flagValidation.warnings)

    // 验证组件存在性
    const componentValidation = await this.validateComponents()
    errors.push(...componentValidation.errors)
    warnings.push(...componentValidation.warnings)

    // 验证类型系统
    const typeValidation = this.validateTypeSystem()
    errors.push(...typeValidation.errors)
    warnings.push(...typeValidation.warnings)

    // 验证API适配器
    const apiValidation = this.validateApiAdapters()
    errors.push(...apiValidation.errors)
    warnings.push(...apiValidation.warnings)

    // 生成建议
    suggestions.push(...this.generateSuggestions())

    // 计算分数
    const score = this.calculateScore(errors.length, warnings.length)

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      score
    }
  }

  /**
   * 验证功能开关配置
   */
  private validateFeatureFlags(): { errors: string[]; warnings: string[] } {
    const errors: string[] = []
    const warnings: string[] = []

    // 检查关键功能开关
    if (this.flags.useRefactoredKPIManagement && !this.flags.useUniversalKPIDialog) {
      warnings.push('建议在启用重构KPI管理时同时启用通用对话框')
    }

    if (this.flags.useRefactoredAreaKPIManagement && !this.flags.useNewKPICalculator) {
      warnings.push('建议在启用重构领域管理时同时启用新计算器')
    }

    // 检查性能功能
    if (this.flags.enableVirtualizedLists && !this.flags.enableKPICache) {
      warnings.push('虚拟化列表配合缓存使用效果更佳')
    }

    return { errors, warnings }
  }

  /**
   * 验证组件存在性
   */
  private async validateComponents(): Promise<{ errors: string[]; warnings: string[] }> {
    const errors: string[] = []
    const warnings: string[] = []

    const requiredComponents = [
      'AdaptiveKPIManagement',
      'UniversalKPIDialog',
      'RefactoredKPIManagement',
      'RefactoredAreaKPIManagement',
      'FeatureFlagPanel'
    ]

    // 这里可以添加实际的组件存在性检查
    // 由于是静态分析，我们假设组件都存在
    for (const component of requiredComponents) {
      try {
        // 模拟组件检查
        // 在实际实现中，这里可以使用动态导入来检查组件
        console.log(`Checking component: ${component}`)
      } catch (error) {
        errors.push(`组件 ${component} 不存在或无法导入`)
      }
    }

    return { errors, warnings }
  }

  /**
   * 验证类型系统
   */
  private validateTypeSystem(): { errors: string[]; warnings: string[] } {
    const errors: string[] = []
    const warnings: string[] = []

    // 检查类型定义是否存在
    try {
      // 这里可以添加类型检查逻辑
      // 例如检查 BaseKPI, KPIApiInterface 等类型是否正确定义
      console.log('Validating type system...')
    } catch (error) {
      errors.push('类型系统验证失败')
    }

    return { errors, warnings }
  }

  /**
   * 验证API适配器
   */
  private validateApiAdapters(): { errors: string[]; warnings: string[] } {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      // 检查适配器是否正确实现
      console.log('Validating API adapters...')
    } catch (error) {
      errors.push('API适配器验证失败')
    }

    return { errors, warnings }
  }

  /**
   * 生成改进建议
   */
  private generateSuggestions(): string[] {
    const suggestions: string[] = []

    // 基于当前配置生成建议
    if (!this.flags.enableKPICache) {
      suggestions.push('启用KPI缓存可以显著提升性能')
    }

    if (!this.flags.enableRealTimeValidation) {
      suggestions.push('启用实时验证可以改善用户体验')
    }

    if (process.env.NODE_ENV === 'development' && !this.flags.showArchitectureComparison) {
      suggestions.push('在开发环境中启用架构对比有助于测试')
    }

    return suggestions
  }

  /**
   * 计算集成分数
   */
  private calculateScore(errorCount: number, warningCount: number): number {
    const baseScore = 100
    const errorPenalty = errorCount * 20
    const warningPenalty = warningCount * 5
    
    return Math.max(0, baseScore - errorPenalty - warningPenalty)
  }

  /**
   * 验证特定组件
   */
  async validateComponent(componentName: string): Promise<ComponentValidation> {
    return {
      component: componentName,
      exists: true, // 模拟检查
      hasCorrectImports: true,
      hasCorrectTypes: true,
      errors: []
    }
  }

  /**
   * 生成集成报告
   */
  async generateIntegrationReport(): Promise<string> {
    const validation = await this.validateIntegration()
    
    let report = '# KPI架构集成验证报告\n\n'
    
    report += `## 总体状态\n`
    report += `- 验证状态: ${validation.isValid ? '✅ 通过' : '❌ 失败'}\n`
    report += `- 集成分数: ${validation.score}/100\n\n`
    
    if (validation.errors.length > 0) {
      report += `## 错误 (${validation.errors.length})\n`
      validation.errors.forEach((error, index) => {
        report += `${index + 1}. ❌ ${error}\n`
      })
      report += '\n'
    }
    
    if (validation.warnings.length > 0) {
      report += `## 警告 (${validation.warnings.length})\n`
      validation.warnings.forEach((warning, index) => {
        report += `${index + 1}. ⚠️ ${warning}\n`
      })
      report += '\n'
    }
    
    if (validation.suggestions.length > 0) {
      report += `## 建议 (${validation.suggestions.length})\n`
      validation.suggestions.forEach((suggestion, index) => {
        report += `${index + 1}. 💡 ${suggestion}\n`
      })
      report += '\n'
    }
    
    report += `## 功能开关状态\n`
    Object.entries(this.flags).forEach(([flag, enabled]) => {
      report += `- ${flag}: ${enabled ? '✅' : '❌'}\n`
    })
    
    return report
  }
}

// 创建全局验证器实例
export const integrationValidator = new IntegrationValidator()

// 便捷函数
export async function validateIntegration(): Promise<ValidationResult> {
  return integrationValidator.validateIntegration()
}

export async function generateIntegrationReport(): Promise<string> {
  return integrationValidator.generateIntegrationReport()
}

// 开发者工具函数
export function logIntegrationStatus(): void {
  if (process.env.NODE_ENV === 'development') {
    validateIntegration().then(result => {
      console.group('🔧 KPI架构集成状态')
      console.log('验证状态:', result.isValid ? '✅ 通过' : '❌ 失败')
      console.log('集成分数:', `${result.score}/100`)
      
      if (result.errors.length > 0) {
        console.group('❌ 错误')
        result.errors.forEach(error => console.error(error))
        console.groupEnd()
      }
      
      if (result.warnings.length > 0) {
        console.group('⚠️ 警告')
        result.warnings.forEach(warning => console.warn(warning))
        console.groupEnd()
      }
      
      if (result.suggestions.length > 0) {
        console.group('💡 建议')
        result.suggestions.forEach(suggestion => console.info(suggestion))
        console.groupEnd()
      }
      
      console.groupEnd()
    })
  }
}

export default IntegrationValidator
