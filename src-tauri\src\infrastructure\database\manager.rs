// Database Manager - 数据库管理器

use crate::infrastructure::database::{
    DatabaseConnection, DatabaseInitializer, DatabaseHealthChecker, 
    DatabaseHealthStatus, DatabaseStats
};
use crate::infrastructure::config::AppConfig;
use crate::shared::errors::{AppError, Result};
use std::sync::Arc;
use tokio::sync::RwLock;

/// 数据库管理器
/// 负责数据库的初始化、健康检查、统计信息等
pub struct DatabaseManager {
    connection: Arc<DatabaseConnection>,
    health_checker: DatabaseHealthChecker,
    config: AppConfig,
    last_health_status: Arc<RwLock<Option<DatabaseHealthStatus>>>,
}

impl DatabaseManager {
    /// 创建新的数据库管理器
    pub async fn new(config: AppConfig) -> Result<Self> {
        // 初始化数据库
        let initializer = DatabaseInitializer::new(config.clone());
        let connection = Arc::new(initializer.initialize().await?);
        
        // 创建健康检查器
        let health_checker = DatabaseHealthChecker::new(connection.as_ref().clone());
        
        Ok(Self {
            connection,
            health_checker,
            config,
            last_health_status: Arc::new(RwLock::new(None)),
        })
    }

    /// 获取数据库连接
    pub fn connection(&self) -> Arc<DatabaseConnection> {
        self.connection.clone()
    }

    /// 执行健康检查
    pub async fn check_health(&self) -> Result<DatabaseHealthStatus> {
        let status = self.health_checker.check_health().await?;
        
        // 更新缓存的健康状态
        {
            let mut last_status = self.last_health_status.write().await;
            *last_status = Some(status.clone());
        }
        
        Ok(status)
    }

    /// 快速健康检查
    pub async fn quick_health_check(&self) -> Result<bool> {
        self.health_checker.quick_health_check().await
    }

    /// 获取最后一次健康检查状态
    pub async fn get_last_health_status(&self) -> Option<DatabaseHealthStatus> {
        let last_status = self.last_health_status.read().await;
        last_status.clone()
    }

    /// 获取数据库统计信息
    pub async fn get_stats(&self) -> Result<DatabaseStats> {
        let initializer = DatabaseInitializer::new(self.config.clone());
        initializer.get_database_stats(&self.connection).await
    }

    /// 修复数据完整性问题
    pub async fn repair_integrity_issues(&self) -> Result<u32> {
        self.health_checker.repair_integrity_issues().await
    }

    /// 执行数据库备份
    pub async fn backup_database(&self, backup_path: &str) -> Result<()> {
        // 获取数据库文件路径
        let db_path = self.config.database.url.replace("sqlite:", "");
        
        // 执行文件复制备份
        tokio::fs::copy(&db_path, backup_path)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to backup database: {}", e)))?;
        
        Ok(())
    }

    /// 从备份恢复数据库
    pub async fn restore_from_backup(&self, backup_path: &str) -> Result<()> {
        // 关闭当前连接
        self.connection.close().await;
        
        // 获取数据库文件路径
        let db_path = self.config.database.url.replace("sqlite:", "");
        
        // 从备份恢复
        tokio::fs::copy(backup_path, &db_path)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to restore database: {}", e)))?;
        
        Ok(())
    }

    /// 优化数据库
    pub async fn optimize_database(&self) -> Result<()> {
        // 执行 VACUUM 清理数据库
        sqlx::query("VACUUM")
            .execute(&**self.connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to vacuum database: {}", e)))?;

        // 重建索引
        sqlx::query("REINDEX")
            .execute(&**self.connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to reindex database: {}", e)))?;

        // 分析表统计信息
        sqlx::query("ANALYZE")
            .execute(&**self.connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to analyze database: {}", e)))?;

        Ok(())
    }

    /// 清理过期数据
    pub async fn cleanup_expired_data(&self) -> Result<u32> {
        let mut cleaned_count = 0;

        // 清理已删除状态超过30天的记录
        let cutoff_date = chrono::Utc::now() - chrono::Duration::days(30);

        // 清理用户表
        let deleted_users = sqlx::query(
            "DELETE FROM users WHERE entity_status = 'deleted' AND updated_at < ?"
        )
        .bind(cutoff_date)
        .execute(&**self.connection.pool())
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to cleanup users: {}", e)))?;
        cleaned_count += deleted_users.rows_affected() as u32;

        // 清理项目表
        let deleted_projects = sqlx::query(
            "DELETE FROM projects WHERE entity_status = 'deleted' AND updated_at < ?"
        )
        .bind(cutoff_date)
        .execute(&**self.connection.pool())
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to cleanup projects: {}", e)))?;
        cleaned_count += deleted_projects.rows_affected() as u32;

        // 清理任务表
        let deleted_tasks = sqlx::query(
            "DELETE FROM tasks WHERE entity_status = 'deleted' AND updated_at < ?"
        )
        .bind(cutoff_date)
        .execute(&**self.connection.pool())
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to cleanup tasks: {}", e)))?;
        cleaned_count += deleted_tasks.rows_affected() as u32;

        // 清理资源表
        let deleted_resources = sqlx::query(
            "DELETE FROM resources WHERE entity_status = 'deleted' AND updated_at < ?"
        )
        .bind(cutoff_date)
        .execute(&**self.connection.pool())
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to cleanup resources: {}", e)))?;
        cleaned_count += deleted_resources.rows_affected() as u32;

        // 清理已读通知（超过7天）
        let notification_cutoff = chrono::Utc::now() - chrono::Duration::days(7);
        let deleted_notifications = sqlx::query(
            "DELETE FROM notifications WHERE read = TRUE AND read_at < ?"
        )
        .bind(notification_cutoff)
        .execute(&**self.connection.pool())
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to cleanup notifications: {}", e)))?;
        cleaned_count += deleted_notifications.rows_affected() as u32;

        Ok(cleaned_count)
    }

    /// 获取数据库配置信息
    pub fn get_config(&self) -> &AppConfig {
        &self.config
    }

    /// 检查数据库是否需要升级
    pub async fn check_schema_version(&self) -> Result<bool> {
        // 检查当前数据库版本
        let current_version = sqlx::query_scalar::<_, String>(
            "SELECT version FROM migrations ORDER BY id DESC LIMIT 1"
        )
        .fetch_optional(&**self.connection.pool())
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to check schema version: {}", e)))?;

        // 比较版本（简化实现）
        match current_version {
            Some(version) => Ok(version != "001_initial_schema"),
            None => Ok(true), // 需要初始化
        }
    }

    /// 关闭数据库连接
    pub async fn close(&self) {
        self.connection.close().await;
    }
}

impl Drop for DatabaseManager {
    fn drop(&mut self) {
        // 在析构时确保连接被关闭
        // 注意：这里不能使用 async，所以只是标记
    }
}

/// 数据库管理器的构建器
pub struct DatabaseManagerBuilder {
    config: Option<AppConfig>,
}

impl DatabaseManagerBuilder {
    pub fn new() -> Self {
        Self { config: None }
    }

    pub fn with_config(mut self, config: AppConfig) -> Self {
        self.config = Some(config);
        self
    }

    pub async fn build(self) -> Result<DatabaseManager> {
        let config = self.config.ok_or_else(|| {
            AppError::ConfigError("Database config is required".to_string())
        })?;

        DatabaseManager::new(config).await
    }
}

impl Default for DatabaseManagerBuilder {
    fn default() -> Self {
        Self::new()
    }
}
