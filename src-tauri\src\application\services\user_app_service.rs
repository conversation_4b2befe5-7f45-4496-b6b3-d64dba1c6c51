// User Application Service - 用户应用服务

use crate::domain::entities::{User, UserPreferences};
use crate::domain::repositories::UserRepository;
use crate::domain::services::UserDomainService;
use crate::shared::errors::{AppError, Result};
use crate::shared::types::{Id, QueryParams, EntityStatus};
use std::sync::Arc;

/// 用户应用服务
/// 负责用户相关的业务流程编排
pub struct UserAppService {
    user_repository: Arc<dyn UserRepository>,
    user_domain_service: UserDomainService,
}

impl UserAppService {
    pub fn new(
        user_repository: Arc<dyn UserRepository>,
        user_domain_service: UserDomainService,
    ) -> Self {
        Self {
            user_repository,
            user_domain_service,
        }
    }

    /// 创建新用户
    pub async fn create_user(&self, username: String, email: Option<String>) -> Result<User> {
        // 验证用户名
        self.user_domain_service.validate_username(&username)?;
        
        // 验证邮箱（如果提供）
        if let Some(ref email) = email {
            self.user_domain_service.validate_email(email)?;
        }

        // 检查用户名是否已存在
        if self.user_repository.username_exists(&username).await? {
            return Err(AppError::ValidationError("Username already exists".to_string()));
        }

        // 检查邮箱是否已存在（如果提供）
        if let Some(ref email) = email {
            if self.user_repository.email_exists(email).await? {
                return Err(AppError::ValidationError("Email already exists".to_string()));
            }
        }

        // 创建用户实体
        let mut user = User::new(username);
        user.email = email;

        // 保存用户
        self.user_repository.save(&user).await?;

        Ok(user)
    }

    /// 根据ID获取用户
    pub async fn get_user_by_id(&self, id: &Id) -> Result<User> {
        self.user_repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| AppError::NotFound("User not found".to_string()))
    }

    /// 根据用户名获取用户
    pub async fn get_user_by_username(&self, username: &str) -> Result<User> {
        self.user_repository
            .find_by_username(username)
            .await?
            .ok_or_else(|| AppError::NotFound("User not found".to_string()))
    }

    /// 更新用户信息
    pub async fn update_user(
        &self,
        id: &Id,
        display_name: Option<String>,
        email: Option<String>,
        avatar_url: Option<String>,
    ) -> Result<User> {
        // 获取现有用户
        let mut user = self.get_user_by_id(id).await?;

        // 验证邮箱（如果提供且与当前不同）
        if let Some(ref new_email) = email {
            if user.email.as_ref() != Some(new_email) {
                self.user_domain_service.validate_email(new_email)?;
                
                // 检查邮箱是否已被其他用户使用
                if self.user_repository.email_exists(new_email).await? {
                    return Err(AppError::ValidationError("Email already exists".to_string()));
                }
            }
        }

        // 更新用户信息
        user.display_name = display_name;
        user.email = email;
        user.avatar_url = avatar_url;
        user.update_metadata();

        // 保存更新
        self.user_repository.update(&user).await?;

        Ok(user)
    }

    /// 更新用户偏好设置
    pub async fn update_user_preferences(
        &self,
        id: &Id,
        preferences: UserPreferences,
    ) -> Result<User> {
        // 获取现有用户
        let mut user = self.get_user_by_id(id).await?;

        // 检查是否有权限更新偏好设置
        if !self.user_domain_service.can_update_preferences(&user) {
            return Err(AppError::PermissionDenied("Cannot update preferences".to_string()));
        }

        // 更新偏好设置
        user.preferences = preferences;
        user.update_metadata();

        // 保存更新
        self.user_repository.update(&user).await?;

        Ok(user)
    }

    /// 激活用户
    pub async fn activate_user(&self, id: &Id) -> Result<User> {
        let mut user = self.get_user_by_id(id).await?;
        
        user.entity_status = EntityStatus::Active;
        user.update_metadata();

        self.user_repository.update(&user).await?;

        Ok(user)
    }

    /// 停用用户
    pub async fn deactivate_user(&self, id: &Id) -> Result<User> {
        let mut user = self.get_user_by_id(id).await?;
        
        user.entity_status = EntityStatus::Inactive;
        user.update_metadata();

        self.user_repository.update(&user).await?;

        Ok(user)
    }

    /// 删除用户（软删除）
    pub async fn delete_user(&self, id: &Id) -> Result<()> {
        // 检查用户是否存在
        let _user = self.get_user_by_id(id).await?;

        // 执行软删除
        self.user_repository.delete(id).await?;

        Ok(())
    }

    /// 获取所有活跃用户
    pub async fn get_active_users(&self) -> Result<Vec<User>> {
        self.user_repository.find_all_active().await
    }

    /// 分页获取用户列表
    pub async fn get_users_with_pagination(&self, params: &QueryParams) -> Result<(Vec<User>, u64)> {
        self.user_repository.find_with_pagination(params).await
    }

    /// 搜索用户
    pub async fn search_users(&self, query: &str) -> Result<Vec<User>> {
        if query.trim().is_empty() {
            return Ok(Vec::new());
        }

        // 尝试按用户名精确匹配
        if let Ok(user) = self.get_user_by_username(query).await {
            return Ok(vec![user]);
        }

        // 如果包含@符号，尝试按邮箱查找
        if query.contains('@') {
            if let Ok(Some(user)) = self.user_repository.find_by_email(query).await {
                return Ok(vec![user]);
            }
        }

        // 否则返回空结果（简化实现）
        Ok(Vec::new())
    }

    /// 获取用户统计信息
    pub async fn get_user_statistics(&self) -> Result<UserStatistics> {
        let total_users = self.user_repository.count_active_users().await?;
        let active_users = self.user_repository.find_by_status("active").await?.len() as u64;
        let inactive_users = self.user_repository.find_by_status("inactive").await?.len() as u64;

        Ok(UserStatistics {
            total_users,
            active_users,
            inactive_users,
        })
    }

    /// 检查用户名是否可用
    pub async fn is_username_available(&self, username: &str) -> Result<bool> {
        // 验证用户名格式
        self.user_domain_service.validate_username(username)?;
        
        // 检查是否已存在
        let exists = self.user_repository.username_exists(username).await?;
        Ok(!exists)
    }

    /// 检查邮箱是否可用
    pub async fn is_email_available(&self, email: &str) -> Result<bool> {
        // 验证邮箱格式
        self.user_domain_service.validate_email(email)?;
        
        // 检查是否已存在
        let exists = self.user_repository.email_exists(email).await?;
        Ok(!exists)
    }

    /// 重置用户偏好设置为默认值
    pub async fn reset_user_preferences(&self, id: &Id) -> Result<User> {
        let mut user = self.get_user_by_id(id).await?;
        
        user.preferences = UserPreferences::default();
        user.update_metadata();

        self.user_repository.update(&user).await?;

        Ok(user)
    }

    /// 归档用户
    pub async fn archive_user(&self, id: &Id) -> Result<User> {
        let mut user = self.get_user_by_id(id).await?;
        
        user.entity_status = EntityStatus::Archived;
        user.update_metadata();

        self.user_repository.update(&user).await?;

        Ok(user)
    }
}

/// 用户统计信息
#[derive(Debug, Clone)]
pub struct UserStatistics {
    pub total_users: u64,
    pub active_users: u64,
    pub inactive_users: u64,
}
