import { useState, useMemo, useEffect, useCallback } from 'react'
import { usePara<PERSON>, Link, useNavigate, useLocation } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Progress } from '../ui/progress'
import { Textarea } from '../ui/textarea'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { Tooltip, TooltipTrigger, TooltipContent } from '../ui/tooltip'
import { Search, Plus, Edit, Archive, Trash2 } from 'lucide-react'
import { cn } from '../../lib/utils'
import { useProjectStore } from '../../store/projectStore'
import { useTaskStore } from '../../store/taskStore'
import { useAreaStore } from '../../store/areaStore'
import { useUIStore } from '../../store/uiStore'
import { useLanguage } from '../../contexts/LanguageContext'
import { useConfirmDialog } from '../shared/ConfirmDialog'
import { databaseApi } from '../../lib/api'
import CreateProjectDialog from './CreateProjectDialog'
import TaskList from './TaskList'
import CreateTaskDialog from './CreateTaskDialog'
import { DeleteTaskDialog } from './DeleteTaskDialog'
import TaskDetailPanel from './TaskDetailPanel'
// 使用最终版本的KPI管理组件
import ProjectKPIManagement from './ProjectKPIManagement'
import ProjectResources from './ProjectResources'
import ProjectDeliverables from './ProjectDeliverables'
import TaskSearchAndFilter from './TaskSearchAndFilter'
import ResourceLinkDialog from './ResourceLinkDialog'
import type { Project } from '../../../../shared/types'
import type { ExtendedTask } from '../../store/taskStore'

export function ProjectDetailPage() {
  const { projectId } = useParams<{ projectId: string }>()
  const navigate = useNavigate()
  const location = useLocation()
  const isArchived = location.state?.archived || false
  const { projects, updateProject, deleteProject, archiveProject } = useProjectStore()
  const { tasks, createTask, updateTask, deleteTask, moveTask, getDescendantCount, getTaskChildren } = useTaskStore()
  const { areas } = useAreaStore()
  const { addNotification } = useUIStore()
  const { t } = useLanguage()
  const { confirm, ConfirmDialog } = useConfirmDialog()

  // {{ AURA-X: Add - 单独获取项目数据状态，支持归档项目. Approval: 寸止(ID:归档修复). }}
  const [projectData, setProjectData] = useState<any>(null)
  const [isLoadingProject, setIsLoadingProject] = useState(false)

  // {{ AURA-X: Add - 动态返回逻辑. Approval: 寸止(ID:1738157400). }}
  const getBackPath = () => {
    // 检查是否从领域详情页进入
    if (location.state?.from === 'area') {
      return `/areas/${location.state.areaId}`
    }
    // 默认返回项目页面
    return '/projects'
  }

  const getBackText = () => {
    if (location.state?.from === 'area') {
      return t('pages.projects.detail.navigation.backToArea', { areaName: location.state.areaName || t('pages.projects.detail.navigation.backToAreaDefault') })
    }
    return t('pages.projects.detail.navigation.backToProjects')
  }

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isCreateTaskDialogOpen, setIsCreateTaskDialogOpen] = useState(false)
  const [isDeleteTaskDialogOpen, setIsDeleteTaskDialogOpen] = useState(false)
  const [editingTask, setEditingTask] = useState<ExtendedTask | null>(null)
  const [deletingTask, setDeletingTask] = useState<ExtendedTask | null>(null)
  const [parentTaskId, setParentTaskId] = useState<string | undefined>()
  const [notes, setNotes] = useState('')
  const [isNotesEditing, setIsNotesEditing] = useState(false)
  const [isResourceLinkDialogOpen, setIsResourceLinkDialogOpen] = useState(false)
  const [filteredTasks, setFilteredTasks] = useState<ExtendedTask[]>([])
  const [showTaskFilters, setShowTaskFilters] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // {{ AURA-X: Add - 获取项目数据，支持归档项目. Approval: 寸止(ID:归档修复). }}
  useEffect(() => {
    const fetchProjectData = async () => {
      if (!projectId) return

      setIsLoadingProject(true)
      try {
        // 如果是归档状态，直接通过API获取项目数据（包括归档项目）
        if (isArchived) {
          const result = await databaseApi.getProjectById(projectId, true)
          if (result.success && result.data) {
            setProjectData(result.data)
          } else {
            addNotification({
              type: 'error',
              title: '项目未找到',
              message: '未找到该项目，可能已被删除。'
            })
            navigate('/archive')
          }
        } else {
          // 非归档状态，从store获取数据
          const project = projects.find(p => p.id === projectId)
          if (project) {
            setProjectData(project)
          } else {
            // 如果store中没有，尝试通过API获取
            const result = await databaseApi.getProjectById(projectId, false)
            if (result.success && result.data) {
              setProjectData(result.data)
            } else {
              addNotification({
                type: 'error',
                title: '项目未找到',
                message: '未找到该项目，可能已被删除。'
              })
              navigate('/projects')
            }
          }
        }
      } catch (error) {
        console.error('Failed to fetch project data:', error)
        addNotification({
          type: 'error',
          title: '加载失败',
          message: '加载项目数据时发生错误。'
        })
      } finally {
        setIsLoadingProject(false)
      }
    }

    fetchProjectData()
  }, [projectId, isArchived, projects])

  // {{ AURA-X: Add - 页面加载时滚动到顶部. Approval: 寸止(ID:1738157400). }}
  useEffect(() => {
    const scrollToTop = () => {
      // 找到真正的滚动容器 - Layout 中的主内容滚动容器
      const scrollContainer = document.querySelector('main .overflow-y-auto')
      if (scrollContainer) {
        scrollContainer.scrollTop = 0
      }

      // 备用方案：重置其他可能的滚动容器
      window.scrollTo({ top: 0, left: 0, behavior: 'instant' })
      document.documentElement.scrollTop = 0
      document.body.scrollTop = 0
    }

    // 立即执行
    scrollToTop()

    // 延迟执行，确保 DOM 完全更新
    const timer1 = setTimeout(scrollToTop, 0)
    const timer2 = setTimeout(scrollToTop, 100)

    return () => {
      clearTimeout(timer1)
      clearTimeout(timer2)
    }
  }, [projectId])

  // TaskDetailPanel 状态
  const [selectedTaskForDetail, setSelectedTaskForDetail] = useState<ExtendedTask | null>(null)
  const [isTaskDetailPanelOpen, setIsTaskDetailPanelOpen] = useState(false)

  // {{ AURA-X: Modify - 使用projectData替代从store获取的project数据. Approval: 寸止(ID:归档修复). }}
  const project = projectData || projects.find((p) => p.id === projectId)
  const projectTasks = tasks.filter((task) => task.projectId === projectId)
  const associatedArea = project?.areaId ? areas.find((a) => a.id === project.areaId) : null

  // 优化的过滤任务回调函数
  const handleFilteredTasksChange = useCallback((filtered: ExtendedTask[]) => {
    setFilteredTasks(filtered)
  }, [])

  // 初始化过滤后的任务列表
  useEffect(() => {
    if (filteredTasks.length === 0 && projectTasks.length > 0 && !showTaskFilters) {
      setFilteredTasks(projectTasks)
    }
  }, [projectTasks, filteredTasks.length, showTaskFilters])

  // 注意：为避免 React Hooks 顺序错误，不在这里提前 return。改为在最终 return 前统一处理。

  // Calculate project statistics
  const stats = useMemo(() => {
    const totalTasks = projectTasks.length
    const completedTasks = projectTasks.filter((task) => task.completed).length
    const overdueTasks = projectTasks.filter((task) => {
      if (!task.deadline || task.completed) return false
      return new Date(task.deadline) < new Date()
    }).length
    const upcomingTasks = projectTasks.filter((task) => {
      if (!task.deadline || task.completed) return false
      const deadline = new Date(task.deadline)
      const now = new Date()
      const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
      return deadline >= now && deadline <= sevenDaysFromNow
    }).length

    return {
      totalTasks,
      completedTasks,
      overdueTasks,
      upcomingTasks,
      progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
    }
  }, [projectTasks])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Not Started':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'In Progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'At Risk':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'Paused':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'Not Started':
        return t('components.projectCard.status.notStarted')
      case 'In Progress':
        return t('components.projectCard.status.inProgress')
      case 'At Risk':
        return t('components.projectCard.status.atRisk')
      case 'Paused':
        return t('components.projectCard.status.paused')
      default:
        return status
    }
  }

  const getDaysUntilDeadline = () => {
    // 归档详情加载早期，project 可能尚未加载；需空值保护
    if (!project || !project.deadline) return null
    const now = new Date()
    const deadline = new Date(project.deadline)
    const diffTime = deadline.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const daysUntil = project ? getDaysUntilDeadline() : null

  // Function to update project progress based on task completion
  const updateProjectProgress = () => {
    // Use a small delay to ensure task state is updated first
    setTimeout(() => {
      const { tasks: latestTasks } = useTaskStore.getState()
      const currentProjectTasks = latestTasks.filter((task) => task.projectId === project.id)
      const completedTasks = currentProjectTasks.filter((task) => task.completed)
      const progressPercentage =
        currentProjectTasks.length > 0
          ? Math.round((completedTasks.length / currentProjectTasks.length) * 100)
          : 0

      console.log('🔄 Updating project progress:', {
        projectId: project.id,
        totalTasks: currentProjectTasks.length,
        completedTasks: completedTasks.length,
        oldProgress: project.progress,
        newProgress: progressPercentage
      })

      // Always update to ensure synchronization
      updateProject(project.id, {
        progress: progressPercentage,
        updatedAt: new Date()
      })
    }, 10) // Small delay to ensure task state is updated
  }

  const handleEditProject = async (
    projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>
  ) => {
    // 统一编辑：持久化 + 回读校验
    updateProject(project.id, {
      ...projectData,
      updatedAt: new Date()
    })
    // 轻量回读，保证UI与后端一致
    try {
      const res = await databaseApi.getProjectById(project.id, false)
      if (res.success && res.data) {
        // 用后端返回值覆盖一次，防止字段默认值差异
        useProjectStore.getState().setProjects(
          useProjectStore.getState().projects.map(p => p.id === project.id ? { ...p, ...res.data } : p)
        )
      }
    } catch {}
    setIsEditDialogOpen(false)
  }

  const handleDeleteProject = async () => {
    const confirmed = await confirm({
      title: t('pages.projects.detail.confirmDialogs.deleteProject.title'),
      description: t('pages.projects.detail.confirmDialogs.deleteProject.description', { name: project.name }),
      variant: 'destructive',
      confirmText: t('pages.projects.detail.confirmDialogs.deleteProject.confirm'),
      cancelText: t('pages.projects.detail.confirmDialogs.deleteProject.cancel')
    })

    if (confirmed) {
      try {
        await deleteProject(project.id)
        addNotification({
          type: 'success',
          title: '项目删除成功',
          message: `项目 "${project.name}" 已删除`
        })
        navigate(getBackPath())
      } catch (error) {
        console.error('Failed to delete project:', error)
        addNotification({
          type: 'error',
          title: '项目删除失败',
          message: error instanceof Error ? error.message : '删除项目时发生未知错误'
        })
      }
    }
  }

  const handleArchiveProject = async () => {
    // {{ AURA-X: Fix - 修复确认对话框的使用方式，使用Promise而不是回调. Approval: 寸止(ID:1738157400). }}
    const confirmed = await confirm({
      title: t('pages.projects.detail.confirmDialogs.archiveProject.title'),
      description: t('pages.projects.detail.confirmDialogs.archiveProject.description', { name: project.name }),
      variant: 'warning',
      confirmText: t('pages.projects.detail.confirmDialogs.archiveProject.confirm'),
      cancelText: t('pages.projects.detail.confirmDialogs.archiveProject.cancel')
    })

    if (confirmed) {
      try {
        await archiveProject(project.id)
        // {{ AURA-X: Add - 添加归档成功通知. Approval: 寸止(ID:1738157400). }}
        addNotification({
          type: 'success',
          title: t('messages.success.archived'),
          message: `"${project.name}" 已成功归档，您可以在归档页面中找到它。`
        })
        navigate(getBackPath())
      } catch (error) {
        addNotification({
          type: 'error',
          title: t('messages.error.archiveFailed'),
          message: '归档项目时发生错误，请重试。'
        })
      }
    }
  }

  const handleSaveNotes = () => {
    // In a real implementation, this would save to a notes field or separate notes system
    setIsNotesEditing(false)
  }

  const handleCreateTask = async (
    taskData: Omit<ExtendedTask, 'id' | 'createdAt' | 'updatedAt'>
  ) => {
    console.log('📝 Creating task with data:', {
      content: taskData.content,
      parentId: taskData.parentId,
      projectId: project.id
    })

    try {
      // Ensure projectId is set
      const taskWithProject = {
        ...taskData,
        projectId: project.id
      }

      await createTask(taskWithProject)
      console.log('✅ Task created successfully')

      // Update project progress when task is created
      updateProjectProgress()
    } catch (error) {
      console.error('❌ Failed to create task:', error)
      // Error handling is done in the store
    }
  }

  const handleEditTask = async (taskData: Omit<ExtendedTask, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (editingTask) {
      updateTask(editingTask.id, {
        ...taskData,
        updatedAt: new Date()
      })
      setEditingTask(null)

      // Update project progress when task is updated
      updateProjectProgress()
    }
  }

  // 递归获取所有子任务ID
  const getAllSubtaskIds = (taskId: string): string[] => {
    const children = getTaskChildren(taskId)
    const allIds: string[] = []

    children.forEach(child => {
      allIds.push(child.id)
      // 递归获取子任务的子任务
      allIds.push(...getAllSubtaskIds(child.id))
    })

    return allIds
  }

  // 检查任务是否有子任务
  const hasSubtasks = (taskId: string): boolean => {
    return getTaskChildren(taskId).length > 0
  }

  // 批量更新任务状态
  const updateTasksStatus = async (taskIds: string[], completed: boolean) => {
    try {
      // 使用Promise.all并行更新所有任务
      await Promise.all(
        taskIds.map(async (id) => {
          const updates: any = { completed }

          // 如果标记为完成，同时更新相关字段
          if (completed) {
            updates.status = 'done'
            updates.completedAt = new Date()
          } else {
            // 如果取消完成，重置相关字段
            updates.status = 'todo'
            updates.completedAt = null
          }

          updateTask(id, updates)
        })
      )

      // 更新项目进度
      updateProjectProgress()

      return true
    } catch (error) {
      console.error('Failed to update tasks status:', error)
      return false
    }
  }

  const handleToggleTask = async (taskId: string, completed: boolean) => {
    const task = tasks.find(t => t.id === taskId)
    if (!task) return

    // 如果是叶子任务（没有子任务），直接切换状态
    if (!hasSubtasks(taskId)) {
      updateTask(taskId, {
        completed,
        status: completed ? 'done' : 'todo',
        completedAt: completed ? new Date() : null
      })
      updateProjectProgress()
      return
    }

    // 如果是父任务且要标记为完成，显示确认对话框
    if (completed) {
      const allSubtaskIds = getAllSubtaskIds(taskId)
      const totalTasks = allSubtaskIds.length + 1 // 包含父任务本身
      confirm({
        title: t('pages.projects.detail.confirmDialogs.batchCompleteTask.title'),
        description: t('pages.projects.detail.confirmDialogs.batchCompleteTask.description', { count: allSubtaskIds.length }),
        variant: 'default',
        confirmText: t('pages.projects.detail.confirmDialogs.batchCompleteTask.confirm'),
        cancelText: t('pages.projects.detail.confirmDialogs.batchCompleteTask.cancel'),
        onConfirm: async () => {
          setIsSubmitting(true)
          try {
            // 更新父任务和所有子任务
            const allTaskIds = [taskId, ...allSubtaskIds]
            const success = await updateTasksStatus(allTaskIds, true)

            if (success) {
              addNotification({
                type: 'success',
                title: t('pages.projects.detail.notifications.taskCompleted'),
                message: t('pages.projects.detail.notifications.taskCompletedMessage', { count: totalTasks })
              })
            } else {
              addNotification({
                type: 'error',
                title: t('pages.projects.detail.notifications.updateFailed'),
                message: t('pages.projects.detail.notifications.updateFailedMessage')
              })
            }
          } catch (error) {
            console.error('Failed to complete tasks:', error)
            addNotification({
              type: 'error',
              title: t('pages.projects.detail.notifications.operationFailed'),
              message: t('pages.projects.detail.notifications.operationFailedMessage')
            })
          } finally {
            setIsSubmitting(false)
          }
        },
        onCancel: () => {
          // 用户取消操作，不做任何处理
          console.log('User cancelled task completion')
        }
      })
    } else {
      // 如果是取消完成状态，直接处理（不需要确认）
      updateTask(taskId, {
        completed: false,
        status: 'todo',
        completedAt: null
      })
      updateProjectProgress()
    }
  }

  const handleDeleteTask = (taskId: string) => {
    const task = tasks.find((t) => t.id === taskId)
    if (task) {
      setDeletingTask(task)
      setIsDeleteTaskDialogOpen(true)
    }
  }

  const handleConfirmDeleteTask = () => {
    if (deletingTask) {
      deleteTask(deletingTask.id)
      setDeletingTask(null)

      // Update project progress when task is deleted
      updateProjectProgress()
    }
  }

  const handleAddSubtask = (parentId: string) => {
    setParentTaskId(parentId)
    setIsCreateTaskDialogOpen(true)
  }

  const handleTaskMove = (taskId: string, newParentId?: string, newIndex?: number) => {
    console.log(`🎯 ProjectDetailPage - handleTaskMove called:`, { taskId, newParentId, newIndex })
    moveTask(taskId, newParentId, newIndex)

    // Update project progress after task move (in case task hierarchy affects completion calculation)
    updateProjectProgress()
  }

  // TaskDetailPanel 处理函数
  const handleTaskDetailSave = async (taskData: Partial<ExtendedTask>) => {
    if (selectedTaskForDetail) {
      updateTask(selectedTaskForDetail.id, {
        ...taskData,
        updatedAt: new Date()
      })

      // 更新项目进度
      updateProjectProgress()

      // 关闭面板
      setIsTaskDetailPanelOpen(false)
      setSelectedTaskForDetail(null)
    }
  }

  const handleTaskDetailClose = () => {
    setIsTaskDetailPanelOpen(false)
    setSelectedTaskForDetail(null)
  }

  const handleTaskDetailDelete = (taskId: string) => {
    const task = tasks.find((t) => t.id === taskId)
    if (task) {
      setDeletingTask(task)
      setIsDeleteTaskDialogOpen(true)
      // 关闭详情面板
      setIsTaskDetailPanelOpen(false)
      setSelectedTaskForDetail(null)
    }
  }

  const handleTaskDetailAddSubtask = (parentId: string) => {
    setParentTaskId(parentId)
    setIsCreateTaskDialogOpen(true)
    // 保持详情面板打开状态
  }

  // {{ AURA-X: Add - 加载状态和项目不存在检查. Approval: 寸止(ID:归档修复). }}
  if (isLoadingProject) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-2 text-muted-foreground">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            <span>加载项目数据...</span>
          </div>
        </div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <div className="text-muted-foreground">
            <div className="text-4xl mb-2">📋</div>
            <p className="text-sm">项目未找到</p>
            <p className="text-xs mt-1">该项目可能已被删除或您没有访问权限</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* {{ AURA-X: Modify - 删除顶部返回按钮，统一使用全局面包屑导航。Confirmed via 寸止 }} */}
      {/* 返回按钮移除，保留逻辑函数供程序化返回时使用（归档/删除后）。 */}

      {/* 归档横幅提示 */}
      {isArchived && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="flex-1">
              <h3 className="text-sm font-medium text-amber-800">
                {t('pages.projects.detail.archivedBanner.title')}
              </h3>
              <p className="text-sm text-amber-700 mt-1">
                {t('pages.projects.detail.archivedBanner.description')}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 顶部主信息卡片 - 全宽 */}
      <Card>
        <CardContent className="pt-6">
          {/* 三列式垂直排列 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* 第一列：项目基本信息 */}
            <div className="space-y-4">
              {/* 项目名称和状态 */}
              <div className="space-y-2">
                <div className="flex items-center gap-3 flex-wrap">
                  <h1 className="text-2xl font-bold">{project.name}</h1>

                  {/* 领域徽章：显示关联领域并可点击跳转 */}
                  {associatedArea && (
                    <Link
                      to={`/areas/${associatedArea.id}`}
                      className="inline-flex items-center px-2 py-0.5 rounded-full border text-xs hover:bg-accent transition-colors text-muted-foreground"
                      title={t('pages.projects.detail.dates.area')}
                    >
                      <div className="w-2 h-2 rounded-full bg-area mr-1"></div>
                      {associatedArea.name}
                    </Link>
                  )}

                  {/* 状态显示 - 归档状态下只读 */}
                  {isArchived ? (
                    <Badge
                      variant="outline"
                      className={cn('text-sm', getStatusColor(project.status))}
                    >
                      {getStatusText(project.status)} ({t('common.archived')})
                    </Badge>
                  ) : (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Badge
                          variant="outline"
                          className={cn('text-sm cursor-pointer hover:bg-muted', getStatusColor(project.status))}
                        >
                          {getStatusText(project.status)}
                        </Badge>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="start">
                        <DropdownMenuItem onClick={() => updateProject(project.id, { status: 'Not Started' })}>
                          {t('components.projectCard.status.notStarted')}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => updateProject(project.id, { status: 'In Progress' })}>
                          {t('components.projectCard.status.inProgress')}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => updateProject(project.id, { status: 'At Risk' })}>
                          {t('components.projectCard.status.atRisk')}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => updateProject(project.id, { status: 'Paused' })}>
                          {t('components.projectCard.status.paused')}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>

              {/* 项目描述 */}
              {project.description && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm text-muted-foreground">{t('pages.projects.detail.projectInfo.description')}</h4>
                  <p className="text-sm">{project.description}</p>
                </div>
              )}

              {/* 项目目标 */}
              {project.goal && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm text-muted-foreground">{t('pages.projects.detail.projectInfo.goal')}</h4>
                  <p className="text-sm">{project.goal}</p>
                </div>
              )}

              {/* 最终交付物 */}
              {project.deliverable && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm text-muted-foreground">{t('pages.projects.detail.projectInfo.deliverable')}</h4>
                  <p className="text-sm">{project.deliverable}</p>
                </div>
              )}
            </div>

            {/* 第二列：进度和统计 */}
            <div className="space-y-4">
              {/* 进度圆环 */}
              <div className="flex flex-col items-center space-y-2">
                <div className="text-sm font-medium text-muted-foreground">{t('pages.projects.detail.progress.overall')}</div>
                <div className="relative w-20 h-20">
                  <svg className="w-full h-full transform -rotate-90" viewBox="0 0 36 36">
                    {/* 背景圆环 */}
                    <circle
                      cx="18"
                      cy="18"
                      r="15"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="3"
                      className="text-muted-foreground/20"
                    />
                    {/* 进度圆环 */}
                    <circle
                      cx="18"
                      cy="18"
                      r="15"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="3"
                      strokeDasharray={`${2 * Math.PI * 15}`}
                      strokeDashoffset={`${2 * Math.PI * 15 * (1 - Math.max(stats.progressPercentage, 2) / 100)}`}
                      className={cn(
                        'transition-all duration-500',
                        stats.progressPercentage === 0 ? 'text-muted-foreground/40' :
                        stats.progressPercentage >= 100 ? 'text-green-500' :
                        stats.progressPercentage >= 75 ? 'text-blue-500' :
                        stats.progressPercentage >= 50 ? 'text-yellow-500' : 'text-red-500'
                      )}
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-base font-bold">{stats.progressPercentage}%</span>
                  </div>
                </div>
              </div>

              {/* 四个统计数据 */}
              <div className="grid grid-cols-2 gap-3 text-center">
                <div className="space-y-1">
                  <div className="text-xl font-bold text-blue-600">{stats.totalTasks}</div>
                  <div className="text-xs text-muted-foreground">{t('pages.projects.detail.progress.totalTasks')}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-xl font-bold text-green-600">{stats.completedTasks}</div>
                  <div className="text-xs text-muted-foreground">{t('pages.projects.detail.progress.completed')}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-xl font-bold text-red-600">{stats.overdueTasks}</div>
                  <div className="text-xs text-muted-foreground">{t('pages.projects.detail.progress.overdue')}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-xl font-bold text-yellow-600">{stats.upcomingTasks}</div>
                  <div className="text-xs text-muted-foreground">{t('pages.projects.detail.progress.dueSoon')}</div>
                </div>
              </div>
            </div>

            {/* 第三列：倒计时和日期信息 */}
            <div className="space-y-4">
              {/* 倒计时 */}
              {project.deadline && daysUntil !== null && (
                <div className="text-center p-4 rounded-lg bg-muted/50">
                  <div className="text-sm font-medium text-muted-foreground mb-2">{t('pages.projects.detail.countdown.title')}</div>
                  <div className={cn(
                    "text-3xl font-bold mb-1",
                    daysUntil < 0 ? "text-red-600" :
                    daysUntil <= 7 ? "text-yellow-600" : "text-green-600"
                  )}>
                    {daysUntil < 0 ? Math.abs(daysUntil) : daysUntil}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {daysUntil < 0 ? t('pages.projects.detail.countdown.daysOverdue') :
                     daysUntil === 0 ? t('pages.projects.detail.countdown.dueToday') :
                     daysUntil === 1 ? t('pages.projects.detail.countdown.dayLeft') : t('pages.projects.detail.countdown.daysLeft')}
                  </div>
                </div>
              )}

              {/* 各种日期信息 */}
              <div className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">{t('pages.projects.detail.dates.created')}</span>
                    <span>{new Date(project.createdAt).toLocaleDateString()}</span>
                  </div>

                  {project.deadline && (
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">{t('pages.projects.detail.dates.deadline')}</span>
                      <span>{new Date(project.deadline).toLocaleDateString()}</span>
                    </div>
                  )}

                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">{t('pages.projects.detail.dates.updated')}</span>
                    <span>{new Date(project.updatedAt).toLocaleDateString()}</span>
                  </div>

                  {associatedArea && (
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">{t('pages.projects.detail.dates.area')}</span>
                      <Link
                        to={`/areas/${associatedArea.id}`}
                        className="text-area hover:underline flex items-center gap-1"
                      >
                        <div className="w-2 h-2 rounded-full bg-area"></div>
                        {associatedArea.name}
                      </Link>
                    </div>
                  )}
                </div>
              </div>

              {/* 操作按钮 - 归档状态下隐藏 */}
              {!isArchived && (
                <div className="pt-4 border-t border-border">
                  <div className="flex items-center gap-2">
                    {/* 编辑 */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="sm" aria-label={t('pages.projects.detail.actions.editProject')} onClick={() => setIsEditDialogOpen(true)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">{t('pages.projects.detail.actions.editProject')}</TooltipContent>
                    </Tooltip>

                    {/* 归档 */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="sm" aria-label={t('pages.projects.detail.actions.archiveProject')} onClick={handleArchiveProject}>
                          <Archive className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">{t('pages.projects.detail.actions.archiveProject')}</TooltipContent>
                    </Tooltip>

                    {/* 删除 */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                          aria-label={t('pages.projects.detail.actions.deleteProject')}
                          onClick={handleDeleteProject}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="bottom">{t('pages.projects.detail.actions.deleteProject')}</TooltipContent>
                    </Tooltip>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 主要内容区域 - 左右布局 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：项目任务 */}
        <div className="lg:col-span-2 space-y-6">
          {/* Tasks */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>{t('pages.projects.detail.tasks.title')}</CardTitle>
                  <CardDescription>{t('pages.projects.detail.tasks.description')}</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  {!isArchived && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowTaskFilters(!showTaskFilters)}
                    >
                      <Search className="h-4 w-4 mr-2" />
                      {showTaskFilters ? t('pages.projects.detail.tasks.hideFilters') : t('pages.projects.detail.tasks.showFilters')}
                    </Button>
                  )}
                  {!isArchived && (
                    <Button variant="outline" size="sm" onClick={() => setIsCreateTaskDialogOpen(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      {t('pages.projects.detail.tasks.addTask')}
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* 搜索和过滤器 */}
              {showTaskFilters && (
                <div className="mb-6">
                  <TaskSearchAndFilter
                    tasks={projectTasks as any[]}
                    onFilteredTasksChange={handleFilteredTasksChange}
                    className="mb-4"
                  />
                </div>
              )}

              {projectTasks.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <div className="text-4xl mb-2">📝</div>
                  <p className="text-sm">{t('pages.projects.detail.tasks.noTasks')}</p>
                  <p className="text-xs mt-1">{t('pages.projects.detail.tasks.noTasksHint')}</p>
                </div>
              ) : (
                <TaskList
                  tasks={showTaskFilters ? filteredTasks : projectTasks}
                  onTaskToggle={isArchived ? undefined : handleToggleTask}
                  onTaskEdit={isArchived ? undefined : setEditingTask}
                  onTaskDelete={isArchived ? undefined : handleDeleteTask}
                  onTaskAddSubtask={isArchived ? undefined : handleAddSubtask}
                  onTaskMove={isArchived ? undefined : handleTaskMove}
                  onTaskClick={(task) => {
                    setSelectedTaskForDetail(task)
                    setIsTaskDetailPanelOpen(true)
                  }}
                  className="space-y-2"
                  useEnhancedView={false}
                  useHierarchicalView={projectTasks.length <= 50}
                  useVirtualizedView={projectTasks.length > 50}
                  showHierarchyControls={!isArchived}
                />
              )}
            </CardContent>
          </Card>
        </div>

        {/* 右侧：其他内容 */}
        <div className="space-y-6">
          {/* KPI Management */}
          <ProjectKPIManagement
            projectId={project.id}
          />

          {/* Project Deliverables */}
          <ProjectDeliverables projectId={project.id} />

          {/* Project Resources */}
          <ProjectResources projectId={project.id} />

          {/* Notes */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>{t('pages.projects.detail.notes.title')}</CardTitle>
                {!isArchived && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsNotesEditing(!isNotesEditing)}
                  >
                    {isNotesEditing ? t('pages.projects.detail.notes.cancel') : t('pages.projects.detail.notes.edit')}
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {isNotesEditing ? (
                <div className="space-y-3">
                  <Textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder={t('pages.projects.detail.notes.placeholder')}
                    rows={6}
                  />
                  <div className="flex gap-2">
                    <Button size="sm" onClick={handleSaveNotes}>
                      {t('pages.projects.detail.notes.save')}
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => setIsNotesEditing(false)}>
                      {t('pages.projects.detail.notes.cancel')}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">
                  {notes || t('pages.projects.detail.notes.empty')}
                </div>
              )}
            </CardContent>
          </Card>


        </div>
      </div>

      {/* Edit Project Dialog */}
      <CreateProjectDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        onSubmit={handleEditProject}
        initialData={project}
      />

      {/* Create/Edit Task Dialog */}
      <CreateTaskDialog
        isOpen={isCreateTaskDialogOpen || !!editingTask}
        onClose={() => {
          setIsCreateTaskDialogOpen(false)
          setEditingTask(null)
          setParentTaskId(undefined)
        }}
        onSubmit={editingTask ? handleEditTask : handleCreateTask}
        initialData={editingTask || undefined}
        parentTaskId={parentTaskId}
        projectId={project.id}
      />

      <DeleteTaskDialog
        isOpen={isDeleteTaskDialogOpen}
        onClose={() => {
          setIsDeleteTaskDialogOpen(false)
          setDeletingTask(null)
        }}
        onConfirm={handleConfirmDeleteTask}
        task={deletingTask}
        childrenCount={deletingTask ? getDescendantCount(deletingTask.id) : 0}
      />

      <ResourceLinkDialog
        isOpen={isResourceLinkDialogOpen}
        onClose={() => setIsResourceLinkDialogOpen(false)}
        projectId={project.id}
        onResourcesLinked={() => {
          // Resources will be automatically updated by the ProjectResources component
          setIsResourceLinkDialogOpen(false)
        }}
      />

      {/* Task Detail Panel */}
      <TaskDetailPanel
        task={selectedTaskForDetail}
        isOpen={isTaskDetailPanelOpen}
        onClose={handleTaskDetailClose}
        onSave={isArchived ? () => {} : handleTaskDetailSave}
        onDelete={isArchived ? undefined : handleTaskDetailDelete}
        onAddSubtask={isArchived ? undefined : handleTaskDetailAddSubtask}
        projects={projects}
        areas={areas}
      />

      {/* Confirmation Dialog */}
      <ConfirmDialog />
    </div>
  )
}

export default ProjectDetailPage
