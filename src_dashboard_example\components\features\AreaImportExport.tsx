import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '../ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { useUIStore } from '../../store/uiStore'
import { useLanguage } from '../../contexts/LanguageContext'

interface AreaImportExportProps {
  areaId: string
  areaName: string
  className?: string
}

interface ExportData {
  area: any
  projects: any[]
  habits: any[]
  metrics: any[]
  checklists: any[]
  resources: any[]
  notes: string
}

export function AreaImportExport({ areaId, areaName, className }: AreaImportExportProps) {
  const { t } = useLanguage()
  const { addNotification } = useUIStore()
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false)
  const [exportFormat, setExportFormat] = useState<'json' | 'markdown' | 'csv'>('json')
  const [exportScope, setExportScope] = useState<'all' | 'selective'>('all')
  const [selectedComponents, setSelectedComponents] = useState<string[]>([
    'area', 'projects', 'habits', 'metrics', 'checklists', 'resources', 'notes'
  ])

  const exportComponents = [
    { id: 'area', label: '领域信息', description: '基本信息和标准' },
    { id: 'projects', label: '关联项目', description: '项目列表和状态' },
    { id: 'habits', label: '习惯记录', description: '习惯和打卡记录' },
    { id: 'metrics', label: '关键指标', description: '指标数据和历史' },
    { id: 'checklists', label: '清单模板', description: '模板和实例' },
    { id: 'resources', label: '关联资源', description: '知识资源链接' },
    { id: 'notes', label: '领域笔记', description: 'Markdown笔记内容' }
  ]

  const handleExport = async () => {
    try {
      // 这里应该调用实际的导出API
      const exportData: ExportData = {
        area: { id: areaId, name: areaName },
        projects: [],
        habits: [],
        metrics: [],
        checklists: [],
        resources: [],
        notes: ''
      }

      let content = ''
      let filename = ''
      let mimeType = ''

      switch (exportFormat) {
        case 'json':
          content = JSON.stringify(exportData, null, 2)
          filename = `${areaName}-export-${new Date().toISOString().split('T')[0]}.json`
          mimeType = 'application/json'
          break
        
        case 'markdown':
          content = generateMarkdownExport(exportData)
          filename = `${areaName}-export-${new Date().toISOString().split('T')[0]}.md`
          mimeType = 'text/markdown'
          break
        
        case 'csv':
          content = generateCSVExport(exportData)
          filename = `${areaName}-export-${new Date().toISOString().split('T')[0]}.csv`
          mimeType = 'text/csv'
          break
      }

      // 创建下载链接
      const blob = new Blob([content], { type: mimeType })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      addNotification({
        type: 'success',
        title: '导出成功',
        message: `领域数据已导出为 ${exportFormat.toUpperCase()} 格式`
      })

      setIsExportDialogOpen(false)
    } catch (error) {
      console.error('Export failed:', error)
      addNotification({
        type: 'error',
        title: '导出失败',
        message: '导出过程中发生错误，请重试'
      })
    }
  }

  const handleImport = async (file: File) => {
    try {
      const content = await file.text()
      let importData: any

      if (file.name.endsWith('.json')) {
        importData = JSON.parse(content)
      } else if (file.name.endsWith('.md')) {
        // 解析Markdown格式的导入
        importData = parseMarkdownImport(content)
      } else {
        throw new Error('不支持的文件格式')
      }

      // 这里应该调用实际的导入API
      console.log('Import data:', importData)

      addNotification({
        type: 'success',
        title: '导入成功',
        message: '领域数据已成功导入'
      })

      setIsImportDialogOpen(false)
    } catch (error) {
      console.error('Import failed:', error)
      addNotification({
        type: 'error',
        title: '导入失败',
        message: '导入过程中发生错误，请检查文件格式'
      })
    }
  }

  const generateMarkdownExport = (data: ExportData): string => {
    return `# ${data.area.name} - 领域导出

## 基本信息
- 领域ID: ${data.area.id}
- 导出时间: ${new Date().toLocaleString()}

## 关联项目 (${data.projects.length})
${data.projects.map(p => `- ${p.name} (${p.status})`).join('\n')}

## 习惯记录 (${data.habits.length})
${data.habits.map(h => `- ${h.name} (${h.frequency})`).join('\n')}

## 关键指标 (${data.metrics.length})
${data.metrics.map(m => `- ${m.name}: ${m.value} ${m.unit}`).join('\n')}

## 清单模板 (${data.checklists.length})
${data.checklists.map(c => `- ${c.name}`).join('\n')}

## 关联资源 (${data.resources.length})
${data.resources.map(r => `- [${r.name}](${r.url})`).join('\n')}

## 领域笔记
${data.notes}
`
  }

  const generateCSVExport = (data: ExportData): string => {
    // 简化的CSV导出，主要导出项目和习惯数据
    const headers = ['类型', '名称', '状态', '创建时间']
    const rows = [
      ...data.projects.map(p => ['项目', p.name, p.status, p.createdAt]),
      ...data.habits.map(h => ['习惯', h.name, h.frequency, h.createdAt]),
      ...data.metrics.map(m => ['指标', m.name, `${m.value} ${m.unit}`, m.createdAt])
    ]

    return [headers, ...rows].map(row => row.join(',')).join('\n')
  }

  const parseMarkdownImport = (content: string): any => {
    // 简化的Markdown解析
    return {
      area: { name: '导入的领域' },
      projects: [],
      habits: [],
      metrics: [],
      checklists: [],
      resources: [],
      notes: content
    }
  }

  const toggleComponent = (componentId: string) => {
    setSelectedComponents(prev => 
      prev.includes(componentId)
        ? prev.filter(id => id !== componentId)
        : [...prev, componentId]
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          数据管理
        </CardTitle>
        <CardDescription>
          导入导出领域数据，支持多种格式
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 导出功能 */}
        <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" className="w-full">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              导出领域数据
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>导出领域数据</DialogTitle>
              <DialogDescription>
                选择导出格式和包含的数据组件
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">导出格式</label>
                <Select value={exportFormat} onValueChange={(value: any) => setExportFormat(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="json">JSON - 完整数据结构</SelectItem>
                    <SelectItem value="markdown">Markdown - 可读性文档</SelectItem>
                    <SelectItem value="csv">CSV - 表格数据</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">包含的数据组件</label>
                <div className="grid grid-cols-2 gap-2">
                  {exportComponents.map(component => (
                    <div
                      key={component.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedComponents.includes(component.id)
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => toggleComponent(component.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm font-medium">{component.label}</div>
                          <div className="text-xs text-muted-foreground">{component.description}</div>
                        </div>
                        {selectedComponents.includes(component.id) && (
                          <Badge variant="secondary" className="text-xs">✓</Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleExport}>
                导出数据
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 导入功能 */}
        <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" className="w-full">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
              </svg>
              导入领域数据
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>导入领域数据</DialogTitle>
              <DialogDescription>
                选择要导入的数据文件（支持 JSON 和 Markdown 格式）
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <input
                  type="file"
                  accept=".json,.md"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) {
                      handleImport(file)
                    }
                  }}
                  className="hidden"
                  id="import-file"
                />
                <label htmlFor="import-file" className="cursor-pointer">
                  <div className="text-4xl mb-2">📁</div>
                  <div className="text-sm font-medium">点击选择文件</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    支持 .json 和 .md 格式
                  </div>
                </label>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsImportDialogOpen(false)}>
                取消
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 快速操作 */}
        <div className="pt-4 border-t">
          <div className="text-xs text-muted-foreground mb-2">快速操作</div>
          <div className="flex gap-2">
            <Button variant="ghost" size="sm" className="text-xs">
              备份到云端
            </Button>
            <Button variant="ghost" size="sm" className="text-xs">
              同步设置
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default AreaImportExport
