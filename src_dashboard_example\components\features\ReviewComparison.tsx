import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../ui/tabs'
import { X, Calendar, TrendingUp, TrendingDown, Minus, BarChart3 } from 'lucide-react'
import { useLanguage } from '../../contexts/LanguageContext'
import { databaseApi } from '../../lib/api'
import type { Review } from '../../../../shared/types'

interface ReviewComparisonProps {
  reviewIds: string[]
  onClose: () => void
}

interface ComparisonData {
  reviews: Review[]
  metrics: {
    contentSimilarity: number
    progressTrend: 'up' | 'down' | 'stable'
    keyDifferences: string[]
    commonThemes: string[]
  }
}

export function ReviewComparison({ reviewIds, onClose }: ReviewComparisonProps) {
  const { t } = useLanguage()
  const [loading, setLoading] = useState(true)
  const [comparisonData, setComparisonData] = useState<ComparisonData | null>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadComparisonData()
  }, [reviewIds])

  const loadComparisonData = async () => {
    setLoading(true)
    setError(null)
    try {
      // Load all reviews
      const reviewPromises = reviewIds.map(id => databaseApi.getReviewById(id))
      const results = await Promise.all(reviewPromises)

      const reviews = results
        .filter(result => result.success)
        .map(result => result.data)
        .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())

      if (reviews.length < 2) {
        setError('Need at least 2 reviews to compare')
        return
      }

      // Analyze comparison metrics
      const metrics = analyzeReviews(reviews)

      setComparisonData({ reviews, metrics })
    } catch (err) {
      setError('Failed to load comparison data')
      console.error('Error loading comparison data:', err)
    } finally {
      setLoading(false)
    }
  }

  const analyzeReviews = (reviews: Review[]): ComparisonData['metrics'] => {
    // Simple analysis - in a real app, this would be more sophisticated
    const contentSimilarity = calculateContentSimilarity(reviews)
    const progressTrend = calculateProgressTrend(reviews)
    const keyDifferences = findKeyDifferences(reviews)
    const commonThemes = findCommonThemes(reviews)

    return {
      contentSimilarity,
      progressTrend,
      keyDifferences,
      commonThemes
    }
  }

  const calculateContentSimilarity = (reviews: Review[]): number => {
    // Simplified similarity calculation
    if (reviews.length < 2) return 0
    
    const contents = reviews.map(r => JSON.stringify(r.content || {}))
    const similarities = []
    
    for (let i = 0; i < contents.length - 1; i++) {
      for (let j = i + 1; j < contents.length; j++) {
        const similarity = calculateStringSimilarity(contents[i], contents[j])
        similarities.push(similarity)
      }
    }
    
    return Math.round(similarities.reduce((sum, sim) => sum + sim, 0) / similarities.length)
  }

  const calculateStringSimilarity = (str1: string, str2: string): number => {
    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1
    
    if (longer.length === 0) return 100
    
    const editDistance = levenshteinDistance(longer, shorter)
    return Math.round(((longer.length - editDistance) / longer.length) * 100)
  }

  const levenshteinDistance = (str1: string, str2: string): number => {
    const matrix = []
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }
    
    return matrix[str2.length][str1.length]
  }

  const calculateProgressTrend = (reviews: Review[]): 'up' | 'down' | 'stable' => {
    if (reviews.length < 2) return 'stable'
    
    // Simple trend calculation based on completion status and insights
    const scores = reviews.map(review => {
      let score = 0
      if (review.status === 'completed') score += 30
      if (review.insights) {
        // Simplified scoring based on insights
        score += Object.keys(review.insights).length * 10
      }
      return score
    })
    
    const firstHalf = scores.slice(0, Math.floor(scores.length / 2))
    const secondHalf = scores.slice(Math.floor(scores.length / 2))
    
    const firstAvg = firstHalf.reduce((sum, score) => sum + score, 0) / firstHalf.length
    const secondAvg = secondHalf.reduce((sum, score) => sum + score, 0) / secondHalf.length
    
    const difference = secondAvg - firstAvg
    
    if (Math.abs(difference) < 5) return 'stable'
    return difference > 0 ? 'up' : 'down'
  }

  const findKeyDifferences = (reviews: Review[]): string[] => {
    const differences = []
    
    // Compare types
    const types = [...new Set(reviews.map(r => r.type))]
    if (types.length > 1) {
      differences.push(`Different review types: ${types.join(', ')}`)
    }
    
    // Compare periods
    const periods = reviews.map(r => r.period).sort()
    differences.push(`Time span: ${periods[0]} to ${periods[periods.length - 1]}`)
    
    // Compare status
    const statuses = [...new Set(reviews.map(r => r.status))]
    if (statuses.length > 1) {
      differences.push(`Mixed completion status: ${statuses.join(', ')}`)
    }
    
    return differences
  }

  const findCommonThemes = (reviews: Review[]): string[] => {
    const themes = []
    
    // Check for common content patterns
    const allContent = reviews.map(r => JSON.stringify(r.content || {})).join(' ').toLowerCase()
    
    const commonWords = ['project', 'goal', 'habit', 'task', 'improvement', 'challenge', 'success']
    commonWords.forEach(word => {
      if (allContent.includes(word)) {
        themes.push(`Focus on ${word}s`)
      }
    })
    
    // Check for similar periods
    const sameType = reviews.every(r => r.type === reviews[0].type)
    if (sameType) {
      themes.push(`Consistent ${reviews[0].type} review pattern`)
    }
    
    return themes.slice(0, 5) // Limit to 5 themes
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      case 'stable':
        return <Minus className="h-4 w-4 text-gray-500" />
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 className="h-6 w-6" />
            {t('pages.reviews.comparison.title')}
          </h2>
          <Button variant="outline" onClick={onClose}>
            <X className="h-4 w-4 mr-2" />
            {t('common.close')}
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-20 bg-muted rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error || !comparisonData) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 className="h-6 w-6" />
            {t('pages.reviews.comparison.title')}
          </h2>
          <Button variant="outline" onClick={onClose}>
            <X className="h-4 w-4 mr-2" />
            {t('common.close')}
          </Button>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">{t('pages.reviews.comparison.comparisonUnavailable')}</h3>
              <p className="text-muted-foreground mb-4">
                {error || t('pages.reviews.comparison.needAtLeast2')}
              </p>
              <Button onClick={loadComparisonData}>
                {t('pages.reviews.analysis.tryAgain')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 className="h-6 w-6" />
            {t('pages.reviews.comparison.title')}
          </h2>
          <p className="text-muted-foreground">
            {t('pages.reviews.comparison.description', { count: comparisonData.reviews.length })}
          </p>
        </div>
        <Button variant="outline" onClick={onClose}>
          <X className="h-4 w-4 mr-2" />
          {t('common.close')}
        </Button>
      </div>

      {/* Comparison Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">{t('pages.reviews.comparison.contentSimilarity')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {comparisonData.metrics.contentSimilarity}%
            </div>
            <p className="text-xs text-muted-foreground">
              {t('pages.reviews.comparison.contentAnalysis')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">{t('pages.reviews.comparison.progressTrend')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              {getTrendIcon(comparisonData.metrics.progressTrend)}
              <span className="text-2xl font-bold capitalize">
                {comparisonData.metrics.progressTrend}
              </span>
            </div>
            <p className="text-xs text-muted-foreground">
              {t('pages.reviews.comparison.progressAssessment')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">{t('pages.reviews.comparison.timeSpan')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {comparisonData.reviews.length}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('pages.reviews.comparison.reviewsCompared')}
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">{t('pages.reviews.comparison.overview')}</TabsTrigger>
          <TabsTrigger value="details">{t('pages.reviews.comparison.details')}</TabsTrigger>
          <TabsTrigger value="insights">{t('pages.reviews.comparison.insights')}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {comparisonData.reviews.map((review, index) => (
              <Card key={review.id}>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {review.title || `${t(`pages.reviews.editor.types.${review.type}`)}`}
                  </CardTitle>
                  <CardDescription>
                    {review.period} • {formatDate(review.createdAt)}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">{t('pages.reviews.comparison.labels.status')}:</span>
                      <Badge variant={review.status === 'completed' ? 'default' : 'secondary'}>
                        {t(`pages.reviews.editor.status.${review.status}`)}
                      </Badge>
                    </div>
                    {review.summary && (
                      <div>
                        <span className="text-sm font-medium">{t('pages.reviews.comparison.labels.summary')}:</span>
                        <p className="text-sm text-muted-foreground mt-1 line-clamp-3">
                          {review.summary}
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="details" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('pages.reviews.comparison.keyDifferences')}</CardTitle>
              </CardHeader>
              <CardContent>
                {comparisonData.metrics.keyDifferences.length > 0 ? (
                  <div className="space-y-2">
                    {comparisonData.metrics.keyDifferences.map((diff, index) => (
                      <div key={index} className="text-sm p-2 bg-muted rounded">
                        {diff}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">{t('pages.reviews.comparison.noDifferences')}</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t('pages.reviews.comparison.commonThemes')}</CardTitle>
              </CardHeader>
              <CardContent>
                {comparisonData.metrics.commonThemes.length > 0 ? (
                  <div className="space-y-2">
                    {comparisonData.metrics.commonThemes.map((theme, index) => (
                      <div key={index} className="text-sm p-2 bg-green-50 border-l-2 border-green-500 rounded">
                        {theme}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">{t('pages.reviews.comparison.noThemes')}</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('pages.reviews.comparison.comparisonInsights')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">{t('pages.reviews.comparison.contentAnalysis')}</h4>
                  <p className="text-sm text-muted-foreground">
                    {t('pages.reviews.recent.description')} {comparisonData.metrics.contentSimilarity}% {t('pages.reviews.comparison.contentSimilarity')},
                    {t('pages.reviews.comparison.description', { count: comparisonData.reviews.length })} {comparisonData.metrics.contentSimilarity > 70 ? t('pages.reviews.comparison.consistent') : t('pages.reviews.comparison.varied')}.
                  </p>
                </div>

                <div>
                  <h4 className="font-medium mb-2">{t('pages.reviews.comparison.progressAssessment')}</h4>
                  <p className="text-sm text-muted-foreground">
                    {t('pages.reviews.comparison.progressTrend')}: {comparisonData.metrics.progressTrend},
                    {comparisonData.metrics.progressTrend === 'up'
                      ? t('pages.reviews.comparison.showingImprovement')
                      : comparisonData.metrics.progressTrend === 'down'
                      ? t('pages.reviews.comparison.needsAttention')
                      : t('pages.reviews.comparison.maintainingConsistent')}
                  </p>
                </div>

                <div>
                  <h4 className="font-medium mb-2">{t('pages.reviews.comparison.recommendations')}</h4>
                  <div className="space-y-1 text-sm text-muted-foreground">
                    {comparisonData.metrics.progressTrend === 'down' && (
                      <p>• {t('pages.reviews.comparison.needsAttention')}</p>
                    )}
                    {comparisonData.metrics.contentSimilarity < 50 && (
                      <p>• {t('pages.reviews.comparison.varied')}</p>
                    )}
                    {comparisonData.metrics.commonThemes.length > 0 && (
                      <p>• {t('pages.reviews.comparison.commonThemes')}</p>
                    )}
                    <p>• {t('pages.reviews.analysis.description')}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default ReviewComparison
