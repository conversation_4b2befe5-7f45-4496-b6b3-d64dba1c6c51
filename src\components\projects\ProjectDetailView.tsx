import { createSignal, onMount, onCleanup, For, Show, type Component } from 'solid-js';
import type { Project, Task, KPI } from './types';
import { getStatusDotColor } from './types';
import { TaskManager } from '../tasks';

// --- Sub-components for Detail View ---

const TaskItem: Component<{ task: Task; level: number }> = (props) => {
    // Basic task rendering, can be extended with drag-and-drop libraries
    return (
        <div style={{ 'padding-left': `${props.level * 24}px` }}>
            <div class="group flex items-center gap-3 py-2 border-b border-gray-200 dark:border-gray-700/50 hover:bg-gray-50 dark:hover:bg-gray-700/20 px-2 rounded">
                <input type="checkbox" checked={props.task.completed} class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 flex-shrink-0" />
                <span class="flex-1 text-sm">{props.task.title}</span>
                {/* Add more details like priority, due date */}
            </div>
            <For each={props.task.subtasks}>
                {subtask => <TaskItem task={subtask} level={props.level + 1} />}
            </For>
        </div>
    );
};

const KpiItem: Component<{ kpi: KPI }> = (props) => {
    const progress = () => (props.kpi.currentValue / props.kpi.targetValue) * 100;
    return (
        <div>
            <div class="flex justify-between items-baseline mb-1">
                <span class="text-sm font-medium">{props.kpi.name}</span>
                <span class="text-xs text-gray-500 dark:text-gray-400">
                    {props.kpi.currentValue}{props.kpi.unit} / {props.kpi.targetValue}{props.kpi.unit}
                </span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div class="bg-blue-500 h-2 rounded-full" style={{ width: `${progress()}%` }}></div>
            </div>
        </div>
    )
}

// --- Main Detail View Component ---

interface ProjectDetailViewProps {
    project: Project;
    onClose: () => void;
    calculateProgress: (project: Project) => number;
}

export const ProjectDetailView: Component<ProjectDetailViewProps> = (props) => {
    const [isVisible, setIsVisible] = createSignal(false);
    const progress = () => props.calculateProgress(props.project);

    // 笔记功能状态
    const [notes, setNotes] = createSignal('');
    const [isEditingNotes, setIsEditingNotes] = createSignal(false);

    // 保存笔记
    const saveNotes = () => {
        // TODO: 实现笔记保存逻辑
        console.log('Saving notes:', notes());
        setIsEditingNotes(false);
    };

    onMount(() => {
        // Animation trigger
        requestAnimationFrame(() => setIsVisible(true));
        // Handle Esc key to close
        const handleKeyDown = (e: KeyboardEvent) => { if (e.key === 'Escape') props.onClose(); };
        window.addEventListener('keydown', handleKeyDown);
        onCleanup(() => window.removeEventListener('keydown', handleKeyDown));
    });

    const handleClose = () => {
        setIsVisible(false);
        setTimeout(props.onClose, 200); // Wait for animation to finish
    };

    return (
        <div
            class="fixed inset-0 bg-black/60 z-40 flex items-center justify-center transition-opacity duration-200"
            classList={{ 'opacity-0': !isVisible(), 'opacity-100': isVisible() }}
            onClick={handleClose}
        >
            <div
                class="bg-gray-50 dark:bg-gray-800 w-[95vw] h-[90vh] max-w-6xl rounded-lg shadow-2xl flex flex-col transition-all duration-200"
                classList={{ 'scale-95 opacity-0': !isVisible(), 'scale-100 opacity-100': isVisible() }}
                onClick={e => e.stopPropagation()}
            >
                {/* Header */}
                <header class="p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700/50 flex justify-between items-center flex-shrink-0">
                    <div>
                        <div class="flex items-center gap-3">
                            <div class={`w-3 h-3 rounded-full ${getStatusDotColor(props.project.status)}`}></div>
                            <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100">{props.project.name}</h1>
                        </div>
                        <Show when={props.project.areaName}>
                            <a href="#" class="text-sm text-blue-600 hover:underline mt-1">{props.project.areaName}</a>
                        </Show>
                    </div>
                    <button onClick={handleClose} class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                    </button>
                </header>

                {/* Body - 新的2:1布局 */}
                <div class="flex-1 p-4 sm:p-6 overflow-hidden">
                    <div class="grid grid-cols-3 gap-6 h-full">
                        {/* 左侧：任务管理 (2/3 宽度) */}
                        <div class="col-span-2 overflow-y-auto">
                            <div class="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                                <h2 class="text-lg font-semibold mb-4 flex items-center gap-2">
                                    <span>✅</span>
                                    任务管理
                                </h2>
                                <TaskManager
                                    mode="list"
                                    context={{ projectId: props.project.id }}
                                    features={{
                                        enableDragSort: true,
                                        enableInlineEdit: true,
                                        enableSubtasks: true,
                                        enableFilters: true,
                                        showStats: true
                                    }}
                                />
                            </div>
                        </div>

                        {/* 右侧：KPI管理、资源关联、笔记功能 (1/3 宽度) */}
                        <div class="overflow-y-auto space-y-4">
                            {/* KPI管理 */}
                            <div class="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                                <h3 class="font-semibold mb-3 flex items-center gap-2">
                                    <span>📈</span>
                                    KPI管理
                                </h3>
                                <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                                    <div class="text-4xl mb-2">📈</div>
                                    <p class="text-sm">KPI管理功能开发中...</p>
                                </div>
                            </div>

                            {/* 资源关联 */}
                            <div class="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                                <h3 class="font-semibold mb-3 flex items-center gap-2">
                                    <span>📎</span>
                                    资源关联
                                </h3>
                                <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                                    <div class="text-4xl mb-2">📎</div>
                                    <p class="text-sm">资源关联功能开发中...</p>
                                </div>
                            </div>

                            {/* 笔记功能 */}
                            <div class="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                                <h3 class="font-semibold mb-3 flex items-center gap-2">
                                    <span>📝</span>
                                    项目笔记
                                </h3>
                                <Show when={!isEditingNotes()}>
                                    <div class="space-y-3">
                                        <Show when={notes()} fallback={
                                            <p class="text-gray-500 dark:text-gray-400 text-sm">暂无笔记</p>
                                        }>
                                            <div class="prose prose-sm max-w-none">
                                                <pre class="whitespace-pre-wrap text-sm">{notes()}</pre>
                                            </div>
                                        </Show>
                                        <button
                                            type="button"
                                            class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800"
                                            onClick={() => setIsEditingNotes(true)}
                                        >
                                            {notes() ? '编辑笔记' : '添加笔记'}
                                        </button>
                                    </div>
                                </Show>

                                <Show when={isEditingNotes()}>
                                    <div class="space-y-3">
                                        <textarea
                                            class="w-full h-32 p-3 border border-gray-300 dark:border-gray-600 rounded-md resize-none text-sm"
                                            placeholder="在这里记录项目相关的笔记..."
                                            value={notes()}
                                            onInput={(e) => setNotes(e.currentTarget.value)}
                                        />
                                        <div class="flex gap-2">
                                            <button
                                                type="button"
                                                class="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                                                onClick={saveNotes}
                                            >
                                                保存
                                            </button>
                                            <button
                                                type="button"
                                                class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800"
                                                onClick={() => setIsEditingNotes(false)}
                                            >
                                                取消
                                            </button>
                                        </div>
                                    </div>
                                </Show>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

