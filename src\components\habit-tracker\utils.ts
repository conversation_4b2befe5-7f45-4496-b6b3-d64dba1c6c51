/**
 * 习惯追踪工具函数
 * 提供习惯数据处理、计算和格式化等实用功能
 */

import type { 
  BaseHabit, 
  HabitRecord, 
  HabitType,
  HabitFrequency,
  HabitStatistics 
} from './types'

/**
 * 计算习惯连击天数
 */
export function calculateStreak(records: HabitRecord[], fromDate?: Date): number {
  const sortedRecords = records
    .filter(r => r.completed)
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  
  if (sortedRecords.length === 0) return 0
  
  let streak = 0
  const startDate = fromDate || new Date()
  
  for (let i = 0; i < sortedRecords.length; i++) {
    const recordDate = new Date(sortedRecords[i].date)
    const expectedDate = new Date(startDate)
    expectedDate.setDate(startDate.getDate() - i)
    
    if (recordDate.toDateString() === expectedDate.toDateString()) {
      streak++
    } else {
      break
    }
  }
  
  return streak
}

/**
 * 计算最长连击天数
 */
export function calculateLongestStreak(records: HabitRecord[]): number {
  const completedRecords = records
    .filter(r => r.completed)
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
  
  if (completedRecords.length === 0) return 0
  
  let maxStreak = 0
  let currentStreak = 0
  let lastDate: Date | null = null
  
  for (const record of completedRecords) {
    const recordDate = new Date(record.date)
    
    if (lastDate) {
      const daysDiff = Math.floor((recordDate.getTime() - lastDate.getTime()) / (1000 * 60 * 60 * 24))
      
      if (daysDiff === 1) {
        currentStreak++
      } else {
        maxStreak = Math.max(maxStreak, currentStreak)
        currentStreak = 1
      }
    } else {
      currentStreak = 1
    }
    
    lastDate = recordDate
  }
  
  return Math.max(maxStreak, currentStreak)
}

/**
 * 计算指定时期的完成率
 */
export function calculateCompletionRate(
  records: HabitRecord[], 
  days: number, 
  endDate?: Date
): number {
  const end = endDate || new Date()
  const start = new Date(end.getTime() - (days - 1) * 24 * 60 * 60 * 1000)
  
  const periodRecords = records.filter(r => {
    const recordDate = new Date(r.date)
    return recordDate >= start && recordDate <= end
  })
  
  const completedCount = periodRecords.filter(r => r.completed).length
  return days > 0 ? Math.round((completedCount / days) * 100) : 0
}

/**
 * 获取习惯类型的显示名称
 */
export function getHabitTypeLabel(type: HabitType): string {
  switch (type) {
    case 'boolean':
      return 'Yes/No'
    case 'numeric':
      return 'Number'
    case 'duration':
      return 'Duration'
    default:
      return type
  }
}

/**
 * 获取习惯频率的显示名称
 */
export function getHabitFrequencyLabel(frequency: HabitFrequency): string {
  switch (frequency) {
    case 'daily':
      return 'Daily'
    case 'weekly':
      return 'Weekly'
    case 'monthly':
      return 'Monthly'
    default:
      return frequency
  }
}

/**
 * 获取习惯类型的颜色类名
 */
export function getHabitTypeColor(type: HabitType): string {
  switch (type) {
    case 'boolean':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'numeric':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'duration':
      return 'bg-purple-100 text-purple-800 border-purple-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

/**
 * 格式化习惯目标显示
 */
export function formatHabitTarget(habit: BaseHabit): string {
  if (habit.type === 'boolean') {
    return 'Complete'
  }
  
  const target = habit.target.toString()
  const unit = habit.unit ? ` ${habit.unit}` : ''
  
  return `${target}${unit}`
}

/**
 * 格式化记录值显示
 */
export function formatRecordValue(record: HabitRecord, habit: BaseHabit): string {
  if (habit.type === 'boolean') {
    return record.completed ? 'Completed' : 'Not completed'
  }
  
  const value = record.value?.toString() || '0'
  const unit = habit.unit ? ` ${habit.unit}` : ''
  
  return `${value}${unit}`
}

/**
 * 检查习惯是否在指定日期完成
 */
export function isHabitCompletedOnDate(
  habitId: string, 
  date: Date, 
  records: HabitRecord[]
): boolean {
  const dateString = date.toISOString().split('T')[0]
  const record = records.find(r => r.habitId === habitId && r.date === dateString)
  return record?.completed || false
}

/**
 * 获取习惯在指定日期的记录值
 */
export function getHabitValueOnDate(
  habitId: string, 
  date: Date, 
  records: HabitRecord[]
): number {
  const dateString = date.toISOString().split('T')[0]
  const record = records.find(r => r.habitId === habitId && r.date === dateString)
  return record?.value || 0
}

/**
 * 生成日期范围数组
 */
export function generateDateRange(startDate: Date, endDate: Date): Date[] {
  const dates: Date[] = []
  const current = new Date(startDate)
  
  while (current <= endDate) {
    dates.push(new Date(current))
    current.setDate(current.getDate() + 1)
  }
  
  return dates
}

/**
 * 格式化日期为字符串
 */
export function formatDateString(date: Date): string {
  return date.toISOString().split('T')[0]
}

/**
 * 解析日期字符串
 */
export function parseDateString(dateString: string): Date {
  return new Date(dateString + 'T00:00:00.000Z')
}

/**
 * 检查日期是否为今天
 */
export function isToday(date: Date): boolean {
  const today = new Date()
  return date.toDateString() === today.toDateString()
}

/**
 * 检查日期是否为昨天
 */
export function isYesterday(date: Date): boolean {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return date.toDateString() === yesterday.toDateString()
}

/**
 * 获取相对日期描述
 */
export function getRelativeDateDescription(date: Date): string {
  if (isToday(date)) return 'Today'
  if (isYesterday(date)) return 'Yesterday'
  
  const today = new Date()
  const diffTime = today.getTime() - date.getTime()
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays > 0) {
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
  } else {
    return `In ${Math.abs(diffDays)} day${Math.abs(diffDays) > 1 ? 's' : ''}`
  }
}

/**
 * 计算习惯趋势
 */
export function calculateHabitTrend(records: HabitRecord[]): 'up' | 'down' | 'stable' {
  if (records.length < 14) return 'stable'
  
  const recentWeek = calculateCompletionRate(records.slice(-7), 7)
  const previousWeek = calculateCompletionRate(records.slice(-14, -7), 7)
  
  if (recentWeek > previousWeek * 1.1) return 'up'
  if (recentWeek < previousWeek * 0.9) return 'down'
  return 'stable'
}

/**
 * 获取习惯完成等级
 */
export function getCompletionGrade(completionRate: number): {
  grade: string
  color: string
  description: string
} {
  if (completionRate >= 95) {
    return {
      grade: 'A+',
      color: 'text-green-700 bg-green-100',
      description: 'Excellent'
    }
  }
  if (completionRate >= 90) {
    return {
      grade: 'A',
      color: 'text-green-600 bg-green-100',
      description: 'Great'
    }
  }
  if (completionRate >= 80) {
    return {
      grade: 'B',
      color: 'text-blue-600 bg-blue-100',
      description: 'Good'
    }
  }
  if (completionRate >= 70) {
    return {
      grade: 'C',
      color: 'text-yellow-600 bg-yellow-100',
      description: 'Fair'
    }
  }
  if (completionRate >= 60) {
    return {
      grade: 'D',
      color: 'text-orange-600 bg-orange-100',
      description: 'Needs Improvement'
    }
  }
  return {
    grade: 'F',
    color: 'text-red-600 bg-red-100',
    description: 'Poor'
  }
}

/**
 * 计算习惯统计摘要
 */
export function calculateHabitSummary(habits: BaseHabit[], records: HabitRecord[]): {
  totalHabits: number
  activeHabits: number
  averageCompletion: number
  bestStreak: number
  todayCompletion: number
} {
  const activeHabits = habits.filter(h => h.isActive)
  const today = formatDateString(new Date())
  
  const todayRecords = records.filter(r => r.date === today && r.completed)
  const todayCompletion = activeHabits.length > 0 
    ? Math.round((todayRecords.length / activeHabits.length) * 100)
    : 0
  
  const averageCompletion = habits.length > 0
    ? Math.round(habits.reduce((sum, h) => sum + h.completionRate, 0) / habits.length)
    : 0
  
  const bestStreak = Math.max(...habits.map(h => h.longestStreak), 0)
  
  return {
    totalHabits: habits.length,
    activeHabits: activeHabits.length,
    averageCompletion,
    bestStreak,
    todayCompletion
  }
}

/**
 * 生成习惯颜色
 */
export function generateHabitColor(index: number): string {
  const colors = [
    '#3b82f6', // blue
    '#10b981', // emerald
    '#f59e0b', // amber
    '#ef4444', // red
    '#8b5cf6', // violet
    '#06b6d4', // cyan
    '#84cc16', // lime
    '#f97316', // orange
    '#ec4899', // pink
    '#6b7280'  // gray
  ]
  
  return colors[index % colors.length]
}

/**
 * 验证习惯数据
 */
export function validateHabitData(data: Partial<BaseHabit>): string[] {
  const errors: string[] = []
  
  if (!data.name?.trim()) {
    errors.push('Habit name is required')
  }
  
  if (data.name && data.name.length > 100) {
    errors.push('Habit name must be less than 100 characters')
  }
  
  if (data.description && data.description.length > 500) {
    errors.push('Description must be less than 500 characters')
  }
  
  if (data.type && !['boolean', 'numeric', 'duration'].includes(data.type)) {
    errors.push('Invalid habit type')
  }
  
  if (data.frequency && !['daily', 'weekly', 'monthly'].includes(data.frequency)) {
    errors.push('Invalid habit frequency')
  }
  
  if (data.target !== undefined && data.target <= 0) {
    errors.push('Target must be greater than 0')
  }
  
  if (data.unit && data.unit.length > 20) {
    errors.push('Unit must be less than 20 characters')
  }
  
  return errors
}

/**
 * 搜索习惯
 */
export function searchHabits(
  habits: BaseHabit[], 
  query: string,
  options: {
    searchFields?: ('name' | 'description')[]
    caseSensitive?: boolean
  } = {}
): BaseHabit[] {
  if (!query.trim()) return habits
  
  const {
    searchFields = ['name', 'description'],
    caseSensitive = false
  } = options
  
  const searchQuery = caseSensitive ? query : query.toLowerCase()
  
  return habits.filter(habit => {
    return searchFields.some(field => {
      const fieldValue = habit[field] || ''
      const searchValue = caseSensitive ? fieldValue : fieldValue.toLowerCase()
      return searchValue.includes(searchQuery)
    })
  })
}

/**
 * 排序习惯
 */
export function sortHabits(
  habits: BaseHabit[],
  sortBy: 'name' | 'createdAt' | 'completionRate' | 'streak',
  sortOrder: 'asc' | 'desc' = 'asc'
): BaseHabit[] {
  const sorted = [...habits].sort((a, b) => {
    let comparison = 0
    
    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name)
        break
      case 'createdAt':
        comparison = a.createdAt.getTime() - b.createdAt.getTime()
        break
      case 'completionRate':
        comparison = a.completionRate - b.completionRate
        break
      case 'streak':
        comparison = a.streak - b.streak
        break
    }
    
    return sortOrder === 'desc' ? -comparison : comparison
  })
  
  return sorted
}

/**
 * 分组习惯
 */
export function groupHabits(
  habits: BaseHabit[],
  groupBy: 'type' | 'frequency' | 'status'
): Record<string, BaseHabit[]> {
  const groups: Record<string, BaseHabit[]> = {}
  
  habits.forEach(habit => {
    const groupKey = habit[groupBy]
    
    if (!groups[groupKey]) {
      groups[groupKey] = []
    }
    groups[groupKey].push(habit)
  })
  
  return groups
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}
