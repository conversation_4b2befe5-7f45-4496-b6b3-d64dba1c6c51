// Checklist Entity - 清单实体

use crate::shared::types::{Id, Metadata, EntityStatus};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Checklist {
    pub id: Id,
    pub name: String,
    pub description: Option<String>,
    pub items: Vec<ChecklistItem>,
    pub entity_status: EntityStatus,
    pub metadata: Metadata,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChecklistItem {
    pub id: Id,
    pub text: String,
    pub completed: bool,
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
}

impl Checklist {
    pub fn new(name: String) -> Self {
        Self {
            id: crate::shared::utils::IdGenerator::new_id_with_prefix("checklist"),
            name,
            description: None,
            items: Vec::new(),
            entity_status: EntityStatus::Active,
            metadata: Metadata::default(),
        }
    }

    pub fn add_item(&mut self, text: String) {
        let item = ChecklistItem {
            id: crate::shared::utils::IdGenerator::new_id(),
            text,
            completed: false,
            completed_at: None,
        };
        self.items.push(item);
        self.metadata.updated_at = chrono::Utc::now();
        self.metadata.version += 1;
    }

    pub fn complete_item(&mut self, item_id: &Id) {
        if let Some(item) = self.items.iter_mut().find(|i| &i.id == item_id) {
            item.completed = true;
            item.completed_at = Some(chrono::Utc::now());
            self.metadata.updated_at = chrono::Utc::now();
            self.metadata.version += 1;
        }
    }

    pub fn completion_rate(&self) -> f32 {
        if self.items.is_empty() {
            0.0
        } else {
            let completed = self.items.iter().filter(|i| i.completed).count();
            completed as f32 / self.items.len() as f32
        }
    }
}
