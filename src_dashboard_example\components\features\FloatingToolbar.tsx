import React, { useState, useEffect } from 'react'
import { Button } from '../ui/button'
import { Separator } from '../ui/separator'
import { cn } from '../../lib/utils'

/**
 * 悬浮工具栏属性
 */
interface FloatingToolbarProps {
  /** 是否显示工具栏 */
  visible: boolean
  /** 工具栏位置 */
  position?: { x: number; y: number }
  /** 选中的文本 */
  selectedText?: string
  /** 格式化回调 */
  onFormat?: (type: string, value?: string) => void
  /** 自定义样式 */
  className?: string
}

/**
 * Typora风格的悬浮工具栏
 * 在选中文本时显示格式化选项
 */
export function FloatingToolbar({
  visible,
  position = { x: 0, y: 0 },
  selectedText = '',
  onFormat,
  className
}: FloatingToolbarProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (visible && selectedText.trim()) {
      setIsVisible(true)
    } else {
      // 延迟隐藏，避免点击工具栏时闪烁
      const timer = setTimeout(() => setIsVisible(false), 100)
      return () => clearTimeout(timer)
    }
  }, [visible, selectedText])

  if (!isVisible) return null

  const handleFormat = (type: string, value?: string) => {
    onFormat?.(type, value)
  }

  return (
    <div
      className={cn(
        'fixed z-50 bg-background border border-border rounded-lg shadow-lg',
        'flex items-center gap-1 p-2',
        'animate-in fade-in-0 zoom-in-95 duration-200',
        className
      )}
      style={{
        left: position.x,
        top: position.y - 50, // 显示在选中文本上方
        transform: 'translateX(-50%)'
      }}
    >
      {/* 粗体 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleFormat('bold')}
        className="h-8 w-8 p-0"
        title="粗体 (Ctrl+B)"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M6 4h8a4 4 0 014 4 4 4 0 01-4 4H6z"
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M6 12h9a4 4 0 014 4 4 4 0 01-4 4H6z"
          />
        </svg>
      </Button>

      {/* 斜体 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleFormat('italic')}
        className="h-8 w-8 p-0"
        title="斜体 (Ctrl+I)"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 4h-9m4 16H5m4-8h6m-6 0l4-8m-4 8l-4 8"
          />
        </svg>
      </Button>

      {/* 删除线 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleFormat('strikethrough')}
        className="h-8 w-8 p-0"
        title="删除线"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M6 12h12M6 12l2-2m-2 2l2 2m8-2l-2-2m2 2l-2 2"
          />
        </svg>
      </Button>

      <Separator orientation="vertical" className="h-6" />

      {/* 代码 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleFormat('code')}
        className="h-8 w-8 p-0"
        title="行内代码 (Ctrl+`)"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
          />
        </svg>
      </Button>

      {/* 链接 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleFormat('link')}
        className="h-8 w-8 p-0"
        title="链接 (Ctrl+K)"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
          />
        </svg>
      </Button>

      <Separator orientation="vertical" className="h-6" />

      {/* 高亮 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleFormat('highlight')}
        className="h-8 w-8 p-0"
        title="高亮"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"
          />
        </svg>
      </Button>

      {/* 颜色 */}
      <div className="relative">
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" title="文字颜色">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 21h16-16zM15 5l2 2-2 2"
            />
          </svg>
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-3 h-0.5 bg-red-500 rounded"></div>
        </Button>
      </div>

      {/* 小箭头指向选中文本 */}
      <div className="absolute top-full left-1/2 transform -translate-x-1/2">
        <div className="w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-border"></div>
        <div className="w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-background -mt-px"></div>
      </div>
    </div>
  )
}

/**
 * 悬浮工具栏钩子
 * 处理文本选择和工具栏显示逻辑
 */
export function useFloatingToolbar() {
  const [selection, setSelection] = useState<{
    text: string
    range: Range | null
    rect: DOMRect | null
  }>({
    text: '',
    range: null,
    rect: null
  })

  useEffect(() => {
    const handleSelectionChange = () => {
      const selection = window.getSelection()
      if (!selection || selection.rangeCount === 0) {
        setSelection({ text: '', range: null, rect: null })
        return
      }

      const range = selection.getRangeAt(0)
      const text = selection.toString().trim()

      if (text.length === 0) {
        setSelection({ text: '', range: null, rect: null })
        return
      }

      const rect = range.getBoundingClientRect()
      setSelection({ text, range, rect })
    }

    document.addEventListener('selectionchange', handleSelectionChange)
    return () => document.removeEventListener('selectionchange', handleSelectionChange)
  }, [])

  const formatText = (type: string, value?: string) => {
    if (!selection.range) return

    const { range } = selection
    const selectedText = range.toString()

    let formattedText = ''
    switch (type) {
      case 'bold':
        formattedText = `**${selectedText}**`
        break
      case 'italic':
        formattedText = `*${selectedText}*`
        break
      case 'strikethrough':
        formattedText = `~~${selectedText}~~`
        break
      case 'code':
        formattedText = `\`${selectedText}\``
        break
      case 'link':
        formattedText = `[${selectedText}](${value || 'url'})`
        break
      case 'highlight':
        formattedText = `==${selectedText}==`
        break
      default:
        return
    }

    // 替换选中的文本
    range.deleteContents()
    range.insertNode(document.createTextNode(formattedText))

    // 清除选择
    window.getSelection()?.removeAllRanges()
    setSelection({ text: '', range: null, rect: null })
  }

  return {
    selection,
    formatText,
    isVisible: selection.text.length > 0,
    position: selection.rect
      ? {
          x: selection.rect.left + selection.rect.width / 2,
          y: selection.rect.top + window.scrollY
        }
      : { x: 0, y: 0 }
  }
}
