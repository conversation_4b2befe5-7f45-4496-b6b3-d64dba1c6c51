import type { Language } from '../contexts/LanguageContext'

/**
 * 国际化辅助工具函数
 * 提供日期时间格式化、数字格式化、复数形式处理等功能
 */

// 日期格式化选项
interface DateFormatOptions {
  dateStyle?: 'full' | 'long' | 'medium' | 'short'
  timeStyle?: 'full' | 'long' | 'medium' | 'short'
  relative?: boolean
}

/**
 * 格式化日期时间，支持中英文本地化
 * @param date 要格式化的日期
 * @param language 语言
 * @param options 格式化选项
 * @returns 格式化后的日期字符串
 */
export function formatDate(
  date: Date | string | number,
  language: Language,
  options: DateFormatOptions = {}
): string {
  const dateObj = new Date(date)

  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date'
  }

  const { dateStyle = 'medium', timeStyle, relative = false } = options

  // 相对时间格式化
  if (relative) {
    return formatRelativeTime(dateObj, language)
  }

  // 标准日期格式化
  const locale = language === 'zh' ? 'zh-CN' : 'en-US'

  const formatOptions: Intl.DateTimeFormatOptions = {
    dateStyle,
    ...(timeStyle && { timeStyle })
  }

  return new Intl.DateTimeFormat(locale, formatOptions).format(dateObj)
}

/**
 * 格式化相对时间（如：2小时前、3天前）
 * @param date 目标日期
 * @param language 语言
 * @returns 相对时间字符串
 */
export function formatRelativeTime(date: Date, language: Language): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  // 时间单位（秒）
  const units = {
    year: 31536000,
    month: 2592000,
    week: 604800,
    day: 86400,
    hour: 3600,
    minute: 60
  }

  // 如果是未来时间
  if (diffInSeconds < 0) {
    const absDiff = Math.abs(diffInSeconds)
    for (const [unit, seconds] of Object.entries(units)) {
      const value = Math.floor(absDiff / seconds)
      if (value >= 1) {
        return language === 'zh'
          ? `${value}${getChineseTimeUnit(unit)}后`
          : `in ${value} ${unit}${value > 1 ? 's' : ''}`
      }
    }
    return language === 'zh' ? '即将' : 'soon'
  }

  // 过去时间
  if (diffInSeconds < 60) {
    return language === 'zh' ? '刚刚' : 'just now'
  }

  for (const [unit, seconds] of Object.entries(units)) {
    const value = Math.floor(diffInSeconds / seconds)
    if (value >= 1) {
      return language === 'zh'
        ? `${value}${getChineseTimeUnit(unit)}前`
        : `${value} ${unit}${value > 1 ? 's' : ''} ago`
    }
  }

  return language === 'zh' ? '刚刚' : 'just now'
}

/**
 * 获取中文时间单位
 */
function getChineseTimeUnit(unit: string): string {
  const unitMap: Record<string, string> = {
    year: '年',
    month: '个月',
    week: '周',
    day: '天',
    hour: '小时',
    minute: '分钟'
  }
  return unitMap[unit] || unit
}

/**
 * 格式化数字，支持本地化
 * @param number 要格式化的数字
 * @param language 语言
 * @param options 格式化选项
 * @returns 格式化后的数字字符串
 */
export function formatNumber(
  number: number,
  language: Language,
  options: Intl.NumberFormatOptions = {}
): string {
  const locale = language === 'zh' ? 'zh-CN' : 'en-US'
  return new Intl.NumberFormat(locale, options).format(number)
}

/**
 * 格式化货币
 * @param amount 金额
 * @param language 语言
 * @param currency 货币代码
 * @returns 格式化后的货币字符串
 */
export function formatCurrency(
  amount: number,
  language: Language,
  currency: string = 'CNY'
): string {
  const locale = language === 'zh' ? 'zh-CN' : 'en-US'
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency
  }).format(amount)
}

/**
 * 处理英文复数形式
 * @param count 数量
 * @param singular 单数形式
 * @param plural 复数形式（可选，默认加s）
 * @returns 正确的单复数形式
 */
export function pluralize(count: number, singular: string, plural?: string): string {
  if (count === 1) {
    return singular
  }
  return plural || `${singular}s`
}

/**
 * 增强的参数替换函数
 * @param template 模板字符串
 * @param params 参数对象
 * @param language 语言（用于数字格式化）
 * @returns 替换后的字符串
 */
export function interpolate(
  template: string,
  params: Record<string, any>,
  language: Language = 'zh'
): string {
  return template.replace(/\{(\w+)(?::(\w+))?\}/g, (match, key, formatter) => {
    const value = params[key]

    if (value === undefined || value === null) {
      return match
    }

    // 应用格式化器
    if (formatter) {
      switch (formatter) {
        case 'number':
          return typeof value === 'number' ? formatNumber(value, language) : String(value)
        case 'currency':
          return typeof value === 'number' ? formatCurrency(value, language) : String(value)
        case 'date':
          return value instanceof Date ? formatDate(value, language) : String(value)
        case 'relative':
          return value instanceof Date ? formatRelativeTime(value, language) : String(value)
        default:
          return String(value)
      }
    }

    return String(value)
  })
}

/**
 * 获取本地化的数量描述
 * @param count 数量
 * @param itemKey 项目类型键
 * @param language 语言
 * @returns 本地化的数量描述
 */
export function getCountText(count: number, itemKey: string, language: Language): string {
  if (language === 'zh') {
    return `${count} 个${itemKey}`
  } else {
    const itemText = getItemText(itemKey, language)
    return `${count} ${pluralize(count, itemText)}`
  }
}

/**
 * 获取项目类型的本地化文本
 */
function getItemText(itemKey: string, language: Language): string {
  const itemMap: Record<string, Record<Language, string>> = {
    project: { zh: '项目', en: 'project' },
    area: { zh: '领域', en: 'area' },
    resource: { zh: '资源', en: 'resource' },
    task: { zh: '任务', en: 'task' },
    item: { zh: '项目', en: 'item' }
  }

  return itemMap[itemKey]?.[language] || itemKey
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param language 语言
 * @returns 格式化后的文件大小
 */
export function formatFileSize(bytes: number, language: Language): string {
  const units =
    language === 'zh' ? ['字节', 'KB', 'MB', 'GB', 'TB'] : ['bytes', 'KB', 'MB', 'GB', 'TB']

  if (bytes === 0) return `0 ${units[0]}`

  const k = 1024
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  const size = parseFloat((bytes / Math.pow(k, i)).toFixed(2))

  return `${formatNumber(size, language)} ${units[i]}`
}

/**
 * 格式化百分比
 * @param value 数值（0-1之间）
 * @param language 语言
 * @returns 格式化后的百分比
 */
export function formatPercentage(value: number, language: Language): string {
  return formatNumber(value, language, {
    style: 'percent',
    minimumFractionDigits: 0,
    maximumFractionDigits: 1
  })
}
