// Task DTOs - 任务数据传输对象

use crate::domain::entities::{Task, TaskStatus};
use crate::shared::types::{Id, Priority, EntityStatus};
use serde::{Deserialize, Serialize};

/// 创建任务请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTaskRequest {
    pub title: String,
    pub description: Option<String>,
    pub project_id: Option<Id>,
    pub parent_task_id: Option<Id>,
    pub due_date: Option<chrono::DateTime<chrono::Utc>>,
    pub priority: Option<String>,
}

/// 更新任务请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateTaskRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub project_id: Option<Id>,
    pub parent_task_id: Option<Id>,
    pub due_date: Option<chrono::DateTime<chrono::Utc>>,
    pub priority: Option<String>,
}

/// 更新任务状态请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateTaskStatusRequest {
    pub status: String,
}

/// 更新任务进度请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateTaskProgressRequest {
    pub progress: f32,
}

/// 批量更新任务状态请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchUpdateTaskStatusRequest {
    pub task_ids: Vec<Id>,
    pub status: String,
}

/// 任务响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskResponse {
    pub id: Id,
    pub title: String,
    pub description: Option<String>,
    pub project_id: Option<Id>,
    pub parent_task_id: Option<Id>,
    pub due_date: Option<chrono::DateTime<chrono::Utc>>,
    pub status: String,
    pub priority: String,
    pub progress: f32,
    pub entity_status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub version: u64,
}

/// 任务列表响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskListResponse {
    pub tasks: Vec<TaskResponse>,
    pub total: u64,
    pub page: u64,
    pub size: u64,
}

/// 任务统计响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskStatisticsResponse {
    pub total_tasks: u64,
    pub active_tasks: u64,
    pub completed_tasks: u64,
    pub overdue_tasks: u64,
    pub today_tasks: u64,
    pub average_progress: f32,
    pub completion_rate: f32,
    pub tasks_by_status: Vec<TaskStatusCount>,
    pub tasks_by_priority: Vec<TaskPriorityCount>,
}

/// 任务状态统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskStatusCount {
    pub status: String,
    pub count: u64,
}

/// 任务优先级统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskPriorityCount {
    pub priority: String,
    pub count: u64,
}

/// 任务优先级分数响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskPriorityScoreResponse {
    pub task_id: Id,
    pub priority_score: u32,
}

/// 克隆任务请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CloneTaskRequest {
    pub new_title: String,
}

impl From<Task> for TaskResponse {
    fn from(task: Task) -> Self {
        Self {
            id: task.id,
            title: task.title,
            description: task.description,
            project_id: task.project_id,
            parent_task_id: task.parent_task_id,
            due_date: task.due_date,
            status: match task.status {
                TaskStatus::Todo => "todo".to_string(),
                TaskStatus::InProgress => "in_progress".to_string(),
                TaskStatus::Completed => "completed".to_string(),
                TaskStatus::Cancelled => "cancelled".to_string(),
            },
            priority: match task.priority {
                Priority::Low => "low".to_string(),
                Priority::Medium => "medium".to_string(),
                Priority::High => "high".to_string(),
                Priority::Critical => "critical".to_string(),
            },
            progress: task.progress,
            entity_status: match task.entity_status {
                EntityStatus::Active => "active".to_string(),
                EntityStatus::Inactive => "inactive".to_string(),
                EntityStatus::Deleted => "deleted".to_string(),
                EntityStatus::Archived => "archived".to_string(),
            },
            created_at: task.metadata.created_at,
            updated_at: task.metadata.updated_at,
            version: task.metadata.version,
        }
    }
}

impl From<crate::domain::repositories::TaskStatistics> for TaskStatisticsResponse {
    fn from(stats: crate::domain::repositories::TaskStatistics) -> Self {
        Self {
            total_tasks: stats.total_tasks,
            active_tasks: stats.active_tasks,
            completed_tasks: stats.completed_tasks,
            overdue_tasks: stats.overdue_tasks,
            today_tasks: stats.today_tasks,
            average_progress: stats.average_progress,
            completion_rate: stats.completion_rate,
            tasks_by_status: stats.tasks_by_status.into_iter().map(|(status, count)| {
                TaskStatusCount {
                    status: match status {
                        TaskStatus::Todo => "todo".to_string(),
                        TaskStatus::InProgress => "in_progress".to_string(),
                        TaskStatus::Completed => "completed".to_string(),
                        TaskStatus::Cancelled => "cancelled".to_string(),
                    },
                    count,
                }
            }).collect(),
            tasks_by_priority: stats.tasks_by_priority.into_iter().map(|(priority, count)| {
                TaskPriorityCount {
                    priority: match priority {
                        Priority::Low => "low".to_string(),
                        Priority::Medium => "medium".to_string(),
                        Priority::High => "high".to_string(),
                        Priority::Critical => "critical".to_string(),
                    },
                    count,
                }
            }).collect(),
        }
    }
}

/// 验证创建任务请求
impl CreateTaskRequest {
    pub fn validate(&self) -> Result<(), String> {
        if self.title.trim().is_empty() {
            return Err("Task title cannot be empty".to_string());
        }
        
        if self.title.len() > 200 {
            return Err("Task title cannot be longer than 200 characters".to_string());
        }
        
        if let Some(ref description) = self.description {
            if description.len() > 2000 {
                return Err("Task description cannot be longer than 2000 characters".to_string());
            }
        }
        
        if let Some(ref priority) = self.priority {
            if !["low", "medium", "high", "critical"].contains(&priority.as_str()) {
                return Err("Invalid priority value".to_string());
            }
        }
        
        Ok(())
    }
    
    pub fn get_priority(&self) -> Priority {
        match self.priority.as_deref() {
            Some("low") => Priority::Low,
            Some("medium") => Priority::Medium,
            Some("high") => Priority::High,
            Some("critical") => Priority::Critical,
            _ => Priority::Medium,
        }
    }
}

/// 验证更新任务请求
impl UpdateTaskRequest {
    pub fn validate(&self) -> Result<(), String> {
        if let Some(ref title) = self.title {
            if title.trim().is_empty() {
                return Err("Task title cannot be empty".to_string());
            }
            if title.len() > 200 {
                return Err("Task title cannot be longer than 200 characters".to_string());
            }
        }
        
        if let Some(ref description) = self.description {
            if description.len() > 2000 {
                return Err("Task description cannot be longer than 2000 characters".to_string());
            }
        }
        
        if let Some(ref priority) = self.priority {
            if !["low", "medium", "high", "critical"].contains(&priority.as_str()) {
                return Err("Invalid priority value".to_string());
            }
        }
        
        Ok(())
    }
    
    pub fn get_priority(&self) -> Option<Priority> {
        self.priority.as_deref().map(|p| match p {
            "low" => Priority::Low,
            "medium" => Priority::Medium,
            "high" => Priority::High,
            "critical" => Priority::Critical,
            _ => Priority::Medium,
        })
    }
}

/// 验证更新任务状态请求
impl UpdateTaskStatusRequest {
    pub fn validate(&self) -> Result<(), String> {
        if !["todo", "in_progress", "completed", "cancelled"].contains(&self.status.as_str()) {
            return Err("Invalid task status".to_string());
        }
        Ok(())
    }
    
    pub fn get_status(&self) -> TaskStatus {
        match self.status.as_str() {
            "todo" => TaskStatus::Todo,
            "in_progress" => TaskStatus::InProgress,
            "completed" => TaskStatus::Completed,
            "cancelled" => TaskStatus::Cancelled,
            _ => TaskStatus::Todo,
        }
    }
}

/// 验证更新任务进度请求
impl UpdateTaskProgressRequest {
    pub fn validate(&self) -> Result<(), String> {
        if self.progress < 0.0 || self.progress > 1.0 {
            return Err("Progress must be between 0.0 and 1.0".to_string());
        }
        Ok(())
    }
}

/// 验证批量更新任务状态请求
impl BatchUpdateTaskStatusRequest {
    pub fn validate(&self) -> Result<(), String> {
        if self.task_ids.is_empty() {
            return Err("Task IDs cannot be empty".to_string());
        }
        
        if self.task_ids.len() > 100 {
            return Err("Cannot update more than 100 tasks at once".to_string());
        }
        
        if !["todo", "in_progress", "completed", "cancelled"].contains(&self.status.as_str()) {
            return Err("Invalid task status".to_string());
        }
        
        Ok(())
    }
    
    pub fn get_status(&self) -> TaskStatus {
        match self.status.as_str() {
            "todo" => TaskStatus::Todo,
            "in_progress" => TaskStatus::InProgress,
            "completed" => TaskStatus::Completed,
            "cancelled" => TaskStatus::Cancelled,
            _ => TaskStatus::Todo,
        }
    }
}
