/**
 * 习惯追踪组件常量定义
 * 定义各种预设值、配置选项和常量
 */

import type { HabitType, HabitFrequency, HabitStatus } from './types'

// 习惯类型选项
export const HABIT_TYPES: Array<{
  value: HabitType
  label: string
  description: string
  icon: string
}> = [
  {
    value: 'boolean',
    label: 'Yes/No',
    description: 'Simple completion tracking (e.g., Did I exercise today?)',
    icon: '✓'
  },
  {
    value: 'numeric',
    label: 'Number',
    description: 'Track specific values (e.g., 8 glasses of water, 30 minutes)',
    icon: '#'
  },
  {
    value: 'duration',
    label: 'Duration',
    description: 'Track time spent (e.g., 30 minutes of reading)',
    icon: '⏱'
  }
]

// 习惯频率选项
export const HABIT_FREQUENCIES: Array<{
  value: HabitFrequency
  label: string
  description: string
  icon: string
}> = [
  {
    value: 'daily',
    label: 'Daily',
    description: 'Every day',
    icon: '📅'
  },
  {
    value: 'weekly',
    label: 'Weekly',
    description: 'Once per week',
    icon: '📆'
  },
  {
    value: 'monthly',
    label: 'Monthly',
    description: 'Once per month',
    icon: '🗓'
  }
]

// 习惯状态选项
export const HABIT_STATUSES: Array<{
  value: HabitStatus
  label: string
  color: string
}> = [
  {
    value: 'active',
    label: 'Active',
    color: 'text-green-600 bg-green-100'
  },
  {
    value: 'paused',
    label: 'Paused',
    color: 'text-yellow-600 bg-yellow-100'
  },
  {
    value: 'archived',
    label: 'Archived',
    color: 'text-gray-600 bg-gray-100'
  }
]

// 预设颜色选项
export const PRESET_COLORS = [
  { value: '#3b82f6', name: 'Blue' },
  { value: '#10b981', name: 'Emerald' },
  { value: '#f59e0b', name: 'Amber' },
  { value: '#ef4444', name: 'Red' },
  { value: '#8b5cf6', name: 'Violet' },
  { value: '#06b6d4', name: 'Cyan' },
  { value: '#84cc16', name: 'Lime' },
  { value: '#f97316', name: 'Orange' },
  { value: '#ec4899', name: 'Pink' },
  { value: '#6b7280', name: 'Gray' },
  { value: '#14b8a6', name: 'Teal' },
  { value: '#f43f5e', name: 'Rose' }
]

// 习惯类型颜色映射
export const HABIT_TYPE_COLORS = {
  boolean: {
    bg: 'bg-blue-100',
    text: 'text-blue-800',
    border: 'border-blue-200',
    icon: 'text-blue-600'
  },
  numeric: {
    bg: 'bg-green-100',
    text: 'text-green-800',
    border: 'border-green-200',
    icon: 'text-green-600'
  },
  duration: {
    bg: 'bg-purple-100',
    text: 'text-purple-800',
    border: 'border-purple-200',
    icon: 'text-purple-600'
  }
}

// 常用单位选项
export const COMMON_UNITS = [
  // 时间单位
  'minutes',
  'hours',
  'seconds',
  
  // 数量单位
  'times',
  'reps',
  'sets',
  'pages',
  'chapters',
  'lessons',
  
  // 容量单位
  'glasses',
  'cups',
  'liters',
  'ml',
  
  // 距离单位
  'steps',
  'km',
  'miles',
  'meters',
  
  // 重量单位
  'kg',
  'lbs',
  'grams',
  
  // 其他
  'calories',
  'points',
  'dollars',
  'euros'
]

// 完成率等级配置
export const COMPLETION_GRADES = [
  {
    min: 95,
    grade: 'A+',
    color: 'text-green-700 bg-green-100',
    description: 'Excellent',
    emoji: '🏆'
  },
  {
    min: 90,
    grade: 'A',
    color: 'text-green-600 bg-green-100',
    description: 'Great',
    emoji: '🌟'
  },
  {
    min: 80,
    grade: 'B',
    color: 'text-blue-600 bg-blue-100',
    description: 'Good',
    emoji: '👍'
  },
  {
    min: 70,
    grade: 'C',
    color: 'text-yellow-600 bg-yellow-100',
    description: 'Fair',
    emoji: '👌'
  },
  {
    min: 60,
    grade: 'D',
    color: 'text-orange-600 bg-orange-100',
    description: 'Needs Improvement',
    emoji: '📈'
  },
  {
    min: 0,
    grade: 'F',
    color: 'text-red-600 bg-red-100',
    description: 'Poor',
    emoji: '💪'
  }
]

// 连击里程碑
export const STREAK_MILESTONES = [
  { days: 7, title: 'Week Warrior', emoji: '🔥', color: 'text-orange-500' },
  { days: 14, title: 'Two Week Champion', emoji: '💪', color: 'text-red-500' },
  { days: 30, title: 'Month Master', emoji: '🏆', color: 'text-yellow-500' },
  { days: 60, title: 'Consistency King', emoji: '👑', color: 'text-purple-500' },
  { days: 90, title: 'Quarter Conqueror', emoji: '🎯', color: 'text-blue-500' },
  { days: 180, title: 'Half Year Hero', emoji: '🌟', color: 'text-green-500' },
  { days: 365, title: 'Year Legend', emoji: '🚀', color: 'text-indigo-500' }
]

// 图表类型选项
export const CHART_TYPES = [
  {
    value: 'line',
    label: 'Line Chart',
    description: 'Show trends over time',
    icon: 'LineChart'
  },
  {
    value: 'bar',
    label: 'Bar Chart',
    description: 'Compare daily progress',
    icon: 'BarChart3'
  },
  {
    value: 'heatmap',
    label: 'Heatmap',
    description: 'Visual activity calendar',
    icon: 'Calendar'
  },
  {
    value: 'progress',
    label: 'Progress Bars',
    description: 'Individual habit progress',
    icon: 'Activity'
  }
]

// 时间范围选项
export const TIME_RANGES = [
  {
    value: 'week',
    label: 'Last 7 days',
    days: 7
  },
  {
    value: 'month',
    label: 'Last 30 days',
    days: 30
  },
  {
    value: 'year',
    label: 'Last 365 days',
    days: 365
  }
]

// 布局选项
export const LAYOUT_OPTIONS = [
  {
    value: 'grid',
    label: 'Grid View',
    description: 'Card-based layout',
    icon: 'Grid3X3'
  },
  {
    value: 'list',
    label: 'List View',
    description: 'Detailed list layout',
    icon: 'List'
  },
  {
    value: 'compact',
    label: 'Compact View',
    description: 'Space-efficient layout',
    icon: 'Menu'
  },
  {
    value: 'calendar',
    label: 'Calendar View',
    description: 'Calendar-based layout',
    icon: 'Calendar'
  }
]

// 排序选项
export const SORT_OPTIONS = [
  {
    value: 'name',
    label: 'Name',
    icon: 'SortAsc'
  },
  {
    value: 'createdAt',
    label: 'Date Created',
    icon: 'Calendar'
  },
  {
    value: 'completionRate',
    label: 'Completion Rate',
    icon: 'TrendingUp'
  },
  {
    value: 'streak',
    label: 'Current Streak',
    icon: 'Flame'
  }
]

// 过滤选项
export const FILTER_OPTIONS = {
  type: HABIT_TYPES.map(t => ({ value: t.value, label: t.label })),
  frequency: HABIT_FREQUENCIES.map(f => ({ value: f.value, label: f.label })),
  status: HABIT_STATUSES.map(s => ({ value: s.value, label: s.label }))
}

// 默认配置值
export const DEFAULT_VALUES = {
  habitColor: PRESET_COLORS[0].value,
  habitType: 'boolean' as HabitType,
  habitFrequency: 'daily' as HabitFrequency,
  habitTarget: 1,
  chartType: 'line',
  timeRange: 'month',
  layout: 'grid',
  calendarView: 'month'
}

// 验证规则
export const VALIDATION_RULES = {
  habitName: {
    minLength: 1,
    maxLength: 100,
    required: true
  },
  habitDescription: {
    maxLength: 500,
    required: false
  },
  habitTarget: {
    min: 1,
    max: 10000,
    required: true
  },
  habitUnit: {
    maxLength: 20,
    required: false
  }
}

// 动画配置
export const ANIMATION_CONFIG = {
  duration: {
    fast: 150,
    normal: 300,
    slow: 500
  },
  easing: {
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out'
  }
}

// 响应式断点
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
}

// 错误消息
export const ERROR_MESSAGES = {
  habitNameRequired: 'Habit name is required',
  habitNameTooLong: 'Habit name must be less than 100 characters',
  descriptionTooLong: 'Description must be less than 500 characters',
  invalidHabitType: 'Invalid habit type',
  invalidFrequency: 'Invalid habit frequency',
  targetTooSmall: 'Target must be greater than 0',
  targetTooLarge: 'Target must be less than 10000',
  unitTooLong: 'Unit must be less than 20 characters',
  loadFailed: 'Failed to load habits',
  saveFailed: 'Failed to save habit',
  deleteFailed: 'Failed to delete habit',
  recordFailed: 'Failed to record habit',
  networkError: 'Network error occurred'
}

// 成功消息
export const SUCCESS_MESSAGES = {
  habitCreated: 'Habit created successfully',
  habitUpdated: 'Habit updated successfully',
  habitDeleted: 'Habit deleted successfully',
  recordSaved: 'Progress recorded successfully',
  streakAchieved: 'New streak milestone achieved!',
  goalReached: 'Goal reached! Keep it up!'
}

// 提示消息
export const HINT_MESSAGES = {
  createFirstHabit: 'Create your first habit to start tracking your progress',
  noRecordsToday: 'No habits completed today. Start building your streak!',
  perfectDay: 'Perfect day! All habits completed!',
  streakBroken: 'Streak broken, but every day is a new opportunity',
  weeklyGoal: 'You\'re on track to reach your weekly goal',
  monthlyGoal: 'Great progress towards your monthly goal'
}

// 键盘快捷键
export const KEYBOARD_SHORTCUTS = {
  createHabit: 'Ctrl+N',
  toggleToday: 'Space',
  nextDay: 'ArrowRight',
  previousDay: 'ArrowLeft',
  search: 'Ctrl+F',
  save: 'Ctrl+S'
}

// 本地存储键名
export const STORAGE_KEYS = {
  preferences: 'habit-tracker-preferences',
  layout: 'habit-tracker-layout',
  filters: 'habit-tracker-filters',
  lastSync: 'habit-tracker-last-sync'
}

// API端点（如果需要）
export const API_ENDPOINTS = {
  habits: '/api/habits',
  records: '/api/habit-records',
  statistics: '/api/habit-statistics',
  progress: '/api/habit-progress'
}
