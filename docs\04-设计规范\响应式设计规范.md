# PaoLife 响应式设计规范

## 📋 概述

本文档定义了PaoLife应用的响应式设计标准，确保在不同设备和屏幕尺寸上都能提供优秀的用户体验。采用移动优先的设计策略。

## 🎯 响应式设计原则

### 1. 核心原则

#### 移动优先 (Mobile First)
- 从最小屏幕开始设计
- 逐步增强到大屏幕
- 确保核心功能在小屏幕可用
- 优化触摸交互体验

#### 内容优先 (Content First)
- 重要内容优先显示
- 根据内容重要性调整布局
- 保持信息层次清晰
- 避免内容截断或隐藏

#### 性能优先 (Performance First)
- 优化图片和资源加载
- 减少不必要的网络请求
- 使用适当的图片格式和尺寸
- 实现渐进式加载

#### 可访问性优先 (Accessibility First)
- 确保所有设备上的可访问性
- 保持足够的触摸目标大小
- 维护良好的对比度
- 支持屏幕阅读器

### 2. 设计目标

- **一致性**: 跨设备的一致用户体验
- **适应性**: 优雅地适应不同屏幕尺寸
- **可用性**: 在任何设备上都易于使用
- **性能**: 快速加载和流畅交互

## 📐 断点系统

### 1. 标准断点

#### 断点定义
```css
/* 断点变量 */
:root {
  --breakpoint-xs: 475px;    /* 超小屏幕 */
  --breakpoint-sm: 640px;    /* 小屏幕 */
  --breakpoint-md: 768px;    /* 中等屏幕 */
  --breakpoint-lg: 1024px;   /* 大屏幕 */
  --breakpoint-xl: 1280px;   /* 超大屏幕 */
  --breakpoint-2xl: 1536px;  /* 超超大屏幕 */
}

/* 媒体查询 */
@media (min-width: 475px) { /* xs */ }
@media (min-width: 640px) { /* sm */ }
@media (min-width: 768px) { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
@media (min-width: 1536px) { /* 2xl */ }
```

#### 设备分类
```css
/* 移动设备 */
@media (max-width: 767px) {
  .mobile-only { display: block; }
  .desktop-only { display: none; }
}

/* 平板设备 */
@media (min-width: 768px) and (max-width: 1023px) {
  .tablet-layout { 
    display: grid;
    grid-template-columns: 1fr 2fr;
  }
}

/* 桌面设备 */
@media (min-width: 1024px) {
  .desktop-only { display: block; }
  .mobile-only { display: none; }
}
```

### 2. 容器系统

#### 响应式容器
```css
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

/* 断点容器 */
@media (min-width: 640px) {
  .container {
    max-width: 640px;
    padding: 0 var(--spacing-6);
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
    padding: 0 var(--spacing-8);
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}
```

#### 流体容器
```css
.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-4);
}

@media (min-width: 768px) {
  .container-fluid {
    padding: 0 var(--spacing-6);
  }
}

@media (min-width: 1024px) {
  .container-fluid {
    padding: 0 var(--spacing-8);
  }
}
```

## 🏗️ 布局系统

### 1. 网格系统

#### Flexbox 网格
```css
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(var(--spacing-3) * -1);
}

.col {
  flex: 1;
  padding: 0 var(--spacing-3);
}

/* 响应式列 */
.col-12 { flex: 0 0 100%; }
.col-6 { flex: 0 0 50%; }
.col-4 { flex: 0 0 33.333333%; }
.col-3 { flex: 0 0 25%; }

@media (min-width: 768px) {
  .col-md-12 { flex: 0 0 100%; }
  .col-md-6 { flex: 0 0 50%; }
  .col-md-4 { flex: 0 0 33.333333%; }
  .col-md-3 { flex: 0 0 25%; }
}

@media (min-width: 1024px) {
  .col-lg-12 { flex: 0 0 100%; }
  .col-lg-6 { flex: 0 0 50%; }
  .col-lg-4 { flex: 0 0 33.333333%; }
  .col-lg-3 { flex: 0 0 25%; }
}
```

#### CSS Grid 系统
```css
.grid {
  display: grid;
  gap: var(--spacing-6);
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 自适应网格 */
.grid-auto {
  display: grid;
  gap: var(--spacing-6);
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}
```

### 2. 布局模式

#### 侧边栏布局
```css
.layout-sidebar {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.sidebar {
  width: 100%;
  background: var(--color-gray-50);
  border-bottom: 1px solid var(--color-gray-200);
}

.main-content {
  flex: 1;
  padding: var(--spacing-4);
}

@media (min-width: 768px) {
  .layout-sidebar {
    flex-direction: row;
  }
  
  .sidebar {
    width: 240px;
    border-bottom: none;
    border-right: 1px solid var(--color-gray-200);
  }
  
  .main-content {
    padding: var(--spacing-6);
  }
}

@media (min-width: 1024px) {
  .sidebar {
    width: 280px;
  }
  
  .main-content {
    padding: var(--spacing-8);
  }
}
```

#### 卡片布局
```css
.card-grid {
  display: grid;
  gap: var(--spacing-4);
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-6);
  }
}

@media (min-width: 1024px) {
  .card-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .card-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

## 📱 移动端优化

### 1. 触摸交互

#### 触摸目标大小
```css
/* 最小触摸目标 */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 按钮触摸优化 */
.btn-mobile {
  padding: var(--spacing-3) var(--spacing-4);
  min-height: 44px;
  font-size: var(--text-base);
}

@media (min-width: 768px) {
  .btn-mobile {
    padding: var(--spacing-2) var(--spacing-4);
    min-height: auto;
    font-size: var(--text-sm);
  }
}
```

#### 手势友好设计
```css
/* 滑动区域 */
.swipe-area {
  touch-action: pan-x;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.swipe-area::-webkit-scrollbar {
  display: none;
}

/* 长按区域 */
.long-press {
  user-select: none;
  -webkit-touch-callout: none;
}
```

### 2. 导航优化

#### 移动端导航
```css
.mobile-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid var(--color-gray-200);
  padding: var(--spacing-2) 0;
  z-index: 1000;
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-2);
  min-height: 44px;
  text-decoration: none;
  color: var(--color-gray-600);
}

.mobile-nav-item.active {
  color: var(--color-primary-600);
}

@media (min-width: 768px) {
  .mobile-nav {
    display: none;
  }
}
```

#### 汉堡菜单
```css
.hamburger {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.hamburger-line {
  width: 100%;
  height: 2px;
  background: currentColor;
  transition: all 0.3s ease;
  margin: 2px 0;
}

.hamburger.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.hamburger.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

@media (min-width: 768px) {
  .hamburger {
    display: none;
  }
}
```

### 3. 内容优化

#### 文字大小调整
```css
.text-responsive {
  font-size: var(--text-sm);
  line-height: 1.5;
}

@media (min-width: 768px) {
  .text-responsive {
    font-size: var(--text-base);
  }
}

@media (min-width: 1024px) {
  .text-responsive {
    font-size: var(--text-lg);
    line-height: 1.6;
  }
}
```

#### 间距调整
```css
.spacing-responsive {
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

@media (min-width: 768px) {
  .spacing-responsive {
    padding: var(--spacing-6);
    margin-bottom: var(--spacing-6);
  }
}

@media (min-width: 1024px) {
  .spacing-responsive {
    padding: var(--spacing-8);
    margin-bottom: var(--spacing-8);
  }
}
```

## 🖼️ 图片响应式

### 1. 响应式图片

#### 基础响应式图片
```html
<!-- 响应式图片 -->
<img 
  src="image-800.jpg"
  srcset="
    image-400.jpg 400w,
    image-800.jpg 800w,
    image-1200.jpg 1200w
  "
  sizes="
    (max-width: 640px) 100vw,
    (max-width: 1024px) 50vw,
    33vw
  "
  alt="项目截图"
  loading="lazy"
>
```

#### Picture 元素
```html
<!-- 不同设备使用不同图片 -->
<picture>
  <source 
    media="(max-width: 640px)" 
    srcset="mobile-image.jpg"
  >
  <source 
    media="(max-width: 1024px)" 
    srcset="tablet-image.jpg"
  >
  <img 
    src="desktop-image.jpg" 
    alt="响应式图片"
  >
</picture>
```

### 2. CSS 图片优化

#### 背景图片响应式
```css
.hero-image {
  background-image: url('hero-mobile.jpg');
  background-size: cover;
  background-position: center;
  height: 200px;
}

@media (min-width: 768px) {
  .hero-image {
    background-image: url('hero-tablet.jpg');
    height: 300px;
  }
}

@media (min-width: 1024px) {
  .hero-image {
    background-image: url('hero-desktop.jpg');
    height: 400px;
  }
}
```

#### 图片容器
```css
.image-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 比例 */
  overflow: hidden;
}

.image-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
```

## 🎨 组件响应式

### 1. 表格响应式

#### 响应式表格
```css
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table {
  width: 100%;
  min-width: 600px;
}

@media (max-width: 767px) {
  .table-mobile {
    display: block;
  }
  
  .table-mobile thead {
    display: none;
  }
  
  .table-mobile tbody,
  .table-mobile tr,
  .table-mobile td {
    display: block;
  }
  
  .table-mobile tr {
    border: 1px solid var(--color-gray-200);
    margin-bottom: var(--spacing-4);
    padding: var(--spacing-4);
  }
  
  .table-mobile td {
    border: none;
    padding: var(--spacing-2) 0;
  }
  
  .table-mobile td::before {
    content: attr(data-label) ": ";
    font-weight: bold;
  }
}
```

### 2. 表单响应式

#### 响应式表单
```css
.form-responsive {
  display: grid;
  gap: var(--spacing-4);
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .form-responsive {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-6);
  }
}

.form-group-full {
  grid-column: 1 / -1;
}

.form-input {
  width: 100%;
  padding: var(--spacing-3);
  font-size: var(--text-base);
}

@media (max-width: 640px) {
  .form-input {
    font-size: 16px; /* 防止iOS缩放 */
  }
}
```

### 3. 模态框响应式

#### 响应式模态框
```css
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
}

.modal-content {
  background: white;
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

@media (max-width: 640px) {
  .modal {
    align-items: flex-end;
    padding: 0;
  }
  
  .modal-content {
    width: 100%;
    max-height: 80vh;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  }
}
```

## 📊 性能优化

### 1. 关键CSS

#### 内联关键CSS
```html
<style>
  /* 关键CSS - 首屏渲染 */
  .header { /* 头部样式 */ }
  .hero { /* 主要内容样式 */ }
  .container { /* 容器样式 */ }
</style>

<link rel="preload" href="styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
```

### 2. 图片优化

#### 懒加载实现
```typescript
// 图片懒加载
const imageObserver = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      const img = entry.target as HTMLImageElement;
      img.src = img.dataset.src!;
      img.classList.remove('lazy');
      imageObserver.unobserve(img);
    }
  });
});

document.querySelectorAll('img[data-src]').forEach(img => {
  imageObserver.observe(img);
});
```

## 📋 响应式检查清单

### 布局检查
- [ ] 在所有断点下布局正常
- [ ] 内容不会水平溢出
- [ ] 导航在移动端可用
- [ ] 触摸目标足够大

### 内容检查
- [ ] 文字在小屏幕上可读
- [ ] 图片正确缩放
- [ ] 表格在移动端可用
- [ ] 表单易于填写

### 性能检查
- [ ] 图片优化和懒加载
- [ ] 关键CSS内联
- [ ] 资源压缩
- [ ] 网络请求优化

### 交互检查
- [ ] 触摸交互流畅
- [ ] 手势支持
- [ ] 键盘导航
- [ ] 可访问性保持

---

**文档版本**: 1.0  
**创建日期**: 2024年12月  
**维护者**: 前端设计团队  
**下次更新**: 根据设备发展趋势更新
