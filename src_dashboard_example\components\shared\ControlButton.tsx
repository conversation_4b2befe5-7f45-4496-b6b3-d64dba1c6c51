import React from 'react'

// 定义按钮的类型和对应的样式
const typeClasses = {
  minimize: 'bg-yellow-400 hover:bg-yellow-500',
  maximize: 'bg-green-400 hover:bg-green-500',
  close: 'bg-red-500 hover:bg-red-600',
};

// 定义 props 类型
interface ControlButtonProps {
  type: 'minimize' | 'maximize' | 'close';
  onClick?: () => void;
}

const ControlButton: React.FC<ControlButtonProps> = ({ type, onClick }) => {
  return (
    <button
      onClick={onClick}
      className={`
        w-3.5 h-3.5 rounded-full
        flex justify-center items-center
        transition-colors duration-150
        ${typeClasses[type]}
      `}
      aria-label={`${type} window`}
    >
      {/* 图标将在父组件中注入 */}
    </button>
  );
};

export default ControlButton;