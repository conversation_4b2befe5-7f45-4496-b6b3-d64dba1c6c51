import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '../ui/dialog'
import { Button } from '../ui/button'
import { Checkbox } from '../ui/checkbox'
import { useLanguage } from '../../contexts/LanguageContext'

interface SimpleExitConfirmDialogProps {
  isOpen: boolean
  onClose: () => void
}

export function SimpleExitConfirmDialog({ isOpen, onClose }: SimpleExitConfirmDialogProps) {
  const { t } = useLanguage()
  const [dontShowAgain, setDontShowAgain] = useState(false)

  // 检查是否禁用确认
  useEffect(() => {
    const isDisabled = localStorage.getItem('paolife-exit-confirm-disabled') === 'true'
    if (isDisabled && isOpen) {
      // 如果禁用了确认，直接退出
      handleExit()
    }
  }, [isOpen])

  const handleExit = async () => {
    console.log('🚪 [ExitDialog] Exit button clicked')
    if (dontShowAgain) {
      localStorage.setItem('paolife-exit-confirm-disabled', 'true')
    }

    console.log('🚪 [ExitDialog] Closing dialog before exit action')
    onClose()

    // 等待一下确保弹窗关闭
    await new Promise(resolve => setTimeout(resolve, 100))

    try {
      console.log('🚪 [ExitDialog] Calling exitAction(exit)')
      if (window.electronAPI?.window?.exitAction) {
        await window.electronAPI.window.exitAction('exit')
        console.log('🚪 [ExitDialog] Exit action completed')
      }
    } catch (error) {
      console.error('❌ [ExitDialog] Error calling exit action:', error)
    }
  }

  const handleMinimizeToTray = async () => {
    console.log('🔽 [ExitDialog] Minimize to tray button clicked')
    if (dontShowAgain) {
      localStorage.setItem('paolife-exit-confirm-disabled', 'true')
    }

    console.log('🔽 [ExitDialog] Closing dialog before minimize action')
    onClose()

    // 等待一下确保弹窗关闭
    await new Promise(resolve => setTimeout(resolve, 100))

    try {
      console.log('🔽 [ExitDialog] Calling exitAction(minimize)')
      if (window.electronAPI?.window?.exitAction) {
        await window.electronAPI.window.exitAction('minimize')
        console.log('🔽 [ExitDialog] Minimize action completed')
      }
    } catch (error) {
      console.error('❌ [ExitDialog] Error calling minimize action:', error)
    }
  }

  const handleCancel = async () => {
    console.log('❌ [ExitDialog] Cancel button clicked')
    onClose()
    
    try {
      if (window.electronAPI?.window?.exitAction) {
        await window.electronAPI.window.exitAction('cancel')
      }
    } catch (error) {
      console.error('❌ [ExitDialog] Error calling cancel action:', error)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleCancel()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t('components.dialog.exitConfirm.title')}</DialogTitle>
          <DialogDescription>
            {t('components.dialog.exitConfirm.message')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 托盘提示 */}
          <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              {t('components.dialog.exitConfirm.trayHint')}
            </p>
          </div>

          {/* 不再显示选项 */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="dont-show-again"
              checked={dontShowAgain}
              onCheckedChange={(checked) => setDontShowAgain(checked as boolean)}
            />
            <label
              htmlFor="dont-show-again"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {t('components.dialog.exitConfirm.dontShowAgain')}
            </label>
          </div>
        </div>

        <DialogFooter>
          <div className="flex gap-2 w-full">
            <Button
              variant="outline"
              onClick={handleCancel}
              className="flex-1"
            >
              {t('common.cancel')}
            </Button>
            <Button
              variant="secondary"
              onClick={handleMinimizeToTray}
              className="flex-1"
            >
              {t('components.dialog.exitConfirm.minimizeToTray')}
            </Button>
            <Button
              variant="destructive"
              onClick={handleExit}
              className="flex-1"
            >
              {t('components.dialog.exitConfirm.confirm')}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Hook to manage the exit confirmation dialog
export function useSimpleExitConfirmation() {
  const [showDialog, setShowDialog] = useState(false)

  useEffect(() => {
    const handleCloseRequest = () => {
      console.log('🔔 [ExitDialog] Received close-requested from main process')
      const isDisabled = localStorage.getItem('paolife-exit-confirm-disabled') === 'true'
      
      if (isDisabled) {
        console.log('🔔 [ExitDialog] Confirmation disabled, exiting directly')
        // 直接退出
        if (window.electronAPI?.window?.exitAction) {
          window.electronAPI.window.exitAction('exit')
        }
      } else {
        console.log('🔔 [ExitDialog] Showing confirmation dialog')
        setShowDialog(true)
      }
    }

    // 监听来自主进程的关闭请求
    if (window.electronAPI?.ipcRenderer) {
      window.electronAPI.ipcRenderer.on('close-requested', handleCloseRequest)
    }

    return () => {
      if (window.electronAPI?.ipcRenderer) {
        window.electronAPI.ipcRenderer.removeAllListeners('close-requested')
      }
    }
  }, [])

  const closeDialog = () => {
    setShowDialog(false)
  }

  return {
    showDialog,
    closeDialog
  }
}
