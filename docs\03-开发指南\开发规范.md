# PaoLife 开发规范

## 📋 概述

本文档定义了 PaoLife 项目的开发规范，包括代码风格、命名约定、项目结构、Git 工作流程等。遵循这些规范有助于提高代码质量、团队协作效率和项目可维护性。

## 🦀 Rust 开发规范

### 1. 代码风格

#### 格式化工具
```bash
# 使用 rustfmt 格式化代码
cargo fmt

# 检查格式化
cargo fmt -- --check
```

#### 基本规则
- **缩进**: 使用 4 个空格，不使用 Tab
- **行长度**: 最大 100 字符
- **导入顺序**: std -> 第三方 -> 本地模块
- **尾随逗号**: 在多行结构中使用尾随逗号

```rust
// ✅ 正确的导入顺序
use std::collections::HashMap;
use std::fs;

use serde::{Deserialize, Serialize};
use sqlx::Row;

use crate::domain::entities::User;
use crate::shared::errors::AppError;

// ✅ 正确的结构定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateUserData {
    pub username: String,
    pub email: String,
    pub password: String,
    pub full_name: Option<String>, // 尾随逗号
}
```

### 2. 命名约定

#### 基本规则
- **函数和变量**: snake_case
- **类型和 trait**: PascalCase
- **常量**: SCREAMING_SNAKE_CASE
- **模块**: snake_case
- **生命周期**: 单个小写字母

```rust
// ✅ 正确的命名
const MAX_RETRY_COUNT: usize = 3;

pub struct UserRepository;

pub trait UserRepositoryTrait {
    async fn find_by_id(&self, user_id: &str) -> AppResult<Option<User>>;
}

impl UserRepository {
    pub async fn create_user(&self, data: CreateUserData) -> AppResult<User> {
        // 实现
    }
}
```

#### 特殊约定
- **错误类型**: 以 `Error` 结尾
- **结果类型**: 使用 `AppResult<T>` 作为统一返回类型
- **数据传输对象**: 以 `Data` 结尾
- **响应对象**: 以 `Response` 结尾

### 3. 错误处理

#### 统一错误类型
```rust
// 使用项目统一的错误类型
pub type AppResult<T> = Result<T, AppError>;

// ✅ 正确的错误处理
pub async fn get_user_by_id(id: &str) -> AppResult<User> {
    let user = repository
        .find_by_id(id)
        .await?
        .ok_or_else(|| AppError::not_found("用户不存在"))?;
    
    Ok(user)
}

// ❌ 避免使用 unwrap() 和 expect()
let user = repository.find_by_id(id).await.unwrap(); // 不要这样做
```

#### 错误传播
```rust
// ✅ 使用 ? 操作符传播错误
pub async fn update_user_profile(
    id: &str, 
    data: UpdateUserData
) -> AppResult<User> {
    let mut user = self.get_user_by_id(id).await?;
    user.update_profile(data)?;
    self.repository.save(&user).await?;
    Ok(user)
}
```

### 4. 文档注释

#### 公共 API 文档
```rust
/// 创建新用户
/// 
/// # 参数
/// 
/// * `data` - 用户创建数据，包含用户名、邮箱等信息
/// 
/// # 返回值
/// 
/// 返回创建成功的用户实体
/// 
/// # 错误
/// 
/// * `AppError::Validation` - 当输入数据验证失败时
/// * `AppError::Conflict` - 当用户名或邮箱已存在时
/// 
/// # 示例
/// 
/// ```rust
/// let data = CreateUserData {
///     username: "john_doe".to_string(),
///     email: "<EMAIL>".to_string(),
///     password: "secure_password".to_string(),
///     full_name: Some("John Doe".to_string()),
/// };
/// 
/// let user = user_service.create_user(data).await?;
/// ```
pub async fn create_user(&self, data: CreateUserData) -> AppResult<User> {
    // 实现
}
```

### 5. 测试规范

#### 测试组织
```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    // 测试辅助函数
    fn create_test_user() -> User {
        // 创建测试用户
    }
    
    #[tokio::test]
    async fn test_create_user_success() {
        // 测试成功场景
    }
    
    #[tokio::test]
    async fn test_create_user_duplicate_email() {
        // 测试失败场景
    }
}
```

#### 测试命名
- 测试函数以 `test_` 开头
- 使用描述性名称：`test_[功能]_[场景]_[期望结果]`
- 例如：`test_create_user_with_duplicate_email_returns_error`

## 🎨 前端开发规范

### 1. TypeScript 规范

#### 类型定义
```typescript
// ✅ 使用接口定义对象类型
interface User {
  id: string;
  username: string;
  email: string;
  fullName?: string;
  createdAt: Date;
}

// ✅ 使用联合类型定义枚举
type TaskStatus = 'not_started' | 'in_progress' | 'completed' | 'cancelled';

// ✅ 使用泛型提高复用性
interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}
```

#### 函数定义
```typescript
// ✅ 明确的参数和返回类型
async function createUser(data: CreateUserData): Promise<User> {
  const response = await invoke<ApiResponse<User>>('create_user', { data });
  return response.data;
}

// ✅ 使用可选参数
function formatUserName(user: User, includeEmail?: boolean): string {
  return includeEmail 
    ? `${user.fullName || user.username} (${user.email})`
    : user.fullName || user.username;
}
```

### 2. SolidJS 组件规范

#### 组件结构
```typescript
// ✅ 组件文件结构
import { Component, createSignal, onMount } from 'solid-js';
import { User } from '../types/user';
import { UserService } from '../services/user-service';

interface UserProfileProps {
  userId: string;
  onUserUpdate?: (user: User) => void;
}

const UserProfile: Component<UserProfileProps> = (props) => {
  const [user, setUser] = createSignal<User | null>(null);
  const [loading, setLoading] = createSignal(false);

  onMount(async () => {
    setLoading(true);
    try {
      const userData = await UserService.getById(props.userId);
      setUser(userData);
    } catch (error) {
      console.error('Failed to load user:', error);
    } finally {
      setLoading(false);
    }
  });

  return (
    <div class="user-profile">
      {/* 组件内容 */}
    </div>
  );
};

export default UserProfile;
```

#### 命名约定
- **组件**: PascalCase (UserProfile)
- **Props 接口**: 组件名 + Props (UserProfileProps)
- **信号**: camelCase (user, setUser)
- **CSS 类**: kebab-case (user-profile)

### 3. 样式规范

#### Tailwind CSS 使用
```typescript
// ✅ 使用 Tailwind 类名
const Button: Component<ButtonProps> = (props) => {
  return (
    <button
      class={`
        px-4 py-2 rounded-md font-medium transition-colors
        ${props.variant === 'primary' 
          ? 'bg-blue-600 text-white hover:bg-blue-700' 
          : 'bg-gray-200 text-gray-900 hover:bg-gray-300'
        }
        ${props.disabled ? 'opacity-50 cursor-not-allowed' : ''}
      `}
      disabled={props.disabled}
      onClick={props.onClick}
    >
      {props.children}
    </button>
  );
};
```

#### 响应式设计
```typescript
// ✅ 移动优先的响应式设计
<div class="
  grid grid-cols-1 gap-4
  sm:grid-cols-2 sm:gap-6
  lg:grid-cols-3 lg:gap-8
">
  {/* 内容 */}
</div>
```

## 📁 项目结构规范

### 1. 目录组织

#### 后端结构
```
src-tauri/src/
├── api/                    # API 层
│   ├── commands/          # Tauri 命令
│   │   ├── mod.rs
│   │   ├── user_commands.rs
│   │   └── project_commands.rs
│   ├── dto/              # 数据传输对象
│   └── middleware/       # 中间件
├── application/          # 应用层
│   ├── services/         # 应用服务
│   └── use_cases/        # 用例实现
├── domain/              # 领域层
│   ├── entities/        # 实体
│   ├── repositories/    # 仓储接口
│   └── services/        # 领域服务
├── infrastructure/     # 基础设施层
│   ├── database/       # 数据库实现
│   └── repositories/   # 仓储实现
└── shared/            # 共享模块
    ├── errors/        # 错误定义
    ├── types/         # 共享类型
    └── utils/         # 工具函数
```

#### 前端结构
```
src/
├── components/         # 通用组件
│   ├── ui/            # 基础 UI 组件
│   └── layout/        # 布局组件
├── pages/             # 页面组件
├── services/          # 服务层
├── stores/            # 状态管理
├── types/             # 类型定义
├── utils/             # 工具函数
└── assets/            # 静态资源
```

### 2. 文件命名

#### 规则
- **Rust 文件**: snake_case.rs
- **TypeScript 文件**: kebab-case.ts
- **组件文件**: PascalCase.tsx
- **样式文件**: kebab-case.css

#### 示例
```
user_repository.rs      # Rust 模块
user-service.ts         # TypeScript 服务
UserProfile.tsx         # React/SolidJS 组件
user-profile.css        # 样式文件
```

## 🔄 Git 工作流程

### 1. 分支策略

#### 分支类型
- **main**: 主分支，包含生产就绪代码
- **develop**: 开发分支，集成最新功能
- **feature/**: 功能分支，开发新功能
- **bugfix/**: 修复分支，修复 bug
- **hotfix/**: 热修复分支，紧急修复生产问题

#### 分支命名
```bash
# 功能分支
feature/user-authentication
feature/project-management

# 修复分支
bugfix/login-validation-error
bugfix/task-deletion-issue

# 热修复分支
hotfix/security-vulnerability
hotfix/data-corruption-fix
```

### 2. 提交规范

#### 提交消息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 类型说明
- **feat**: 新功能
- **fix**: 修复 bug
- **docs**: 文档更新
- **style**: 代码格式化
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

#### 示例
```bash
feat(auth): add user registration functionality

- Implement user registration form
- Add email validation
- Create user registration API endpoint
- Add unit tests for registration logic

Closes #123
```

### 3. 代码审查

#### Pull Request 规范
- **标题**: 简洁描述变更内容
- **描述**: 详细说明变更原因和实现方式
- **测试**: 说明如何测试变更
- **截图**: 如有 UI 变更，提供截图

#### 审查检查清单
- [ ] 代码符合项目规范
- [ ] 包含适当的测试
- [ ] 文档已更新
- [ ] 无明显的性能问题
- [ ] 错误处理完善

## 🧪 测试规范

### 1. 测试策略

#### 测试金字塔
- **单元测试**: 70% - 测试单个函数/方法
- **集成测试**: 20% - 测试模块间交互
- **端到端测试**: 10% - 测试完整用户流程

#### 测试覆盖率
- **最低要求**: 80%
- **目标**: 90%
- **关键模块**: 95%

### 2. 测试命名

#### Rust 测试
```rust
#[tokio::test]
async fn test_create_user_with_valid_data_returns_success() {
    // 测试实现
}

#[tokio::test]
async fn test_create_user_with_duplicate_email_returns_error() {
    // 测试实现
}
```

#### 前端测试
```typescript
describe('UserService', () => {
  it('should create user with valid data', async () => {
    // 测试实现
  });

  it('should throw error when email already exists', async () => {
    // 测试实现
  });
});
```

## 📝 文档规范

### 1. 代码文档

#### 必需文档
- 公共 API 的文档注释
- 复杂算法的实现说明
- 配置选项的说明
- 错误处理的文档

### 2. 项目文档

#### 文档类型
- **README**: 项目概述和快速开始
- **API 文档**: 接口规范和使用示例
- **架构文档**: 系统设计和技术决策
- **部署文档**: 部署流程和配置说明

## 🔍 代码质量

### 1. 静态分析工具

#### Rust
```bash
# Clippy 检查
cargo clippy -- -D warnings

# 格式检查
cargo fmt -- --check

# 安全审计
cargo audit
```

#### 前端
```bash
# Biome 检查
pnpm lint

# 类型检查
pnpm type-check

# 格式检查
pnpm format:check
```

### 2. 持续集成

#### CI 流程
1. 代码格式检查
2. 静态分析
3. 单元测试
4. 集成测试
5. 构建验证
6. 安全扫描

---

**文档版本**: 1.0  
**创建日期**: 2024年12月  
**维护者**: 开发团队  
**下次更新**: 根据项目发展需要
