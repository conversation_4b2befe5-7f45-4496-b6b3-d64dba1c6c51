# PaoLife 交互设计规范

## 📋 概述

本文档定义了PaoLife应用的交互设计规范，包括用户交互模式、动效设计、反馈机制等，确保用户体验的一致性和可用性。

## 🎯 交互设计原则

### 1. 核心原则

#### 可预测性
- 相同的操作产生相同的结果
- 界面元素行为符合用户预期
- 保持交互模式的一致性

#### 即时反馈
- 用户操作后立即给出反馈
- 明确显示系统状态
- 提供操作结果确认

#### 容错性
- 防止用户误操作
- 提供撤销和恢复功能
- 友好的错误处理

#### 效率性
- 减少用户操作步骤
- 提供快捷操作方式
- 智能化的默认选项

### 2. 设计目标

- **降低认知负担**: 简化操作流程，减少用户思考时间
- **提升操作效率**: 优化常用功能的访问路径
- **增强用户信心**: 清晰的反馈让用户了解系统状态
- **保证操作安全**: 防止误操作和数据丢失

## 🖱️ 基础交互模式

### 1. 点击交互

#### 按钮点击
```css
/* 按钮交互状态 */
.button {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}
```

#### 链接点击
```css
.link {
  color: var(--color-primary-600);
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

.link:hover {
  color: var(--color-primary-700);
  text-decoration: underline;
}

.link:visited {
  color: var(--color-purple-600);
}
```

### 2. 悬停交互

#### 卡片悬停
```css
.card {
  transition: all 0.3s ease-in-out;
  cursor: pointer;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}
```

#### 图标悬停
```css
.icon-button {
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease-in-out;
}

.icon-button:hover {
  background-color: var(--color-gray-100);
}
```

### 3. 焦点交互

#### 键盘焦点
```css
.focusable {
  outline: none;
  transition: box-shadow 0.2s ease-in-out;
}

.focusable:focus-visible {
  box-shadow: 0 0 0 3px var(--color-primary-200);
  border-color: var(--color-primary-500);
}
```

#### 焦点顺序
- Tab键按逻辑顺序导航
- 跳过装饰性元素
- 模态框内焦点循环

## 🎬 动效设计规范

### 1. 动效原则

#### 自然性
- 模拟真实世界的物理规律
- 使用缓动函数创造自然感
- 避免机械化的线性动画

#### 功能性
- 动效服务于功能目的
- 引导用户注意力
- 提供状态变化反馈

#### 性能优化
- 使用CSS transform和opacity
- 避免引起重排的属性
- 控制动画数量和复杂度

### 2. 动效时长

#### 标准时长
```css
/* 动效时长标准 */
--duration-fast: 150ms;     /* 快速反馈 */
--duration-normal: 250ms;   /* 标准动效 */
--duration-slow: 350ms;     /* 复杂动效 */
--duration-slower: 500ms;   /* 页面转场 */
```

#### 使用场景
- **150ms**: 按钮状态变化、悬停效果
- **250ms**: 组件显示隐藏、颜色变化
- **350ms**: 布局变化、复杂状态转换
- **500ms**: 页面切换、模态框显示

### 3. 缓动函数

#### 标准缓动
```css
/* 缓动函数 */
--ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
--ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
--ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
--ease-back: cubic-bezier(0.68, -0.55, 0.265, 1.55);
```

#### 应用场景
- **ease-out**: 元素进入动画
- **ease-in**: 元素退出动画
- **ease-in-out**: 状态转换动画
- **ease-back**: 强调性动画

### 4. 常用动效模式

#### 淡入淡出
```css
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}
```

#### 滑动进入
```css
@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-in-up {
  animation: slideInUp var(--duration-normal) var(--ease-out);
}
```

#### 缩放动画
```css
@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.scale-in {
  animation: scaleIn var(--duration-normal) var(--ease-back);
}
```

## 💬 反馈机制

### 1. 视觉反馈

#### 状态指示器
```css
/* 加载状态 */
.loading {
  position: relative;
  color: transparent;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid var(--color-gray-300);
  border-top-color: var(--color-primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
```

#### 成功反馈
```css
.success-feedback {
  background-color: var(--color-success-50);
  border: 1px solid var(--color-success-200);
  color: var(--color-success-800);
  padding: 12px 16px;
  border-radius: 6px;
  animation: slideInUp var(--duration-normal) var(--ease-out);
}
```

#### 错误反馈
```css
.error-feedback {
  background-color: var(--color-error-50);
  border: 1px solid var(--color-error-200);
  color: var(--color-error-800);
  padding: 12px 16px;
  border-radius: 6px;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}
```

### 2. 触觉反馈

#### 按钮点击
```typescript
// 触觉反馈实现
const hapticFeedback = {
  light: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(10);
    }
  },
  
  medium: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(20);
    }
  },
  
  heavy: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate([30, 10, 30]);
    }
  }
};

// 使用示例
button.addEventListener('click', () => {
  hapticFeedback.light();
});
```

### 3. 音效反馈

#### 系统音效
```typescript
// 音效反馈系统
class AudioFeedback {
  private sounds: Map<string, HTMLAudioElement> = new Map();
  
  constructor() {
    this.loadSounds();
  }
  
  private loadSounds() {
    const soundFiles = {
      click: '/sounds/click.mp3',
      success: '/sounds/success.mp3',
      error: '/sounds/error.mp3',
      notification: '/sounds/notification.mp3'
    };
    
    Object.entries(soundFiles).forEach(([name, url]) => {
      const audio = new Audio(url);
      audio.preload = 'auto';
      this.sounds.set(name, audio);
    });
  }
  
  play(soundName: string, volume: number = 0.5) {
    const sound = this.sounds.get(soundName);
    if (sound) {
      sound.volume = volume;
      sound.currentTime = 0;
      sound.play().catch(() => {
        // 处理自动播放限制
      });
    }
  }
}
```

## 🔄 状态管理

### 1. 加载状态

#### 按钮加载
```typescript
// 按钮加载状态组件
const LoadingButton = (props) => {
  return (
    <button
      class={`btn ${props.loading ? 'loading' : ''}`}
      disabled={props.loading || props.disabled}
      onClick={props.onClick}
    >
      {props.loading ? (
        <>
          <LoadingSpinner />
          {props.loadingText || '处理中...'}
        </>
      ) : (
        props.children
      )}
    </button>
  );
};
```

#### 页面加载
```css
.page-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  flex-direction: column;
  gap: 16px;
}

.skeleton {
  background: linear-gradient(
    90deg,
    var(--color-gray-200) 25%,
    var(--color-gray-100) 50%,
    var(--color-gray-200) 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
```

### 2. 错误状态

#### 表单验证
```css
.form-field.error .input {
  border-color: var(--color-error-500);
  box-shadow: 0 0 0 3px var(--color-error-100);
}

.form-field.error .error-message {
  color: var(--color-error-600);
  font-size: var(--text-sm);
  margin-top: 4px;
  animation: slideInUp var(--duration-fast) var(--ease-out);
}
```

#### 网络错误
```typescript
// 错误状态处理
const ErrorBoundary = (props) => {
  const [error, setError] = createSignal(null);
  
  const retry = () => {
    setError(null);
    props.onRetry?.();
  };
  
  return (
    <Show when={error()} fallback={props.children}>
      <div class="error-state">
        <div class="error-icon">⚠️</div>
        <h3>出现了一些问题</h3>
        <p>{error().message}</p>
        <button class="btn btn-primary" onClick={retry}>
          重试
        </button>
      </div>
    </Show>
  );
};
```

### 3. 空状态

#### 空列表
```css
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
}

.empty-state-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state-title {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: 8px;
}

.empty-state-description {
  color: var(--text-secondary);
  margin-bottom: 24px;
}
```

## 🎮 手势交互

### 1. 触摸手势

#### 滑动手势
```typescript
// 滑动手势处理
class SwipeGesture {
  private startX: number = 0;
  private startY: number = 0;
  private threshold: number = 50;
  
  constructor(
    private element: HTMLElement,
    private onSwipe: (direction: 'left' | 'right' | 'up' | 'down') => void
  ) {
    this.bindEvents();
  }
  
  private bindEvents() {
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this));
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this));
  }
  
  private handleTouchStart(e: TouchEvent) {
    this.startX = e.touches[0].clientX;
    this.startY = e.touches[0].clientY;
  }
  
  private handleTouchEnd(e: TouchEvent) {
    const endX = e.changedTouches[0].clientX;
    const endY = e.changedTouches[0].clientY;
    
    const deltaX = endX - this.startX;
    const deltaY = endY - this.startY;
    
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      if (Math.abs(deltaX) > this.threshold) {
        this.onSwipe(deltaX > 0 ? 'right' : 'left');
      }
    } else {
      if (Math.abs(deltaY) > this.threshold) {
        this.onSwipe(deltaY > 0 ? 'down' : 'up');
      }
    }
  }
}
```

#### 长按手势
```typescript
// 长按手势处理
class LongPressGesture {
  private timer: number | null = null;
  private duration: number = 500;
  
  constructor(
    private element: HTMLElement,
    private onLongPress: () => void
  ) {
    this.bindEvents();
  }
  
  private bindEvents() {
    this.element.addEventListener('mousedown', this.start.bind(this));
    this.element.addEventListener('touchstart', this.start.bind(this));
    this.element.addEventListener('mouseup', this.cancel.bind(this));
    this.element.addEventListener('touchend', this.cancel.bind(this));
    this.element.addEventListener('mouseleave', this.cancel.bind(this));
  }
  
  private start() {
    this.timer = window.setTimeout(() => {
      this.onLongPress();
    }, this.duration);
  }
  
  private cancel() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }
}
```

## 📱 移动端适配

### 1. 触摸目标

#### 最小触摸区域
```css
/* 最小触摸目标 44px */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 小图标的触摸区域扩展 */
.icon-touch {
  padding: 12px;
  margin: -12px;
}
```

### 2. 移动端交互

#### 下拉刷新
```css
.pull-to-refresh {
  position: relative;
  overflow: hidden;
}

.pull-indicator {
  position: absolute;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  transition: transform var(--duration-normal) var(--ease-out);
}

.pull-to-refresh.pulling .pull-indicator {
  transform: translateX(-50%) translateY(60px);
}
```

---

**文档版本**: 1.0  
**创建日期**: 2024年12月  
**维护者**: 设计团队  
**下次更新**: 根据交互设计演进需要
