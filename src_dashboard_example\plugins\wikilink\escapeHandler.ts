import { $prose } from '@milkdown/kit/utils'
import { Plugin, PluginKey } from '@milkdown/kit/prose/state'

/**
 * 转义字符处理插件
 * 在 ProseMirror 层面拦截和处理转义字符
 */

const escapeHandlerKey = new PluginKey('escapeHandler')

export const escapeHandlerPlugin = $prose(() => {
  return new Plugin({
    key: escapeHandlerKey,
    
    // 拦截文档变化，只清理 WikiLink 相关的转义字符
    appendTransaction(transactions, oldState, newState) {
      // 只在有实际变化时处理
      if (!transactions.some(tr => tr.docChanged)) {
        return null
      }

      console.log('🔧 转义处理插件检查文档变化')

      let modified = false
      let tr = newState.tr

      // 遍历文档，查找并清理 WikiLink 相关的转义字符
      newState.doc.descendants((node, pos) => {
        if (node.isText && node.text) {
          const originalText = node.text

          // 只处理包含 WikiLink 模式的转义字符
          if (originalText.includes('\\[') || originalText.includes('\\]')) {
            // 检查是否为 WikiLink 相关的转义（前后有双方括号模式）
            const hasWikiLinkEscape = /\\\[\\\[|\\\]\\\]|\\\[(?![^\\])/g.test(originalText)

            if (hasWikiLinkEscape) {
              const cleanedText = originalText
                .replace(/\\\[/g, '[')
                .replace(/\\\]/g, ']')

              if (cleanedText !== originalText) {
                console.log('🧹 清理 WikiLink 转义字符:', {
                  位置: pos,
                  原始: originalText.substring(0, 50),
                  清理后: cleanedText.substring(0, 50)
                })

                tr = tr.replaceWith(pos, pos + node.nodeSize, newState.schema.text(cleanedText))
                modified = true
              }
            }
          }
        }
      })

      return modified ? tr : null
    },
    
    // 处理输入事件 - 只处理 WikiLink 相关的转义
    handleTextInput(view, from, to, text) {
      // 只处理方括号相关的输入
      if (text === '[' || text === ']' || text.includes('\\[') || text.includes('\\]')) {
        console.log('🔧 处理方括号输入:', { from, to, text })

        // 检查上下文，确定是否为 WikiLink 相关
        const beforeText = view.state.doc.textBetween(Math.max(0, from - 10), from)
        const afterText = view.state.doc.textBetween(to, Math.min(view.state.doc.content.size, to + 10))

        console.log('🔍 输入上下文:', { beforeText, afterText })

        // 如果输入的是转义字符，立即清理
        if (text.includes('\\[') || text.includes('\\]')) {
          console.log('🚫 检测到方括号转义字符，立即清理')

          const cleanedText = text
            .replace(/\\\[/g, '[')
            .replace(/\\\]/g, ']')

          if (cleanedText !== text) {
            const tr = view.state.tr.replaceWith(from, to, view.state.schema.text(cleanedText))
            view.dispatch(tr)
            return true
          }
        }
      }

      return false
    },
    
    // 处理粘贴事件
    handlePaste(view, event, slice) {
      console.log('🔧 处理粘贴事件')
      
      let modified = false
      const newSlice = slice.content.map((node) => {
        if (node.isText && node.text) {
          const originalText = node.text
          const cleanedText = originalText
            .replace(/\\\[/g, '[')
            .replace(/\\\]/g, ']')
          
          if (cleanedText !== originalText) {
            console.log('🧹 清理粘贴内容中的转义字符')
            modified = true
            return view.state.schema.text(cleanedText)
          }
        }
        return node
      })
      
      if (modified) {
        const tr = view.state.tr.replaceSelection(newSlice)
        view.dispatch(tr)
        return true
      }
      
      return false
    }
  })
})

/**
 * 强制清理文档中的所有转义字符
 */
export function forceCleanEscapeChars(view: any) {
  console.log('🧹 强制清理文档中的所有转义字符')
  
  let modified = false
  let tr = view.state.tr
  
  view.state.doc.descendants((node: any, pos: number) => {
    if (node.isText && node.text) {
      const originalText = node.text
      const cleanedText = originalText
        .replace(/\\\[/g, '[')
        .replace(/\\\]/g, ']')
        .replace(/\\\(/g, '(')
        .replace(/\\\)/g, ')')
      
      if (cleanedText !== originalText) {
        console.log('🧹 清理节点:', { 位置: pos, 原始长度: originalText.length, 清理后长度: cleanedText.length })
        tr = tr.replaceWith(pos, pos + node.nodeSize, view.state.schema.text(cleanedText))
        modified = true
      }
    }
  })
  
  if (modified) {
    view.dispatch(tr)
    console.log('✅ 转义字符清理完成')
  } else {
    console.log('ℹ️ 未发现需要清理的转义字符')
  }
}

export default escapeHandlerPlugin
