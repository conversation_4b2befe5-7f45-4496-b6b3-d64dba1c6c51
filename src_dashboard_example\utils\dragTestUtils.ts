// Testing utilities for drag functionality

import type { ExtendedTask } from '../store/taskStore'

export interface DragTestResult {
  success: boolean
  message: string
  details?: any
}

export class DragTester {
  /**
   * Test circular dependency detection
   */
  static testCircularDependency(tasks: ExtendedTask[]): DragTestResult {
    try {
      // Create test scenario: A -> B -> C, try to move A under C
      const taskA = tasks.find((t) => t.content.includes('Parent'))
      const taskB = tasks.find((t) => t.parentId === taskA?.id)
      const taskC = tasks.find((t) => t.parentId === taskB?.id)

      if (!taskA || !taskB || !taskC) {
        return {
          success: false,
          message: 'Test setup incomplete: need parent->child->grandchild structure'
        }
      }

      // Simulate circular dependency check
      const isCircular = this.checkCircularDependency(tasks, taskA.id, taskC.id)

      return {
        success: isCircular,
        message: isCircular
          ? 'Circular dependency correctly detected'
          : 'Circular dependency check failed',
        details: { taskA: taskA.id, taskC: taskC.id }
      }
    } catch (error) {
      return {
        success: false,
        message: 'Error during circular dependency test',
        details: error
      }
    }
  }

  /**
   * Test deep nesting performance
   */
  static testDeepNesting(tasks: ExtendedTask[], maxDepth = 10): DragTestResult {
    try {
      const startTime = performance.now()

      // Find deepest nesting level
      let maxFoundDepth = 0
      const calculateDepth = (taskId: string, currentDepth = 0): number => {
        if (currentDepth > maxDepth) return currentDepth // Prevent infinite recursion

        const children = tasks.filter((t) => t.parentId === taskId)
        if (children.length === 0) return currentDepth

        return Math.max(...children.map((child) => calculateDepth(child.id, currentDepth + 1)))
      }

      // Test all root tasks
      const rootTasks = tasks.filter((t) => !t.parentId)
      rootTasks.forEach((root) => {
        const depth = calculateDepth(root.id)
        maxFoundDepth = Math.max(maxFoundDepth, depth)
      })

      const endTime = performance.now()
      const duration = endTime - startTime

      return {
        success: duration < 100, // Should complete within 100ms
        message: `Deep nesting test completed in ${duration.toFixed(2)}ms`,
        details: {
          maxDepth: maxFoundDepth,
          duration,
          taskCount: tasks.length
        }
      }
    } catch (error) {
      return {
        success: false,
        message: 'Error during deep nesting test',
        details: error
      }
    }
  }

  /**
   * Test large dataset performance
   */
  static testLargeDataset(tasks: ExtendedTask[]): DragTestResult {
    try {
      const startTime = performance.now()

      // Simulate tree building (similar to TaskList component)
      const taskMap = new Map<string, ExtendedTask & { children: ExtendedTask[] }>()
      const rootTasks: (ExtendedTask & { children: ExtendedTask[] })[] = []

      // Initialize all tasks with children array
      tasks.forEach((task) => {
        taskMap.set(task.id, { ...task, children: [] })
      })

      // Build tree structure
      tasks.forEach((task) => {
        const taskWithChildren = taskMap.get(task.id)!
        if (task.parentId && taskMap.has(task.parentId)) {
          taskMap.get(task.parentId)!.children.push(taskWithChildren)
        } else {
          rootTasks.push(taskWithChildren)
        }
      })

      const endTime = performance.now()
      const duration = endTime - startTime

      return {
        success: duration < 50, // Should complete within 50ms for good UX
        message: `Large dataset test completed in ${duration.toFixed(2)}ms`,
        details: {
          taskCount: tasks.length,
          rootCount: rootTasks.length,
          duration
        }
      }
    } catch (error) {
      return {
        success: false,
        message: 'Error during large dataset test',
        details: error
      }
    }
  }

  /**
   * Helper method for circular dependency checking
   */
  private static checkCircularDependency(
    tasks: ExtendedTask[],
    parentId: string,
    childId: string,
    depth = 0
  ): boolean {
    if (depth > 50) return true // Prevent infinite recursion

    const checkTask = tasks.find((task) => task.id === childId)
    if (!checkTask) return false
    if (checkTask.parentId === parentId) return true
    if (checkTask.parentId) {
      return this.checkCircularDependency(tasks, parentId, checkTask.parentId, depth + 1)
    }
    return false
  }

  /**
   * Run all tests
   */
  static runAllTests(tasks: ExtendedTask[]): DragTestResult[] {
    return [
      this.testCircularDependency(tasks),
      this.testDeepNesting(tasks),
      this.testLargeDataset(tasks)
    ]
  }
}

// Convenience function for console testing
export const runDragTests = (tasks: ExtendedTask[]) => {
  console.group('🧪 Drag Functionality Tests')

  const results = DragTester.runAllTests(tasks)

  results.forEach((result, index) => {
    const testNames = ['Circular Dependency', 'Deep Nesting', 'Large Dataset']
    const status = result.success ? '✅' : '❌'

    console.log(`${status} ${testNames[index]}: ${result.message}`)
    if (result.details) {
      console.log('  Details:', result.details)
    }
  })

  const passedTests = results.filter((r) => r.success).length
  console.log(`\n📊 Results: ${passedTests}/${results.length} tests passed`)

  console.groupEnd()

  return results
}
