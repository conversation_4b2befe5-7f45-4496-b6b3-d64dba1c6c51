// Project DTOs - 项目数据传输对象

use crate::domain::entities::{Project, ProjectStatus};
use crate::shared::types::{Id, Priority, Tag, EntityStatus};
use serde::{Deserialize, Serialize};

/// 创建项目请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateProjectRequest {
    pub name: String,
    pub description: Option<String>,
    pub area_id: Option<Id>,
    pub priority: Option<String>,
    pub start_date: Option<chrono::DateTime<chrono::Utc>>,
    pub due_date: Option<chrono::DateTime<chrono::Utc>>,
    pub goals: Option<Vec<String>>,
    pub deliverables: Option<Vec<String>>,
    pub tags: Option<Vec<String>>,
}

/// 更新项目请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateProjectRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub goals: Option<Vec<String>>,
    pub deliverables: Option<Vec<String>>,
    pub start_date: Option<chrono::DateTime<chrono::Utc>>,
    pub due_date: Option<chrono::DateTime<chrono::Utc>>,
    pub area_id: Option<Id>,
    pub priority: Option<String>,
    pub tags: Option<Vec<String>>,
}

/// 更新项目状态请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateProjectStatusRequest {
    pub status: String,
}

/// 更新项目进度请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateProjectProgressRequest {
    pub progress: f32,
}

/// 项目响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectResponse {
    pub id: Id,
    pub name: String,
    pub description: Option<String>,
    pub goals: Vec<String>,
    pub deliverables: Vec<String>,
    pub start_date: Option<chrono::DateTime<chrono::Utc>>,
    pub due_date: Option<chrono::DateTime<chrono::Utc>>,
    pub area_id: Option<Id>,
    pub status: String,
    pub priority: String,
    pub progress: f32,
    pub tags: Vec<String>,
    pub entity_status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub version: u64,
}

/// 项目列表响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectListResponse {
    pub projects: Vec<ProjectResponse>,
    pub total: u64,
    pub page: u64,
    pub size: u64,
}

/// 项目统计响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectStatisticsResponse {
    pub total_projects: u64,
    pub active_projects: u64,
    pub completed_projects: u64,
    pub overdue_projects: u64,
    pub average_progress: f32,
    pub projects_by_status: Vec<ProjectStatusCount>,
    pub projects_by_priority: Vec<ProjectPriorityCount>,
}

/// 项目状态统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectStatusCount {
    pub status: String,
    pub count: u64,
}

/// 项目优先级统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectPriorityCount {
    pub priority: String,
    pub count: u64,
}

/// 克隆项目请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CloneProjectRequest {
    pub new_name: String,
}

impl From<Project> for ProjectResponse {
    fn from(project: Project) -> Self {
        Self {
            id: project.id,
            name: project.name,
            description: project.description,
            goals: project.goals,
            deliverables: project.deliverables,
            start_date: project.start_date,
            due_date: project.due_date,
            area_id: project.area_id,
            status: match project.status {
                ProjectStatus::NotStarted => "not_started".to_string(),
                ProjectStatus::InProgress => "in_progress".to_string(),
                ProjectStatus::AtRisk => "at_risk".to_string(),
                ProjectStatus::OnHold => "on_hold".to_string(),
                ProjectStatus::Completed => "completed".to_string(),
                ProjectStatus::Cancelled => "cancelled".to_string(),
            },
            priority: match project.priority {
                Priority::Low => "low".to_string(),
                Priority::Medium => "medium".to_string(),
                Priority::High => "high".to_string(),
                Priority::Critical => "critical".to_string(),
            },
            progress: project.progress,
            tags: project.tags.into_iter().map(|tag| tag.name).collect(),
            entity_status: match project.entity_status {
                EntityStatus::Active => "active".to_string(),
                EntityStatus::Inactive => "inactive".to_string(),
                EntityStatus::Deleted => "deleted".to_string(),
                EntityStatus::Archived => "archived".to_string(),
            },
            created_at: project.metadata.created_at,
            updated_at: project.metadata.updated_at,
            version: project.metadata.version,
        }
    }
}

impl From<crate::domain::repositories::ProjectStatistics> for ProjectStatisticsResponse {
    fn from(stats: crate::domain::repositories::ProjectStatistics) -> Self {
        Self {
            total_projects: stats.total_projects,
            active_projects: stats.active_projects,
            completed_projects: stats.completed_projects,
            overdue_projects: stats.overdue_projects,
            average_progress: stats.average_progress,
            projects_by_status: stats.projects_by_status.into_iter().map(|(status, count)| {
                ProjectStatusCount {
                    status: match status {
                        ProjectStatus::NotStarted => "not_started".to_string(),
                        ProjectStatus::InProgress => "in_progress".to_string(),
                        ProjectStatus::AtRisk => "at_risk".to_string(),
                        ProjectStatus::OnHold => "on_hold".to_string(),
                        ProjectStatus::Completed => "completed".to_string(),
                        ProjectStatus::Cancelled => "cancelled".to_string(),
                    },
                    count,
                }
            }).collect(),
            projects_by_priority: stats.projects_by_priority.into_iter().map(|(priority, count)| {
                ProjectPriorityCount {
                    priority: match priority {
                        Priority::Low => "low".to_string(),
                        Priority::Medium => "medium".to_string(),
                        Priority::High => "high".to_string(),
                        Priority::Critical => "critical".to_string(),
                    },
                    count,
                }
            }).collect(),
        }
    }
}

/// 验证创建项目请求
impl CreateProjectRequest {
    pub fn validate(&self) -> Result<(), String> {
        if self.name.trim().is_empty() {
            return Err("Project name cannot be empty".to_string());
        }
        
        if self.name.len() > 200 {
            return Err("Project name cannot be longer than 200 characters".to_string());
        }
        
        if let Some(ref description) = self.description {
            if description.len() > 2000 {
                return Err("Project description cannot be longer than 2000 characters".to_string());
            }
        }
        
        if let Some(ref priority) = self.priority {
            if !["low", "medium", "high", "critical"].contains(&priority.as_str()) {
                return Err("Invalid priority value".to_string());
            }
        }
        
        // 验证日期范围
        if let (Some(start), Some(due)) = (self.start_date, self.due_date) {
            if start >= due {
                return Err("Start date must be before due date".to_string());
            }
        }
        
        Ok(())
    }
    
    pub fn get_priority(&self) -> Priority {
        match self.priority.as_deref() {
            Some("low") => Priority::Low,
            Some("medium") => Priority::Medium,
            Some("high") => Priority::High,
            Some("critical") => Priority::Critical,
            _ => Priority::Medium,
        }
    }
    
    pub fn get_tags(&self) -> Vec<Tag> {
        self.tags.as_ref().map_or(Vec::new(), |tags| {
            tags.iter().map(|name| Tag::new(name.clone())).collect()
        })
    }
}

/// 验证更新项目请求
impl UpdateProjectRequest {
    pub fn validate(&self) -> Result<(), String> {
        if let Some(ref name) = self.name {
            if name.trim().is_empty() {
                return Err("Project name cannot be empty".to_string());
            }
            if name.len() > 200 {
                return Err("Project name cannot be longer than 200 characters".to_string());
            }
        }
        
        if let Some(ref description) = self.description {
            if description.len() > 2000 {
                return Err("Project description cannot be longer than 2000 characters".to_string());
            }
        }
        
        if let Some(ref priority) = self.priority {
            if !["low", "medium", "high", "critical"].contains(&priority.as_str()) {
                return Err("Invalid priority value".to_string());
            }
        }
        
        // 验证日期范围
        if let (Some(start), Some(due)) = (self.start_date, self.due_date) {
            if start >= due {
                return Err("Start date must be before due date".to_string());
            }
        }
        
        Ok(())
    }
    
    pub fn get_priority(&self) -> Option<Priority> {
        self.priority.as_deref().map(|p| match p {
            "low" => Priority::Low,
            "medium" => Priority::Medium,
            "high" => Priority::High,
            "critical" => Priority::Critical,
            _ => Priority::Medium,
        })
    }
    
    pub fn get_tags(&self) -> Option<Vec<Tag>> {
        self.tags.as_ref().map(|tags| {
            tags.iter().map(|name| Tag::new(name.clone())).collect()
        })
    }
}

/// 验证更新项目状态请求
impl UpdateProjectStatusRequest {
    pub fn validate(&self) -> Result<(), String> {
        if !["not_started", "in_progress", "at_risk", "on_hold", "completed", "cancelled"].contains(&self.status.as_str()) {
            return Err("Invalid project status".to_string());
        }
        Ok(())
    }
    
    pub fn get_status(&self) -> ProjectStatus {
        match self.status.as_str() {
            "not_started" => ProjectStatus::NotStarted,
            "in_progress" => ProjectStatus::InProgress,
            "at_risk" => ProjectStatus::AtRisk,
            "on_hold" => ProjectStatus::OnHold,
            "completed" => ProjectStatus::Completed,
            "cancelled" => ProjectStatus::Cancelled,
            _ => ProjectStatus::NotStarted,
        }
    }
}

/// 验证更新项目进度请求
impl UpdateProjectProgressRequest {
    pub fn validate(&self) -> Result<(), String> {
        if self.progress < 0.0 || self.progress > 1.0 {
            return Err("Progress must be between 0.0 and 1.0".to_string());
        }
        Ok(())
    }
}
