import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON><PERSON>eader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { AlertTriangle, Trash2 } from 'lucide-react'
import { useLanguage } from '../../contexts/LanguageContext'
import type { ExtendedTask } from '../../store/taskStore'

interface DeleteTaskDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  task: ExtendedTask | null
  childrenCount: number
}

export function DeleteTaskDialog({
  isOpen,
  onClose,
  onConfirm,
  task,
  childrenCount
}: DeleteTaskDialogProps) {
  const { t } = useLanguage()

  if (!task) return null

  const handleConfirm = () => {
    onConfirm()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            {t('components.deleteTaskDialog.title')}
          </DialogTitle>
        </DialogHeader>

        <div className="text-left space-y-2">
          <p className="text-sm text-muted-foreground">{t('components.deleteTaskDialog.confirmMessage')}</p>
          <div className="font-medium text-foreground bg-muted p-2 rounded">"{task.content}"</div>
          {childrenCount > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-md">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-yellow-800">
                  <div className="font-medium">{t('components.deleteTaskDialog.warning')}:</div>
                  <div>
                    {t('components.deleteTaskDialog.subtaskWarning', {
                      count: childrenCount,
                      subtasks: childrenCount > 1 ? t('components.deleteTaskDialog.subtasks') : t('components.deleteTaskDialog.subtask')
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}
          <p className="text-sm text-muted-foreground">{t('components.deleteTaskDialog.cannotUndo')}</p>
        </div>
        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={onClose}>
            {t('common.cancel')}
          </Button>
          <Button variant="destructive" onClick={handleConfirm} className="gap-2">
            <Trash2 className="h-4 w-4" />
            {childrenCount > 0
              ? t('components.deleteTaskDialog.deleteWithSubtasks', {
                  count: childrenCount,
                  subtasks: childrenCount > 1 ? t('components.deleteTaskDialog.subtasks') : t('components.deleteTaskDialog.subtask')
                })
              : t('components.deleteTaskDialog.deleteTask')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
