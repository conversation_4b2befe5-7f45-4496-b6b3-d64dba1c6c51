// ProjectDetail Page - 重新设计的项目详情页面
// 采用左右2:1布局，左侧任务管理，右侧KPI管理、资源关联、笔记功能

import { createSignal, createEffect, Show, For, onMount } from 'solid-js';
import { PageContainer } from '../components/layout/Layout';
import { Card, CardHeader, CardTitle, CardContent } from '../components/ui/Card';
import { Button } from '../components/ui/button';
import { cn } from '@/lib/utils';
import { Project, Task } from '../types/business';
import { getProjectById, getTasksByProject } from '../services/apiFactory';

// 导入新创建的组件
import { TaskManager } from '../components/tasks';
import { KPIManager } from '../components/kpi';
import { ResourceAssociation } from '../components/resource-association';
import { createHabitDataSource } from '../components/habit-tracker';

// 获取项目ID的辅助函数
function getProjectIdFromPath(): string {
  const path = window.location.pathname;
  const segments = path.split('/');
  return segments[segments.length - 1] || '';
}

// 格式化日期的辅助函数
function formatDate(date: string | Date | undefined): string {
  if (!date) return '';
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString();
}

export default function ProjectDetailNew() {
  const projectId = getProjectIdFromPath();
  
  const [project, setProject] = createSignal<Project | null>(null);
  const [tasks, setTasks] = createSignal<Task[]>([]);
  const [loading, setLoading] = createSignal(true);
  const [error, setError] = createSignal<string | null>(null);
  
  // 创建数据源
  const taskDataSource = createHabitDataSource(); // 临时使用，后续需要创建专门的任务数据源
  const kpiDataSource = createHabitDataSource(); // 临时使用，后续需要创建专门的KPI数据源
  const resourceDataSource = createHabitDataSource(); // 临时使用，后续需要创建专门的资源数据源

  // 笔记功能状态
  const [notes, setNotes] = createSignal('');
  const [isEditingNotes, setIsEditingNotes] = createSignal(false);

  // 保存笔记
  const saveNotes = () => {
    // TODO: 实现笔记保存逻辑
    console.log('Saving notes:', notes());
    setIsEditingNotes(false);
  };

  // 加载项目数据
  const loadProject = async () => {
    if (!projectId) {
      setError('项目ID无效');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const [projectData, tasksData] = await Promise.all([
        getProjectById(projectId),
        getTasksByProject(projectId)
      ]);
      
      setProject(projectData);
      setTasks(tasksData || []);
    } catch (err) {
      console.error('加载项目数据失败:', err);
      setError('加载项目数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 计算剩余天数
  const remainingDays = () => {
    if (!project()?.dueDate) return null;
    const due = new Date(project()!.dueDate);
    const now = new Date();
    const diffTime = due.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-700';
      case 'in_progress':
        return 'bg-blue-100 text-blue-700';
      case 'on_hold':
        return 'bg-yellow-100 text-yellow-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  // 获取状态标签
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'in_progress':
        return '进行中';
      case 'on_hold':
        return '暂停';
      default:
        return '未开始';
    }
  };

  // 获取优先级标签
  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high':
        return '高优先级';
      case 'medium':
        return '中优先级';
      case 'low':
        return '低优先级';
      default:
        return '普通';
    }
  };

  // 初始化
  onMount(() => {
    loadProject();
  });

  return (
    <PageContainer>
      <Show when={loading()}>
        <div class="flex items-center justify-center min-h-96">
          <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p class="text-muted-foreground">加载项目数据中...</p>
          </div>
        </div>
      </Show>

      <Show when={error()}>
        <Card>
          <CardContent class="flex items-center justify-center py-12">
            <div class="text-center">
              <div class="text-4xl mb-4">⚠️</div>
              <h3 class="text-lg font-medium mb-2">加载失败</h3>
              <p class="text-muted-foreground mb-4">{error()}</p>
              <Button onClick={loadProject}>重试</Button>
            </div>
          </CardContent>
        </Card>
      </Show>

      <Show when={!loading() && !error() && project()}>
        <div class="space-y-6">
          {/* 返回按钮和项目标题 */}
          <div class="space-y-4">
            {/* 返回按钮 */}
            <div class="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => {
                  window.history.pushState({}, '', '/projects');
                  window.dispatchEvent(new Event('routechange'));
                }}
                class="flex items-center space-x-2"
              >
                <span>←</span>
                <span>返回项目列表</span>
              </Button>
            </div>

            {/* 项目标题和基本信息 */}
            <div class="flex items-start justify-between">
              <div class="space-y-2">
                <h1 class="text-3xl font-bold">{project()?.name}</h1>
                <Show when={project()?.description}>
                  <p class="text-lg text-muted-foreground">{project()?.description}</p>
                </Show>
              </div>
              <div class="flex items-center space-x-2">
                <span class={cn('px-3 py-1 rounded-full text-sm font-medium', getStatusColor(project()?.status || ''))}>
                  {getStatusLabel(project()?.status || '')}
                </span>
                <span class="px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-700">
                  {getPriorityLabel(project()?.priority || '')}
                </span>
              </div>
            </div>

            {/* 进度条 */}
            <div class="space-y-2">
              <div class="flex justify-between text-sm">
                <span class="text-muted-foreground">项目进度</span>
                <span class="font-medium">{Math.round((project()?.progress || 0) * 100)}%</span>
              </div>
              <div class="w-full bg-muted rounded-full h-3 relative overflow-hidden">
                <div
                  class="bg-primary h-full rounded-full transition-all duration-300 absolute top-0 left-0"
                  style={{
                    width: `${Math.round((project()?.progress || 0) * 100)}%`
                  }}
                />
              </div>
            </div>
          </div>

          {/* 新的左右布局 */}
          <div class="grid grid-cols-3 gap-6">
            {/* 左侧：任务管理 (2/3 宽度) */}
            <div class="col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle class="flex items-center gap-2">
                    <span>✅</span>
                    任务管理
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <TaskManager
                    projectId={projectId}
                    dataSource={taskDataSource}
                    config={{
                      allowCreate: true,
                      allowEdit: true,
                      allowDelete: true,
                      showProgress: true,
                      showPriority: true,
                      showAssignee: true,
                      layout: 'list'
                    }}
                  />
                </CardContent>
              </Card>
            </div>

            {/* 右侧：KPI管理、资源关联、笔记功能 (1/3 宽度) */}
            <div class="space-y-6">
              {/* KPI管理 */}
              <Card>
                <CardHeader>
                  <CardTitle class="flex items-center gap-2">
                    <span>📈</span>
                    KPI管理
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <KPIManager
                    projectId={projectId}
                    dataSource={kpiDataSource}
                    config={{
                      allowCreate: true,
                      allowEdit: true,
                      showProgress: true,
                      showTrends: true,
                      layout: 'compact'
                    }}
                  />
                </CardContent>
              </Card>

              {/* 资源关联 */}
              <Card>
                <CardHeader>
                  <CardTitle class="flex items-center gap-2">
                    <span>📎</span>
                    资源关联
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResourceAssociation
                    entityType="project"
                    entityId={projectId}
                    dataSource={resourceDataSource}
                    config={{
                      enableMarkdownAssociation: true,
                      enableLinkManagement: true,
                      enableFileUpload: true,
                      layout: 'compact',
                      showResourcePreview: true,
                      allowCreate: true,
                      allowEdit: true
                    }}
                  />
                </CardContent>
              </Card>

              {/* 笔记功能 */}
              <Card>
                <CardHeader>
                  <CardTitle class="flex items-center gap-2">
                    <span>📝</span>
                    项目笔记
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Show when={!isEditingNotes()}>
                    <div class="space-y-3">
                      <Show when={notes()} fallback={
                        <p class="text-muted-foreground text-sm">暂无笔记</p>
                      }>
                        <div class="prose prose-sm max-w-none">
                          <pre class="whitespace-pre-wrap text-sm">{notes()}</pre>
                        </div>
                      </Show>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsEditingNotes(true)}
                      >
                        {notes() ? '编辑笔记' : '添加笔记'}
                      </Button>
                    </div>
                  </Show>
                  
                  <Show when={isEditingNotes()}>
                    <div class="space-y-3">
                      <textarea
                        class="w-full h-32 p-3 border rounded-md resize-none"
                        placeholder="在这里记录项目相关的笔记..."
                        value={notes()}
                        onInput={(e) => setNotes(e.currentTarget.value)}
                      />
                      <div class="flex gap-2">
                        <Button size="sm" onClick={saveNotes}>
                          保存
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setIsEditingNotes(false)}
                        >
                          取消
                        </Button>
                      </div>
                    </div>
                  </Show>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </Show>
    </PageContainer>
  );
}
