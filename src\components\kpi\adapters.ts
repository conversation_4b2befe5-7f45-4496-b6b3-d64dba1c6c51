/**
 * KPI数据源适配器
 * 将现有的API接口适配为统一的KPI数据源接口
 */

import type {
  KPIDataSource,
  BaseKPI,
  KPIRecord,
  KPIStatistics,
  CreateKPIData,
  CreateRecordData,
  QueryOptions
} from './types'

// 导入现有的API
// 注意：这些导入路径需要根据实际项目结构调整
import { databaseApi } from '../../lib/api'
import type { ProjectKPI, AreaMetric, ProjectKPIRecord, AreaMetricRecord } from '../../../shared/types'

/**
 * 项目KPI数据源适配器
 */
export class ProjectKPIDataSource implements KPIDataSource {
  async getKPIs(projectId: string, options?: QueryOptions): Promise<BaseKPI[]> {
    try {
      const projectKPIs = await databaseApi.getProjectKPIs(projectId)
      return projectKPIs.map(this.convertProjectKPIToBase)
    } catch (error) {
      console.error('Failed to get project KPIs:', error)
      throw error
    }
  }

  async getKPI(id: string): Promise<BaseKPI | null> {
    try {
      const kpi = await databaseApi.getProjectKPI(id)
      return kpi ? this.convertProjectKPIToBase(kpi) : null
    } catch (error) {
      console.error('Failed to get project KPI:', error)
      throw error
    }
  }

  async createKPI(projectId: string, data: CreateKPIData): Promise<BaseKPI> {
    try {
      const kpiData = {
        projectId,
        name: data.name,
        description: data.description,
        value: data.value.toString(),
        target: data.target?.toString(),
        unit: data.unit,
        direction: data.direction,
        frequency: data.frequency || 'weekly'
      }
      
      const newKPI = await databaseApi.createProjectKPI(kpiData)
      return this.convertProjectKPIToBase(newKPI)
    } catch (error) {
      console.error('Failed to create project KPI:', error)
      throw error
    }
  }

  async updateKPI(id: string, data: Partial<CreateKPIData>): Promise<BaseKPI> {
    try {
      const updateData: any = {}
      
      if (data.name !== undefined) updateData.name = data.name
      if (data.description !== undefined) updateData.description = data.description
      if (data.value !== undefined) updateData.value = data.value.toString()
      if (data.target !== undefined) updateData.target = data.target?.toString()
      if (data.unit !== undefined) updateData.unit = data.unit
      if (data.direction !== undefined) updateData.direction = data.direction
      if (data.frequency !== undefined) updateData.frequency = data.frequency
      
      const updatedKPI = await databaseApi.updateProjectKPI(id, updateData)
      return this.convertProjectKPIToBase(updatedKPI)
    } catch (error) {
      console.error('Failed to update project KPI:', error)
      throw error
    }
  }

  async deleteKPI(id: string): Promise<void> {
    try {
      await databaseApi.deleteProjectKPI(id)
    } catch (error) {
      console.error('Failed to delete project KPI:', error)
      throw error
    }
  }

  async getRecords(kpiId: string, options?: QueryOptions): Promise<KPIRecord[]> {
    try {
      const records = await databaseApi.getProjectKPIRecords(kpiId, {
        limit: options?.limit || 20,
        offset: options?.offset || 0
      })
      return records.map(this.convertProjectKPIRecordToBase)
    } catch (error) {
      console.error('Failed to get project KPI records:', error)
      throw error
    }
  }

  async createRecord(kpiId: string, data: CreateRecordData): Promise<KPIRecord> {
    try {
      const recordData = {
        kpiId,
        value: data.value.toString(),
        note: data.note,
        recordedAt: data.recordedAt || new Date()
      }
      
      const newRecord = await databaseApi.createProjectKPIRecord(recordData)
      return this.convertProjectKPIRecordToBase(newRecord)
    } catch (error) {
      console.error('Failed to create project KPI record:', error)
      throw error
    }
  }

  async deleteRecord(recordId: string): Promise<void> {
    try {
      await databaseApi.deleteProjectKPIRecord(recordId)
    } catch (error) {
      console.error('Failed to delete project KPI record:', error)
      throw error
    }
  }

  async getStatistics(projectId: string): Promise<KPIStatistics> {
    try {
      const kpis = await this.getKPIs(projectId)
      return this.calculateStatistics(kpis)
    } catch (error) {
      console.error('Failed to get project KPI statistics:', error)
      throw error
    }
  }

  private convertProjectKPIToBase(kpi: ProjectKPI): BaseKPI {
    return {
      id: kpi.id,
      name: kpi.name,
      description: kpi.description,
      value: parseFloat(kpi.value) || 0,
      target: kpi.target ? parseFloat(kpi.target) : undefined,
      unit: kpi.unit,
      direction: kpi.direction as 'increase' | 'decrease',
      frequency: kpi.frequency as any,
      isActive: true, // 假设所有KPI都是活跃的
      createdAt: new Date(kpi.createdAt),
      updatedAt: new Date(kpi.updatedAt)
    }
  }

  private convertProjectKPIRecordToBase(record: ProjectKPIRecord): KPIRecord {
    return {
      id: record.id,
      kpiId: record.kpiId,
      value: parseFloat(record.value) || 0,
      note: record.note,
      recordedAt: new Date(record.recordedAt),
      source: 'manual' // 默认为手动录入
    }
  }

  private calculateStatistics(kpis: BaseKPI[]): KPIStatistics {
    const total = kpis.length
    let achieved = 0
    let onTrack = 0
    let atRisk = 0
    let behind = 0
    let noTarget = 0
    let totalProgress = 0
    let withTarget = 0

    kpis.forEach(kpi => {
      if (!kpi.target) {
        noTarget++
        return
      }

      withTarget++
      const progress = this.calculateKPIProgress(kpi)
      totalProgress += progress

      if (progress >= 100) achieved++
      else if (progress >= 75) onTrack++
      else if (progress >= 50) atRisk++
      else behind++
    })

    const averageProgress = withTarget > 0 ? Math.round(totalProgress / withTarget) : 0

    return {
      total,
      achieved,
      onTrack,
      atRisk,
      behind,
      noTarget,
      averageProgress
    }
  }

  private calculateKPIProgress(kpi: BaseKPI): number {
    if (!kpi.target) return 0
    
    if (kpi.direction === 'decrease') {
      const estimatedStart = Math.max(kpi.value * 1.5, kpi.target * 2)
      const totalReduction = estimatedStart - kpi.target
      const currentReduction = estimatedStart - kpi.value
      
      if (totalReduction <= 0) return 100
      return Math.min((currentReduction / totalReduction) * 100, 100)
    } else {
      if (kpi.target === 0) return 0
      return Math.min((kpi.value / kpi.target) * 100, 100)
    }
  }
}

/**
 * 领域指标数据源适配器
 */
export class AreaMetricDataSource implements KPIDataSource {
  async getKPIs(areaId: string, options?: QueryOptions): Promise<BaseKPI[]> {
    try {
      const metrics = await databaseApi.getAreaMetrics(areaId)
      return metrics.map(this.convertAreaMetricToBase)
    } catch (error) {
      console.error('Failed to get area metrics:', error)
      throw error
    }
  }

  async getKPI(id: string): Promise<BaseKPI | null> {
    try {
      const metric = await databaseApi.getAreaMetric(id)
      return metric ? this.convertAreaMetricToBase(metric) : null
    } catch (error) {
      console.error('Failed to get area metric:', error)
      throw error
    }
  }

  async createKPI(areaId: string, data: CreateKPIData): Promise<BaseKPI> {
    try {
      const metricData = {
        areaId,
        name: data.name,
        description: data.description,
        value: data.value.toString(),
        target: data.target?.toString(),
        unit: data.unit,
        direction: data.direction,
        frequency: data.frequency || 'daily'
      }
      
      const newMetric = await databaseApi.createAreaMetric(metricData)
      return this.convertAreaMetricToBase(newMetric)
    } catch (error) {
      console.error('Failed to create area metric:', error)
      throw error
    }
  }

  async updateKPI(id: string, data: Partial<CreateKPIData>): Promise<BaseKPI> {
    try {
      const updateData: any = {}
      
      if (data.name !== undefined) updateData.name = data.name
      if (data.description !== undefined) updateData.description = data.description
      if (data.value !== undefined) updateData.value = data.value.toString()
      if (data.target !== undefined) updateData.target = data.target?.toString()
      if (data.unit !== undefined) updateData.unit = data.unit
      if (data.direction !== undefined) updateData.direction = data.direction
      if (data.frequency !== undefined) updateData.frequency = data.frequency
      
      const updatedMetric = await databaseApi.updateAreaMetric(id, updateData)
      return this.convertAreaMetricToBase(updatedMetric)
    } catch (error) {
      console.error('Failed to update area metric:', error)
      throw error
    }
  }

  async deleteKPI(id: string): Promise<void> {
    try {
      await databaseApi.deleteAreaMetric(id)
    } catch (error) {
      console.error('Failed to delete area metric:', error)
      throw error
    }
  }

  async getRecords(metricId: string, options?: QueryOptions): Promise<KPIRecord[]> {
    try {
      const records = await databaseApi.getAreaMetricRecords(metricId, {
        limit: options?.limit || 20,
        offset: options?.offset || 0
      })
      return records.map(this.convertAreaMetricRecordToBase)
    } catch (error) {
      console.error('Failed to get area metric records:', error)
      throw error
    }
  }

  async createRecord(metricId: string, data: CreateRecordData): Promise<KPIRecord> {
    try {
      const recordData = {
        metricId,
        value: data.value.toString(),
        note: data.note,
        recordedAt: data.recordedAt || new Date()
      }
      
      const newRecord = await databaseApi.createAreaMetricRecord(recordData)
      return this.convertAreaMetricRecordToBase(newRecord)
    } catch (error) {
      console.error('Failed to create area metric record:', error)
      throw error
    }
  }

  async deleteRecord(recordId: string): Promise<void> {
    try {
      await databaseApi.deleteAreaMetricRecord(recordId)
    } catch (error) {
      console.error('Failed to delete area metric record:', error)
      throw error
    }
  }

  async getStatistics(areaId: string): Promise<KPIStatistics> {
    try {
      const metrics = await this.getKPIs(areaId)
      return this.calculateStatistics(metrics)
    } catch (error) {
      console.error('Failed to get area metric statistics:', error)
      throw error
    }
  }

  private convertAreaMetricToBase(metric: AreaMetric): BaseKPI {
    return {
      id: metric.id,
      name: metric.name,
      description: metric.description,
      value: parseFloat(metric.value) || 0,
      target: metric.target ? parseFloat(metric.target) : undefined,
      unit: metric.unit,
      direction: metric.direction as 'increase' | 'decrease',
      frequency: metric.frequency as any,
      isActive: true,
      createdAt: new Date(metric.createdAt),
      updatedAt: new Date(metric.updatedAt)
    }
  }

  private convertAreaMetricRecordToBase(record: AreaMetricRecord): KPIRecord {
    return {
      id: record.id,
      kpiId: record.metricId,
      value: parseFloat(record.value) || 0,
      note: record.note,
      recordedAt: new Date(record.recordedAt),
      source: 'manual'
    }
  }

  private calculateStatistics(metrics: BaseKPI[]): KPIStatistics {
    // 复用ProjectKPIDataSource的统计计算逻辑
    const projectDataSource = new ProjectKPIDataSource()
    return (projectDataSource as any).calculateStatistics(metrics)
  }
}

/**
 * 数据源工厂函数
 */
export function createKPIDataSource(type: 'project' | 'area'): KPIDataSource {
  switch (type) {
    case 'project':
      return new ProjectKPIDataSource()
    case 'area':
      return new AreaMetricDataSource()
    default:
      throw new Error(`Unsupported KPI type: ${type}`)
  }
}
