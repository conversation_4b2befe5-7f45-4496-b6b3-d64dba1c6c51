# 资源关联组件库

一个可复用的资源关联组件库，支持Markdown文档、外部链接、文件附件和双向链接系统的统一管理。

## ✨ 特性

- 📄 **Markdown文档关联** - 支持现有文档关联和新文档创建
- 🔗 **外部链接管理** - 自动获取链接预览、验证链接有效性
- 📁 **文件附件上传** - 支持拖拽上传、多文件上传、文件预览
- 🔄 **双向链接系统** - WikiLink语法、反向链接展示、链接强度分析
- 🎨 **统一界面** - 一致的用户体验和交互设计
- ⚡ **高性能** - 基于SolidJS的响应式更新
- 🛡️ **类型安全** - 完整的TypeScript类型定义
- 🔧 **高度可配置** - 丰富的配置选项适应不同场景

## 🚀 快速开始

### 基本使用

```tsx
import { ResourceAssociation, createResourceDataSource } from '@/components/resource-association'

function MyComponent() {
  const dataSource = createResourceDataSource('project')
  
  return (
    <ResourceAssociation
      entityType="project"
      entityId="project-123"
      dataSource={dataSource}
    />
  )
}
```

### 项目资源管理

```tsx
import { 
  ResourceAssociation, 
  createResourceDataSource,
  type ResourceEventHandlers 
} from '@/components/resource-association'

function ProjectDetailPage({ projectId }: { projectId: string }) {
  const dataSource = createResourceDataSource('project')
  
  const config = {
    enableMarkdownAssociation: true,
    enableLinkManagement: true,
    enableFileUpload: true,
    enableBidirectionalLinks: true,
    layout: 'list',
    allowBulkOperations: true
  }
  
  const eventHandlers: ResourceEventHandlers = {
    onCreate: (resource) => console.log('Resource created:', resource),
    onUpdate: (resource) => console.log('Resource updated:', resource),
    onUploadComplete: (resource) => console.log('Upload completed:', resource)
  }
  
  return (
    <ResourceAssociation
      entityType="project"
      entityId={projectId}
      dataSource={dataSource}
      config={config}
      eventHandlers={eventHandlers}
    />
  )
}
```

### 领域资源管理

```tsx
import { 
  ResourceAssociation, 
  createResourceDataSource 
} from '@/components/resource-association'

function AreaDetailPage({ areaId }: { areaId: string }) {
  const dataSource = createResourceDataSource('area')
  
  const config = {
    layout: 'grid',
    allowedFileTypes: ['image/*', 'application/pdf', 'text/*'],
    maxFileSize: 20 * 1024 * 1024, // 20MB
    enableThumbnails: true
  }
  
  return (
    <ResourceAssociation
      entityType="area"
      entityId={areaId}
      dataSource={dataSource}
      config={config}
    />
  )
}
```

## 📦 组件说明

### ResourceAssociation - 主要组件

完整的资源关联和管理组件，包含所有功能模块。

**Props:**
- `entityType: 'project' | 'area'` - 实体类型
- `entityId: string` - 实体ID
- `dataSource: ResourceDataSource` - 数据源接口
- `config?: ResourceAssociationConfig` - 组件配置
- `eventHandlers?: ResourceEventHandlers` - 事件处理器

### FileUpload - 文件上传组件

支持拖拽上传、多文件上传、进度显示的文件上传组件。

**Props:**
- `onUpload: (data: FileUploadData) => Promise<void>` - 上传回调
- `accept?: string` - 允许的文件类型
- `multiple?: boolean` - 是否支持多文件
- `maxSize?: number` - 最大文件大小

### LinkCreate - 链接创建组件

外部链接创建组件，支持链接预览获取和验证。

**Props:**
- `onCreateLink: (data: LinkCreateData) => Promise<void>` - 创建回调
- `placeholder?: string` - 输入框占位符

### MarkdownAssociation - Markdown关联组件

Markdown文档关联组件，支持浏览现有文档和创建新文档。

**Props:**
- `onAssociate: (data: MarkdownAssociationData) => Promise<void>` - 关联回调
- `basePath?: string` - 文档基础路径

### BidirectionalLinks - 双向链接组件

双向链接展示和管理组件，支持WikiLink语法和链接分析。

**Props:**
- `documentPath: string` - 文档路径
- `links: BidirectionalLink[]` - 链接数据
- `onLinkClick?: (link: BidirectionalLink) => void` - 链接点击回调

## ⚙️ 配置选项

### ResourceAssociationConfig

```tsx
interface ResourceAssociationConfig {
  // 功能开关
  enableMarkdownAssociation?: boolean
  enableLinkManagement?: boolean
  enableFileUpload?: boolean
  enableBidirectionalLinks?: boolean
  
  // 显示选项
  showResourcePreview?: boolean
  showResourceStats?: boolean
  showRecentResources?: boolean
  showResourceSearch?: boolean
  
  // 交互选项
  allowCreate?: boolean
  allowEdit?: boolean
  allowDelete?: boolean
  allowBulkOperations?: boolean
  allowDragDrop?: boolean
  
  // 布局选项
  layout?: 'grid' | 'list' | 'compact' | 'tree'
  groupBy?: 'type' | 'date' | 'entity' | 'tags' | 'none'
  sortBy?: 'name' | 'date' | 'type' | 'access' | 'size'
  
  // 文件上传配置
  maxFileSize?: number
  allowedFileTypes?: string[]
  enableThumbnails?: boolean
  
  // 链接配置
  enableLinkPreview?: boolean
  enableFaviconFetch?: boolean
  
  // Markdown配置
  enableWikiLinks?: boolean
  enableAutoLinking?: boolean
}
```

## 🔌 数据源接口

### 创建数据源

```tsx
import { createResourceDataSource } from '@/components/resource-association'

// 项目资源数据源
const projectDataSource = createResourceDataSource('project')

// 领域资源数据源
const areaDataSource = createResourceDataSource('area')
```

### 自定义数据源

```tsx
import { ResourceDataSource } from '@/components/resource-association'

class CustomResourceDataSource implements ResourceDataSource {
  async getResources(entityType: string, entityId: string): Promise<UnifiedResource[]> {
    // 实现获取资源列表
  }
  
  async uploadFile(uploadData: FileUploadData): Promise<FileResource> {
    // 实现文件上传
  }
  
  // ... 其他方法
}
```

## 🎯 事件处理

```tsx
const eventHandlers: ResourceEventHandlers = {
  onCreate: (resource) => {
    console.log('Resource created:', resource)
    // 发送通知、更新缓存等
  },
  
  onUploadComplete: (resource) => {
    console.log('Upload completed:', resource)
    // 检查文件类型、创建相关任务等
  },
  
  onLinkCreate: (link) => {
    console.log('Bidirectional link created:', link)
    // 更新链接图谱、发送通知等
  },
  
  onError: (error, context) => {
    console.error('Error:', error, context)
    // 错误处理、用户提示等
  }
}
```

## 🎨 主题和样式

组件支持自动主题切换和自定义样式：

```tsx
<ResourceAssociation
  config={{
    theme: 'auto', // 'light' | 'dark' | 'auto'
    compactMode: false
  }}
  className="custom-resource-manager"
/>
```

## 📊 支持的资源类型

- **Markdown文档** - `.md`、`.markdown`等格式
- **外部链接** - HTTP/HTTPS URL
- **图片文件** - JPEG、PNG、GIF、WebP等
- **视频文件** - MP4、AVI、MOV等
- **音频文件** - MP3、WAV、FLAC等
- **文档文件** - PDF、Word、Excel、PowerPoint等
- **压缩文件** - ZIP、RAR、7Z等
- **代码文件** - JS、TS、HTML、CSS等

## 🔧 工具函数

```tsx
import { 
  formatFileSize,
  validateUrl,
  extractDomain,
  calculateResourceStats,
  searchResources,
  sortResources
} from '@/components/resource-association'

// 格式化文件大小
const size = formatFileSize(1024000) // "1.0 MB"

// 验证URL
const isValid = validateUrl('https://example.com') // true

// 提取域名
const domain = extractDomain('https://www.example.com/path') // "example.com"

// 搜索资源
const results = searchResources(resources, 'keyword')

// 排序资源
const sorted = sortResources(resources, 'date', 'desc')
```

## 🚀 最佳实践

1. **选择合适的配置**：根据使用场景选择合适的组件配置
2. **处理事件**：实现必要的事件处理器以提供良好的用户体验
3. **错误处理**：妥善处理文件上传和链接验证错误
4. **性能优化**：合理设置文件大小限制和缓存策略
5. **用户体验**：提供清晰的反馈和引导信息

## 📝 类型定义

完整的TypeScript类型定义请参考 `types.ts` 文件。
