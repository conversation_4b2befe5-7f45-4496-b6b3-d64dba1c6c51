// TaskFilters - 基础筛选器组件

import { For } from 'solid-js'
import type { TaskManagerProps, TaskFilters } from './types'
import { Input } from '@/components/ui/Input'

const STATUSES: Array<TaskFilters['status'] extends (infer U)[] ? U : never> = ['todo','in_progress','completed','cancelled'] as any
const PRIORITIES = ['low','medium','high','critical']

export function TaskFiltersBar(props: {
  value: TaskFilters
  onChange: (patch: Partial<TaskFilters>) => void
}) {
  const toggleArr = (arr: string[] | undefined, v: string) => {
    const set = new Set(arr || [])
    if (set.has(v)) set.delete(v); else set.add(v)
    return Array.from(set)
  }

  return (
    <div class="flex flex-wrap items-center gap-2">
      <div class="min-w-[220px]">
        <Input placeholder="搜索任务标题/描述" value={props.value.search || ''} onInput={(e) => props.onChange({ search: (e.currentTarget as HTMLInputElement).value })} />
      </div>

      <div class="flex items-center gap-1 text-xs">
        <span class="text-muted-foreground">状态:</span>
        <For each={STATUSES}>{s => (
          <button
            class="rounded border px-2 py-1 hover:bg-muted"
            classList={{ 'bg-muted': (props.value.status||[]).includes(s as any) }}
            onClick={() => props.onChange({ status: toggleArr(props.value.status as any, s) as any })}
          >{s}</button>
        )}</For>
      </div>

      <div class="flex items-center gap-1 text-xs">
        <span class="text-muted-foreground">优先级:</span>
        <For each={PRIORITIES}>{p => (
          <button
            class="rounded border px-2 py-1 hover:bg-muted"
            classList={{ 'bg-muted': (props.value.priority||[]).includes(p as any) }}
            onClick={() => props.onChange({ priority: toggleArr(props.value.priority as any, p) as any })}
          >{p}</button>
        )}</For>
      </div>
    </div>
  )
}

