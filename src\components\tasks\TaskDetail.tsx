// TaskDetail - 可复用的任务详情面板（抽屉/侧栏内容）
import { Show } from 'solid-js'
import type { Task } from '@/types/business'

export interface TaskDetailProps {
  task: Task | null | undefined
  onClose?: () => void
  onUpdate: (id: string, patch: Partial<Task>) => void
  onToggleStatus: (id: string, completed: boolean) => void
}

export function TaskDetail(props: TaskDetailProps) {
  return (
    <div class="space-y-3 text-sm">
      <Show when={props.task} fallback={<div class="text-sm text-muted-foreground">未选择任务</div>}>
        {(it) => (
          <div class="space-y-3">
            <div>
              <label class="block text-xs text-muted-foreground" for="task-title">标题</label>
              <input id="task-title" class="w-full border rounded px-2 py-1" value={it().title} onInput={(e) => props.onUpdate(it().id, { title: (e.currentTarget as HTMLInputElement).value })} />
            </div>
            <div>
              <label class="block text-xs text-muted-foreground" for="task-desc">描述</label>
              <textarea id="task-desc" class="w-full border rounded px-2 py-1 min-h-[80px]" value={it().description || ''} onInput={(e) => props.onUpdate(it().id, { description: (e.currentTarget as HTMLTextAreaElement).value })} />
            </div>
            <div class="grid grid-cols-2 gap-2">
              <div>
                <label class="block text-xs text-muted-foreground" for="task-due">到期日</label>
                <input id="task-due" type="date" class="w-full border rounded px-2 py-1" value={it().dueDate?.slice(0,10)} onInput={(e) => props.onUpdate(it().id, { dueDate: (e.currentTarget as HTMLInputElement).value })} />
              </div>
              <div>
                <label class="block text-xs text-muted-foreground" for="task-pri">优先级</label>
                <select id="task-pri" class="w-full border rounded px-2 py-1" value={it().priority} onChange={(e) => props.onUpdate(it().id, { priority: (e.currentTarget as HTMLSelectElement).value as any })}>
                  <option value="low">低</option>
                  <option value="medium">中</option>
                  <option value="high">高</option>
                  <option value="critical">紧急</option>
                </select>
              </div>
            </div>
            <div>
              <label class="inline-flex items-center gap-2">
                <input type="checkbox" checked={it().status === 'completed'} onChange={(e) => props.onToggleStatus(it().id, (e.currentTarget as HTMLInputElement).checked)} /> 完成
              </label>
            </div>
          </div>
        )}
      </Show>
    </div>
  )
}

export default TaskDetail

