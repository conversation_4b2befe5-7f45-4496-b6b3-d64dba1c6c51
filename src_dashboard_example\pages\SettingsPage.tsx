import { <PERSON>Header } from '../components/shared'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { Label } from '../components/ui/label'
import { Switch } from '../components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../components/ui/select'
import { useLanguage, type Language } from '../contexts/LanguageContext'
import { useUserSettingsStore } from '../store/userSettingsStore'
import { useUIStore } from '../store/uiStore'
import { usePARASettingsStore } from '../store/paraSettingsStore'
import { settingsApi } from '../lib/api'
import { ShortcutsDialog } from '../components/features/ShortcutsDialog'
import { useState, useEffect } from 'react'

export function SettingsPage() {
  const { language, setLanguage, t } = useLanguage()
  const {
    settings,
    updateSettings,
    setUsername,
    setEditorMode,
    setEditorTheme,
    setFocusMode,
    setAutoSave,
    setAutoSaveInterval,
    setShowExitConfirm,
    resetSettings
  } = useUserSettingsStore()
  const { theme, setTheme, addNotification } = useUIStore()
  const {
    settings: paraSettings,
    updateAutoArchive,
    updateWeeklyReview,
    updateProjectTemplate
  } = usePARASettingsStore()

  // 本地状态用于输入控制
  const [localUsername, setLocalUsername] = useState(settings.username)
  const [localAutoSaveInterval, setLocalAutoSaveInterval] = useState(settings.autoSaveInterval || 30)
  const [databaseInfo, setDatabaseInfo] = useState<any>(null)
  const [shortcutsDialogOpen, setShortcutsDialogOpen] = useState(false)

  // 同步设置变化
  useEffect(() => {
    setLocalUsername(settings.username)
    setLocalAutoSaveInterval(settings.autoSaveInterval || 30)
  }, [settings.username, settings.autoSaveInterval])

  // 加载数据库信息
  useEffect(() => {
    const loadDatabaseInfo = async () => {
      try {
        const result = await settingsApi.getDatabaseInfo()
        if (result.success) {
          setDatabaseInfo(result.data)
        }
      } catch (error) {
        console.error('Failed to load database info:', error)
      }
    }
    loadDatabaseInfo()
  }, [])

  const handleLanguageChange = (newLanguage: Language) => {
    setLanguage(newLanguage)
    addNotification({
      type: 'success',
      title: t('settings.notifications.languageChanged'),
      message: t('settings.notifications.languageChangedMessage')
    })
  }

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme)
    addNotification({
      type: 'success',
      title: t('settings.notifications.themeChanged'),
      message: t('settings.notifications.themeChangedMessage')
    })
  }

  // 初始化“已记住的选择”显示
  useEffect(() => {
    (async () => {
      try {
        const res = await window.electronAPI.app.getExitPreference()
        const el = document.getElementById('exit-pref-display')
        if (el) {
          el.textContent = !res?.data ? '无' : (res.data.action === 'minimize' ? '最小化到托盘' : '退出')
        }
      } catch {}
    })()
  }, [])

  const handleUsernameChange = (value: string) => {
    setLocalUsername(value)
  }

  const handleUsernameSave = () => {
    if (localUsername.trim() && localUsername !== settings.username) {
      setUsername(localUsername.trim())
      addNotification({
        type: 'success',
        title: t('settings.notifications.usernameSaved'),
        message: t('settings.notifications.usernameSavedMessage')
      })
    }
  }

  const handleAutoSaveIntervalChange = (value: string) => {
    const interval = parseInt(value)
    if (!isNaN(interval) && interval > 0) {
      setLocalAutoSaveInterval(interval)
      setAutoSaveInterval(interval)
    }
  }

  const handleResetSettings = () => {
    if (window.confirm(t('settings.confirmReset'))) {
      resetSettings()
      addNotification({
        type: 'success',
        title: t('settings.notifications.settingsReset'),
        message: t('settings.notifications.settingsResetMessage')
      })
    }
  }

  const handleExportData = async () => {
    try {
      // 从数据库导出完整数据
      const result = await settingsApi.exportData()

      if (!result.success) {
        throw new Error(result.error)
      }

      // 创建下载链接
      const dataStr = JSON.stringify(result.data, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)

      const link = document.createElement('a')
      link.href = url
      link.download = `paolife-data-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      addNotification({
        type: 'success',
        title: t('settings.notifications.exportSuccess'),
        message: t('settings.notifications.exportSuccessMessage')
      })
    } catch (error) {
      console.error('Export failed:', error)
      addNotification({
        type: 'error',
        title: t('settings.notifications.exportFailed'),
        message: t('settings.notifications.exportFailedMessage')
      })
    }
  }

  const handleImportData = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      try {
        const text = await file.text()
        const importData = JSON.parse(text)

        // 验证数据格式
        if (!importData.userSettings || !importData.uiSettings) {
          throw new Error('Invalid settings file format')
        }

        // 导入用户设置
        if (importData.userSettings) {
          updateSettings(importData.userSettings)
        }

        // 导入UI设置
        if (importData.uiSettings) {
          if (importData.uiSettings.theme) {
            setTheme(importData.uiSettings.theme)
          }
          if (importData.uiSettings.language) {
            setLanguage(importData.uiSettings.language)
          }
        }

        addNotification({
          type: 'success',
          title: t('settings.notifications.importSuccess'),
          message: t('settings.notifications.importSuccessMessage')
        })
      } catch (error) {
        console.error('Import failed:', error)
        addNotification({
          type: 'error',
          title: t('settings.notifications.importFailed'),
          message: t('settings.notifications.importFailedMessage')
        })
      }
    }
    input.click()
  }

  const handleCreateBackup = async () => {
    try {
      const result = await settingsApi.createBackup()

      if (!result.success) {
        throw new Error(result.error)
      }

      addNotification({
        type: 'success',
        title: t('settings.notifications.backupSuccess'),
        message: t('settings.notifications.backupSuccessMessage')
      })
    } catch (error) {
      console.error('Backup failed:', error)
      addNotification({
        type: 'error',
        title: t('settings.notifications.backupFailed'),
        message: t('settings.notifications.backupFailedMessage')
      })
    }
  }

  // 移除了资源路径更改功能，避免数据丢失风险
  // 工作目录在首次设置后不允许更改

  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader title={t('settings.title')} description={t('settings.description')} />

      {/* General Settings */}
      <Card>
        <CardHeader>
          <CardTitle>{t('settings.general')}</CardTitle>
          <CardDescription>{t('settings.generalDescription')}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="username">{t('settings.displayName')}</Label>
              <div className="flex gap-2">
                <Input
                  id="username"
                  placeholder={t('settings.displayName')}
                  value={localUsername}
                  onChange={(e) => handleUsernameChange(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleUsernameSave()}
                />
                <Button
                  variant="outline"
                  onClick={handleUsernameSave}
                  disabled={!localUsername.trim() || localUsername === settings.username}
                >
                  {t('settings.save')}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="theme">{t('settings.theme')}</Label>
              <Select value={theme} onValueChange={handleThemeChange}>
                <SelectTrigger>
                  <SelectValue placeholder={t('settings.selectTheme')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">{t('settings.themes.light')}</SelectItem>
                  <SelectItem value="dark">{t('settings.themes.dark')}</SelectItem>
                  <SelectItem value="system">{t('settings.themes.system')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="language">{t('settings.language')}</Label>
            <Select value={language} onValueChange={handleLanguageChange}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder={t('settings.selectLanguage')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="zh">{t('settings.languages.zh')}</SelectItem>
                <SelectItem value="en">{t('settings.languages.en')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-4">
            <div className="p-4 border rounded-lg space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">{t('settings.app.exitConfirm')}</h4>
                  <p className="text-sm text-muted-foreground">
                    {t('settings.app.exitConfirmDescription')}
                  </p>
                </div>
                <Switch
                  checked={settings.showExitConfirm !== false}
                  onCheckedChange={setShowExitConfirm}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="space-y-1">
                  <Label>关闭行为</Label>
                  <Select onValueChange={(v) => {
                    if (v === 'ask') {
                      window.electronAPI.app.setExitPreference('ask')
                    } else if (v === 'minimize') {
                      window.electronAPI.app.setExitPreference('minimize')
                    } else if (v === 'exit') {
                      window.electronAPI.app.setExitPreference('exit')
                    }
                  }}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择关闭行为" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ask">总是询问</SelectItem>
                      <SelectItem value="minimize">总是最小化到托盘</SelectItem>
                      <SelectItem value="exit">总是退出</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-1">
                  <Label>已记住的选择</Label>
                  <div className="text-sm text-muted-foreground" id="exit-pref-display">加载中…</div>
                </div>

                <div className="space-y-1">
                  <Label className="invisible">操作</Label>
                  <Button variant="outline" onClick={async () => {
                    await window.electronAPI.app.clearExitPreference()
                    // 更新显示
                    const res = await window.electronAPI.app.getExitPreference()
                    const el = document.getElementById('exit-pref-display')
                    if (el) {
                      el.textContent = !res.data ? '无' : (res.data.action === 'minimize' ? '最小化到托盘' : '退出')
                    }
                  }}>清除记忆</Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Editor Settings */}
      <Card>
        <CardHeader>
          <CardTitle>{t('settings.editor.title')}</CardTitle>
          <CardDescription>{t('settings.editor.description')}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="editorMode">{t('settings.editor.mode')}</Label>
              <Select value={settings.editorMode || 'ir'} onValueChange={(value: 'wysiwyg' | 'ir') => setEditorMode(value)}>
                <SelectTrigger>
                  <SelectValue placeholder={t('settings.editor.selectMode')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ir">{t('settings.editor.modes.ir')}</SelectItem>
                  <SelectItem value="wysiwyg">{t('settings.editor.modes.wysiwyg')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="editorTheme">{t('settings.editor.theme')}</Label>
              <Select value={settings.editorTheme || 'dark'} onValueChange={(value: 'classic' | 'dark') => setEditorTheme(value)}>
                <SelectTrigger>
                  <SelectValue placeholder={t('settings.editor.selectTheme')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="dark">{t('settings.editor.themes.dark')}</SelectItem>
                  <SelectItem value="classic">{t('settings.editor.themes.classic')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">{t('settings.editor.focusMode')}</h4>
                <p className="text-sm text-muted-foreground">
                  {t('settings.editor.focusModeDescription')}
                </p>
              </div>
              <Switch
                checked={settings.focusMode || false}
                onCheckedChange={setFocusMode}
              />
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">{t('settings.editor.autoSave')}</h4>
                <p className="text-sm text-muted-foreground">
                  {t('settings.editor.autoSaveDescription')}
                </p>
              </div>
              <Switch
                checked={settings.autoSave || false}
                onCheckedChange={setAutoSave}
              />
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">{t('settings.editor.autoSaveInterval')}</h4>
                <p className="text-sm text-muted-foreground">
                  {t('settings.editor.autoSaveIntervalDescription')}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Input
                  type="number"
                  value={localAutoSaveInterval}
                  onChange={(e) => handleAutoSaveIntervalChange(e.target.value)}
                  className="w-20"
                  min="5"
                  max="300"
                />
                <span className="text-sm text-muted-foreground">{t('settings.editor.seconds')}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* P.A.R.A. Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {t('settings.para.title')}
            <Badge variant="secondary">{t('settings.para.badge')}</Badge>
          </CardTitle>
          <CardDescription>{t('settings.para.description')}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">{t('settings.para.autoArchive.title')}</h4>
                <p className="text-sm text-muted-foreground">
                  {t('settings.para.autoArchive.description')}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Switch
                  checked={paraSettings.autoArchive.enabled}
                  onCheckedChange={(enabled) => updateAutoArchive({ enabled })}
                />
                <Input
                  type="number"
                  value={paraSettings.autoArchive.daysAfterCompletion}
                  onChange={(e) => updateAutoArchive({ daysAfterCompletion: parseInt(e.target.value) || 30 })}
                  className="w-20"
                  min="1"
                  max="365"
                  disabled={!paraSettings.autoArchive.enabled}
                />
                <span className="text-sm text-muted-foreground">{t('settings.para.autoArchive.unit')}</span>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">{t('settings.para.weeklyReview.title')}</h4>
                <p className="text-sm text-muted-foreground">
                  {t('settings.para.weeklyReview.description')}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Switch
                  checked={paraSettings.weeklyReview.enabled}
                  onCheckedChange={(enabled) => updateWeeklyReview({ enabled })}
                />
                <Select
                  value={paraSettings.weeklyReview.dayOfWeek}
                  onValueChange={(dayOfWeek: 'sunday' | 'monday' | 'friday') => updateWeeklyReview({ dayOfWeek })}
                  disabled={!paraSettings.weeklyReview.enabled}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sunday">{t('settings.para.weeklyReview.days.sunday')}</SelectItem>
                    <SelectItem value="monday">{t('settings.para.weeklyReview.days.monday')}</SelectItem>
                    <SelectItem value="friday">{t('settings.para.weeklyReview.days.friday')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">{t('settings.para.projectTemplate.title')}</h4>
                <p className="text-sm text-muted-foreground">
                  {t('settings.para.projectTemplate.description')}
                </p>
              </div>
              <Select
                value={paraSettings.projectTemplate.defaultTemplate}
                onValueChange={(defaultTemplate: 'basic' | 'detailed' | 'agile') => updateProjectTemplate({ defaultTemplate })}
              >
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="basic">{t('settings.para.projectTemplate.options.basic')}</SelectItem>
                  <SelectItem value="detailed">{t('settings.para.projectTemplate.options.detailed')}</SelectItem>
                  <SelectItem value="agile">{t('settings.para.projectTemplate.options.agile')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data & Storage */}
      <Card>
        <CardHeader>
          <CardTitle>{t('settings.dataStorage.title')}</CardTitle>
          <CardDescription>{t('settings.dataStorage.description')}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold">{databaseInfo?.projectCount || 0}</div>
              <p className="text-sm text-muted-foreground">{t('settings.dataStorage.stats.projects')}</p>
            </div>

            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold">{databaseInfo?.areaCount || 0}</div>
              <p className="text-sm text-muted-foreground">{t('settings.dataStorage.stats.areas')}</p>
            </div>

            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold">{databaseInfo?.totalItems || 0}</div>
              <p className="text-sm text-muted-foreground">{t('settings.dataStorage.stats.totalItems')}</p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-2">
            <Button variant="outline" className="flex-1" onClick={handleExportData}>
              {t('settings.dataStorage.actions.exportData')}
            </Button>
            <Button variant="outline" className="flex-1" onClick={handleImportData}>
              {t('settings.dataStorage.actions.importData')}
            </Button>
            <Button variant="outline" className="flex-1" onClick={handleCreateBackup}>
              {t('settings.dataStorage.actions.createBackup')}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Advanced Settings */}
      <Card>
        <CardHeader>
          <CardTitle>{t('settings.advanced.title')}</CardTitle>
          <CardDescription>{t('settings.advanced.description')}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            {/* 快捷键管理 */}
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">{t('settings.advanced.shortcuts.title')}</h4>
                <p className="text-sm text-muted-foreground">
                  {t('settings.advanced.shortcuts.description')}
                </p>
              </div>
              <Button variant="outline" onClick={() => setShortcutsDialogOpen(true)}>
                {t('settings.advanced.shortcuts.configure')}
              </Button>
            </div>

            {/* 工作目录信息（只读） */}
            <div className="flex items-center justify-between p-4 border rounded-lg bg-muted/50">
              <div>
                <h4 className="font-medium">{t('settings.advanced.workspaceDirectory.title')}</h4>
                <p className="text-sm text-muted-foreground">
                  {t('settings.advanced.workspaceDirectory.description')}
                </p>
                <p className="text-xs text-muted-foreground mt-1 font-mono">
                  {settings.workspaceDirectory || t('settings.advanced.workspaceDirectory.notSet')}
                </p>
              </div>
              <Badge variant="secondary">{t('settings.advanced.workspaceDirectory.readonly')}</Badge>
            </div>
          </div>

          <div className="pt-4 border-t">
            <div className="flex flex-col sm:flex-row gap-2">
              <Button variant="outline" onClick={handleResetSettings}>
                {t('settings.advanced.actions.resetSettings')}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* About */}
      <Card>
        <CardHeader>
          <CardTitle>{t('settings.about.title')}</CardTitle>
          <CardDescription>{t('settings.about.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p>
              <strong>{t('settings.about.info.version')}</strong> 1.0.0
            </p>
            <p>
              <strong>{t('settings.about.info.build')}</strong> 2025.01.14
            </p>
            <p>
              <strong>{t('settings.about.info.electron')}</strong> 28.0.0
            </p>
            <p>
              <strong>{t('settings.about.info.nodejs')}</strong> 18.18.2
            </p>
            <p>
              <strong>{t('settings.about.info.license')}</strong> MIT
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 快捷键设置对话框 */}
      <ShortcutsDialog
        open={shortcutsDialogOpen}
        onOpenChange={setShortcutsDialogOpen}
      />
    </div>
  )
}

export default SettingsPage
