import { createSignal, For, Show, type Component } from 'solid-js';
import type { Project, ProjectStatus } from './types';
import { getStatusText, getRemainingDaysColor } from './types';

const ProgressRing: Component<{ progress: number }> = (props) => {
    const size = 36; const strokeWidth = 3.5; const radius = (size - strokeWidth) / 2;
    const circumference = radius * 2 * Math.PI;
    const offset = () => circumference - (props.progress / 100) * circumference;
    return (
        <svg width={size} height={size} class="transform -rotate-90">
            <circle class="text-gray-200 dark:text-gray-700" stroke="currentColor" stroke-width={strokeWidth} fill="transparent" r={radius} cx={size / 2} cy={size / 2} />
            <circle class="text-blue-500 transition-all duration-300" stroke="currentColor" stroke-width={strokeWidth} stroke-dasharray={circumference} stroke-dashoffset={offset()} stroke-linecap="round" fill="transparent" r={radius} cx={size / 2} cy={size / 2} />
            <text x="50%" y="50%" text-anchor="middle" dy=".3em" class="text-[10px] font-bold fill-current text-gray-700 dark:text-gray-300 transform rotate-90" style={{ "transform-origin": "center" }}>{`${Math.round(props.progress)}%`}</text>
        </svg>
    );
};

const ProjectActions: Component<{ onEdit: () => void; onArchive: () => void; onDelete: () => void }> = (props) => {
    const [isOpen, setIsOpen] = createSignal(false);
    let menuRef: HTMLDivElement | undefined;
    const handleClickOutside = (e: MouseEvent) => { if (menuRef && !menuRef.contains(e.target as Node)) setIsOpen(false); };
    const toggleMenu = (e: MouseEvent) => { e.stopPropagation(); const wasOpen = isOpen(); setIsOpen(!wasOpen); if (!wasOpen) document.addEventListener('click', handleClickOutside, { once: true }); };
    return (
        <div class="relative" ref={menuRef}>
            <button onClick={toggleMenu} class="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle></svg>
            </button>
            <Show when={isOpen()}>
                <div class="absolute right-0 top-full mt-2 w-32 bg-white dark:bg-gray-800 rounded-md shadow-lg border dark:border-gray-700 z-10 text-sm">
                    <button onClick={props.onEdit} class="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700">编辑</button>
                    <button onClick={props.onArchive} class="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700">归档</button>
                    <button onClick={props.onDelete} class="w-full text-left px-3 py-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/50">删除</button>
                </div>
            </Show>
        </div>
    );
};

interface KanbanViewProps {
    projects: Project[];
    onUpdateStatus: (projectId: string, newStatus: ProjectStatus) => void;
    onSelect: (id: string) => void;
    onEdit: (project: Project) => void;
    onArchive: (project: Project) => void;
    onDelete: (id: string) => void;
}

const KANBAN_COLUMNS: { status: ProjectStatus }[] = [{ status: 'not-started' }, { status: 'in-progress' }, { status: 'at-risk' }, { status: 'paused' }];

export const ProjectKanbanView: Component<KanbanViewProps> = (props) => {
    const [draggedProjectId, setDraggedProjectId] = createSignal<string | null>(null);
    const calculateProgress = (project: Project) => { if (project.tasks.length === 0) return 0; return (project.tasks.filter(t => t.completed).length / project.tasks.length) * 100; };
    const getRemainingDaysInfo = (dueDate: string | undefined): { text: string; color: string } => {
        if (!dueDate) return { text: '未设截止', color: 'text-gray-500' };
        const today = new Date(); const due = new Date(dueDate);
        today.setHours(0, 0, 0, 0); due.setHours(0, 0, 0, 0);
        const diffDays = Math.ceil((due.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        const color = getRemainingDaysColor(diffDays);
        let text = diffDays < 0 ? `逾期 ${Math.abs(diffDays)} 天` : diffDays === 0 ? '今天截止' : `剩余 ${diffDays} 天`;
        return { text, color };
    };

    const handleDragStart = (e: DragEvent, projectId: string) => { e.dataTransfer?.setData('text/plain', projectId); setDraggedProjectId(projectId); };
    const handleDragOver = (e: DragEvent) => e.preventDefault();
    const handleDrop = (e: DragEvent, newStatus: ProjectStatus) => { e.preventDefault(); const projectId = e.dataTransfer?.getData('text/plain'); if (projectId) props.onUpdateStatus(projectId, newStatus); setDraggedProjectId(null); };
    
    return (
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 h-full">
            <For each={KANBAN_COLUMNS}>
                {column => {
                    const projectsInColumn = () => props.projects.filter(p => p.status === column.status);
                    return (
                        <div onDragOver={handleDragOver} onDrop={(e) => handleDrop(e, column.status)} class="bg-gray-100 dark:bg-gray-800/50 rounded-lg flex flex-col">
                            <h2 class="font-semibold p-4 border-b border-gray-200 dark:border-gray-700/50 flex-shrink-0">{getStatusText(column.status)} <span class="text-sm font-normal text-gray-500 ml-2">({projectsInColumn().length})</span></h2>
                            <div class="p-3 space-y-3 flex-1 overflow-y-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
                                <For each={projectsInColumn()}>
                                    {project => {
                                        const remainingDays = getRemainingDaysInfo(project.dueDate);
                                        return (
                                        <div draggable onDragStart={(e) => handleDragStart(e, project.id)} onClick={() => props.onSelect(project.id)} class={`p-4 bg-white dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700/50 cursor-grab active:cursor-grabbing transition-opacity group relative flex flex-col justify-between min-h-[120px] ${draggedProjectId() === project.id ? 'opacity-50' : 'opacity-100'}`}>
                                            <div>
                                                <div class="absolute top-2 right-2 z-10">
                                                    <ProjectActions onEdit={() => props.onEdit(project)} onArchive={() => props.onArchive(project)} onDelete={() => props.onDelete(project.id)} />
                                                </div>
                                                <div class="flex items-center gap-2 mb-2 flex-wrap">
                                                    <h3 class="font-semibold text-sm truncate" title={project.name}>{project.name}</h3>
                                                    <Show when={project.areaName}><a href="#" onClick={e => e.stopPropagation()} class="text-xs font-medium text-blue-600 dark:text-blue-400 hover:underline flex-shrink-0">{project.areaName}</a></Show>
                                                </div>
                                                <p class="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">{project.description}</p>
                                            </div>
                                            <div class="mt-4 flex justify-between items-end">
                                                <p class={`text-xs font-medium ${remainingDays.color}`} title={`截止日期: ${project.dueDate || 'N/A'}`}>{remainingDays.text}</p>
                                                <ProgressRing progress={calculateProgress(project)} />
                                            </div>
                                        </div>
                                    )}}
                                </For>
                            </div>
                        </div>
                    );
                }}
            </For>
        </div>
    );
};

