// Project Application Service - 项目应用服务

use crate::domain::entities::{Project, ProjectStatus};
use crate::domain::repositories::{ProjectRepository, TaskRepository, AreaRepository};
use crate::domain::services::ProjectDomainService;
use crate::shared::errors::{AppError, Result};
use crate::shared::types::{Id, QueryParams, Priority, Tag};
use std::sync::Arc;

/// 项目应用服务
/// 负责项目相关的业务流程编排
pub struct ProjectAppService {
    project_repository: Arc<dyn ProjectRepository>,
    task_repository: Arc<dyn TaskRepository>,
    area_repository: Arc<dyn AreaRepository>,
    project_domain_service: ProjectDomainService,
}

impl ProjectAppService {
    pub fn new(
        project_repository: Arc<dyn ProjectRepository>,
        task_repository: Arc<dyn TaskRepository>,
        area_repository: Arc<dyn AreaRepository>,
        project_domain_service: ProjectDomainService,
    ) -> Self {
        Self {
            project_repository,
            task_repository,
            area_repository,
            project_domain_service,
        }
    }

    /// 创建新项目
    pub async fn create_project(
        &self,
        name: String,
        description: Option<String>,
        area_id: Option<Id>,
        priority: Option<Priority>,
    ) -> Result<Project> {
        // 验证项目名称
        self.project_domain_service.validate_project_name(&name)?;

        // 验证领域是否存在（如果提供）
        if let Some(ref area_id) = area_id {
            if self.area_repository.find_by_id(area_id).await?.is_none() {
                return Err(AppError::ValidationError("Area not found".to_string()));
            }
        }

        // 创建项目实体
        let mut project = Project::new(name, description);
        project.area_id = area_id;
        if let Some(priority) = priority {
            project.priority = priority;
        }

        // 保存项目
        self.project_repository.save(&project).await?;

        Ok(project)
    }

    /// 根据ID获取项目
    pub async fn get_project_by_id(&self, id: &Id) -> Result<Project> {
        self.project_repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| AppError::NotFound("Project not found".to_string()))
    }

    /// 更新项目信息
    pub async fn update_project(
        &self,
        id: &Id,
        name: Option<String>,
        description: Option<String>,
        goals: Option<Vec<String>>,
        deliverables: Option<Vec<String>>,
        start_date: Option<chrono::DateTime<chrono::Utc>>,
        due_date: Option<chrono::DateTime<chrono::Utc>>,
        area_id: Option<Id>,
        priority: Option<Priority>,
        tags: Option<Vec<Tag>>,
    ) -> Result<Project> {
        // 获取现有项目
        let mut project = self.get_project_by_id(id).await?;

        // 验证项目名称（如果提供）
        if let Some(ref new_name) = name {
            self.project_domain_service.validate_project_name(new_name)?;
            project.name = new_name.clone();
        }

        // 验证领域是否存在（如果提供）
        if let Some(ref new_area_id) = area_id {
            if self.area_repository.find_by_id(new_area_id).await?.is_none() {
                return Err(AppError::ValidationError("Area not found".to_string()));
            }
            project.area_id = Some(new_area_id.clone());
        }

        // 更新其他字段
        if let Some(desc) = description {
            project.description = Some(desc);
        }
        if let Some(goals) = goals {
            project.goals = goals;
        }
        if let Some(deliverables) = deliverables {
            project.deliverables = deliverables;
        }
        if let Some(start) = start_date {
            project.start_date = Some(start);
        }
        if let Some(due) = due_date {
            project.due_date = Some(due);
        }
        if let Some(priority) = priority {
            project.priority = priority;
        }
        if let Some(tags) = tags {
            project.tags = tags;
        }

        // 验证日期范围
        self.project_domain_service.validate_date_range(&project)?;

        project.update_metadata();

        // 保存更新
        self.project_repository.update(&project).await?;

        Ok(project)
    }

    /// 更新项目状态
    pub async fn update_project_status(&self, id: &Id, status: ProjectStatus) -> Result<Project> {
        let mut project = self.get_project_by_id(id).await?;

        // 验证状态转换是否合法
        match (&project.status, &status) {
            (ProjectStatus::Completed, _) | (ProjectStatus::Cancelled, _) => {
                return Err(AppError::ValidationError(
                    "Cannot change status of completed or cancelled project".to_string(),
                ));
            }
            _ => {}
        }

        project.status = status;
        project.update_metadata();

        self.project_repository.update(&project).await?;

        Ok(project)
    }

    /// 更新项目进度
    pub async fn update_project_progress(&self, id: &Id, progress: f32) -> Result<Project> {
        if progress < 0.0 || progress > 1.0 {
            return Err(AppError::ValidationError(
                "Progress must be between 0.0 and 1.0".to_string(),
            ));
        }

        let mut project = self.get_project_by_id(id).await?;
        project.progress = progress;
        project.update_metadata();

        // 如果进度达到100%，自动设置为完成状态
        if progress >= 1.0 && project.status != ProjectStatus::Completed {
            project.status = ProjectStatus::Completed;
        }

        self.project_repository.update(&project).await?;

        Ok(project)
    }

    /// 完成项目
    pub async fn complete_project(&self, id: &Id) -> Result<Project> {
        let mut project = self.get_project_by_id(id).await?;

        if !self.project_domain_service.can_complete_project(&project) {
            return Err(AppError::ValidationError("Project cannot be completed".to_string()));
        }

        project.status = ProjectStatus::Completed;
        project.progress = 1.0;
        project.update_metadata();

        self.project_repository.update(&project).await?;

        Ok(project)
    }

    /// 取消项目
    pub async fn cancel_project(&self, id: &Id) -> Result<Project> {
        let mut project = self.get_project_by_id(id).await?;

        if project.status == ProjectStatus::Completed {
            return Err(AppError::ValidationError("Cannot cancel completed project".to_string()));
        }

        project.status = ProjectStatus::Cancelled;
        project.update_metadata();

        self.project_repository.update(&project).await?;

        Ok(project)
    }

    /// 删除项目（软删除）
    pub async fn delete_project(&self, id: &Id) -> Result<()> {
        // 检查项目是否存在
        let _project = self.get_project_by_id(id).await?;

        // 执行软删除
        self.project_repository.delete(id).await?;

        Ok(())
    }

    /// 获取所有活跃项目
    pub async fn get_active_projects(&self) -> Result<Vec<Project>> {
        self.project_repository.find_all_active().await
    }

    /// 根据领域获取项目
    pub async fn get_projects_by_area(&self, area_id: &Id) -> Result<Vec<Project>> {
        self.project_repository.find_by_area_id(area_id).await
    }

    /// 根据状态获取项目
    pub async fn get_projects_by_status(&self, status: &ProjectStatus) -> Result<Vec<Project>> {
        self.project_repository.find_by_status(status).await
    }

    /// 获取即将到期的项目
    pub async fn get_projects_due_soon(&self, days: u32) -> Result<Vec<Project>> {
        self.project_repository.find_due_soon(days).await
    }

    /// 获取已逾期的项目
    pub async fn get_overdue_projects(&self) -> Result<Vec<Project>> {
        self.project_repository.find_overdue().await
    }

    /// 分页获取项目列表
    pub async fn get_projects_with_pagination(&self, params: &QueryParams) -> Result<(Vec<Project>, u64)> {
        self.project_repository.find_with_pagination(params).await
    }

    /// 搜索项目
    pub async fn search_projects(&self, query: &str) -> Result<Vec<Project>> {
        if query.trim().is_empty() {
            return Ok(Vec::new());
        }

        self.project_repository.search(query).await
    }

    /// 获取项目统计信息
    pub async fn get_project_statistics(&self) -> Result<crate::domain::repositories::ProjectStatistics> {
        self.project_repository.get_statistics().await
    }

    /// 计算项目完成率（基于任务）
    pub async fn calculate_project_completion_rate(&self, id: &Id) -> Result<f32> {
        self.task_repository.get_project_completion_rate(id).await
    }

    /// 同步项目进度（基于任务完成情况）
    pub async fn sync_project_progress(&self, id: &Id) -> Result<Project> {
        let completion_rate = self.calculate_project_completion_rate(id).await?;
        self.update_project_progress(id, completion_rate).await
    }

    /// 获取风险项目（进度落后且临近截止日期）
    pub async fn get_at_risk_projects(&self) -> Result<Vec<Project>> {
        let all_projects = self.project_repository.find_all_active().await?;
        
        let at_risk_projects = all_projects
            .into_iter()
            .filter(|project| self.project_domain_service.is_at_risk(project))
            .collect();

        Ok(at_risk_projects)
    }

    /// 归档项目
    pub async fn archive_project(&self, id: &Id) -> Result<Project> {
        let mut project = self.get_project_by_id(id).await?;
        
        // 只有完成或取消的项目才能归档
        if project.status != ProjectStatus::Completed && project.status != ProjectStatus::Cancelled {
            return Err(AppError::ValidationError(
                "Only completed or cancelled projects can be archived".to_string(),
            ));
        }

        project.entity_status = crate::shared::types::EntityStatus::Archived;
        project.update_metadata();

        self.project_repository.update(&project).await?;

        Ok(project)
    }

    /// 克隆项目（创建副本）
    pub async fn clone_project(&self, id: &Id, new_name: String) -> Result<Project> {
        let original_project = self.get_project_by_id(id).await?;

        // 验证新项目名称
        self.project_domain_service.validate_project_name(&new_name)?;

        // 创建新项目
        let mut new_project = Project::new(new_name, original_project.description.clone());
        new_project.goals = original_project.goals.clone();
        new_project.deliverables = original_project.deliverables.clone();
        new_project.area_id = original_project.area_id.clone();
        new_project.priority = original_project.priority;
        new_project.tags = original_project.tags.clone();

        // 保存新项目
        self.project_repository.save(&new_project).await?;

        Ok(new_project)
    }
}
