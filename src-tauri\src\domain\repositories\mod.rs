// Domain Repository Interfaces - 仓储接口
// 数据访问抽象

use crate::shared::errors::Result;
use async_trait::async_trait;

pub mod user_repository;
pub mod project_repository;
pub mod task_repository;
pub mod area_repository;
pub mod habit_repository;
pub mod resource_repository;
pub mod inbox_repository;
pub mod review_repository;
pub mod notification_repository;
pub mod checklist_repository;
pub mod template_repository;
pub mod file_repository;

// 重新导出仓储接口
pub use user_repository::*;
pub use project_repository::*;
pub use task_repository::*;
pub use area_repository::*;
pub use habit_repository::*;
pub use resource_repository::*;
pub use inbox_repository::*;
pub use review_repository::*;
pub use notification_repository::*;
pub use checklist_repository::*;
pub use template_repository::*;
pub use file_repository::*;

// 基础仓储特征
#[async_trait]
pub trait Repository<T, ID> {
    async fn find_by_id(&self, id: ID) -> Result<Option<T>>;
    async fn save(&self, entity: &T) -> Result<()>;
    async fn delete(&self, id: ID) -> Result<()>;
    async fn find_all(&self) -> Result<Vec<T>>;
}
