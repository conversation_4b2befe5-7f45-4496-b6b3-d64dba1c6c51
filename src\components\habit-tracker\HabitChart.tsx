/**
 * 习惯图表组件
 * 提供多种图表类型展示习惯数据
 */

import { createMemo, For, Show } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { 
  BarChart3, 
  LineChart, 
  Activity,
  TrendingUp,
  Calendar
} from 'lucide-solid'
import { cn } from '../../lib/utils'

import type { HabitChartProps, BaseHabit, HabitRecord } from './types'

export function HabitChart(props: HabitChartProps) {
  // 生成时间范围的日期数组
  const dateRange = createMemo(() => {
    const dates: string[] = []
    const today = new Date()
    
    let days: number
    switch (props.timeRange) {
      case 'week':
        days = 7
        break
      case 'month':
        days = 30
        break
      case 'year':
        days = 365
        break
      default:
        days = 30
    }
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)
      dates.push(date.toISOString().split('T')[0])
    }
    
    return dates
  })

  // 计算每日完成率数据
  const dailyCompletionData = createMemo(() => {
    const dates = dateRange()
    const activeHabits = props.habits.filter(habit => habit.isActive)
    
    return dates.map(date => {
      const dayRecords = props.records.filter(record => 
        record.date === date && record.completed
      )
      
      const completionRate = activeHabits.length > 0 
        ? Math.round((dayRecords.length / activeHabits.length) * 100)
        : 0
      
      return {
        date,
        completionRate,
        completedCount: dayRecords.length,
        totalHabits: activeHabits.length
      }
    })
  })

  // 计算每个习惯的完成率
  const habitCompletionData = createMemo(() => {
    const dates = dateRange()
    
    return props.habits.map(habit => {
      const habitRecords = props.records.filter(record => 
        record.habitId === habit.id && dates.includes(record.date)
      )
      
      const completedRecords = habitRecords.filter(record => record.completed)
      const completionRate = dates.length > 0 
        ? Math.round((completedRecords.length / dates.length) * 100)
        : 0
      
      return {
        habit,
        completionRate,
        completedDays: completedRecords.length,
        totalDays: dates.length,
        records: habitRecords
      }
    }).sort((a, b) => b.completionRate - a.completionRate)
  })

  // 获取最大值用于图表缩放
  const maxCompletionRate = createMemo(() => {
    const rates = dailyCompletionData().map(d => d.completionRate)
    return Math.max(...rates, 100)
  })

  // 格式化日期显示
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    switch (props.timeRange) {
      case 'week':
        return date.toLocaleDateString('en-US', { weekday: 'short' })
      case 'month':
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
      case 'year':
        return date.toLocaleDateString('en-US', { month: 'short' })
      default:
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    }
  }

  // 获取图表图标
  const getChartIcon = () => {
    switch (props.type) {
      case 'line':
        return LineChart
      case 'bar':
        return BarChart3
      case 'heatmap':
        return Calendar
      case 'progress':
        return Activity
      default:
        return BarChart3
    }
  }

  const ChartIcon = getChartIcon()

  return (
    <Card class={cn("", props.class)} style={{ height: props.height ? `${props.height}px` : 'auto' }}>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <ChartIcon class="h-5 w-5" />
          Habit Progress Chart
          <Badge variant="outline" class="text-xs capitalize">
            {props.type}
          </Badge>
        </CardTitle>
        <CardDescription>
          {props.timeRange === 'week' && 'Last 7 days'}
          {props.timeRange === 'month' && 'Last 30 days'}
          {props.timeRange === 'year' && 'Last 365 days'}
          {' '}completion trends
        </CardDescription>
      </CardHeader>
      
      <CardContent class="space-y-6">
        {/* 线性图 */}
        <Show when={props.type === 'line'}>
          <div class="space-y-4">
            <div class="h-48 flex items-end justify-between gap-1">
              <For each={dailyCompletionData()}>
                {(data, index) => {
                  const height = (data.completionRate / maxCompletionRate()) * 100
                  return (
                    <div class="flex flex-col items-center gap-1 flex-1">
                      <div 
                        class="w-full bg-blue-500 rounded-t transition-all hover:bg-blue-600"
                        style={{ height: `${height}%`, minHeight: data.completionRate > 0 ? '4px' : '0' }}
                        title={`${formatDate(data.date)}: ${data.completionRate}% (${data.completedCount}/${data.totalHabits})`}
                      />
                      <div class="text-xs text-muted-foreground text-center">
                        {formatDate(data.date)}
                      </div>
                    </div>
                  )
                }}
              </For>
            </div>
          </div>
        </Show>

        {/* 柱状图 */}
        <Show when={props.type === 'bar'}>
          <div class="space-y-4">
            <div class="h-48 flex items-end justify-between gap-2">
              <For each={dailyCompletionData()}>
                {(data) => {
                  const height = (data.completionRate / maxCompletionRate()) * 100
                  return (
                    <div class="flex flex-col items-center gap-1 flex-1">
                      <div 
                        class="w-full bg-gradient-to-t from-blue-500 to-blue-400 rounded transition-all hover:from-blue-600 hover:to-blue-500"
                        style={{ height: `${height}%`, minHeight: data.completionRate > 0 ? '4px' : '0' }}
                        title={`${formatDate(data.date)}: ${data.completionRate}%`}
                      />
                      <div class="text-xs text-muted-foreground text-center">
                        {formatDate(data.date)}
                      </div>
                    </div>
                  )
                }}
              </For>
            </div>
          </div>
        </Show>

        {/* 热力图 */}
        <Show when={props.type === 'heatmap'}>
          <div class="space-y-4">
            <div class="grid gap-1" style={{ 
              gridTemplateColumns: `repeat(${Math.min(dateRange().length, 7)}, 1fr)` 
            }}>
              <For each={dailyCompletionData()}>
                {(data) => {
                  const intensity = data.completionRate / 100
                  const bgColor = intensity === 0 
                    ? 'bg-gray-100' 
                    : `bg-green-${Math.ceil(intensity * 5) * 100}`
                  
                  return (
                    <div
                      class={cn(
                        "aspect-square rounded-sm transition-all hover:scale-110",
                        bgColor
                      )}
                      style={{
                        backgroundColor: intensity > 0 
                          ? `rgba(34, 197, 94, ${0.2 + intensity * 0.8})` 
                          : undefined
                      }}
                      title={`${formatDate(data.date)}: ${data.completionRate}%`}
                    />
                  )
                }}
              </For>
            </div>
            
            <div class="flex items-center justify-between text-xs text-muted-foreground">
              <span>Less</span>
              <div class="flex gap-1">
                <div class="w-3 h-3 bg-gray-100 rounded-sm" />
                <div class="w-3 h-3 bg-green-200 rounded-sm" />
                <div class="w-3 h-3 bg-green-300 rounded-sm" />
                <div class="w-3 h-3 bg-green-400 rounded-sm" />
                <div class="w-3 h-3 bg-green-500 rounded-sm" />
              </div>
              <span>More</span>
            </div>
          </div>
        </Show>

        {/* 进度条图 */}
        <Show when={props.type === 'progress'}>
          <div class="space-y-4">
            <For each={habitCompletionData()}>
              {(data) => (
                <div class="space-y-2">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <div 
                        class="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: data.habit.color }}
                      />
                      <span class="font-medium text-sm">{data.habit.name}</span>
                      <Badge variant="outline" class="text-xs">
                        {data.habit.type}
                      </Badge>
                    </div>
                    <div class="flex items-center gap-2 text-sm">
                      <span class="text-muted-foreground">
                        {data.completedDays}/{data.totalDays}
                      </span>
                      <span class="font-medium">{data.completionRate}%</span>
                    </div>
                  </div>
                  <Progress value={data.completionRate} class="h-2" />
                </div>
              )}
            </For>
          </div>
        </Show>

        {/* 图例 */}
        <Show when={props.showLegend}>
          <div class="flex items-center justify-center gap-6 pt-4 border-t text-sm">
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-blue-500 rounded" />
              <span>Completion Rate</span>
            </div>
            <div class="flex items-center gap-2">
              <TrendingUp class="h-4 w-4 text-green-500" />
              <span>Trend</span>
            </div>
          </div>
        </Show>

        {/* 统计摘要 */}
        <div class="grid grid-cols-3 gap-4 pt-4 border-t text-center">
          <div>
            <div class="text-lg font-semibold">
              {Math.round(dailyCompletionData().reduce((sum, d) => sum + d.completionRate, 0) / dailyCompletionData().length)}%
            </div>
            <div class="text-xs text-muted-foreground">Average</div>
          </div>
          <div>
            <div class="text-lg font-semibold">
              {Math.max(...dailyCompletionData().map(d => d.completionRate))}%
            </div>
            <div class="text-xs text-muted-foreground">Best Day</div>
          </div>
          <div>
            <div class="text-lg font-semibold">
              {dailyCompletionData().filter(d => d.completionRate === 100).length}
            </div>
            <div class="text-xs text-muted-foreground">Perfect Days</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default HabitChart
