// TaskManager - 可复用任务管理容器组件
// 负责：加载数据、应用筛选、提供CRUD操作、渲染不同显示模式

import { For, Show, createEffect, createMemo, createSignal, onMount } from 'solid-js'
import { createStore, produce } from 'solid-js/store'
import type { Task } from '@/types/business'
import { Input } from '@/components/ui/Input'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { getTasks, getTasksByProject, getTodayTasks, getOverdueTasks, createTask as apiCreateTask, updateTask as apiUpdateTask, updateTaskStatus as apiUpdateTaskStatus, deleteTask as apiDeleteTask, getTaskStatistics } from '@/services/apiFactory'
import type { TaskManagerProps, TaskWithChildren, TaskFilters, TaskDataSourceOverrides } from './types'
import { TaskFiltersBar } from './TaskFilters'
import TaskDetail from './TaskDetail'

// 简单的拖拽排序占位实现（无外部依赖），用于行内Drag手柄
function useDragSort(onReorder: (sourceId: string, targetId: string) => void) {
  let dragging: string | null = null
  const onDragStart = (id: string) => (e: DragEvent) => {
    dragging = id
    e.dataTransfer?.setData('text/plain', id)
    e.dataTransfer?.setDragImage?.(document.createElement('div'), 0, 0)
  }
  const onDragOver = (id: string) => (e: DragEvent) => {
    if (!dragging || dragging === id) return
    e.preventDefault()
  }
  const onDrop = (id: string) => (e: DragEvent) => {
    e.preventDefault()
    if (!dragging || dragging === id) return
    onReorder(dragging, id)
    dragging = null
  }
  const onDragEnd = () => {
    dragging = null
  }
  return { onDragStart, onDragOver, onDrop, onDragEnd }
}

export default function TaskManager(props: TaskManagerProps) {
  const features = () => ({
    enableSubtasks: true,
    enableInlineEdit: true,
    enableDragSort: true,
    enableFilters: true,
    showStats: true,
    ...(props.features || {}),
  })

  const dataSource: Required<TaskDataSourceOverrides> = {
    fetchTasks: async (ctx, filters) => {
      if (filters.onlyToday) return await getTodayTasks()
      if (filters.onlyOverdue) return await getOverdueTasks()
      if (ctx.projectId) {
        const list = await getTasksByProject(ctx.projectId)
        // 如果该项目下暂无任务，则回退到全部任务，避免详情页空白
        return (list && list.length > 0) ? list : await getTasks()
      }
      return await getTasks()
    },
    createTask: apiCreateTask,
    updateTask: apiUpdateTask,
    updateTaskStatus: apiUpdateTaskStatus,
    deleteTask: apiDeleteTask,
    ...(props.dataSource || {}),
  }

  const [loading, setLoading] = createSignal(true)
  const [error, setError] = createSignal<string | null>(null)
  const [filters, setFilters] = createStore<TaskFilters>(props.initialFilters || {})
  const [tasks, setTasks] = createStore<Task[]>([])
  const [selection, setSelection] = createSignal<Set<string>>(new Set())

  const reload = async () => {
    setError(null)
    setLoading(true)
    try {
      const list = await dataSource.fetchTasks(props.context || {}, filters)
      setTasks(list)
    } catch (e: any) {
      setError(e?.message || '加载失败')
    } finally {
      setLoading(false)
    }
  }

  onMount(reload)
  createEffect(() => {
    // 当筛选或上下文变化时刷新
    void reload()
  })

  const viewMode = () => props.mode || 'list'

  const filtered = createMemo(() => {
    const kw = (filters.search || '').toLowerCase().trim()
    return tasks.filter(t => {
      if (filters.status && filters.status.length && !filters.status.includes(t.status)) return false
      if (filters.priority && filters.priority.length && !filters.priority.includes(t.priority as any)) return false
      if (filters.dateRange?.start && t.dueDate && t.dueDate < filters.dateRange.start) return false
      if (filters.dateRange?.end && t.dueDate && t.dueDate > filters.dateRange.end) return false
      if (kw && !(t.title.toLowerCase().includes(kw) || (t.description || '').toLowerCase().includes(kw))) return false
      return true
    })
  })

  // 子任务简单支持：根据 parentTaskId 生成层级并在列表中缩进展示
  const childrenMap = createMemo(() => {
    const map = new Map<string, Task[]>()
    for (const t of filtered()) {
      if (t.parentTaskId) {
        const list = map.get(t.parentTaskId) || []
        list.push(t)
        map.set(t.parentTaskId, list)
      }
    }
    return map
  })

  // 层级渲染与展开/折叠、任务详情抽屉
  const presentIds = createMemo(() => new Set(filtered().map(t => t.id)))
  const roots = createMemo(() => filtered().filter(t => !t.parentTaskId || !presentIds().has(t.parentTaskId!)))

  const [expanded, setExpanded] = createSignal<Set<string>>(new Set())
  const toggleExpand = (id: string) => setExpanded(prev => {
    const next = new Set(prev); if (next.has(id)) next.delete(id); else next.add(id); return next
  })

  const [detailOpen, setDetailOpen] = createSignal(false)
  const [detailTaskId, setDetailTaskId] = createSignal<string | null>(null)
  const openDetail = (id: string) => { setDetailTaskId(id); setDetailOpen(true) }
  const closeDetail = () => setDetailOpen(false)
  const taskMap = createMemo(() => { const m = new Map<string, Task>(); tasks.forEach(t => m.set(t.id, t)); return m })
  const detailTask = () => (detailTaskId() ? taskMap().get(detailTaskId()!) || null : null)

  const renderRow = (t: Task, level: number) => {
    const kids = childrenMap().get(t.id) || []
    const hasKids = kids.length > 0
    return (
      <>
        <div
          draggable={features().enableDragSort}
          onDragStart={onDragStart(t.id) as any}
          onDragOver={onDragOver(t.id) as any}
          onDrop={onDrop(t.id) as any}
          onDragEnd={onDragEnd as any}
          class="grid grid-cols-[32px_1fr_140px_140px_120px_60px] items-center gap-2 px-3 h-12 border-t"
        >
          <div>
            <input type="checkbox" checked={isSelected(t.id)} onChange={() => toggleSelect(t.id)} />
          </div>
          <div class="truncate">
            <div class="flex items-center gap-2" style={{ 'margin-left': `${level * 16}px` }}>
              {hasKids && (
                <button type="button" class="text-xs" aria-label="展开/折叠" onClick={() => toggleExpand(t.id)}>
                  {expanded().has(t.id) ? '▾' : '▸'}
                </button>
              )}
              <span class="cursor-grab select-none" title="拖拽排序">⠿</span>
              <input
                class="w-full bg-transparent outline-none"
                value={t.title}
                onInput={(e) => features().enableInlineEdit && updateTask(t.id, { title: (e.currentTarget as HTMLInputElement).value })}
              />
            </div>
          </div>
          <div>
            <input aria-label="到期日" type="date" class="w-full bg-transparent outline-none text-sm" value={t.dueDate?.slice(0,10)} onInput={(e) => updateTask(t.id, { dueDate: (e.currentTarget as HTMLInputElement).value })} />
          </div>
          <div>
            <select aria-label="优先级" class="w-full bg-transparent outline-none text-sm" value={t.priority} onChange={(e) => updateTask(t.id, { priority: (e.currentTarget as HTMLSelectElement).value as any })}>
              <option value="low">低</option>
              <option value="medium">中</option>
              <option value="high">高</option>
              <option value="critical">紧急</option>
            </select>
          </div>
          <div>
            <label class="inline-flex items-center gap-1 text-sm">
              <input aria-label="完成" type="checkbox" checked={t.status === 'completed'} onChange={(e) => toggleTaskStatus(t.id, (e.currentTarget as HTMLInputElement).checked)} /> 完成
            </label>
          </div>
          <div class="flex items-center gap-2">
            <button type="button" class="text-xs text-blue-600" onClick={() => openDetail(t.id)}>详情</button>
            <button type="button" class="text-destructive" title="删除" onClick={() => removeTask(t.id)}>×</button>
          </div>
        </div>
        <Show when={hasKids && expanded().has(t.id)}>
          <For each={kids}>{(c) => renderRow(c, level + 1)}</For>
        </Show>
      </>
    )
  }

  const completionRate = createMemo(() => {
    const total = filtered().length
    const completed = filtered().filter(t => t.status === 'completed').length
    return total ? Math.round((completed / total) * 100) : 0
  })

  // CRUD actions
  async function createNewTask(partial: Partial<Task>) {
    const created = await dataSource.createTask({
      title: partial.title || '未命名任务',
      description: partial.description,
      projectId: props.context?.projectId,
      parentTaskId: (features().enableSubtasks ? partial.parentTaskId : undefined),
      dueDate: partial.dueDate,
      priority: partial.priority || 'medium',
    })
    setTasks(p => [created, ...p])
    props.onTaskCreated?.(created)
  }

  async function updateTask(id: string, patch: Partial<Task>) {
    const updated = await dataSource.updateTask(id, patch)
    setTasks(produce(list => {
      const idx = list.findIndex(t => t.id === id)
      if (idx >= 0) list[idx] = updated
    }))
    props.onTaskUpdated?.(updated)
  }

  async function toggleTaskStatus(id: string, checked: boolean) {
    const newStatus: Task['status'] = checked ? 'completed' : 'todo'
    const updated = await dataSource.updateTaskStatus(id, newStatus)
    setTasks(produce(list => {
      const idx = list.findIndex(t => t.id === id)
      if (idx >= 0) list[idx] = updated
    }))
    props.onTaskUpdated?.(updated)
  }

  async function removeTask(id: string) {
    await dataSource.deleteTask(id)
    setTasks(p => p.filter(t => t.id !== id))
    props.onTaskDeleted?.(id)
  }

  // 排序（简单：在同一列表内前后交换）
  function reorder(sourceId: string, targetId: string) {
    setTasks(produce(list => {
      const sIdx = list.findIndex(t => t.id === sourceId)
      const tIdx = list.findIndex(t => t.id === targetId)
      if (sIdx < 0 || tIdx < 0) return
      const [s] = list.splice(sIdx, 1)
      list.splice(tIdx, 0, s)
    }))
  }

  const { onDragStart, onDragOver, onDrop, onDragEnd } = useDragSort(reorder)

  const isSelected = (id: string) => selection().has(id)
  const toggleSelect = (id: string) => {
    setSelection(prev => {
      const next = new Set(prev)
      if (next.has(id)) next.delete(id)
      else next.add(id)
      props.onSelectionChange?.(Array.from(next))
      return next
    })
  }

  return (
    <div class={cn('space-y-3', props.class)}>
      <Show when={features().enableFilters}>
        <div class="flex items-center gap-2">
          <Input placeholder="搜索任务..." value={filters.search || ''} onInput={(e) => setFilters('search', (e.currentTarget as HTMLInputElement).value)} />
          <Button variant="secondary" onClick={() => setFilters({})}>清除</Button>
          <Button onClick={() => createNewTask({ title: '新任务' })}>新建任务</Button>
        </div>
      </Show>

      <Show when={!loading()} fallback={<div class="text-sm text-muted-foreground">加载中...</div>}>
        <Show when={!error()} fallback={<div class="text-sm text-destructive">{error()}</div>}>
          <Show when={viewMode() === 'list'}>
            <div class="rounded-xl border border-input overflow-hidden">
              <div class="grid grid-cols-[32px_1fr_140px_140px_80px_40px] items-center gap-2 px-3 h-10 text-xs bg-muted/50">
                <div></div>
                <div>标题</div>
                <div>到期日</div>
                <div>优先级</div>
                <div>状态</div>
                <div></div>
              </div>
              <For each={filtered()}>
                {(t) => (
                  <div
                    draggable={features().enableDragSort}
                    onDragStart={onDragStart(t.id) as any}
                    onDragOver={onDragOver(t.id) as any}
                    onDrop={onDrop(t.id) as any}
                    onDragEnd={onDragEnd as any}
                    class="grid grid-cols-[32px_1fr_140px_140px_80px_40px] items-center gap-2 px-3 h-12 border-t"
                  >
                    <div>
                      <input type="checkbox" checked={isSelected(t.id)} onChange={() => toggleSelect(t.id)} />
                    </div>
                    <div class="truncate">
                      <div class="flex items-center gap-2">
                        <span class="cursor-grab select-none" title="拖拽排序">⠿</span>
                        <input
                          class="w-full bg-transparent outline-none"
                          value={t.title}
                          onInput={(e) => features().enableInlineEdit && updateTask(t.id, { title: (e.currentTarget as HTMLInputElement).value })}
                        />
                      </div>
                    </div>
                    <div>
                      <input aria-label="到期日" type="date" class="w-full bg-transparent outline-none text-sm" value={t.dueDate?.slice(0,10)} onInput={(e) => updateTask(t.id, { dueDate: (e.currentTarget as HTMLInputElement).value })} />
                    </div>
                    <div>
                      <select aria-label="优先级" class="w-full bg-transparent outline-none text-sm" value={t.priority} onChange={(e) => updateTask(t.id, { priority: (e.currentTarget as HTMLSelectElement).value as any })}>
                        <option value="low">低</option>
                        <option value="medium">中</option>
                        <option value="high">高</option>
                        <option value="critical">紧急</option>
                      </select>
                    </div>
                    <div>
                      <label class="inline-flex items-center gap-1 text-sm">
                        <input aria-label="完成" type="checkbox" checked={t.status === 'completed'} onChange={(e) => toggleTaskStatus(t.id, (e.currentTarget as HTMLInputElement).checked)} /> 完成
                      </label>
                    </div>
                    <div>
                      <button type="button" class="text-destructive" title="删除" onClick={() => removeTask(t.id)}>×</button>
                    </div>
                  </div>
                )}
              </For>
            </div>
          </Show>

          <Show when={features().showStats}>
            <div class="text-xs text-muted-foreground">完成率：{completionRate()}%</div>
          </Show>
        </Show>
      </Show>
    </div>
  )
}

