import React, { useEffect, useRef } from 'react'
import { cn } from '../../../lib/utils'
import type { CompletionUIProps, CompletionItem } from './types'

/**
 * 补全建议UI组件
 * 显示补全建议列表，支持键盘导航和鼠标选择
 */
export function CompletionUI({
  items,
  selectedIndex,
  position,
  visible,
  onSelect,
  onClose
}: CompletionUIProps) {
  const containerRef = useRef<HTMLDivElement>(null)

  // 键盘导航处理
  useEffect(() => {
    if (!visible) return

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowUp':
          event.preventDefault()
          const prevIndex = selectedIndex > 0 ? selectedIndex - 1 : items.length - 1
          if (items[prevIndex]) {
            onSelect(items[prevIndex])
          }
          break
        case 'ArrowDown':
          event.preventDefault()
          const nextIndex = selectedIndex < items.length - 1 ? selectedIndex + 1 : 0
          if (items[nextIndex]) {
            onSelect(items[nextIndex])
          }
          break
        case 'Enter':
          event.preventDefault()
          if (items[selectedIndex]) {
            onSelect(items[selectedIndex])
          }
          break
        case 'Escape':
          event.preventDefault()
          onClose()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [visible, selectedIndex, items, onSelect, onClose])

  // 自动滚动到选中项
  useEffect(() => {
    if (containerRef.current && selectedIndex >= 0) {
      const selectedElement = containerRef.current.children[selectedIndex] as HTMLElement
      if (selectedElement) {
        selectedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        })
      }
    }
  }, [selectedIndex])

  if (!visible || items.length === 0) {
    return null
  }

  return (
    <div
      ref={containerRef}
      className={cn(
        'fixed z-50 min-w-[200px] max-w-[300px] max-h-[200px]',
        'bg-popover border border-border rounded-md shadow-lg',
        'overflow-y-auto overflow-x-hidden',
        'animate-in fade-in-0 zoom-in-95'
      )}
      style={{
        left: position.x,
        top: position.y
      }}
    >
      {items.map((item, index) => (
        <CompletionItem
          key={item.id}
          item={item}
          isSelected={index === selectedIndex}
          onClick={() => onSelect(item)}
        />
      ))}
    </div>
  )
}

/**
 * 单个补全建议项组件
 */
interface CompletionItemProps {
  item: CompletionItem
  isSelected: boolean
  onClick: () => void
}

function CompletionItem({ item, isSelected, onClick }: CompletionItemProps) {
  const getTypeIcon = (type: CompletionItem['type']) => {
    switch (type) {
      case 'emoji':
        return item.icon || '😊'
      case 'mention':
        return '@'
      case 'hashtag':
        return '#'
      case 'wikilink':
        return '[[]]'
      default:
        return ''
    }
  }

  const getTypeColor = (type: CompletionItem['type']) => {
    switch (type) {
      case 'emoji':
        return 'text-yellow-500'
      case 'mention':
        return 'text-blue-500'
      case 'hashtag':
        return 'text-green-500'
      case 'wikilink':
        return 'text-purple-500'
      default:
        return 'text-muted-foreground'
    }
  }

  return (
    <div
      className={cn(
        'flex items-center gap-2 px-3 py-2 cursor-pointer',
        'hover:bg-accent hover:text-accent-foreground',
        'transition-colors duration-150',
        isSelected && 'bg-accent text-accent-foreground'
      )}
      onClick={onClick}
    >
      <span className={cn('text-sm font-mono', getTypeColor(item.type))}>
        {getTypeIcon(item.type)}
      </span>
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium truncate">{item.label}</div>
        {item.description && (
          <div className="text-xs text-muted-foreground truncate">{item.description}</div>
        )}
      </div>
    </div>
  )
}
