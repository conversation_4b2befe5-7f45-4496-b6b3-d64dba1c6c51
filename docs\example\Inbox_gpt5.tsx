// File: src/pages/inbox.tsx
// framework: SolidJS (switch to React if required)

import { createSignal, createMemo, createEffect, onMount, onCleanup, batch, For, Show, JSX } from "solid-js";
import { createStore, produce } from "solid-js/store";

/** ===========================

* Types
* =========================== */
  type InboxType = "note" | "task" | "idea" | "link" | "file";
  type Priority = "low" | "medium" | "high";

type Tag = {
id: string;
name: string;
color?: string; // e.g. "bg-red-500"
icon?: string; // emoji or icon name
};

type InboxItem = {
id: string;
type: InboxType;
title: string;
content?: string;
url?: string;
fileMeta?: { name: string; size: number };
priority: Priority;
tags: Tag[];
processed: boolean;
createdAt: string; // ISO
};

type Filters = {
types: Set<InboxType>;
priorities: Set<Priority>;
status: "all" | "processed" | "unprocessed";
keyword: string;
};

type ConvertToProjectPayload = {
projectName: string;
areaId?: string;
start?: string;
end?: string;
generateInitialTasks?: boolean;
};

type ConvertToAreaPayload = {
areaId: string;
as: "task" | "kpi" | "habit";
};

/** ===========================

* Utilities
* =========================== */
  const nowIso = () => new Date().toISOString();
  const uid = (p = "id") => `${p}_${Math.random().toString(36).slice(2, 9)}`;
  const clamp = (n: number, min: number, max: number) => Math.max(min, Math.min(max, n));
  const classNames = (...xs: Array<string | false | null | undefined>) => xs.filter(Boolean).join(" ");
  const formatSize = (n: number) => {
  if (n < 1024) return `${n}B`;
  if (n < 1024 * 1024) return `${(n / 1024).toFixed(1)}KB`;
  return `${(n / (1024 * 1024)).toFixed(1)}MB`;
  };
  function useDebounced<T>(getter: () => T, delay = 300) {
  const [val, setVal] = createSignal(getter());
  let t: number | undefined;
  createEffect(() => {
  const v = getter();
  window.clearTimeout(t);
  // @ts-ignore
  t = window.setTimeout(() => setVal(v), delay);
  });
  onCleanup(() => window.clearTimeout(t));
  return val;
  }

/** ===========================

* Mock Data (front-end only)
* =========================== */
  const MOCK_TAGS: Tag[] = [
  { id: "t1", name: "inspiration", color: "bg-indigo-500/20", icon: "💡" },
  { id: "t2", name: "work", color: "bg-emerald-500/20", icon: "💼" },
  { id: "t3", name: "read", color: "bg-amber-500/20", icon: "📚" },
  { id: "t4", name: "later", color: "bg-slate-500/20", icon: "⏳" },
  ];

function randomItem(i: number): InboxItem {
const types: InboxType[] = ["note", "task", "idea", "link", "file"];
const prios: Priority[] = ["low", "medium", "high"];
const type = types[i % types.length];
const priority = prios[i % prios.length];
const tag = MOCK_TAGS[i % MOCK_TAGS.length];
const titles: Record<InboxType, string[]> = {
note: ["Meeting notes", "Refactor plan", "UI thoughts", "Keyboard shortcuts"],
task: ["Fix Tauri menu", "Ship v0.3", "Write docs", "Design capture flow"],
idea: ["Inbox zero streak", "Smart tags", "AI triage", "Daily KPI"],
link: ["Read virtual scrolling guide", "Solid store deep dive", "SQLx patterns", "Tailwind v4 notes"],
file: ["Roadmap.xlsx", "KPI.csv", "Mockups.fig", "Export.md"],
};
const id = uid("inb");
const title = titles[type][i % 4];
const processed = i % 7 === 0;
const createdAt = new Date(Date.now() - i * 3600_000).toISOString();
const base: InboxItem = {
id,
type,
title,
content:
type === "note" || type === "idea"
? "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curated snippet..."
: undefined,
url: type === "link" ? "[https://example.com/article](https://example.com/article)" : undefined,
fileMeta: type === "file" ? { name: titles.file[i % 4], size: 20_000 + i * 500 } : undefined,
priority,
tags: [tag],
processed,
createdAt,
};
return base;
}

function createMockData(count = 120): InboxItem[] {
return Array.from({ length: count }, (_, i) => randomItem(i));
}

/** ===========================

* TODO Callbacks (stubbed)
* =========================== */
  function handleBulkMarkProcessed(ids: string[]): void {
  // TODO: integrate with backend
  console.info("handleBulkMarkProcessed", ids);
  }
  function handleBulkMoveToProject(ids: string[], projectId: string): void {
  // TODO
  console.info("handleBulkMoveToProject", { ids, projectId });
  }
  function handleBulkDelete(ids: string[]): void {
  // TODO
  console.info("handleBulkDelete", ids);
  }
  function handleConvertToProject(sourceIds: string[], payload: ConvertToProjectPayload): void {
  // TODO
  console.info("handleConvertToProject", { sourceIds, payload });
  }
  function handleConvertToArea(sourceIds: string[], payload: ConvertToAreaPayload): void {
  // TODO
  console.info("handleConvertToArea", { sourceIds, payload });
  }
  function handleMoveToVault(sourceIds: string[], folderPath: string): void {
  // TODO
  console.info("handleMoveToVault", { sourceIds, folderPath });
  }

/** ===========================

* Page Component
* =========================== */
  export default function Inbox() {
  // ---------- State ----------
  const [items, setItems] = createStore<InboxItem[]>([]);
  const [selected, setSelected] = createSignal<Set<string>>(new Set());
  const [focusedId, setFocusedId] = createSignal<string | null>(null);
  const [viewMode, setViewMode] = createSignal<"list" | "card">("list");
  const [filters, setFilters] = createSignal<Filters>({
  types: new Set<InboxType>(),
  priorities: new Set<Priority>(),
  status: "all",
  keyword: "",
  });
  const debouncedKeyword = useDebounced(() => filters().keyword, 300);

const [isLoading, setIsLoading] = createSignal(true);
const [isError, setIsError] = createSignal(false);

const [showCapture, setShowCapture] = createSignal(false);
const [showWizard, setShowWizard] = createSignal<null | { ids: string[]; target?: "project" | "area" | "vault" }>(null);
const [drawerOpen, setDrawerOpen] = createSignal(true);
const [detailId, setDetailId] = createSignal<string | null>(null);
const [rightPanelCollapsed, setRightPanelCollapsed] = createSignal(false);

// ---------- Load Mock ----------
const load = async () => {
setIsError(false);
setIsLoading(true);
await new Promise((r) => setTimeout(r, 520));
// Simulate occasional error
if (Math.random() < 0.05) {
setIsError(true);
setIsLoading(false);
return;
}
setItems(createMockData(140));
setIsLoading(false);
};
onMount(load);

// ---------- Derived ----------
const visibleItems = createMemo(() => {
if (isLoading() || isError()) return [] as InboxItem[];
const f = filters();
const kw = debouncedKeyword().toLowerCase().trim();
return items.filter((it) => {
if (f.status === "processed" && !it.processed) return false;
if (f.status === "unprocessed" && it.processed) return false;
if (f.types.size && !f.types.has(it.type)) return false;
if (f.priorities.size && !f.priorities.has(it.priority)) return false;
if (kw) {
const hay = `${it.title} ${it.content ?? ""} ${it.url ?? ""} ${it.tags.map((t) => t.name).join(" ")}`.toLowerCase();
if (!hay.includes(kw)) return false;
}
return true;
});
});

const stats = createMemo(() => {
const total = items.length;
const processed = items.filter((i) => i.processed).length;
const todayProcessed = items.filter((i) => i.processed && new Date(i.createdAt) > new Date(Date.now() - 86_400_000)).length;
const byType = ["note", "task", "idea", "link", "file"].map((t) => ({
type: t as InboxType,
count: items.filter((i) => i.type === t).length,
}));
return { total, processed, todayProcessed, byType };
});

// ---------- Selection Helpers ----------
const isSelected = (id: string) => selected().has(id);
const clearSelection = () => setSelected(new Set());
const setSelection = (ids: string[]) => setSelected(new Set(ids));
const toggleSelection = (id: string, multiKey = false) => {
setSelected((prev) => {
const s = new Set(prev);
if (multiKey) {
if (s.has(id)) s.delete(id);
else s.add(id);
} else {
if (s.size === 1 && s.has(id)) s.clear();
else {
s.clear();
s.add(id);
}
}
return s;
});
};
const allVisibleSelected = createMemo(() => {
const vs = visibleItems();
if (vs.length === 0) return false;
for (const it of vs) if (!selected().has(it.id)) return false;
return true;
});
const toggleSelectAllVisible = () => {
const vs = visibleItems();
if (!vs.length) return;
if (allVisibleSelected()) {
setSelected((prev) => {
const s = new Set(prev);
for (const it of vs) s.delete(it.id);
return s;
});
} else {
setSelected((prev) => {
const s = new Set(prev);
for (const it of vs) s.add(it.id);
return s;
});
}
};

// ---------- Keyboard Shortcuts ----------
const searchInputId = "global-search-input";
const onDocKey = (e: KeyboardEvent) => {
// Ignore if in input/textarea/select or contenteditable
const t = e.target as HTMLElement | null;
const tag = (t?.tagName || "").toLowerCase();
const isFormField = ["input", "textarea", "select", "button"].includes(tag) || t?.isContentEditable;
if (isFormField && e.key !== "Escape") return;


if (e.key === "/") {
  e.preventDefault();
  const el = document.getElementById(searchInputId) as HTMLInputElement | null;
  el?.focus();
  el?.select();
  return;
}
if (e.key === "a") {
  e.preventDefault();
  setShowCapture(true);
  return;
}
if (e.key === "e") {
  e.preventDefault();
  const ids = Array.from(selected());
  if (ids.length) setShowWizard({ ids });
  return;
}
if (e.key === "m") {
  e.preventDefault();
  const ids = Array.from(selected());
  if (ids.length) setShowWizard({ ids, target: "project" });
  return;
}
if (e.key === "Delete") {
  e.preventDefault();
  const ids = Array.from(selected());
  if (ids.length) handleBulkDelete(ids);
  return;
}
if (e.key === "Escape") {
  // close dialogs/drawer
  if (showCapture()) {
    setShowCapture(false);
    return;
  }
  if (showWizard()) {
    setShowWizard(null);
    return;
  }
  if (drawerOpen()) {
    setDrawerOpen(false);
    return;
  }
  clearSelection();
}
if (e.key === "Enter" && !showCapture() && !showWizard()) {
  const fid = focusedId();
  if (fid) {
    e.preventDefault();
    setDetailId(fid);
    setDrawerOpen(true);
    return;
  }
  const ids = Array.from(selected());
  if (ids.length) {
    e.preventDefault();
    setShowWizard({ ids });
  }
}


};
onMount(() => window.addEventListener("keydown", onDocKey));
onCleanup(() => window.removeEventListener("keydown", onDocKey));

// ---------- Actions ----------
const retry = () => load();

const onCreateCapture = (payload: Partial<InboxItem>) => {
const id = uid("inb");
const it: InboxItem = {
id,
type: (payload.type as InboxType) ?? "note",
title: payload.title || "Untitled",
content: payload.content,
url: payload.url,
fileMeta: payload.fileMeta,
priority: (payload.priority as Priority) ?? "medium",
tags: payload.tags || [],
processed: false,
createdAt: nowIso(),
};
setItems((prev) => [it, ...prev]);
};

// ---------- Rendering ----------
return ( <div
   class="min-w-[1024px] bg-white text-slate-900 dark:bg-slate-950 dark:text-slate-100"
   data-testid="page-inbox"
 > <header
     class="sticky top-0 z-30 border-b border-slate-200/70 dark:border-slate-800/80 bg-white/80 dark:bg-slate-950/80 backdrop-blur"
     role="banner"
     aria-label="Page header"
   > <div class="container mx-auto px-4 py-3 flex items-center gap-3"> <h1 class="text-lg font-semibold" aria-level={1} role="heading">
收件箱 </h1> <div class="ml-auto flex items-center gap-2">
<button
class="inline-flex items-center gap-2 rounded-xl border border-slate-300 dark:border-slate-700 px-3 py-1.5 text-sm hover:bg-slate-50 dark:hover:bg-slate-900 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500"
aria-label="New capture (a)"
data-testid="btn-capture"
onClick={() => setShowCapture(true)}
> <span aria-hidden="true">＋</span> 快速捕捉 </button>
<ViewToggle viewMode={viewMode()} onToggle={(m) => setViewMode(m)} /> <ThemeToggle /> </div> </div>


    <FilterBar
      filters={filters()}
      onFiltersChange={(updater) => setFilters(updater(filters()))}
      searchInputId={searchInputId}
    />
  </header>

  <main class="container mx-auto px-4" role="main">
    <div class="grid grid-cols-[1fr_360px] gap-4 xl:grid-cols-[1fr_420px]">
      <section
        class="relative min-h-[420px]"
        aria-label="Inbox list section"
      >
        <Show when={selected().size > 0}>
          <BulkActionsToolbar
            count={selected().size}
            onSelectAll={toggleSelectAllVisible}
            allSelected={allVisibleSelected()}
            onMarkProcessed={() => handleBulkMarkProcessed(Array.from(selected()))}
            onMoveProject={() => setShowWizard({ ids: Array.from(selected()), target: "project" })}
            onConvert={() => setShowWizard({ ids: Array.from(selected()) })}
            onDelete={() => handleBulkDelete(Array.from(selected()))}
          />
        </Show>

        <Show when={!isLoading() && !isError() && visibleItems().length > 0} fallback={
          <ListFallback
            isLoading={isLoading()}
            isError={isError()}
            hasFilters={
              filters().keyword.trim().length > 0 ||
              filters().types.size > 0 ||
              filters().priorities.size > 0 ||
              filters().status !== "all"
            }
            onRetry={retry}
          />
        }>
          <div class="flex items-center justify-between py-2">
            <p class="text-sm text-slate-600 dark:text-slate-400">
              共 <strong class="font-semibold">{visibleItems().length}</strong> 条结果
            </p>
            <div class="flex items-center gap-2">
              <button
                class="rounded-lg px-2.5 py-1 text-xs border border-slate-300 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-900 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500"
                onClick={toggleSelectAllVisible}
                aria-pressed={allVisibleSelected()}
                aria-label="Toggle select all visible"
                data-testid="btn-select-all"
              >
                {allVisibleSelected() ? "取消全选" : "选择全部"}
              </button>
            </div>
          </div>

          <Show when={viewMode() === "list"} fallback={
            <CardListView
              items={visibleItems()}
              isSelected={isSelected}
              onToggle={(id, multi) => toggleSelection(id, multi)}
              onFocus={(id) => setFocusedId(id)}
              onOpenDetail={(id) => {
                setDetailId(id);
                setDrawerOpen(true);
              }}
            />
          }>
            <VirtualizedList
              items={visibleItems()}
              rowHeight={56}
              isSelected={isSelected}
              focusedId={focusedId()}
              onFocusRow={(id) => setFocusedId(id)}
              onToggleRow={(id, multi) => toggleSelection(id, multi)}
              onOpenDetail={(id) => {
                setDetailId(id);
                setDrawerOpen(true);
              }}
            />
          </Show>
        </Show>
      </section>

      {/* Right Side: Isolated Tasks / Detail Drawer */}
      <aside
        class={classNames(
          "relative",
          rightPanelCollapsed() ? "w-6" : "w-full"
        )}
        aria-label="Right info panel"
      >
        <button
          class="absolute -left-3 top-2 z-10 rounded-md border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-900 px-1.5 py-1 text-xs hover:bg-slate-50 dark:hover:bg-slate-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500"
          aria-label={rightPanelCollapsed() ? "展开右侧面板" : "折叠右侧面板"}
          data-testid="btn-toggle-rightpanel"
          onClick={() => setRightPanelCollapsed((v) => !v)}
        >
          {rightPanelCollapsed() ? "⮞" : "⮜"}
        </button>

        <Show when={!rightPanelCollapsed()}>
          <div class="h-[calc(100vh-210px)] sticky top-[110px] overflow-auto rounded-2xl border border-slate-200 dark:border-slate-800 p-3">
            <h2 class="text-sm font-semibold mb-2">孤立任务区</h2>
            <IsolatedTasksPanel
              items={items.filter((i) => i.type === "task" && !i.processed)}
              onBulkAssign={() => {
                // TODO: assign
                console.info("bulk assign");
              }}
              onPriorityChange={(id, p) => {
                setItems(
                  produce((list) => {
                    const it = list.find((x) => x.id === id);
                    if (it) it.priority = p;
                  })
                );
              }}
            />
            <div class="mt-4 border-t border-slate-200 dark:border-slate-800 pt-3">
              <h3 class="text-sm font-semibold mb-2">详情</h3>
              <DetailDrawer
                open={drawerOpen()}
                item={items.find((i) => i.id === detailId()) || null}
                onClose={() => setDrawerOpen(false)}
              />
            </div>
          </div>
        </Show>
      </aside>
    </div>

    {/* Bottom progress panel */}
    <footer class="mt-4 mb-6" role="contentinfo" aria-label="Progress and stats">
      <ProgressPanel stats={stats()} />
    </footer>
  </main>

  {/* Dialogs */}
  <Show when={showCapture()}>
    <QuickCaptureDialog
      onCancel={() => setShowCapture(false)}
      onCreate={(payload) => {
        onCreateCapture(payload);
        setShowCapture(false);
      }}
    />
  </Show>

  <Show when={!!showWizard()}>
    <ConvertWizard
      ids={showWizard()!.ids}
      presetTarget={showWizard()!.target}
      onCancel={() => setShowWizard(null)}
      onConvertProject={(payload) => {
        handleConvertToProject(showWizard()!.ids, payload);
        setShowWizard(null);
      }}
      onConvertArea={(payload) => {
        handleConvertToArea(showWizard()!.ids, payload);
        setShowWizard(null);
      }}
      onMoveVault={(path) => {
        handleMoveToVault(showWizard()!.ids, path);
        setShowWizard(null);
      }}
    />
  </Show>
</div>


);
}

/** ===========================

* UI Subcomponents
* =========================== */

function ThemeToggle() {
const [dark, setDark] = createSignal(false);
onMount(() => {
setDark(window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches);
});
createEffect(() => {
document.documentElement.classList.toggle("dark", dark());
});
return (
<button
class="rounded-xl border border-slate-300 dark:border-slate-700 px-2.5 py-1.5 text-sm hover:bg-slate-50 dark:hover:bg-slate-900 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500"
aria-pressed={dark()}
aria-label="Theme"
data-testid="btn-theme"
onClick={() => setDark((d) => !d)}
title="切换主题"
>
{dark() ? "🌙" : "🌞"} </button>
);
}

function ViewToggle(props: { viewMode: "list" | "card"; onToggle: (m: "list" | "card") => void }) {
return ( <div class="inline-flex rounded-xl border border-slate-300 dark:border-slate-700 overflow-hidden" role="tablist" aria-label="View mode">
<button
role="tab"
aria-selected={props.viewMode === "list"}
class={classNames(
"px-3 py-1.5 text-sm hover:bg-slate-50 dark:hover:bg-slate-900 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500",
props.viewMode === "list" && "bg-slate-100 dark:bg-slate-800"
)}
data-testid="view-list"
onClick={() => props.onToggle("list")}
>
列表 </button>
<button
role="tab"
aria-selected={props.viewMode === "card"}
class={classNames(
"px-3 py-1.5 text-sm hover:bg-slate-50 dark:hover:bg-slate-900 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500",
props.viewMode === "card" && "bg-slate-100 dark:bg-slate-800"
)}
data-testid="view-card"
onClick={() => props.onToggle("card")}
>
卡片 </button> </div>
);
}

function FilterBar(props: {
filters: Filters;
onFiltersChange: (updater: (prev: Filters) => Filters) => void;
searchInputId: string;
}) {
const setKeyword = (s: string) =>
props.onFiltersChange((f) => ({ ...f, keyword: s }));

const toggleType = (t: InboxType) =>
props.onFiltersChange((f) => {
const s = new Set(f.types);
if (s.has(t)) s.delete(t);
else s.add(t);
return { ...f, types: s };
});

const togglePriority = (p: Priority) =>
props.onFiltersChange((f) => {
const s = new Set(f.priorities);
if (s.has(p)) s.delete(p);
else s.add(p);
return { ...f, priorities: s };
});

const setStatus = (s: Filters["status"]) =>
props.onFiltersChange((f) => ({ ...f, status: s }));

return ( <nav
   class="container mx-auto px-4 py-2 grid grid-cols-1 md:grid-cols-[1fr_auto_auto] gap-2 items-center"
   role="navigation"
   aria-label="Filters"
 > <GlobalSearchBar id={props.searchInputId} value={props.filters.keyword} onChange={setKeyword} />


  <div class="flex flex-wrap items-center gap-1.5 md:justify-end">
    {(["note", "task", "idea", "link", "file"] as InboxType[]).map((t) => (
      <button
        type="button"
        class={classNames(
          "rounded-lg border px-2.5 py-1 text-xs hover:bg-slate-50 dark:hover:bg-slate-900 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500",
          props.filters.types.has(t)
            ? "border-blue-500 text-blue-600 dark:text-blue-400"
            : "border-slate-300 dark:border-slate-700"
        )}
        aria-pressed={props.filters.types.has(t)}
        onClick={() => toggleType(t)}
        data-testid={`filter-type-${t}`}
      >
        {iconForType(t)} {labelForType(t)}
      </button>
    ))}
  </div>

  <div class="flex flex-wrap items-center gap-1.5 md:justify-end">
    {(["low", "medium", "high"] as Priority[]).map((p) => (
      <button
        type="button"
        class={classNames(
          "rounded-lg border px-2.5 py-1 text-xs hover:bg-slate-50 dark:hover:bg-slate-900 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500",
          props.filters.priorities.has(p)
            ? "border-emerald-500 text-emerald-600 dark:text-emerald-400"
            : "border-slate-300 dark:border-slate-700"
        )}
        aria-pressed={props.filters.priorities.has(p)}
        onClick={() => togglePriority(p)}
        data-testid={`filter-priority-${p}`}
      >
        {priorityBadge(p)}
      </button>
    ))}

    <div class="ml-1 inline-flex rounded-lg border border-slate-300 dark:border-slate-700 overflow-hidden" role="tablist" aria-label="Status">
      {(["all", "processed", "unprocessed"] as Filters["status"][]).map((s) => (
        <button
          role="tab"
          aria-selected={props.filters.status === s}
          class={classNames(
            "px-2.5 py-1 text-xs hover:bg-slate-50 dark:hover:bg-slate-900 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500",
            props.filters.status === s && "bg-slate-100 dark:bg-slate-800"
          )}
          onClick={() => setStatus(s)}
          data-testid={`filter-status-${s}`}
        >
          {statusLabel(s)}
        </button>
      ))}
    </div>
  </div>
</nav>


);
}

function GlobalSearchBar(props: { id: string; value: string; onChange: (s: string) => void }) {
return ( <label class="relative block" aria-label="Search"> <span class="sr-only">Search</span>
<input
id={props.id}
type="search"
value={props.value}
onInput={(e) => props.onChange((e.currentTarget as HTMLInputElement).value)}
placeholder="搜索（/ 聚焦）"
class="w-full rounded-xl border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-900 px-3 py-2 text-sm outline-none focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500"
role="searchbox"
data-testid="search-input"
/> <kbd class="absolute right-2 top-1/2 -translate-y-1/2 text-[10px] text-slate-500 dark:text-slate-400">/</kbd> </label>
);
}

function BulkActionsToolbar(props: {
count: number;
allSelected: boolean;
onSelectAll: () => void;
onMarkProcessed: () => void;
onMoveProject: () => void;
onConvert: () => void;
onDelete: () => void;
}) {
return ( <div
   class="sticky top-[88px] z-20 mb-2 flex items-center gap-2 rounded-xl border border-slate-300 dark:border-slate-700 bg-white/95 dark:bg-slate-900/95 px-3 py-2 backdrop-blur"
   role="toolbar"
   aria-label="Bulk actions"
   data-testid="bulk-toolbar"
 > <span class="text-sm">已选择 <strong class="font-semibold">{props.count}</strong> 项</span> <button
     class="rounded-md border border-slate-300 dark:border-slate-700 px-2 py-1 text-xs hover:bg-slate-50 dark:hover:bg-slate-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500"
     onClick={props.onSelectAll}
     aria-pressed={props.allSelected}
   >
{props.allSelected ? "取消全选" : "选择全部"} </button> <span class="mx-1 h-4 w-px bg-slate-300 dark:bg-slate-700" aria-hidden="true" /> <button class="rounded-md border px-2 py-1 text-xs hover:bg-slate-50 dark:hover:bg-slate-800" onClick={props.onMarkProcessed} data-testid="bulk-mark-processed">标记已处理</button> <button class="rounded-md border px-2 py-1 text-xs hover:bg-slate-50 dark:hover:bg-slate-800" onClick={props.onMoveProject} data-testid="bulk-move-project">移动到项目</button> <button class="rounded-md border px-2 py-1 text-xs hover:bg-slate-50 dark:hover:bg-slate-800" onClick={props.onConvert} data-testid="bulk-convert">转化向导</button> <button class="rounded-md border px-2 py-1 text-xs hover:bg-red-50 dark:hover:bg-red-900/20 border-red-300 dark:border-red-700 text-red-600 dark:text-red-400" onClick={props.onDelete} data-testid="bulk-delete">删除</button> </div>
);
}

function ListFallback(props: { isLoading: boolean; isError: boolean; hasFilters: boolean; onRetry: () => void }) {
if (props.isLoading) {
return ( <div class="space-y-2 py-4" role="status" aria-live="polite" data-testid="loading"> <SkeletonRow /><SkeletonRow /><SkeletonRow /><SkeletonRow /><SkeletonRow /> </div>
);
}
if (props.isError) {
return ( <div class="rounded-xl border border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-900/20 p-4" role="alert" data-testid="error"> <p class="text-sm font-semibold text-red-700 dark:text-red-300">加载失败</p> <p class="text-sm text-red-700/80 dark:text-red-300/80 mt-1">网络或数据源错误。您可以重试。</p> <button
       class="mt-2 rounded-md border border-red-300 dark:border-red-700 px-2 py-1 text-xs hover:bg-red-100 dark:hover:bg-red-900/40 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500"
       onClick={props.onRetry}
       data-testid="btn-retry"
     >
重试 </button> </div>
);
}
return ( <div class="rounded-xl border border-slate-300 dark:border-slate-700 p-6" role="status" aria-live="polite" data-testid="empty"> <p class="text-sm font-semibold">暂无结果</p> <p class="text-sm text-slate-600 dark:text-slate-400 mt-1">
{props.hasFilters ? "调整筛选或清除关键字以查看更多内容。" : "点击“快速捕捉”创建您的第一条内容。"} </p> </div>
);
}

function SkeletonRow() {
return ( <div class="h-[56px] animate-pulse rounded-lg border border-slate-200 dark:border-slate-800 bg-slate-100 dark:bg-slate-900" />
);
}

function CardListView(props: {
items: InboxItem[];
isSelected: (id: string) => boolean;
onToggle: (id: string, multi: boolean) => void;
onFocus: (id: string) => void;
onOpenDetail: (id: string) => void;
}) {
return ( <div class="grid grid-cols-1 md:grid-cols-2 2xl:grid-cols-3 gap-2 py-2" role="list" aria-label="Card items"> <For each={props.items}>
{(it) => (
<article
role="listitem"
tabindex={0}
aria-selected={props.isSelected(it.id)}
class={classNames(
"rounded-xl border px-3 py-2 outline-none focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500",
props.isSelected(it.id)
? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
: "border-slate-300 dark:border-slate-700"
)}
onFocus={() => props.onFocus(it.id)}
onClick={(e) => props.onToggle(it.id, e.ctrlKey || e.metaKey || e.shiftKey)}
onDblClick={() => props.onOpenDetail(it.id)}
data-testid={`card-${it.id}`}
> <div class="flex items-center justify-between"> <div class="text-sm font-semibold flex items-center gap-2"> <span aria-hidden="true">{iconForType(it.type)}</span>
{it.title} </div> <PriorityPill priority={it.priority} /> </div> <p class="mt-1 text-sm text-slate-600 dark:text-slate-400 line-clamp-2">
{it.content ?? it.url ?? (it.fileMeta ? `${it.fileMeta.name} · ${formatSize(it.fileMeta.size)}` : "")} </p> <div class="mt-2 flex flex-wrap gap-1"> <For each={it.tags}>
{(t) => <span class={classNames("text-[11px] px-1.5 py-0.5 rounded-md", t.color ?? "bg-slate-200/50 dark:bg-slate-700/50")}>{t.icon ?? "#"} {t.name}</span>} </For> </div> </article>
)} </For> </div>
);
}

/** ======= Virtualized List (fixed row height) ======= */
function VirtualizedList(props: {
items: InboxItem[];
rowHeight: number;
isSelected: (id: string) => boolean;
focusedId: string | null;
onFocusRow: (id: string) => void;
onToggleRow: (id: string, multi: boolean) => void;
onOpenDetail: (id: string) => void;
}) {
let scroller!: HTMLDivElement;
const [height, setHeight] = createSignal(480);
const [scrollTop, setScrollTop] = createSignal(0);

onMount(() => {
const ro = new ResizeObserver(() => {
setHeight(scroller.clientHeight);
});
ro.observe(scroller);
setHeight(scroller.clientHeight);
const onScroll = () => setScrollTop(scroller.scrollTop);
scroller.addEventListener("scroll", onScroll);
onCleanup(() => {
ro.disconnect();
scroller.removeEventListener("scroll", onScroll);
});
});

const total = createMemo(() => props.items.length);
const visibleCount = createMemo(() => Math.ceil(height() / props.rowHeight) + 6);
const startIndex = createMemo(() => clamp(Math.floor(scrollTop() / props.rowHeight) - 3, 0, Math.max(0, total() - 1)));
const endIndex = createMemo(() => clamp(startIndex() + visibleCount(), 0, total()));

const onRowKeyDown = (e: KeyboardEvent, idx: number, id: string) => {
const key = e.key;
if (key === "ArrowDown" || key === "ArrowUp") {
e.preventDefault();
const delta = key === "ArrowDown" ? 1 : -1;
const nextIdx = clamp(idx + delta, 0, total() - 1);
const nextId = props.items[nextIdx].id;
props.onFocusRow(nextId);
const rowTop = nextIdx * props.rowHeight;
const rowBottom = rowTop + props.rowHeight;
const viewTop = scroller.scrollTop;
const viewBottom = viewTop + height();
if (rowTop < viewTop) scroller.scrollTop = rowTop;
else if (rowBottom > viewBottom) scroller.scrollTop = rowBottom - height();
const el = document.querySelector<HTMLElement>(`[data-row-id="${nextId}"]`);
el?.focus();
} else if (key === " ") {
e.preventDefault();
props.onToggleRow(id, true);
} else if (key === "Enter") {
e.preventDefault();
props.onOpenDetail(id);
} else if (key === "Escape") {
(e.target as HTMLElement).blur();
}
};

return ( <div
   ref={scroller!}
   class="h-[calc(100vh-280px)] overflow-auto rounded-2xl border border-slate-200 dark:border-slate-800"
   role="grid"
   aria-rowcount={total()}
   aria-colcount={5}
   data-testid="virtual-list"
 >
<div style={{ height: `${total() * props.rowHeight}px`, position: "relative" }}>
<div style={{ position: "absolute", top: `${startIndex() * props.rowHeight}px`, left: 0, right: 0 }}>
<For each={props.items.slice(startIndex(), endIndex())}>
{(it, i) => {
const actualIdx = () => startIndex() + i();
const selected = () => props.isSelected(it.id);
const focused = () => props.focusedId === it.id;
return (
<div
data-row-id={it.id}
role="row"
aria-selected={selected()}
tabindex={0}
class={classNames(
"grid grid-cols-[32px_1fr_120px_160px_120px] items-center gap-2 px-3",
"h-[56px] border-b border-slate-200 dark:border-slate-800 outline-none",
selected()
? "bg-blue-50 dark:bg-blue-900/20"
: focused()
? "bg-slate-50 dark:bg-slate-900/40"
: "bg-white dark:bg-slate-950"
)}
onFocus={() => props.onFocusRow(it.id)}
onClick={(e) => props.onToggleRow(it.id, e.ctrlKey || e.metaKey || e.shiftKey)}
onDblClick={() => props.onOpenDetail(it.id)}
onKeyDown={(e) => onRowKeyDown(e as unknown as KeyboardEvent, actualIdx(), it.id)}
style={{ height: `${props.rowHeight}px` }}
data-testid={`row-${it.id}`}
> <div role="gridcell" class="text-center">{selected() ? "✔" : ""}</div> <div role="gridcell" class="truncate text-sm"> <span class="mr-2" aria-hidden="true">{iconForType(it.type)}</span> <span class="font-medium">{it.title}</span> <span class="ml-2 text-slate-500 dark:text-slate-400">{it.content ? "· " + it.content.slice(0, 50) : it.url || (it.fileMeta ? `· ${it.fileMeta.name}` : "")}</span> </div> <div role="gridcell" class="text-xs"> <div class="inline-flex items-center gap-1"> <For each={it.tags}>
{(t) => <span class={classNames("px-1.5 py-0.5 rounded-md", t.color ?? "bg-slate-200/50 dark:bg-slate-700/50")}>{t.icon ?? "#"} {t.name}</span>} </For> </div> </div> <div role="gridcell" class="text-xs"> <PriorityPill priority={it.priority} /> </div> <div role="gridcell" class="text-xs text-slate-500 dark:text-slate-400">
{new Date(it.createdAt).toLocaleString()} </div> </div>
);
}} </For> </div> </div> </div>
);
}

function PriorityPill(props: { priority: Priority }) {
const color =
props.priority === "high" ? "bg-red-500/20 text-red-700 dark:text-red-300" :
props.priority === "medium" ? "bg-amber-500/20 text-amber-700 dark:text-amber-300" :
"bg-emerald-500/20 text-emerald-700 dark:text-emerald-300";
const label = priorityLabel(props.priority);
return <span class={classNames("px-2 py-0.5 rounded-md text-xs font-medium", color)} aria-label={`Priority ${label}`}>{label}</span>;
}

function IsolatedTasksPanel(props: {
items: InboxItem[];
onBulkAssign: () => void;
onPriorityChange: (id: string, p: Priority) => void;
}) {
return ( <div class="space-y-2" aria-label="孤立任务列表">
<For each={props.items.slice(0, 10)}>
{(it) => ( <div class="rounded-lg border border-slate-300 dark:border-slate-700 p-2"> <div class="flex items-center justify-between gap-2"> <div class="text-sm font-medium truncate">{it.title}</div> <div class="flex items-center gap-1">
{(["low", "medium", "high"] as Priority[]).map((p) => (
<button
class={classNames(
"rounded-md border px-1.5 py-0.5 text-[11px] hover:bg-slate-50 dark:hover:bg-slate-800",
it.priority === p ? "border-blue-500 text-blue-600 dark:text-blue-400" : "border-slate-300 dark:border-slate-700"
)}
aria-pressed={it.priority === p}
onClick={() => props.onPriorityChange(it.id, p)}
>
{priorityLabel(p)} </button>
))} </div> </div> <p class="text-xs text-slate-500 dark:text-slate-400 line-clamp-2 mt-1">
{it.content ?? "无描述"} </p> </div>
)} </For> <button
     class="w-full rounded-lg border border-slate-300 dark:border-slate-700 px-2 py-1 text-xs hover:bg-slate-50 dark:hover:bg-slate-800"
     onClick={props.onBulkAssign}
     data-testid="isolated-bulk-assign"
   >
批量分配 </button> </div>
);
}

function DetailDrawer(props: { open: boolean; item: InboxItem | null; onClose: () => void }) {
return (
<div
class={classNames(
"transition-all duration-200",
props.open ? "opacity-100" : "opacity-60"
)}
aria-expanded={props.open}
aria-label="Detail drawer"
>
<Show when={props.item} fallback={<p class="text-sm text-slate-500 dark:text-slate-400">选择一项查看详情（Enter 打开）</p>}>
{(it) => ( <div class="space-y-2"> <div class="flex items-center justify-between"> <div class="text-sm font-semibold flex items-center gap-2"> <span>{iconForType(it().type)}</span> {it().title} </div> <button
             class="rounded-md border border-slate-300 dark:border-slate-700 px-2 py-1 text-xs hover:bg-slate-50 dark:hover:bg-slate-800"
             onClick={props.onClose}
             aria-label="关闭详情（Esc）"
             data-testid="btn-close-drawer"
           >
关闭 </button> </div> <div class="text-sm text-slate-600 dark:text-slate-300 whitespace-pre-wrap">
{it().content ?? (it().url ? `链接：${it().url}` : it().fileMeta ? `${it().fileMeta.name} · ${formatSize(it().fileMeta.size)}` : "无内容")} </div> <div class="text-xs text-slate-500 dark:text-slate-400">
优先级：{priorityLabel(it().priority)} · 创建于 {new Date(it().createdAt).toLocaleString()} </div> <div class="flex flex-wrap gap-1"> <For each={it().tags}>{(t) => <span class={classNames("text-[11px] px-1.5 py-0.5 rounded-md", t.color ?? "bg-slate-200/50 dark:bg-slate-700/50")}>{t.icon ?? "#"} {t.name}</span>}</For> </div> </div>
)} </Show> </div>
);
}

function ProgressPanel(props: {
stats: { total: number; processed: number; todayProcessed: number; byType: { type: InboxType; count: number }[] };
}) {
const pct = props.stats.total ? Math.round((props.stats.processed / props.stats.total) * 100) : 0;
return ( <div class="grid grid-cols-1 md:grid-cols-3 gap-3"> <StatsCard title="总收件" value={props.stats.total} hint="聚合所有未结构化输入" /> <StatsCard title="今日处理" value={props.stats.todayProcessed} hint="过去24小时完成量" /> <div class="rounded-2xl border border-slate-200 dark:border-slate-800 p-4"> <div class="flex items-center justify-between"> <h4 class="text-sm font-semibold">处理进度</h4> <span class="text-xs text-slate-500 dark:text-slate-400">{pct}%</span> </div> <div class="mt-2 h-2 w-full rounded-full bg-slate-200 dark:bg-slate-800">
<div class="h-2 rounded-full bg-emerald-500" style={{ width: `${pct}%` }} /> </div> <div class="mt-3 grid grid-cols-5 gap-1 text-center"> <For each={props.stats.byType}>
{(t) => ( <div class="rounded-lg border border-slate-200 dark:border-slate-800 py-1"> <div class="text-xs">{iconForType(t.type)}</div> <div class="text-xs">{t.count}</div> </div>
)} </For> </div> </div> </div>
);
}

function StatsCard(props: { title: string; value: number | string; hint?: string }) {
return ( <div class="rounded-2xl border border-slate-200 dark:border-slate-800 p-4"> <div class="text-sm font-semibold">{props.title}</div> <div class="mt-1 text-2xl font-semibold">{props.value}</div> <div class="text-xs text-slate-500 dark:text-slate-400">{props.hint}</div> </div>
);
}

/** ======= Quick Capture Dialog ======= */
function QuickCaptureDialog(props: {
onCancel: () => void;
onCreate: (payload: Partial<InboxItem>) => void;
}) {
const [title, setTitle] = createSignal("");
const [type, setType] = createSignal<InboxType>("note");
const [priority, setPriority] = createSignal<Priority>("medium");
const [tags, setTags] = createSignal<Tag[]>([]);
const [content, setContent] = createSignal("");

const submit = () => {
if (!title().trim()) return;
props.onCreate({ title: title().trim(), type: type(), priority: priority(), tags: tags(), content: content().trim() || undefined });
};

const onKey = (e: KeyboardEvent) => {
if (e.key === "Escape") props.onCancel();
if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) submit();
};

onMount(() => window.addEventListener("keydown", onKey));
onCleanup(() => window.removeEventListener("keydown", onKey));

return (
<div
class="fixed inset-0 z-50 grid place-items-center bg-black/40"
role="dialog"
aria-modal="true"
aria-label="Quick capture"
data-testid="dialog-capture"
onClick={(e) => {
if (e.target === e.currentTarget) props.onCancel();
}}
> <div class="w-[720px] max-w-[95vw] rounded-2xl border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-950 p-4 shadow-xl"> <div class="flex items-center justify-between mb-2"> <h3 class="text-base font-semibold">快速捕捉</h3> <button
         class="rounded-md border border-slate-300 dark:border-slate-700 px-2 py-1 text-xs hover:bg-slate-50 dark:hover:bg-slate-800"
         aria-label="关闭"
         onClick={props.onCancel}
       >
关闭 </button> </div> <div class="grid grid-cols-1 gap-2">
<input
class="w-full rounded-lg border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-900 px-3 py-2 text-sm outline-none focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500"
placeholder="标题"
value={title()}
onInput={(e) => setTitle((e.currentTarget as HTMLInputElement).value)}
data-testid="capture-title"
/>
<textarea
class="min-h-[80px] w-full rounded-lg border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-900 px-3 py-2 text-sm outline-none focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500"
placeholder="内容（可选）"
value={content()}
onInput={(e) => setContent((e.currentTarget as HTMLTextAreaElement).value)}
data-testid="capture-content"
/> <div class="flex gap-2">
<select
class="rounded-lg border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-900 px-2 py-1 text-sm"
value={type()}
onChange={(e) => setType((e.currentTarget as HTMLSelectElement).value as InboxType)}
aria-label="类型"
> <option value="note">笔记</option> <option value="task">任务</option> <option value="idea">想法</option> <option value="link">链接</option> <option value="file">文件</option> </select>


        <select
          class="rounded-lg border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-900 px-2 py-1 text-sm"
          value={priority()}
          onChange={(e) => setPriority((e.currentTarget as HTMLSelectElement).value as Priority)}
          aria-label="优先级"
        >
          <option value="low">低</option>
          <option value="medium">中</option>
          <option value="high">高</option>
        </select>

        <TagInput value={tags()} onChange={setTags} />
      </div>

      <div class="mt-2 flex justify-end gap-2">
        <button class="rounded-md border border-slate-300 dark:border-slate-700 px-3 py-1.5 text-sm hover:bg-slate-50 dark:hover:bg-slate-800" onClick={props.onCancel}>
          取消
        </button>
        <button
          class="rounded-md border border-blue-500 bg-blue-600 text-white px-3 py-1.5 text-sm hover:bg-blue-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500 disabled:opacity-50"
          disabled={!title().trim()}
          onClick={submit}
          data-testid="capture-submit"
          title="Ctrl/⌘ + Enter"
        >
          创建
        </button>
      </div>
    </div>
  </div>
</div>


);
}

function TagInput(props: { value: Tag[]; onChange: (ts: Tag[]) => void }) {
const [input, setInput] = createSignal("");
const suggestions = createMemo(() => {
const kw = input().toLowerCase();
if (!kw) return MOCK_TAGS;
return MOCK_TAGS.filter((t) => t.name.toLowerCase().includes(kw));
});
const add = (t: Tag) => {
if (!props.value.find((x) => x.id === t.id)) props.onChange([...props.value, t]);
setInput("");
};
const create = () => {
const n = input().trim();
if (!n) return;
const t: Tag = { id: uid("tag"), name: n, color: "bg-slate-300/40" };
props.onChange([...props.value, t]);
setInput("");
};
const remove = (id: string) => props.onChange(props.value.filter((x) => x.id !== id));
return ( <div class="flex-1 rounded-lg border border-slate-300 dark:border-slate-700 px-2 py-1"> <div class="flex flex-wrap gap-1"> <For each={props.value}>
{(t) => (
<span class={classNames("text-[11px] px-1.5 py-0.5 rounded-md", t.color ?? "bg-slate-200/50 dark:bg-slate-700/50")}>
{t.icon ?? "#"} {t.name}
<button class="ml-1 text-slate-500 hover:underline" aria-label="移除标签" onClick={() => remove(t.id)}>×</button> </span>
)} </For>
<input
class="min-w-[120px] flex-1 bg-transparent text-sm outline-none"
placeholder="# 添加标签"
value={input()}
onInput={(e) => setInput((e.currentTarget as HTMLInputElement).value)}
/> </div> <div class="mt-1 flex flex-wrap gap-1">
<For each={suggestions().slice(0, 6)}>
{(s) => (
<button
class="text-[11px] rounded-md border border-slate-300 dark:border-slate-700 px-1.5 py-0.5 hover:bg-slate-50 dark:hover:bg-slate-800"
onClick={() => add(s)}
>
{s.icon ?? "#"} {s.name} </button>
)} </For> <button
       class="text-[11px] rounded-md border border-slate-300 dark:border-slate-700 px-1.5 py-0.5 hover:bg-slate-50 dark:hover:bg-slate-800"
       onClick={create}
     >
创建 “{input() || "…"}” </button> </div> </div>
);
}

/** ======= Convert Wizard ======= */
function ConvertWizard(props: {
ids: string[];
presetTarget?: "project" | "area" | "vault";
onCancel: () => void;
onConvertProject: (payload: ConvertToProjectPayload) => void;
onConvertArea: (payload: ConvertToAreaPayload) => void;
onMoveVault: (path: string) => void;
}) {
const [step, setStep] = createSignal(1);
const [target, setTarget] = createSignal<"project" | "area" | "vault">(props.presetTarget ?? "project");

// Step 2 forms
const [projectName, setProjectName] = createSignal("");
const [areaId, setAreaId] = createSignal("");
const [asType, setAsType] = createSignal<"task" | "kpi" | "habit">("task");
const [genTasks, setGenTasks] = createSignal(true);
const [start, setStart] = createSignal<string>("");
const [end, setEnd] = createSignal<string>("");
const [vaultPath, setVaultPath] = createSignal<string>("Resources/InboxExports");

const canNext = createMemo(() => {
if (step() === 1) return true;
if (target() === "project") return projectName().trim().length > 0;
if (target() === "area") return areaId().trim().length > 0;
if (target() === "vault") return vaultPath().trim().length > 0;
return false;
});

const confirm = () => {
if (target() === "project") {
props.onConvertProject({
projectName: projectName().trim(),
areaId: areaId() || undefined,
start: start() || undefined,
end: end() || undefined,
generateInitialTasks: genTasks(),
});
} else if (target() === "area") {
props.onConvertArea({
areaId: areaId().trim(),
as: asType(),
});
} else {
props.onMoveVault(vaultPath().trim());
}
};

return (
<div
class="fixed inset-0 z-50 grid place-items-center bg-black/40"
role="dialog"
aria-modal="true"
aria-label="Convert wizard"
data-testid="dialog-wizard"
onClick={(e) => {
if (e.target === e.currentTarget) props.onCancel();
}}
> <div class="w-[840px] max-w-[95vw] rounded-2xl border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-950 p-4 shadow-xl"> <div class="flex items-center justify-between mb-3"> <h3 class="text-base font-semibold">转化向导</h3> <button class="rounded-md border border-slate-300 dark:border-slate-700 px-2 py-1 text-xs hover:bg-slate-50 dark:hover:bg-slate-800" onClick={props.onCancel}>关闭</button> </div>


    {/* Stepper */}
    <div class="flex items-center gap-2 text-xs mb-3">
      <StepDot active={step() === 1} label="目标选择" />
      <span class="text-slate-400">—</span>
      <StepDot active={step() === 2} label="字段映射" />
      <span class="text-slate-400">—</span>
      <StepDot active={step() === 3} label="预览确认" />
    </div>

    <Show when={step() === 1}>
      <div class="grid grid-cols-3 gap-2">
        {(["project", "area", "vault"] as const).map((t) => (
          <button
            class={classNames(
              "rounded-xl border px-3 py-3 text-left hover:bg-slate-50 dark:hover:bg-slate-900 focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500",
              target() === t ? "border-blue-500" : "border-slate-300 dark:border-slate-700"
            )}
            aria-pressed={target() === t}
            onClick={() => setTarget(t)}
          >
            <div class="text-sm font-semibold mb-1">{targetLabel(t)}</div>
            <div class="text-xs text-slate-600 dark:text-slate-400">{targetDesc(t)}</div>
          </button>
        ))}
      </div>
    </Show>

    <Show when={step() === 2}>
      <div class="space-y-2">
        <Show when={target() === "project"}>
          <div class="grid grid-cols-2 gap-2">
            <label class="block text-sm">项目名称
              <input class="mt-1 w-full rounded-lg border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-900 px-2 py-1.5 text-sm"
                value={projectName()}
                onInput={(e) => setProjectName((e.currentTarget as HTMLInputElement).value)} />
            </label>
            <label class="block text-sm">归属领域（可选）
              <input class="mt-1 w-full rounded-lg border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-900 px-2 py-1.5 text-sm"
                placeholder="Area ID"
                value={areaId()}
                onInput={(e) => setAreaId((e.currentTarget as HTMLInputElement).value)} />
            </label>
            <label class="block text-sm">开始日期
              <input type="date" class="mt-1 w-full rounded-lg border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-900 px-2 py-1.5 text-sm"
                value={start()}
                onInput={(e) => setStart((e.currentTarget as HTMLInputElement).value)} />
            </label>
            <label class="block text-sm">结束日期
              <input type="date" class="mt-1 w-full rounded-lg border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-900 px-2 py-1.5 text-sm"
                value={end()}
                onInput={(e) => setEnd((e.currentTarget as HTMLInputElement).value)} />
            </label>
            <label class="col-span-2 inline-flex items-center gap-2 text-sm">
              <input type="checkbox" checked={genTasks()} onChange={(e) => setGenTasks((e.currentTarget as HTMLInputElement).checked)} />
              生成初始任务
            </label>
          </div>
        </Show>

        <Show when={target() === "area"}>
          <div class="grid grid-cols-2 gap-2">
            <label class="block text-sm">领域ID
              <input class="mt-1 w-full rounded-lg border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-900 px-2 py-1.5 text-sm"
                value={areaId()}
                onInput={(e) => setAreaId((e.currentTarget as HTMLInputElement).value)} />
            </label>
            <label class="block text-sm">转为
              <select class="mt-1 w-full rounded-lg border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-900 px-2 py-1.5 text-sm"
                value={asType()}
                onChange={(e) => setAsType((e.currentTarget as HTMLSelectElement).value as any)}>
                <option value="task">任务</option>
                <option value="kpi">KPI</option>
                <option value="habit">习惯</option>
              </select>
            </label>
          </div>
        </Show>

        <Show when={target() === "vault"}>
          <label class="block text-sm">保存到资源库路径
            <input class="mt-1 w-full rounded-lg border border-slate-300 dark:border-slate-700 bg-white dark:bg-slate-900 px-2 py-1.5 text-sm"
              value={vaultPath()} onInput={(e) => setVaultPath((e.currentTarget as HTMLInputElement).value)} />
          </label>
          <p class="text-xs text-slate-500 dark:text-slate-400">将转为 Markdown 并保留标签/元数据与反链。</p>
        </Show>
      </div>
    </Show>

    <Show when={step() === 3}>
      <div class="rounded-xl border border-slate-300 dark:border-slate-700 p-3 text-sm">
        <p class="mb-1">预览：</p>
        <p>选择项：<strong>{props.ids.length}</strong> 条</p>
        <p>目标：<strong>{targetLabel(target())}</strong></p>
        <Show when={target() === "project"}>
          <p>项目名：<strong>{projectName() || "（未填写）"}</strong> · 领域：{areaId() || "（可选）"} · 生成任务：{genTasks() ? "是" : "否"}</p>
        </Show>
        <Show when={target() === "area"}>
          <p>领域：<strong>{areaId() || "（未填写）"}</strong> · 类型：{asType()}</p>
        </Show>
        <Show when={target() === "vault"}>
          <p>路径：<strong>{vaultPath()}</strong></p>
        </Show>
      </div>
    </Show>

    <div class="mt-3 flex items-center justify-between">
      <div class="text-xs text-slate-500 dark:text-slate-400">Step {step()} / 3</div>
      <div class="flex gap-2">
        <button class="rounded-md border border-slate-300 dark:border-slate-700 px-3 py-1.5 text-sm hover:bg-slate-50 dark:hover:bg-slate-800" onClick={props.onCancel}>取消</button>
        <button
          class="rounded-md border border-slate-300 dark:border-slate-700 px-3 py-1.5 text-sm hover:bg-slate-50 dark:hover:bg-slate-800 disabled:opacity-50"
          disabled={step() === 1}
          onClick={() => setStep((s) => clamp(s - 1, 1, 3))}
        >
          上一步
        </button>
        <Show when={step() < 3} fallback={
          <button
            class="rounded-md border border-blue-500 bg-blue-600 text-white px-3 py-1.5 text-sm hover:bg-blue-700 disabled:opacity-50"
            disabled={!canNext()}
            onClick={confirm}
            data-testid="wizard-confirm"
          >
            确认
          </button>
        }>
          <button
            class="rounded-md border border-blue-500 text-blue-600 px-3 py-1.5 text-sm hover:bg-blue-50 dark:hover:bg-blue-900/20 disabled:opacity-50"
            disabled={!canNext()}
            onClick={() => setStep((s) => clamp(s + 1, 1, 3))}
          >
            下一步
          </button>
        </Show>
      </div>
    </div>
  </div>
</div>


);
}

function StepDot(props: { active: boolean; label: string }) {
return ( <div class="inline-flex items-center gap-1">
<span class={classNames("inline-block h-2.5 w-2.5 rounded-full", props.active ? "bg-blue-600" : "bg-slate-400")} />
<span class={classNames("text-xs", props.active ? "font-semibold" : "")}>{props.label}</span> </div>
);
}

/** ===========================

* Helpers
* =========================== */
  function iconForType(t: InboxType): string {
  switch (t) {
  case "note": return "📝";
  case "task": return "✅";
  case "idea": return "💡";
  case "link": return "🔗";
  case "file": return "📄";
  }
  }
  function labelForType(t: InboxType): string {
  switch (t) {
  case "note": return "笔记";
  case "task": return "任务";
  case "idea": return "想法";
  case "link": return "链接";
  case "file": return "文件";
  }
  }
  function priorityLabel(p: Priority): string {
  return p === "high" ? "高" : p === "medium" ? "中" : "低";
  }
  function priorityBadge(p: Priority): string {
  return `优先级:${priorityLabel(p)}`;
  }
  function statusLabel(s: Filters["status"]) {
  return s === "all" ? "全部" : s === "processed" ? "已处理" : "未处理";
  }
  function targetLabel(t: "project" | "area" | "vault") {
  return t === "project" ? "项目" : t === "area" ? "领域" : "资源库";
  }
  function targetDesc(t: "project" | "area" | "vault") {
  return t === "project" ? "可生成初始任务并归属领域" : t === "area" ? "转为任务/KPI/习惯项" : "转 Markdown 保留标签与反链";
  }
