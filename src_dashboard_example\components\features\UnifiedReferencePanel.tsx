import React, { useState, useEffect } from 'react'
import {
  UnifiedReference,
  WikiLinkReference,
  ProjectReference,
  AreaReference,
  ReferenceStatistics,
  unifiedReferenceService
} from '../../services/unifiedReferenceService'
import ReferencePreview from './ReferencePreview'
import {
  ReferenceStrengthIndicator,
  ReferenceStrengthBadge,
  ReferenceStrengthChart
} from './ReferenceStrengthIndicator'

interface UnifiedReferencePanelProps {
  documentPath: string
  onReferenceClick?: (reference: UnifiedReference) => void
  className?: string
}

/**
 * 统一引用面板组件
 * 显示文档的所有引用类型：WikiLink、项目引用、领域引用
 */
export const UnifiedReferencePanel: React.FC<UnifiedReferencePanelProps> = ({
  documentPath,
  onReferenceClick,
  className = ''
}) => {
  const [references, setReferences] = useState<UnifiedReference[]>([])
  const [statistics, setStatistics] = useState<ReferenceStatistics | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'all' | 'wikilinks' | 'projects' | 'areas'>('all')
  const [previewReference, setPreviewReference] = useState<UnifiedReference | null>(null)
  const [sortBy, setSortBy] = useState<'strength' | 'created' | 'updated' | 'title'>('strength')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  // 加载引用数据
  useEffect(() => {
    if (!documentPath) return

    const loadReferences = async () => {
      setLoading(true)
      setError(null)

      try {
        console.log('🔄 加载统一引用数据:', documentPath)
        const allReferences = await unifiedReferenceService.getAllReferences(documentPath)
        const stats = unifiedReferenceService.calculateStatistics(allReferences)

        setReferences(allReferences)
        setStatistics(stats)
        
        console.log('✅ 统一引用数据加载完成:', {
          总引用数: allReferences.length,
          统计: stats
        })
      } catch (err) {
        console.error('❌ 加载统一引用数据失败:', err)
        setError(err instanceof Error ? err.message : '加载失败')
      } finally {
        setLoading(false)
      }
    }

    loadReferences()
  }, [documentPath])

  // 过滤和排序引用
  const filteredAndSortedReferences = references
    .filter(ref => {
      switch (activeTab) {
        case 'wikilinks':
          return ref.referenceType === 'wikilink'
        case 'projects':
          // 项目引用：包括 task 和 description 类型
          return ref.referenceType === 'task' || ref.referenceType === 'description'
        case 'areas':
          // 领域引用：note 类型
          return ref.referenceType === 'note'
        default:
          return true
      }
    })
    .sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'strength':
          comparison = a.strength - b.strength
          break
        case 'created':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          break
        case 'updated':
          comparison = new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime()
          break
        case 'title':
          comparison = a.targetTitle.localeCompare(b.targetTitle)
          break
      }

      return sortOrder === 'desc' ? -comparison : comparison
    })

  // 处理引用点击
  const handleReferenceClick = (reference: UnifiedReference) => {
    console.log('🔗 点击引用:', reference)
    onReferenceClick?.(reference)
  }

  // 处理引用预览
  const handleReferencePreview = (reference: UnifiedReference, event: React.MouseEvent) => {
    event.stopPropagation()
    setPreviewReference(reference)
  }

  // 关闭预览
  const handleClosePreview = () => {
    setPreviewReference(null)
  }

  // 渲染 WikiLink 引用
  const renderWikiLinkReference = (ref: WikiLinkReference) => (
    <div
      key={ref.id}
      className="reference-item wikilink-reference p-3 border-l-4 border-blue-500 bg-blue-50 hover:bg-blue-100 cursor-pointer transition-colors group"
      onClick={() => handleReferenceClick(ref)}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <div className="font-medium text-blue-800 truncate">
              📄 {ref.sourceTitle}
            </div>
            <ReferenceStrengthBadge strength={ref.strength} />
          </div>
          <div className="text-sm text-blue-600 mt-1">
            {ref.linkText} {ref.displayText && ref.displayText !== ref.linkText && `(${ref.displayText})`}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            行 {ref.lineNumber}, 列 {ref.columnNumber}
          </div>

          {/* 强度指示器 */}
          <div className="mt-2">
            <ReferenceStrengthIndicator
              strength={ref.strength}
              size="sm"
              showPercentage={true}
            />
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            type="button"
            onClick={(e) => handleReferencePreview(ref, e)}
            className="p-1 text-blue-600 hover:bg-blue-200 rounded transition-colors"
            title="预览"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </button>
        </div>
      </div>

      {ref.context && (
        <div className="text-xs text-gray-600 mt-2 p-2 bg-white rounded border">
          <div className="truncate">...{ref.context}...</div>
        </div>
      )}
    </div>
  )

  // 渲染项目引用
  const renderProjectReference = (ref: ProjectReference) => (
    <div
      key={ref.id}
      className="reference-item project-reference p-3 border-l-4 border-green-500 bg-green-50 hover:bg-green-100 cursor-pointer transition-colors group"
      onClick={() => handleReferenceClick(ref)}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <div className="font-medium text-green-800 truncate">
              {ref.referenceType === 'task' ? '✅' : '📋'} {ref.targetTitle}
            </div>
            <ReferenceStrengthBadge strength={ref.strength} />
          </div>
          <div className="text-sm text-green-600 mt-1">
            {ref.referenceType === 'task' ? '任务引用' : '项目引用'}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            来源: {ref.sourceTitle}
          </div>

          {/* 强度指示器 */}
          <div className="mt-2">
            <ReferenceStrengthIndicator
              strength={ref.strength}
              size="sm"
              showPercentage={true}
            />
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            type="button"
            onClick={(e) => handleReferencePreview(ref, e)}
            className="p-1 text-green-600 hover:bg-green-200 rounded transition-colors"
            title="预览"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </button>
        </div>
      </div>

      {ref.context && (
        <div className="text-xs text-gray-600 mt-2 p-2 bg-white rounded border">
          <div className="truncate">...{ref.context}...</div>
        </div>
      )}
    </div>
  )

  // 渲染领域引用
  const renderAreaReference = (ref: AreaReference) => (
    <div
      key={ref.id}
      className="reference-item area-reference p-3 border-l-4 border-purple-500 bg-purple-50 hover:bg-purple-100 cursor-pointer transition-colors group"
      onClick={() => handleReferenceClick(ref)}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <div className="font-medium text-purple-800 truncate">
              🏷️ {ref.targetTitle}
            </div>
            <ReferenceStrengthBadge strength={ref.strength} />
          </div>
          <div className="text-sm text-purple-600 mt-1">
            领域引用
          </div>
          <div className="text-xs text-gray-500 mt-1">
            来源: {ref.sourceTitle}
          </div>

          {/* 强度指示器 */}
          <div className="mt-2">
            <ReferenceStrengthIndicator
              strength={ref.strength}
              size="sm"
              showPercentage={true}
            />
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            type="button"
            onClick={(e) => handleReferencePreview(ref, e)}
            className="p-1 text-purple-600 hover:bg-purple-200 rounded transition-colors"
            title="预览"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </button>
        </div>
      </div>

      {ref.context && (
        <div className="text-xs text-gray-600 mt-2 p-2 bg-white rounded border">
          <div className="truncate">...{ref.context}...</div>
        </div>
      )}
    </div>
  )

  // 渲染引用项
  const renderReference = (ref: UnifiedReference) => {
    switch (ref.referenceType) {
      case 'wikilink':
        return renderWikiLinkReference(ref as WikiLinkReference)
      case 'task':
      case 'description':
        return renderProjectReference(ref as ProjectReference)
      case 'note':
        return renderAreaReference(ref as AreaReference)
      default:
        console.warn('⚠️ 未知的引用类型:', ref.referenceType, ref)
        return null
    }
  }

  if (loading) {
    return (
      <div className={`unified-reference-panel ${className}`}>
        <div className="p-4 text-center text-gray-500">
          <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
          加载引用数据...
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`unified-reference-panel ${className}`}>
        <div className="p-4 text-center text-red-500">
          <div className="mb-2">❌ 加载失败</div>
          <div className="text-sm">{error}</div>
        </div>
      </div>
    )
  }

  return (
    <div className={`unified-reference-panel ${className}`}>
      {/* 统计信息 */}
      {statistics && (
        <div className="statistics-section p-4 bg-gray-50 border-b">
          <h3 className="font-medium text-gray-800 mb-3">引用统计</h3>
          <div className="grid grid-cols-2 gap-4 text-sm mb-4">
            <div className="stat-item">
              <div className="text-gray-600">总引用数</div>
              <div className="font-medium text-lg">{statistics.totalReferences}</div>
            </div>
            <div className="stat-item">
              <div className="text-gray-600">WikiLink</div>
              <div className="font-medium text-blue-600">{statistics.wikiLinks}</div>
            </div>
            <div className="stat-item">
              <div className="text-gray-600">项目引用</div>
              <div className="font-medium text-green-600">{statistics.projectReferences}</div>
            </div>
            <div className="stat-item">
              <div className="text-gray-600">领域引用</div>
              <div className="font-medium text-purple-600">{statistics.areaReferences}</div>
            </div>
          </div>

          {/* 强度分布图表 */}
          <ReferenceStrengthChart
            strengths={references.map(ref => ref.strength)}
            className="mt-3"
          />
        </div>
      )}

      {/* 标签页 */}
      <div className="tabs-section border-b">
        <div className="flex">
          {[
            { key: 'all', label: '全部', count: references.length },
            { key: 'wikilinks', label: 'WikiLink', count: statistics?.wikiLinks || 0 },
            { key: 'projects', label: '项目', count: statistics?.projectReferences || 0 },
            { key: 'areas', label: '领域', count: statistics?.areaReferences || 0 }
          ].map(tab => (
            <button
              key={tab.key}
              type="button"
              className={`tab-button px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.key
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab(tab.key as any)}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>
      </div>

      {/* 排序控件 */}
      {filteredAndSortedReferences.length > 0 && (
        <div className="sort-controls p-3 border-b bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <span>排序:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="text-xs border border-gray-300 rounded px-2 py-1 bg-white"
                title="选择排序方式"
                aria-label="排序方式"
              >
                <option value="strength">强度</option>
                <option value="title">标题</option>
                <option value="created">创建时间</option>
                <option value="updated">更新时间</option>
              </select>
              <button
                type="button"
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
                title={sortOrder === 'asc' ? '升序' : '降序'}
              >
                <svg className={`w-4 h-4 transform ${sortOrder === 'asc' ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </div>
            <div className="text-xs text-gray-500">
              {filteredAndSortedReferences.length} 项
            </div>
          </div>
        </div>
      )}

      {/* 引用列表 */}
      <div className="references-list">
        {filteredAndSortedReferences.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <div className="mb-2">📭</div>
            <div>暂无{activeTab === 'all' ? '' : activeTab === 'wikilinks' ? 'WikiLink' : activeTab === 'projects' ? '项目' : '领域'}引用</div>
          </div>
        ) : (
          <div className="space-y-2 p-2">
            {filteredAndSortedReferences.map(renderReference)}
          </div>
        )}
      </div>

      {/* 预览组件 */}
      {previewReference && (
        <ReferencePreview
          reference={previewReference}
          onClose={handleClosePreview}
          onNavigate={(ref) => {
            handleClosePreview()
            handleReferenceClick(ref)
          }}
        />
      )}
    </div>
  )
}

export default UnifiedReferencePanel
