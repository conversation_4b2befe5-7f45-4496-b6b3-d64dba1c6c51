import { JSX, splitProps, createSignal, createContext, useContext, children, Show } from 'solid-js';
import { cn } from '@/lib/utils';

// Select Context
interface SelectContextValue {
  value: () => string | undefined;
  setValue: (value: string) => void;
  open: () => boolean;
  setOpen: (open: boolean) => void;
}

const SelectContext = createContext<SelectContextValue>();

// Select Root Component
export interface SelectProps {
  value?: string;
  onValueChange?: (value: string) => void;
  children: JSX.Element;
}

export function Select(props: SelectProps) {
  const [value, setValue] = createSignal(props.value);
  const [open, setOpen] = createSignal(false);

  const handleValueChange = (newValue: string) => {
    setValue(newValue);
    props.onValueChange?.(newValue);
    setOpen(false);
  };

  const contextValue: SelectContextValue = {
    value,
    setValue: handleValueChange,
    open,
    setOpen
  };

  return (
    <SelectContext.Provider value={contextValue}>
      {props.children}
    </SelectContext.Provider>
  );
}

// Select Trigger Component
export interface SelectTriggerProps extends JSX.ButtonHTMLAttributes<HTMLButtonElement> {
  class?: string;
  children: JSX.Element;
}

export function SelectTrigger(props: SelectTriggerProps) {
  const [local, others] = splitProps(props, ['class', 'children']);
  const context = useContext(SelectContext);

  if (!context) {
    throw new Error('SelectTrigger must be used within a Select');
  }

  return (
    <button
      type="button"
      class={cn(
        'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
        local.class
      )}
      onClick={() => context.setOpen(!context.open())}
      {...others}
    >
      {local.children}
      <svg
        class="h-4 w-4 opacity-50"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <polyline points="6,9 12,15 18,9" />
      </svg>
    </button>
  );
}

// Select Value Component
export interface SelectValueProps {
  placeholder?: string;
  class?: string;
}

export function SelectValue(props: SelectValueProps) {
  const context = useContext(SelectContext);

  if (!context) {
    throw new Error('SelectValue must be used within a Select');
  }

  return (
    <span class={props.class}>
      {context.value() || props.placeholder}
    </span>
  );
}

// Select Content Component
export interface SelectContentProps {
  class?: string;
  children: JSX.Element;
}

export function SelectContent(props: SelectContentProps) {
  const [local, others] = splitProps(props, ['class', 'children']);
  const context = useContext(SelectContext);

  if (!context) {
    throw new Error('SelectContent must be used within a Select');
  }

  return (
    <Show when={context.open()}>
      <div
        class={cn(
          'relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md',
          'absolute top-full left-0 mt-1 w-full',
          local.class
        )}
        {...others}
      >
        {local.children}
      </div>
    </Show>
  );
}

// Select Item Component
export interface SelectItemProps extends JSX.HTMLAttributes<HTMLDivElement> {
  value: string;
  class?: string;
  children: JSX.Element;
}

export function SelectItem(props: SelectItemProps) {
  const [local, others] = splitProps(props, ['class', 'children', 'value']);
  const context = useContext(SelectContext);

  if (!context) {
    throw new Error('SelectItem must be used within a Select');
  }

  return (
    <div
      class={cn(
        'relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
        local.class
      )}
      onClick={() => context.setValue(local.value)}
      {...others}
    >
      <Show when={context.value() === local.value}>
        <span class="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
          <svg
            class="h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <polyline points="20,6 9,17 4,12" />
          </svg>
        </span>
      </Show>
      {local.children}
    </div>
  );
}
