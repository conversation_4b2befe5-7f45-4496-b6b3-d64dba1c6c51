import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '../ui/dialog'
import { Button } from '../ui/button'
import { AlertTriangle, Folder, File } from 'lucide-react'
import type { FileTreeItem } from './FileTreeNode'

interface DeleteConfirmDialogProps {
  isOpen: boolean
  item: FileTreeItem | null
  onConfirm: () => void
  onCancel: () => void
  isDeleting?: boolean
}

export function DeleteConfirmDialog({
  isOpen,
  item,
  onConfirm,
  onCancel,
  isDeleting = false
}: DeleteConfirmDialogProps) {
  if (!item) return null

  const isFolder = item.type === 'folder'
  const hasChildren = item.children && item.children.length > 0

  // 计算子项数量
  const getChildrenCount = (item: FileTreeItem): { files: number; folders: number } => {
    if (!item.children) return { files: 0, folders: 0 }

    let files = 0
    let folders = 0

    const countRecursive = (children: FileTreeItem[]) => {
      children.forEach((child) => {
        if (child.type === 'file') {
          files++
        } else {
          folders++
          if (child.children) {
            countRecursive(child.children)
          }
        }
      })
    }

    countRecursive(item.children)
    return { files, folders }
  }

  const childrenCount = isFolder ? getChildrenCount(item) : { files: 0, folders: 0 }
  const totalChildren = childrenCount.files + childrenCount.folders

  return (
    <Dialog open={isOpen} onOpenChange={onCancel}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-destructive" />
            确认删除
          </DialogTitle>
          <DialogDescription>此操作无法撤销，请确认是否要删除以下内容：</DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
            {isFolder ? (
              <Folder className="w-8 h-8 text-blue-500" />
            ) : (
              <File className="w-8 h-8 text-gray-500" />
            )}
            <div className="flex-1">
              <div className="font-medium">{item.name}</div>
              <div className="text-sm text-muted-foreground">
                {isFolder ? '文件夹' : '文件'}
                {item.size && ` • ${(item.size / 1024).toFixed(1)} KB`}
              </div>
            </div>
          </div>

          {isFolder && hasChildren && (
            <div className="mt-3 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <div className="flex items-center gap-2 text-destructive font-medium mb-2">
                <AlertTriangle className="w-4 h-4" />
                级联删除警告
              </div>
              <div className="text-sm text-destructive/80">
                此文件夹包含 <strong>{totalChildren}</strong> 个子项：
                <ul className="mt-1 ml-4 list-disc">
                  {childrenCount.folders > 0 && <li>{childrenCount.folders} 个子文件夹</li>}
                  {childrenCount.files > 0 && <li>{childrenCount.files} 个文件</li>}
                </ul>
                <div className="mt-2 font-medium">删除此文件夹将同时删除所有子项！</div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onCancel} disabled={isDeleting}>
            取消
          </Button>
          <Button variant="destructive" onClick={onConfirm} disabled={isDeleting}>
            {isDeleting ? '删除中...' : '确认删除'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
