# PaoLife 品牌设计规范

## 📋 概述

本文档定义了PaoLife品牌的视觉识别系统，包括品牌标识、色彩应用、字体使用、图形元素等，确保品牌形象的一致性和专业性。

## 🎯 品牌理念

### 1. 品牌定位

#### 核心价值
- **高效**: 提升个人和团队的工作效率
- **简洁**: 简化复杂的任务管理流程
- **智能**: 智能化的数据分析和建议
- **可靠**: 稳定可靠的数据存储和同步

#### 品牌个性
- **专业**: 面向知识工作者的专业工具
- **现代**: 采用现代化的设计语言
- **友好**: 易于使用，降低学习成本
- **创新**: 持续改进和功能创新

### 2. 目标用户

#### 主要用户群体
- 知识工作者和创意专业人士
- 项目管理者和团队领导
- 学生和研究人员
- 个人效率提升爱好者

#### 用户特征
- 重视效率和生产力
- 喜欢简洁清晰的界面
- 需要可靠的数据管理
- 追求工作生活平衡

## 🎨 品牌标识系统

### 1. 主标识 (Logo)

#### 设计理念
PaoLife的标识设计融合了"跑"的动感和"生活"的温度，体现了高效生活的品牌理念。

#### 标识构成
```
[P] + aoLife
```

- **图标部分**: 字母"P"的几何化设计，融入向前的箭头元素
- **文字部分**: "aoLife"采用现代无衬线字体
- **整体**: 简洁、现代、具有科技感

#### 标识变体

##### 水平组合 (首选)
```
[P] PaoLife
```
- 用于大多数应用场景
- 图标与文字水平排列
- 保持适当的间距比例

##### 垂直组合
```
[P]
PaoLife
```
- 用于垂直空间受限的场景
- 图标位于文字上方
- 保持视觉平衡

##### 图标单独使用
```
[P]
```
- 用于空间极度受限的场景
- 社交媒体头像
- 应用图标

### 2. 标识规范

#### 最小尺寸
- **水平组合**: 最小宽度 120px
- **垂直组合**: 最小宽度 80px
- **图标单独**: 最小尺寸 24px × 24px

#### 安全区域
标识周围必须保持等于图标高度的空白区域，确保视觉呼吸感。

#### 禁用规范
- ❌ 不得改变标识的比例
- ❌ 不得改变标识的颜色（除规定变体外）
- ❌ 不得添加阴影、描边或其他效果
- ❌ 不得在复杂背景上使用，影响识别度

### 3. 色彩应用

#### 主色版本
```css
/* 标准色彩 */
--logo-primary: #3b82f6;    /* 主蓝色 */
--logo-text: #1f2937;       /* 深灰色文字 */
```

#### 单色版本
```css
/* 深色背景使用 */
--logo-white: #ffffff;

/* 浅色背景使用 */
--logo-black: #000000;
--logo-gray: #6b7280;
```

#### 反色版本
在深色背景上使用白色版本，在浅色背景上使用深色版本。

## 🎨 品牌色彩系统

### 1. 主品牌色

#### 蓝色系 (Primary Blue)
```css
/* 主品牌色 - 代表专业、可靠、科技 */
--brand-blue-50: #eff6ff;
--brand-blue-100: #dbeafe;
--brand-blue-200: #bfdbfe;
--brand-blue-300: #93c5fd;
--brand-blue-400: #60a5fa;
--brand-blue-500: #3b82f6;   /* 主色 */
--brand-blue-600: #2563eb;
--brand-blue-700: #1d4ed8;
--brand-blue-800: #1e40af;
--brand-blue-900: #1e3a8a;
```

#### 应用场景
- 主要操作按钮
- 链接和交互元素
- 重要信息标识
- 品牌强调元素

### 2. 辅助色彩

#### 绿色系 (Success Green)
```css
/* 成功、完成、积极 */
--brand-green-50: #ecfdf5;
--brand-green-500: #10b981;
--brand-green-600: #059669;
```

#### 橙色系 (Warning Orange)
```css
/* 警告、注意、待处理 */
--brand-orange-50: #fff7ed;
--brand-orange-500: #f59e0b;
--brand-orange-600: #d97706;
```

#### 红色系 (Error Red)
```css
/* 错误、危险、删除 */
--brand-red-50: #fef2f2;
--brand-red-500: #ef4444;
--brand-red-600: #dc2626;
```

### 3. 中性色彩

#### 灰色系 (Neutral Gray)
```css
/* 文本、背景、边框 */
--brand-gray-50: #f9fafb;
--brand-gray-100: #f3f4f6;
--brand-gray-200: #e5e7eb;
--brand-gray-300: #d1d5db;
--brand-gray-400: #9ca3af;
--brand-gray-500: #6b7280;
--brand-gray-600: #4b5563;
--brand-gray-700: #374151;
--brand-gray-800: #1f2937;
--brand-gray-900: #111827;
```

## ✍️ 品牌字体系统

### 1. 主字体

#### 英文字体
```css
/* 主字体 - Inter */
font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
```

**特点**:
- 现代几何无衬线字体
- 优秀的屏幕显示效果
- 支持多种字重
- 良好的中英文混排效果

#### 中文字体
```css
/* 中文字体栈 */
font-family: 'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
```

**特点**:
- 优先使用系统字体
- 确保跨平台一致性
- 良好的可读性

### 2. 字体层级

#### 标题字体
```css
/* 主标题 */
.heading-1 {
  font-size: 2.25rem;    /* 36px */
  font-weight: 700;
  line-height: 1.2;
}

/* 副标题 */
.heading-2 {
  font-size: 1.875rem;   /* 30px */
  font-weight: 600;
  line-height: 1.3;
}

/* 小标题 */
.heading-3 {
  font-size: 1.5rem;     /* 24px */
  font-weight: 600;
  line-height: 1.4;
}
```

#### 正文字体
```css
/* 大正文 */
.body-large {
  font-size: 1.125rem;   /* 18px */
  font-weight: 400;
  line-height: 1.6;
}

/* 标准正文 */
.body-normal {
  font-size: 1rem;       /* 16px */
  font-weight: 400;
  line-height: 1.5;
}

/* 小正文 */
.body-small {
  font-size: 0.875rem;   /* 14px */
  font-weight: 400;
  line-height: 1.5;
}
```

#### 辅助字体
```css
/* 说明文字 */
.caption {
  font-size: 0.75rem;    /* 12px */
  font-weight: 400;
  line-height: 1.4;
  color: var(--brand-gray-600);
}

/* 标签文字 */
.label {
  font-size: 0.875rem;   /* 14px */
  font-weight: 500;
  line-height: 1.4;
}
```

## 🖼️ 图形元素系统

### 1. 图标风格

#### 设计原则
- **一致性**: 统一的设计风格和视觉语言
- **简洁性**: 简化的几何形状，易于识别
- **功能性**: 图标含义清晰，符合用户认知
- **可扩展性**: 适用于不同尺寸和分辨率

#### 技术规范
```css
/* 图标基础样式 */
.icon {
  width: 1em;
  height: 1em;
  stroke: currentColor;
  fill: none;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

/* 图标尺寸变体 */
.icon-sm { font-size: 16px; }
.icon-md { font-size: 20px; }
.icon-lg { font-size: 24px; }
.icon-xl { font-size: 32px; }
```

### 2. 插画风格

#### 设计风格
- **扁平化设计**: 简洁的几何形状
- **柔和色彩**: 使用品牌色彩系统
- **友好氛围**: 温暖、亲和的视觉感受
- **现代感**: 符合当代设计趋势

#### 应用场景
- 空状态页面
- 引导页面
- 错误页面
- 功能介绍

### 3. 图案纹理

#### 背景图案
```css
/* 微妙的几何图案 */
.pattern-dots {
  background-image: radial-gradient(circle, var(--brand-gray-200) 1px, transparent 1px);
  background-size: 20px 20px;
}

.pattern-grid {
  background-image: 
    linear-gradient(var(--brand-gray-100) 1px, transparent 1px),
    linear-gradient(90deg, var(--brand-gray-100) 1px, transparent 1px);
  background-size: 20px 20px;
}
```

## 📐 布局和比例

### 1. 黄金比例

#### 应用原则
使用黄金比例 (1:1.618) 创造和谐的视觉比例关系。

```css
/* 黄金比例应用 */
.golden-ratio {
  --ratio: 1.618;
}

/* 卡片比例 */
.card-golden {
  aspect-ratio: var(--ratio) / 1;
}

/* 侧边栏比例 */
.sidebar {
  flex: 1;
}

.main-content {
  flex: var(--ratio);
}
```

### 2. 网格系统

#### 12列网格
```css
.grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: var(--spacing-6);
}

.col-1 { grid-column: span 1; }
.col-2 { grid-column: span 2; }
.col-3 { grid-column: span 3; }
.col-4 { grid-column: span 4; }
.col-6 { grid-column: span 6; }
.col-8 { grid-column: span 8; }
.col-12 { grid-column: span 12; }
```

## 🎭 品牌应用规范

### 1. 数字媒体应用

#### 网站应用
- 页面头部使用水平组合标识
- 保持品牌色彩的一致性
- 使用规范的字体层级

#### 移动应用
- 应用图标使用简化的图标版本
- 启动页面展示完整品牌标识
- 界面元素遵循品牌色彩规范

### 2. 营销材料

#### 宣传海报
- 突出品牌标识的展示
- 使用品牌色彩作为主色调
- 保持简洁的设计风格

#### 社交媒体
- 头像使用图标版本
- 封面图片展示品牌理念
- 内容配图保持品牌风格一致

### 3. 办公用品

#### 名片设计
- 正面展示标识和联系信息
- 背面可使用品牌图案
- 使用品牌色彩和字体

#### 文档模板
- 页眉使用品牌标识
- 标题使用品牌字体
- 色彩搭配遵循品牌规范

## 📋 品牌规范检查清单

### 标识使用
- [ ] 使用正确的标识版本
- [ ] 保持标识的完整性
- [ ] 遵循最小尺寸要求
- [ ] 保持安全区域

### 色彩应用
- [ ] 使用标准品牌色彩
- [ ] 保持色彩的一致性
- [ ] 正确应用语义色彩
- [ ] 考虑可访问性要求

### 字体使用
- [ ] 使用指定的品牌字体
- [ ] 遵循字体层级规范
- [ ] 保持合适的行高和间距
- [ ] 确保跨平台一致性

### 整体风格
- [ ] 保持设计风格一致
- [ ] 体现品牌个性
- [ ] 符合目标用户喜好
- [ ] 传达正确的品牌信息

---

**文档版本**: 1.0  
**创建日期**: 2024年12月  
**维护者**: 品牌设计团队  
**下次更新**: 根据品牌发展需要
