// Review Entity - 复盘实体

use crate::shared::types::{Id, Metadata, EntityStatus};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Review {
    pub id: Id,
    pub title: String,
    pub content: String,
    pub review_type: ReviewType,
    pub period_start: chrono::DateTime<chrono::Utc>,
    pub period_end: chrono::DateTime<chrono::Utc>,
    pub template_id: Option<Id>,
    pub entity_status: EntityStatus,
    pub metadata: Metadata,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReviewType {
    Weekly,
    Monthly,
    Quarterly,
    Yearly,
    Custom,
}

impl Review {
    pub fn new(title: String, review_type: ReviewType) -> Self {
        let now = chrono::Utc::now();
        Self {
            id: crate::shared::utils::IdGenerator::new_id_with_prefix("review"),
            title,
            content: String::new(),
            review_type,
            period_start: now,
            period_end: now,
            template_id: None,
            entity_status: EntityStatus::Active,
            metadata: Metadata::default(),
        }
    }
}
