// Common Value Objects - 通用值对象

use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub struct Email {
    pub value: String,
}

impl Email {
    pub fn new(email: String) -> Result<Self, String> {
        if email.contains('@') && email.contains('.') {
            Ok(Self { value: email })
        } else {
            Err("Invalid email format".to_string())
        }
    }
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub struct Url {
    pub value: String,
}

impl Url {
    pub fn new(url: String) -> Result<Self, String> {
        if url.starts_with("http://") || url.starts_with("https://") {
            Ok(Self { value: url })
        } else {
            Err("Invalid URL format".to_string())
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct Color {
    pub hex: String,
}

impl Color {
    pub fn new(hex: String) -> Result<Self, String> {
        if hex.starts_with('#') && hex.len() == 7 {
            Ok(Self { hex })
        } else {
            Err("Invalid color format".to_string())
        }
    }
}
