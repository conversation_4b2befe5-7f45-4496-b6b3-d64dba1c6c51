// Project Commands - 项目相关的 Tauri 命令

use crate::api::dto::*;
use crate::application::services::ProjectAppService;
use crate::shared::errors::AppError;
use crate::shared::types::{Id, QueryParams, Pagination};
use std::sync::Arc;
use tauri::State;

/// 创建项目
#[tauri::command]
pub async fn create_project(
    request: CreateProjectRequest,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<ProjectResponse>, String> {
    // 验证请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    // 调用应用服务
    match project_service.create_project(
        request.name,
        request.description,
        request.area_id,
        Some(request.get_priority()),
    ).await {
        Ok(project) => Ok(ApiResponse::success(ProjectResponse::from(project))),
        Err(e) => Ok(ApiResponse::error(format!("Failed to create project: {}", e))),
    }
}

/// 根据ID获取项目
#[tauri::command]
pub async fn get_project_by_id(
    id: Id,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<ProjectResponse>, String> {
    match project_service.get_project_by_id(&id).await {
        Ok(project) => Ok(ApiResponse::success(ProjectResponse::from(project))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Project not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to get project: {}", e))),
    }
}

/// 更新项目信息
#[tauri::command]
pub async fn update_project(
    id: Id,
    request: UpdateProjectRequest,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<ProjectResponse>, String> {
    // 验证请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    // 调用应用服务
    match project_service.update_project(
        &id,
        request.name,
        request.description,
        request.goals,
        request.deliverables,
        request.start_date,
        request.due_date,
        request.area_id,
        request.get_priority(),
        request.get_tags(),
    ).await {
        Ok(project) => Ok(ApiResponse::success(ProjectResponse::from(project))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Project not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to update project: {}", e))),
    }
}

/// 更新项目状态
#[tauri::command]
pub async fn update_project_status(
    id: Id,
    request: UpdateProjectStatusRequest,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<ProjectResponse>, String> {
    // 验证请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    match project_service.update_project_status(&id, request.get_status()).await {
        Ok(project) => Ok(ApiResponse::success(ProjectResponse::from(project))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Project not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to update project status: {}", e))),
    }
}

/// 更新项目进度
#[tauri::command]
pub async fn update_project_progress(
    id: Id,
    request: UpdateProjectProgressRequest,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<ProjectResponse>, String> {
    // 验证请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    match project_service.update_project_progress(&id, request.progress).await {
        Ok(project) => Ok(ApiResponse::success(ProjectResponse::from(project))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Project not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to update project progress: {}", e))),
    }
}

/// 完成项目
#[tauri::command]
pub async fn complete_project(
    id: Id,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<ProjectResponse>, String> {
    match project_service.complete_project(&id).await {
        Ok(project) => Ok(ApiResponse::success(ProjectResponse::from(project))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Project not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to complete project: {}", e))),
    }
}

/// 取消项目
#[tauri::command]
pub async fn cancel_project(
    id: Id,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<ProjectResponse>, String> {
    match project_service.cancel_project(&id).await {
        Ok(project) => Ok(ApiResponse::success(ProjectResponse::from(project))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Project not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to cancel project: {}", e))),
    }
}

/// 删除项目
#[tauri::command]
pub async fn delete_project(
    id: Id,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<EmptyResponse>, String> {
    match project_service.delete_project(&id).await {
        Ok(_) => Ok(ApiResponse::success(EmptyResponse)),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Project not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to delete project: {}", e))),
    }
}

/// 获取所有活跃项目
#[tauri::command]
pub async fn get_active_projects(
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<Vec<ProjectResponse>>, String> {
    match project_service.get_active_projects().await {
        Ok(projects) => {
            let project_responses: Vec<ProjectResponse> = projects.into_iter()
                .map(ProjectResponse::from)
                .collect();
            Ok(ApiResponse::success(project_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get active projects: {}", e))),
    }
}

/// 根据领域获取项目
#[tauri::command]
pub async fn get_projects_by_area(
    area_id: Id,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<Vec<ProjectResponse>>, String> {
    match project_service.get_projects_by_area(&area_id).await {
        Ok(projects) => {
            let project_responses: Vec<ProjectResponse> = projects.into_iter()
                .map(ProjectResponse::from)
                .collect();
            Ok(ApiResponse::success(project_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get projects by area: {}", e))),
    }
}

/// 根据状态获取项目
#[tauri::command]
pub async fn get_projects_by_status(
    status: String,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<Vec<ProjectResponse>>, String> {
    let status_request = UpdateProjectStatusRequest { status };
    if let Err(e) = status_request.validate() {
        return Ok(ApiResponse::error(e));
    }

    match project_service.get_projects_by_status(&status_request.get_status()).await {
        Ok(projects) => {
            let project_responses: Vec<ProjectResponse> = projects.into_iter()
                .map(ProjectResponse::from)
                .collect();
            Ok(ApiResponse::success(project_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get projects by status: {}", e))),
    }
}

/// 获取即将到期的项目
#[tauri::command]
pub async fn get_projects_due_soon(
    days: u32,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<Vec<ProjectResponse>>, String> {
    match project_service.get_projects_due_soon(days).await {
        Ok(projects) => {
            let project_responses: Vec<ProjectResponse> = projects.into_iter()
                .map(ProjectResponse::from)
                .collect();
            Ok(ApiResponse::success(project_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get projects due soon: {}", e))),
    }
}

/// 获取已逾期的项目
#[tauri::command]
pub async fn get_overdue_projects(
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<Vec<ProjectResponse>>, String> {
    match project_service.get_overdue_projects().await {
        Ok(projects) => {
            let project_responses: Vec<ProjectResponse> = projects.into_iter()
                .map(ProjectResponse::from)
                .collect();
            Ok(ApiResponse::success(project_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get overdue projects: {}", e))),
    }
}

/// 分页获取项目列表
#[tauri::command]
pub async fn get_projects_with_pagination(
    pagination: PaginationRequest,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<ProjectListResponse>, String> {
    // 验证分页参数
    if let Err(e) = pagination.validate() {
        return Ok(ApiResponse::error(e));
    }

    let query_params = QueryParams {
        pagination: Some(Pagination {
            page: pagination.page,
            size: pagination.size,
        }),
        ..Default::default()
    };

    match project_service.get_projects_with_pagination(&query_params).await {
        Ok((projects, total)) => {
            let project_responses: Vec<ProjectResponse> = projects.into_iter()
                .map(ProjectResponse::from)
                .collect();
            
            let response = ProjectListResponse {
                projects: project_responses,
                total,
                page: pagination.page,
                size: pagination.size,
            };
            
            Ok(ApiResponse::success(response))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get projects: {}", e))),
    }
}

/// 搜索项目
#[tauri::command]
pub async fn search_projects(
    request: SearchRequest,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<Vec<ProjectResponse>>, String> {
    // 验证搜索请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    match project_service.search_projects(&request.query).await {
        Ok(projects) => {
            let project_responses: Vec<ProjectResponse> = projects.into_iter()
                .map(ProjectResponse::from)
                .collect();
            Ok(ApiResponse::success(project_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to search projects: {}", e))),
    }
}

/// 获取项目统计信息
#[tauri::command]
pub async fn get_project_statistics(
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<ProjectStatisticsResponse>, String> {
    match project_service.get_project_statistics().await {
        Ok(stats) => Ok(ApiResponse::success(ProjectStatisticsResponse::from(stats))),
        Err(e) => Ok(ApiResponse::error(format!("Failed to get project statistics: {}", e))),
    }
}

/// 计算项目完成率
#[tauri::command]
pub async fn calculate_project_completion_rate(
    id: Id,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<f32>, String> {
    match project_service.calculate_project_completion_rate(&id).await {
        Ok(rate) => Ok(ApiResponse::success(rate)),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Project not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to calculate completion rate: {}", e))),
    }
}

/// 同步项目进度
#[tauri::command]
pub async fn sync_project_progress(
    id: Id,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<ProjectResponse>, String> {
    match project_service.sync_project_progress(&id).await {
        Ok(project) => Ok(ApiResponse::success(ProjectResponse::from(project))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Project not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to sync project progress: {}", e))),
    }
}

/// 获取风险项目
#[tauri::command]
pub async fn get_at_risk_projects(
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<Vec<ProjectResponse>>, String> {
    match project_service.get_at_risk_projects().await {
        Ok(projects) => {
            let project_responses: Vec<ProjectResponse> = projects.into_iter()
                .map(ProjectResponse::from)
                .collect();
            Ok(ApiResponse::success(project_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get at-risk projects: {}", e))),
    }
}

/// 归档项目
#[tauri::command]
pub async fn archive_project(
    id: Id,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<ProjectResponse>, String> {
    match project_service.archive_project(&id).await {
        Ok(project) => Ok(ApiResponse::success(ProjectResponse::from(project))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Project not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to archive project: {}", e))),
    }
}

/// 克隆项目
#[tauri::command]
pub async fn clone_project(
    id: Id,
    request: CloneProjectRequest,
    project_service: State<'_, Arc<ProjectAppService>>,
) -> Result<ApiResponse<ProjectResponse>, String> {
    match project_service.clone_project(&id, request.new_name).await {
        Ok(project) => Ok(ApiResponse::success(ProjectResponse::from(project))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Project not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to clone project: {}", e))),
    }
}
