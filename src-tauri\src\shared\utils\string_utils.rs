// String Utilities - 字符串工具函数

use std::collections::HashMap;

pub fn truncate(s: &str, max_len: usize) -> String {
    if s.len() <= max_len {
        s.to_string()
    } else {
        format!("{}...", &s[..max_len.saturating_sub(3)])
    }
}

pub fn slugify(s: &str) -> String {
    s.to_lowercase()
        .chars()
        .map(|c| if c.is_alphanumeric() { c } else { '-' })
        .collect::<String>()
        .split('-')
        .filter(|part| !part.is_empty())
        .collect::<Vec<_>>()
        .join("-")
}

pub fn extract_hashtags(text: &str) -> Vec<String> {
    text.split_whitespace()
        .filter(|word| word.starts_with('#') && word.len() > 1)
        .map(|tag| tag[1..].to_string())
        .collect()
}

pub fn extract_mentions(text: &str) -> Vec<String> {
    text.split_whitespace()
        .filter(|word| word.starts_with('@') && word.len() > 1)
        .map(|mention| mention[1..].to_string())
        .collect()
}

pub fn word_count(text: &str) -> usize {
    text.split_whitespace().count()
}

pub fn char_count(text: &str) -> usize {
    text.chars().count()
}

pub fn reading_time_minutes(text: &str, words_per_minute: usize) -> usize {
    let words = word_count(text);
    (words + words_per_minute - 1) / words_per_minute // ceiling division
}

pub fn highlight_search_terms(text: &str, search_terms: &[String]) -> String {
    let mut result = text.to_string();
    for term in search_terms {
        if !term.is_empty() {
            result = result.replace(term, &format!("<mark>{}</mark>", term));
        }
    }
    result
}

pub fn template_replace(template: &str, variables: &HashMap<String, String>) -> String {
    let mut result = template.to_string();
    for (key, value) in variables {
        let placeholder = format!("{{{{{}}}}}", key);
        result = result.replace(&placeholder, value);
    }
    result
}

pub fn is_empty_or_whitespace(s: &str) -> bool {
    s.trim().is_empty()
}

pub fn normalize_whitespace(s: &str) -> String {
    s.split_whitespace().collect::<Vec<_>>().join(" ")
}
