// Domain Events - 领域事件
// 领域内发生的重要业务事件

use serde::{Deserialize, Serialize};
use std::time::SystemTime;

pub mod user_events;
pub mod project_events;
pub mod task_events;
pub mod area_events;
pub mod resource_events;
pub mod system_events;

// 重新导出事件类型
pub use user_events::*;
pub use project_events::*;
pub use task_events::*;
pub use area_events::*;
pub use resource_events::*;
pub use system_events::*;

// 基础领域事件特征
pub trait DomainEvent {
    fn event_type(&self) -> &'static str;
    fn occurred_at(&self) -> SystemTime;
    fn aggregate_id(&self) -> String;
}

// 事件元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventMetadata {
    pub event_id: String,
    pub event_type: String,
    pub aggregate_id: String,
    pub aggregate_type: String,
    pub occurred_at: SystemTime,
    pub version: u64,
}

// 事件存储接口
#[async_trait::async_trait]
pub trait EventStore {
    async fn append_events(&self, events: Vec<Box<dyn DomainEvent>>) -> crate::shared::errors::Result<()>;
    async fn get_events(&self, aggregate_id: &str) -> crate::shared::errors::Result<Vec<Box<dyn DomainEvent>>>;
}
