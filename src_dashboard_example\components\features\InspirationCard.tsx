import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Input } from '../ui/input'
import { Button } from '../ui/button'
import { useUIStore } from '../../store/uiStore'
import type { InboxItem } from './InboxItem'

interface InspirationCardProps {
  className?: string
  size?: 'normal' | 'compact'
}

export function InspirationCard({ className = '', size = 'normal' }: InspirationCardProps) {
  const [quickInputContent, setQuickInputContent] = useState('')
  const [isSubmittingQuickInput, setIsSubmittingQuickInput] = useState(false)
  const [showTagSuggestions, setShowTagSuggestions] = useState(false)
  const [tagSuggestionIndex, setTagSuggestionIndex] = useState(0)
  const [currentTagInput, setCurrentTagInput] = useState('')
  
  const { addNotification } = useUIStore()

  // 常用标签（基础标签）
  const baseTags = ['工作', '学习', '生活', '想法', '待办', '重要', '创意', '灵感']

  // 获取用户自定义标签
  const getUserTags = (): string[] => {
    try {
      const saved = localStorage.getItem('paolife-user-tags')
      return saved ? JSON.parse(saved) : []
    } catch {
      return []
    }
  }

  // 保存用户自定义标签
  const saveUserTag = (tag: string) => {
    try {
      const userTags = getUserTags()
      if (!userTags.includes(tag) && !baseTags.includes(tag)) {
        const updatedTags = [...userTags, tag]
        localStorage.setItem('paolife-user-tags', JSON.stringify(updatedTags))
      }
    } catch (error) {
      console.error('Failed to save user tag:', error)
    }
  }

  // 合并所有标签
  const commonTags = [...baseTags, ...getUserTags()]

  const handleQuickInputSubmit = async () => {
    if (!quickInputContent.trim()) return

    setIsSubmittingQuickInput(true)
    try {
      // 提取标签和内容
      const content = quickInputContent.trim()
      const tags: string[] = []
      let cleanContent = content

      // 提取所有#标签
      const tagMatches = content.match(/#[\u4e00-\u9fa5\w]+/g)
      if (tagMatches) {
        tagMatches.forEach(match => {
          const tag = match.slice(1) // 移除#
          if (tag && !tags.includes(tag)) {
            tags.push(tag)
          }
        })
        // 从内容中移除标签
        cleanContent = content.replace(/#[\u4e00-\u9fa5\w]+/g, '').replace(/\s+/g, ' ').trim()
      }

      const newItem: InboxItem = {
        id: Date.now().toString(),
        content: cleanContent,
        type: 'idea',
        priority: 'medium',
        tags: tags,
        processed: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      // 保存到localStorage（与收件箱页面保持一致）
      const existingItems = JSON.parse(localStorage.getItem('paolife-inbox-items') || '[]')
      const updatedItems = [newItem, ...existingItems]
      localStorage.setItem('paolife-inbox-items', JSON.stringify(updatedItems))

      // 触发自定义事件通知收件箱页面刷新
      document.dispatchEvent(new CustomEvent('inspiration-added', { detail: newItem }))

      setQuickInputContent('')
      setShowTagSuggestions(false)
      setCurrentTagInput('')

      addNotification({
        type: 'success',
        title: '灵感已记录',
        message: tags.length > 0 ? `已添加到收件箱，包含${tags.length}个标签` : '已添加到收件箱'
      })
    } catch (error) {
      console.error('Failed to create quick input item:', error)
      addNotification({
        type: 'error',
        title: '记录失败',
        message: '请重试'
      })
    } finally {
      setIsSubmittingQuickInput(false)
    }
  }

  const handleQuickInputKeyDown = (e: React.KeyboardEvent) => {
    if (showTagSuggestions) {
      const filteredTags = commonTags.filter(tag => tag.toLowerCase().includes(currentTagInput.toLowerCase()))
      const hasNewTag = currentTagInput && !commonTags.some(tag => tag.toLowerCase().includes(currentTagInput.toLowerCase()))
      const totalOptions = filteredTags.length + (hasNewTag ? 1 : 0)

      if (e.key === 'ArrowDown') {
        e.preventDefault()
        setTagSuggestionIndex((prev) => (prev + 1) % totalOptions)
      } else if (e.key === 'ArrowUp') {
        e.preventDefault()
        setTagSuggestionIndex((prev) => (prev - 1 + totalOptions) % totalOptions)
      } else if (e.key === 'Enter') {
        e.preventDefault()
        // 如果当前输入了新标签且选中的是新标签选项，使用新标签
        if (hasNewTag && tagSuggestionIndex === filteredTags.length) {
          handleTagSelect(currentTagInput)
        } else if (filteredTags.length > 0 && tagSuggestionIndex < filteredTags.length) {
          // 否则使用选中的已有标签
          handleTagSelect(filteredTags[tagSuggestionIndex])
        }
      } else if (e.key === 'Escape') {
        setShowTagSuggestions(false)
        setCurrentTagInput('')
      }
    } else if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault()
      handleQuickInputSubmit()
    }
  }

  const handleQuickInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuickInputContent(value)

    // 检查是否输入了#来触发标签选择
    const lastChar = value[value.length - 1]
    const beforeLastChar = value[value.length - 2]
    
    if (lastChar === '#' && (beforeLastChar === ' ' || beforeLastChar === undefined || value.length === 1)) {
      setShowTagSuggestions(true)
      setTagSuggestionIndex(0)
      setCurrentTagInput('')
    } else if (showTagSuggestions) {
      // 如果正在显示标签建议，更新当前标签输入
      const hashIndex = value.lastIndexOf('#')
      if (hashIndex !== -1) {
        const tagInput = value.slice(hashIndex + 1)
        setCurrentTagInput(tagInput)
        
        // 如果输入了空格，完成标签输入
        if (tagInput.includes(' ')) {
          const tag = tagInput.split(' ')[0]
          if (tag) {
            handleTagSelect(tag)
          }
        }
      } else {
        setShowTagSuggestions(false)
        setCurrentTagInput('')
      }
    }
  }

  const handleTagSelect = (tag: string) => {
    // 如果是新标签，保存到用户标签列表
    if (!baseTags.includes(tag) && !getUserTags().includes(tag)) {
      saveUserTag(tag)
    }

    const hashIndex = quickInputContent.lastIndexOf('#')
    if (hashIndex !== -1) {
      const beforeHash = quickInputContent.slice(0, hashIndex)
      const afterTag = quickInputContent.slice(hashIndex + 1 + currentTagInput.length)
      setQuickInputContent(`${beforeHash}#${tag} ${afterTag}`)
    }
    setShowTagSuggestions(false)
    setCurrentTagInput('')
  }

  const cardHeight = size === 'compact' ? 'min-h-[90px]' : 'min-h-[180px]'
  const titleSize = size === 'compact' ? 'text-sm' : 'text-base'
  const descriptionSize = size === 'compact' ? 'text-xs' : 'text-xs'

  return (
    <Card className={`${cardHeight} flex flex-col bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200 ${className}`}>
      <CardHeader className={`${size === 'compact' ? 'pb-2' : 'pb-3'} flex-shrink-0`}>
        <CardTitle className={`${titleSize} font-semibold flex items-center gap-2`}>
          <span className="text-yellow-600">💡</span>
          快速记录灵感
        </CardTitle>
        {size === 'normal' && (
          <CardDescription className={`${descriptionSize} text-muted-foreground`}>
            记录你的想法、创意和灵感
          </CardDescription>
        )}
      </CardHeader>
      <CardContent className={`flex-1 flex flex-col ${size === 'compact' ? 'justify-center' : 'justify-between'}`}>
        <div className="flex gap-2 relative">
          <div className="flex-1 relative">
            <Input
              value={quickInputContent}
              onChange={handleQuickInputChange}
              onKeyDown={handleQuickInputKeyDown}
              placeholder="记录你的想法、创意或灵感... (输入#添加标签，Ctrl+Enter 快速提交)"
              className={`${size === 'compact' ? 'text-xs h-8' : 'text-sm'} flex-1 border-yellow-200 focus:border-yellow-400 focus:ring-yellow-400`}
              disabled={isSubmittingQuickInput}
            />
            
            {/* 标签建议下拉框 */}
            {showTagSuggestions && (() => {
              const filteredTags = commonTags.filter(tag => tag.toLowerCase().includes(currentTagInput.toLowerCase()))
              const hasNewTag = currentTagInput && !commonTags.some(tag => tag.toLowerCase().includes(currentTagInput.toLowerCase()))

              return (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 max-h-40 overflow-y-auto">
                  {filteredTags.map((tag, index) => (
                    <div
                      key={tag}
                      className={`px-3 py-2 cursor-pointer text-sm ${
                        index === tagSuggestionIndex ? 'bg-yellow-100 text-yellow-800' : 'hover:bg-gray-100'
                      }`}
                      onClick={() => handleTagSelect(tag)}
                    >
                      #{tag}
                    </div>
                  ))}
                  {hasNewTag && (
                    <div
                      className={`px-3 py-2 cursor-pointer text-sm ${
                        tagSuggestionIndex === filteredTags.length ? 'bg-yellow-100 text-yellow-800' : 'hover:bg-gray-100'
                      }`}
                      onClick={() => handleTagSelect(currentTagInput)}
                    >
                      #{currentTagInput} (新建)
                    </div>
                  )}
                </div>
              )
            })()}
          </div>
          
          <Button
            onClick={handleQuickInputSubmit}
            disabled={!quickInputContent.trim() || isSubmittingQuickInput}
            size={size === 'compact' ? 'sm' : 'default'}
            className={`${size === 'compact' ? 'h-8 px-3 text-xs' : 'h-9 px-4'} bg-yellow-600 hover:bg-yellow-700 text-white`}
          >
            {isSubmittingQuickInput ? (
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin" />
                <span>记录中...</span>
              </div>
            ) : (
              '记录'
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
