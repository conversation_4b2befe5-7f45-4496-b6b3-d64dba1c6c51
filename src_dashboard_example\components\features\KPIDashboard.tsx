import { useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { cn } from '../../lib/utils'
// {{ AURA-X: Add - 导入国际化上下文. Approval: 寸止(ID:1738157400). }}
import { useLanguage } from '../../contexts/LanguageContext'
import { calculateKPIProgress } from '../../lib/kpiProgressCalculator'
import type { ProjectKPI, AreaMetric } from '../../../../shared/types'

// 通用KPI接口
interface GenericKPI {
  id: string
  name: string
  value: string
  target: string | null
  unit: string | null
  direction: string
  updatedAt: Date
}

interface KPIDashboardProps {
  kpis: (ProjectKPI | AreaMetric)[]
  className?: string
}

interface DashboardMetrics {
  totalKPIs: number
  achievedKPIs: number
  onTrackKPIs: number
  atRiskKPIs: number
  behindKPIs: number
  averageProgress: number
  topPerformer: GenericKPI | null
  needsAttention: GenericKPI | null
}

export function KPIDashboard({ kpis, className }: KPIDashboardProps) {
  // {{ AURA-X: Add - 国际化支持. Approval: 寸止(ID:1738157400). }}
  const { t } = useLanguage()

  const metrics = useMemo((): DashboardMetrics => {
    if (kpis.length === 0) {
      return {
        totalKPIs: 0,
        achievedKPIs: 0,
        onTrackKPIs: 0,
        atRiskKPIs: 0,
        behindKPIs: 0,
        averageProgress: 0,
        topPerformer: null,
        needsAttention: null
      }
    }

    const kpiData = kpis.map(kpi => {
      if (!kpi.target) {
        return { kpi, progress: 0, hasTarget: false }
      }

      // 使用统一的进度计算器
      const progress = calculateKPIProgress(kpi)
      return { kpi, progress, hasTarget: true }
    })

    const kpisWithTargets = kpiData.filter(d => d.hasTarget)
    
    const achievedKPIs = kpisWithTargets.filter(d => d.progress >= 100).length
    const onTrackKPIs = kpisWithTargets.filter(d => d.progress >= 75 && d.progress < 100).length
    const atRiskKPIs = kpisWithTargets.filter(d => d.progress >= 50 && d.progress < 75).length
    const behindKPIs = kpisWithTargets.filter(d => d.progress < 50).length

    const averageProgress = kpisWithTargets.length > 0
      ? kpisWithTargets.reduce((sum, d) => sum + d.progress, 0) / kpisWithTargets.length
      : 0

    const topPerformer = kpisWithTargets.length > 0
      ? kpisWithTargets.reduce((max, current) => 
          current.progress > max.progress ? current : max
        ).kpi
      : null

    const needsAttention = kpisWithTargets.length > 0
      ? kpisWithTargets.reduce((min, current) => 
          current.progress < min.progress ? current : min
        ).kpi
      : null

    return {
      totalKPIs: kpis.length,
      achievedKPIs,
      onTrackKPIs,
      atRiskKPIs,
      behindKPIs,
      averageProgress: Math.round(averageProgress),
      topPerformer,
      needsAttention
    }
  }, [kpis])

  const getHealthScore = () => {
    if (metrics.totalKPIs === 0) return { score: 0, label: t('pages.areas.detail.kpiDashboard.healthLabels.noData'), color: 'text-gray-500' }

    const score = (metrics.achievedKPIs * 100 + metrics.onTrackKPIs * 75 + metrics.atRiskKPIs * 50 + metrics.behindKPIs * 25) / metrics.totalKPIs

    if (score >= 90) return { score: Math.round(score), label: t('pages.areas.detail.kpiDashboard.healthLabels.excellent'), color: 'text-green-600' }
    if (score >= 75) return { score: Math.round(score), label: t('pages.areas.detail.kpiDashboard.healthLabels.good'), color: 'text-blue-600' }
    if (score >= 60) return { score: Math.round(score), label: t('pages.areas.detail.kpiDashboard.healthLabels.fair'), color: 'text-yellow-600' }
    return { score: Math.round(score), label: t('pages.areas.detail.kpiDashboard.healthLabels.needsImprovement'), color: 'text-red-600' }
  }

  const healthScore = getHealthScore()

  if (kpis.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{t('pages.areas.detail.kpiDashboard.title')}</CardTitle>
          <CardDescription>{t('pages.areas.detail.kpiDashboard.noKPIsToDisplay')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <div className="text-4xl mb-2">📊</div>
            <p className="text-sm">{t('pages.areas.detail.kpiDashboard.addKPIsToSee')}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {t('pages.areas.detail.kpiDashboard.title')}
          <Badge variant="outline" className="text-xs">
            {metrics.totalKPIs} KPIs
          </Badge>
        </CardTitle>
        <CardDescription>
          {t('pages.areas.detail.kpiDashboard.realTimeOverview')}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Health Score */}
        <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
          <div className="text-3xl font-bold mb-2 text-blue-900">
            {healthScore.score}
          </div>
          <div className={cn('text-lg font-medium mb-1', healthScore.color)}>
            {healthScore.label}
          </div>
          <div className="text-sm text-blue-700">
            {t('pages.areas.detail.kpiDashboard.overallHealthScore')}
          </div>
        </div>

        {/* Status Distribution */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="text-2xl font-bold text-green-600 mb-1">
              {metrics.achievedKPIs}
            </div>
            <div className="text-xs text-green-700 font-medium">{t('pages.areas.detail.kpiDashboard.achieved')}</div>
            <div className="text-xs text-green-600">{t('pages.areas.detail.kpiDashboard.statusDescriptions.achieved')}</div>
          </div>

          <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {metrics.onTrackKPIs}
            </div>
            <div className="text-xs text-blue-700 font-medium">{t('pages.areas.detail.kpiDashboard.onTrack')}</div>
            <div className="text-xs text-blue-600">{t('pages.areas.detail.kpiDashboard.statusDescriptions.onTrack')}</div>
          </div>

          <div className="text-center p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="text-2xl font-bold text-yellow-600 mb-1">
              {metrics.atRiskKPIs}
            </div>
            <div className="text-xs text-yellow-700 font-medium">{t('pages.areas.detail.kpiDashboard.atRisk')}</div>
            <div className="text-xs text-yellow-600">{t('pages.areas.detail.kpiDashboard.statusDescriptions.atRisk')}</div>
          </div>

          <div className="text-center p-4 bg-red-50 rounded-lg border border-red-200">
            <div className="text-2xl font-bold text-red-600 mb-1">
              {metrics.behindKPIs}
            </div>
            <div className="text-xs text-red-700 font-medium">{t('pages.areas.detail.kpiDashboard.behind')}</div>
            <div className="text-xs text-red-600">{t('pages.areas.detail.kpiDashboard.statusDescriptions.behind')}</div>
          </div>
        </div>

        {/* Average Progress */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">{t('pages.areas.detail.kpiDashboard.averageProgress')}</h4>
            <span className="text-sm font-bold">{metrics.averageProgress}%</span>
          </div>
          <Progress 
            value={metrics.averageProgress} 
            className="h-3"
          />
        </div>

        {/* Key Insights */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Top Performer */}
          {metrics.topPerformer && (
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center gap-2 mb-2">
                <div className="text-green-600">🏆</div>
                <h4 className="text-sm font-medium text-green-800">{t('pages.areas.detail.kpiDashboard.topPerformer')}</h4>
              </div>
              <div className="text-sm font-medium text-green-900">
                {metrics.topPerformer.name}
              </div>
              <div className="text-xs text-green-700">
                {metrics.topPerformer.value}
                {metrics.topPerformer.unit && ` ${metrics.topPerformer.unit}`}
                {metrics.topPerformer.target && (
                  <span> / {metrics.topPerformer.target}{metrics.topPerformer.unit && ` ${metrics.topPerformer.unit}`}</span>
                )}
              </div>
            </div>
          )}

          {/* Needs Attention */}
          {metrics.needsAttention && (
            <div className="p-4 bg-red-50 rounded-lg border border-red-200">
              <div className="flex items-center gap-2 mb-2">
                <div className="text-red-600">⚠️</div>
                <h4 className="text-sm font-medium text-red-800">{t('pages.areas.detail.kpiDashboard.needsAttention')}</h4>
              </div>
              <div className="text-sm font-medium text-red-900">
                {metrics.needsAttention.name}
              </div>
              <div className="text-xs text-red-700">
                {metrics.needsAttention.value}
                {metrics.needsAttention.unit && ` ${metrics.needsAttention.unit}`}
                {metrics.needsAttention.target && (
                  <span> / {metrics.needsAttention.target}{metrics.needsAttention.unit && ` ${metrics.needsAttention.unit}`}</span>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="p-4 bg-muted/50 rounded-lg">
          <h4 className="text-sm font-medium mb-3">{t('pages.areas.detail.kpiDashboard.quickActions')}</h4>
          <div className="space-y-2 text-sm">
            {metrics.behindKPIs > 0 && (
              <div className="flex items-center gap-2 text-red-700">
                <span>•</span>
                <span>{t('pages.areas.detail.kpiDashboard.reviewUnderperforming', { count: metrics.behindKPIs })}</span>
              </div>
            )}
            {metrics.achievedKPIs > 0 && (
              <div className="flex items-center gap-2 text-green-700">
                <span>•</span>
                <span>{t('pages.areas.detail.kpiDashboard.celebrateAchieved', { count: metrics.achievedKPIs })}</span>
              </div>
            )}
            {metrics.onTrackKPIs > 0 && (
              <div className="flex items-center gap-2 text-blue-700">
                <span>•</span>
                <span>Maintain momentum on {metrics.onTrackKPIs} on-track KPI{metrics.onTrackKPIs > 1 ? 's' : ''}</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default KPIDashboard
