import { Outlet, useLocation, useParams } from 'react-router-dom'
import { useEffect, useState } from 'react'
import Navigation from './Navigation'
import Breadcrumbs from './Breadcrumbs'
import { ErrorBoundary } from './ErrorBoundary'
import { ToastContainer } from '../ui/toast'
import { useUIStore } from '../../store/uiStore'
import { useNavigationStore } from '../../store/navigationStore'
import { cn } from '../../lib/utils'
import { windowApi } from '../../lib/api'


export function Layout() {
  const location = useLocation()
  const params = useParams()
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [isMaximized, setIsMaximized] = useState(false)
  const { notifications, removeNotification } = useUIStore()
  const { isCollapsed } = useNavigationStore()

  // {{ AURA-X: Add - 获取当前页面的项目或领域ID. Approval: 寸止(ID:1738157400). }}
  const projectId = params.projectId
  const areaId = params.areaId

  // 监听窗口最大化状态
  useEffect(() => {
    const checkMaximizedState = async () => {
      try {
        const maximized = await windowApi.isMaximized()
        setIsMaximized(maximized)
      } catch (error) {
        console.error('Failed to check window maximized state:', error)
      }
    }

    checkMaximizedState()

    // 定期检查状态（可以优化为事件监听）
    const interval = setInterval(checkMaximizedState, 1000)
    return () => clearInterval(interval)
  }, [])

  // Handle page transitions - disabled to prevent flashing
  // useEffect(() => {
  //   setIsTransitioning(true)
  //   const timer = setTimeout(() => setIsTransitioning(false), 150)
  //   return () => clearTimeout(timer)
  // }, [location.pathname])

  return (
    <div className="flex h-screen bg-background">
      {/* Left Sidebar Navigation */}
      <aside className={cn(
        "sidebar-layout flex-shrink-0 scrollbar-hidden overflow-y-auto transition-all duration-300",
        isCollapsed ? "w-16" : "w-64"
      )}>
        <Navigation />
      </aside>

      {/* Main Content Area */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* Top Bar with Breadcrumbs */}
        <header className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 drag-region">
          <div className="flex items-center justify-between px-6 py-3">
            <Breadcrumbs />

            {/* Window Controls (for Electron) - Modern Windows 11 Style */}
            <div className="flex items-center no-drag">
              {/* 最小化按钮 */}
              <button
                type="button"
                onClick={async () => {
                  try {
                    await windowApi.minimize()
                  } catch (error) {
                    console.error('Failed to minimize window:', error)
                  }
                }}
                className="window-control-btn"
                title="最小化"
                aria-label="Minimize"
              >
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                  <path d="M2 6h8" stroke="currentColor" strokeWidth="1" strokeLinecap="round"/>
                </svg>
              </button>

              {/* 最大化/还原按钮 */}
              <button
                type="button"
                onClick={async () => {
                  try {
                    await windowApi.maximize()
                    // 立即更新状态
                    const newState = await windowApi.isMaximized()
                    setIsMaximized(newState)
                  } catch (error) {
                    console.error('Failed to maximize/restore window:', error)
                  }
                }}
                className="window-control-btn"
                title={isMaximized ? "还原" : "最大化"}
                aria-label={isMaximized ? "Restore" : "Maximize"}
              >
                {isMaximized ? (
                  // 还原图标
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                    <path d="M3 3h6v6H3V3z" stroke="currentColor" strokeWidth="1" fill="none"/>
                    <path d="M5 5V2h5v5h-3" stroke="currentColor" strokeWidth="1" fill="none"/>
                  </svg>
                ) : (
                  // 最大化图标
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                    <rect x="2" y="2" width="8" height="8" stroke="currentColor" strokeWidth="1" fill="none"/>
                  </svg>
                )}
              </button>

              {/* 关闭按钮 */}
              <button
                type="button"
                onClick={async () => {
                  try {
                    await windowApi.close()
                  } catch (error) {
                    console.error('Failed to close window:', error)
                  }
                }}
                className="window-control-btn close"
                title="关闭"
                aria-label="Close"
              >
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                  <path d="M3 3l6 6M9 3l-6 6" stroke="currentColor" strokeWidth="1" strokeLinecap="round"/>
                </svg>
              </button>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto scrollbar-hidden">
            <ErrorBoundary>
              <div className="opacity-100 transform translate-y-0">
                <Outlet />
              </div>
            </ErrorBoundary>
          </div>
        </div>
      </main>

      {/* Toast Notifications */}
      <ToastContainer notifications={notifications} onRemove={removeNotification} />


    </div>
  )
}

export default Layout
