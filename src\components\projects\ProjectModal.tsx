import type { Component } from 'solid-js';
import { createStore } from 'solid-js/store';
import { Show, onMount, For } from 'solid-js';
import type { Project, Area, ProjectStatus } from './types';

interface ProjectModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (project: Project) => void;
    project: Project | null;
    getAreas: () => Area[]; // Changed to a function to simulate fetching
}

const initialProjectState: Omit<Project, 'id' | 'updatedAt' | 'statusHistory'> = {
    name: '',
    description: '',
    status: 'not-started',
    areaId: '',
    startDate: new Date().toISOString().split('T')[0],
    dueDate: '',
    tasks: [],
    resources: [],
    milestones: [],
    kpis: [],
};

export const ProjectModal: Component<ProjectModalProps> = (props) => {
    const [formData, setFormData] = createStore<Project>(initialProjectState as Project);
    let dialogRef: HTMLDialogElement | undefined;

    onMount(() => {
        if(props.project) {
           setFormData(props.project);
        } else {
           setFormData(initialProjectState as Project);
        }
    });

    // Effect to handle dialog visibility
    onMount(() => {
        if (dialogRef) {
            if (props.isOpen && !dialogRef.open) {
                dialogRef.showModal();
            } else if (!props.isOpen && dialogRef.open) {
                dialogRef.close();
            }
        }
    });

    const handleSubmit = (e: Event) => {
        e.preventDefault();
        if(!formData.name) return;
        props.onSave(formData);
    };

    const handleClose = () => props.onClose();

    return (
        <dialog ref={dialogRef} onClose={handleClose} class="p-0 rounded-lg shadow-xl bg-white dark:bg-gray-800 w-full max-w-2xl backdrop:bg-black/50 m-auto">
            <div class="p-6">
                <div class="flex justify-between items-center pb-4 border-b dark:border-gray-700">
                    <h2 class="text-xl font-bold">{props.project ? '编辑项目' : '创建新项目'}</h2>
                    <button onClick={handleClose} class="text-gray-400 hover:text-gray-600 p-1 rounded-full">&times;</button>
                </div>
                <form onSubmit={handleSubmit} class="mt-6 space-y-4">
                    <div>
                        <label class="text-sm font-medium">项目名称 *</label>
                        <input type="text" value={formData.name} onInput={e => setFormData('name', e.currentTarget.value)} required class="mt-1 w-full rounded-md bg-gray-100 dark:bg-gray-700 border-transparent focus:ring-blue-500" />
                    </div>
                    <div>
                        <label class="text-sm font-medium">描述</label>
                        <textarea rows="3" value={formData.description} onInput={e => setFormData('description', e.currentTarget.value)} class="mt-1 w-full rounded-md bg-gray-100 dark:bg-gray-700 border-transparent focus:ring-blue-500"></textarea>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                           <label class="text-sm font-medium">所属领域</label>
                           <select value={formData.areaId} onChange={e => setFormData('areaId', e.currentTarget.value)} class="mt-1 w-full rounded-md bg-gray-100 dark:bg-gray-700 border-transparent focus:ring-blue-500">
                               <option value="">-- 选择领域 --</option>
                               <For each={props.getAreas()}>{area => <option value={area.id}>{area.name}</option>}</For>
                           </select>
                        </div>
                        <div>
                           <label class="text-sm font-medium">状态</label>
                           <select value={formData.status} onChange={e => setFormData('status', e.currentTarget.value as ProjectStatus)} class="mt-1 w-full rounded-md bg-gray-100 dark:bg-gray-700 border-transparent focus:ring-blue-500">
                               <option value="not-started">未开始</option>
                               <option value="in-progress">进行中</option>
                               <option value="paused">已暂停</option>
                           </select>
                        </div>
                    </div>
                     <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="text-sm font-medium">开始日期</label>
                            <input type="date" value={formData.startDate || ''} onInput={e => setFormData('startDate', e.currentTarget.value)} class="mt-1 w-full rounded-md bg-gray-100 dark:bg-gray-700 border-transparent focus:ring-blue-500" />
                        </div>
                         <div>
                            <label class="text-sm font-medium">截止日期</label>
                            <input type="date" value={formData.dueDate || ''} onInput={e => setFormData('dueDate', e.currentTarget.value)} class="mt-1 w-full rounded-md bg-gray-100 dark:bg-gray-700 border-transparent focus:ring-blue-500" />
                        </div>
                    </div>
                    <div class="pt-4 flex justify-end gap-3 border-t dark:border-gray-700">
                        <button type="button" onClick={handleClose} class="px-4 py-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-sm font-semibold">取消</button>
                        <button type="submit" class="px-4 py-2 rounded-lg bg-blue-600 hover:bg-blue-700 text-white text-sm font-semibold">保存</button>
                    </div>
                </form>
            </div>
        </dialog>
    );
};

