// Database Connection - 数据库连接

use crate::infrastructure::database::{DatabaseConfig, DatabasePool};
use crate::shared::errors::{AppError, Result};
use sqlx::{Pool, Sqlite, SqlitePool};
use std::sync::Arc;

pub struct DatabaseConnection {
    pool: DatabasePool,
}

impl DatabaseConnection {
    pub async fn new(config: DatabaseConfig) -> Result<Self> {
        let pool = SqlitePool::connect(&config.database_url)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to connect to database: {}", e)))?;

        Ok(Self {
            pool: Arc::new(pool),
        })
    }

    pub fn pool(&self) -> DatabasePool {
        self.pool.clone()
    }

    pub async fn health_check(&self) -> Result<()> {
        sqlx::query("SELECT 1")
            .execute(&*self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Health check failed: {}", e)))?;
        
        Ok(())
    }

    pub async fn close(&self) {
        self.pool.close().await;
    }
}
