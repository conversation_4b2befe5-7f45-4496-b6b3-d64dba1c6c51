/**
 * 习惯追踪主组件
 * 提供完整的习惯管理和追踪功能
 */

import { createSignal, createEffect, onMount, For, Show } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { But<PERSON> } from '../ui/button'
import { Badge } from '../ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { 
  Plus, 
  Target, 
  Calendar,
  BarChart3,
  Settings,
  Flame,
  TrendingUp
} from 'lucide-solid'
import { cn } from '../../lib/utils'

import type {
  HabitTrackerProps,
  BaseHabit,
  HabitRecord,
  HabitStatistics,
  HabitTrackerConfig,
  DEFAULT_HABIT_CONFIG
} from './types'

// 导入子组件
import HabitList from './HabitList'
import HabitDialog from './HabitDialog'
import HabitStatistics from './HabitStatistics'
import HabitCalendar from './HabitCalendar'
import HabitChart from './HabitChart'

export function HabitTracker(props: HabitTrackerProps) {
  // 状态管理
  const [habits, setHabits] = createSignal<BaseHabit[]>(props.initialHabits || [])
  const [records, setRecords] = createSignal<HabitRecord[]>([])
  const [statistics, setStatistics] = createSignal<HabitStatistics | null>(null)
  const [loading, setLoading] = createSignal(false)
  const [error, setError] = createSignal<string | null>(null)
  
  // 对话框状态
  const [showCreateDialog, setShowCreateDialog] = createSignal(false)
  const [editingHabit, setEditingHabit] = createSignal<BaseHabit | null>(null)
  
  // 视图状态
  const [activeTab, setActiveTab] = createSignal('habits')
  const [selectedDate, setSelectedDate] = createSignal(new Date())
  
  // 配置合并
  const config = (): HabitTrackerConfig => ({
    ...DEFAULT_HABIT_CONFIG,
    ...props.config
  })

  // 加载习惯数据
  const loadHabits = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const habitsData = await props.dataSource.getHabits(props.areaId)
      setHabits(habitsData)
      
      // 加载记录数据
      const allRecords: HabitRecord[] = []
      for (const habit of habitsData) {
        const habitRecords = await props.dataSource.getRecords(habit.id, {
          start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 最近30天
          end: new Date()
        })
        allRecords.push(...habitRecords)
      }
      setRecords(allRecords)
      
      // 加载统计数据
      if (config().showStatistics) {
        const stats = await props.dataSource.getStatistics(props.areaId)
        setStatistics(stats)
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load habits'
      setError(errorMessage)
      props.eventHandlers?.onError?.(err instanceof Error ? err : new Error(errorMessage), 'loadHabits')
    } finally {
      setLoading(false)
    }
  }

  // 创建习惯
  const handleCreateHabit = async (data: any) => {
    try {
      const newHabit = await props.dataSource.createHabit(data)
      setHabits(prev => [...prev, newHabit])
      setShowCreateDialog(false)
      
      props.eventHandlers?.onCreate?.(newHabit)
      
      // 重新加载统计数据
      if (config().showStatistics) {
        const stats = await props.dataSource.getStatistics(props.areaId)
        setStatistics(stats)
      }
      
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to create habit')
      props.eventHandlers?.onError?.(error, 'createHabit')
    }
  }

  // 更新习惯
  const handleUpdateHabit = async (habitId: string, data: any) => {
    try {
      const updatedHabit = await props.dataSource.updateHabit(habitId, data)
      setHabits(prev => prev.map(h => h.id === habitId ? updatedHabit : h))
      setEditingHabit(null)
      
      props.eventHandlers?.onUpdate?.(updatedHabit, data)
      
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to update habit')
      props.eventHandlers?.onError?.(error, 'updateHabit')
    }
  }

  // 删除习惯
  const handleDeleteHabit = async (habitId: string) => {
    try {
      await props.dataSource.deleteHabit(habitId)
      setHabits(prev => prev.filter(h => h.id !== habitId))
      setRecords(prev => prev.filter(r => r.habitId !== habitId))
      
      props.eventHandlers?.onDelete?.(habitId)
      
      // 重新加载统计数据
      if (config().showStatistics) {
        const stats = await props.dataSource.getStatistics(props.areaId)
        setStatistics(stats)
      }
      
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to delete habit')
      props.eventHandlers?.onError?.(error, 'deleteHabit')
    }
  }

  // 切换记录状态
  const handleRecordToggle = async (habitId: string, date: string, completed: boolean) => {
    try {
      // 查找现有记录
      const existingRecord = records().find(r => r.habitId === habitId && r.date === date)
      
      if (existingRecord) {
        // 更新现有记录
        const updatedRecord = await props.dataSource.updateRecord(existingRecord.id, { completed })
        setRecords(prev => prev.map(r => r.id === existingRecord.id ? updatedRecord : r))
      } else {
        // 创建新记录
        const newRecord = await props.dataSource.createRecord({
          habitId,
          date,
          completed,
          value: completed ? 1 : 0
        })
        setRecords(prev => [...prev, newRecord])
      }
      
      // 更新习惯的统计信息
      const habit = habits().find(h => h.id === habitId)
      if (habit) {
        const progress = await props.dataSource.getProgress(habitId)
        const updatedHabit = {
          ...habit,
          streak: progress.currentStreak,
          completionRate: progress.monthProgress
        }
        setHabits(prev => prev.map(h => h.id === habitId ? updatedHabit : h))
        
        // 检查连击成就
        if (completed && progress.currentStreak > habit.longestStreak) {
          props.eventHandlers?.onStreakAchieved?.(habitId, progress.currentStreak)
        }
      }
      
      props.eventHandlers?.onRecord?.(existingRecord || newRecord)
      
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to toggle record')
      props.eventHandlers?.onError?.(error, 'recordToggle')
    }
  }

  // 设置数值记录
  const handleValueSet = async (habitId: string, date: string, value: number) => {
    try {
      const existingRecord = records().find(r => r.habitId === habitId && r.date === date)
      
      if (existingRecord) {
        const updatedRecord = await props.dataSource.updateRecord(existingRecord.id, { 
          value, 
          completed: value > 0 
        })
        setRecords(prev => prev.map(r => r.id === existingRecord.id ? updatedRecord : r))
      } else {
        const newRecord = await props.dataSource.createRecord({
          habitId,
          date,
          completed: value > 0,
          value
        })
        setRecords(prev => [...prev, newRecord])
      }
      
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to set value')
      props.eventHandlers?.onError?.(error, 'valueSet')
    }
  }

  // 初始化
  onMount(() => {
    loadHabits()
  })

  // 监听areaId变化
  createEffect(() => {
    if (props.areaId) {
      loadHabits()
    }
  })

  // 今日完成统计
  const todayStats = () => {
    const today = new Date().toISOString().split('T')[0]
    const todayRecords = records().filter(r => r.date === today && r.completed)
    const activeHabits = habits().filter(h => h.isActive)
    
    return {
      completed: todayRecords.length,
      total: activeHabits.length,
      percentage: activeHabits.length > 0 ? Math.round((todayRecords.length / activeHabits.length) * 100) : 0
    }
  }

  return (
    <div class={cn("space-y-6", props.className)}>
      {/* 错误提示 */}
      <Show when={error()}>
        <Card class="border-red-200 bg-red-50">
          <CardContent class="p-4">
            <p class="text-red-600">{error()}</p>
            <Button variant="outline" size="sm" onClick={loadHabits} class="mt-2">
              Retry
            </Button>
          </CardContent>
        </Card>
      </Show>

      {/* 头部统计 */}
      <Show when={config().showStatistics && !loading()}>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent class="p-4">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm text-muted-foreground">Today's Progress</p>
                  <p class="text-2xl font-bold">{todayStats().completed}/{todayStats().total}</p>
                  <p class="text-xs text-muted-foreground">{todayStats().percentage}% complete</p>
                </div>
                <Target class="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent class="p-4">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm text-muted-foreground">Active Habits</p>
                  <p class="text-2xl font-bold">{habits().filter(h => h.isActive).length}</p>
                </div>
                <Calendar class="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent class="p-4">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm text-muted-foreground">Best Streak</p>
                  <p class="text-2xl font-bold">{Math.max(...habits().map(h => h.longestStreak), 0)}</p>
                </div>
                <Flame class="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent class="p-4">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm text-muted-foreground">Avg Completion</p>
                  <p class="text-2xl font-bold">
                    {habits().length > 0 
                      ? Math.round(habits().reduce((sum, h) => sum + h.completionRate, 0) / habits().length)
                      : 0}%
                  </p>
                </div>
                <TrendingUp class="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      </Show>

      {/* 主要内容 */}
      <Tabs value={activeTab()} onValueChange={setActiveTab}>
        <div class="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="habits">Habits</TabsTrigger>
            <Show when={config().showCalendar}>
              <TabsTrigger value="calendar">Calendar</TabsTrigger>
            </Show>
            <Show when={config().showChart}>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </Show>
          </TabsList>
          
          <Show when={config().allowCreate}>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus class="h-4 w-4 mr-2" />
              Add Habit
            </Button>
          </Show>
        </div>

        {/* 习惯列表 */}
        <TabsContent value="habits">
          <HabitList
            habits={habits()}
            records={records()}
            config={config()}
            selectedDate={selectedDate()}
            onHabitEdit={setEditingHabit}
            onHabitDelete={handleDeleteHabit}
            onRecordToggle={handleRecordToggle}
            onValueSet={handleValueSet}
          />
        </TabsContent>

        {/* 日历视图 */}
        <Show when={config().showCalendar}>
          <TabsContent value="calendar">
            <HabitCalendar
              habits={habits()}
              records={records()}
              selectedDate={selectedDate()}
              onDateSelect={setSelectedDate}
              onRecordToggle={handleRecordToggle}
              view={config().calendarView}
              showHeatmap={true}
            />
          </TabsContent>
        </Show>

        {/* 分析图表 */}
        <Show when={config().showChart}>
          <TabsContent value="analytics">
            <div class="space-y-6">
              <Show when={statistics()}>
                <HabitStatistics
                  statistics={statistics()!}
                  showChart={true}
                  chartType="line"
                />
              </Show>
              
              <HabitChart
                habits={habits()}
                records={records()}
                type={config().chartType || 'line'}
                timeRange="month"
                showLegend={true}
                height={400}
              />
            </div>
          </TabsContent>
        </Show>
      </Tabs>

      {/* 创建对话框 */}
      <Show when={showCreateDialog()}>
        <HabitDialog
          open={showCreateDialog()}
          mode="create"
          areaId={props.areaId}
          onSubmit={handleCreateHabit}
          onCancel={() => setShowCreateDialog(false)}
        />
      </Show>

      {/* 编辑对话框 */}
      <Show when={editingHabit()}>
        <HabitDialog
          open={!!editingHabit()}
          mode="edit"
          habit={editingHabit()!}
          areaId={props.areaId}
          onSubmit={(data) => handleUpdateHabit(editingHabit()!.id, data)}
          onCancel={() => setEditingHabit(null)}
        />
      </Show>
    </div>
  )
}

export default HabitTracker
