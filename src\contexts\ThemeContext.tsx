// Theme Context - 主题管理上下文
// 提供主题切换和配色管理功能

import { createContext, useContext, ParentComponent, createSignal, createEffect } from 'solid-js';
import { createStore } from 'solid-js/store';

// 主题类型定义
export type ThemeMode = 'light' | 'dark' | 'system';
export type ColorScheme = 'blue' | 'green' | 'purple' | 'orange' | 'pink' | 'cyan';

// 主题配置接口
export interface ThemeConfig {
  mode: ThemeMode;
  colorScheme: ColorScheme;
  customColors?: {
    primary?: string;
    secondary?: string;
    accent?: string;
  };
}

// 主题状态接口
export interface ThemeState {
  config: ThemeConfig;
  isDark: boolean;
  systemPrefersDark: boolean;
}

// 主题上下文值接口
export interface ThemeContextValue {
  state: ThemeState;
  actions: {
    setThemeMode: (mode: ThemeMode) => void;
    setColorScheme: (scheme: ColorScheme) => void;
    setCustomColors: (colors: ThemeConfig['customColors']) => void;
    toggleTheme: () => void;
    resetTheme: () => void;
  };
}

// 默认主题配置
const defaultThemeConfig: ThemeConfig = {
  mode: 'system',
  colorScheme: 'blue',
};

// 颜色方案定义
export const colorSchemes: Record<ColorScheme, { primary: string; secondary: string; accent: string }> = {
  blue: {
    primary: 'hsl(221.2 83.2% 53.3%)',
    secondary: 'hsl(210 40% 96%)',
    accent: 'hsl(210 40% 96%)',
  },
  green: {
    primary: 'hsl(142.1 76.2% 36.3%)',
    secondary: 'hsl(138 76% 97%)',
    accent: 'hsl(138 76% 97%)',
  },
  purple: {
    primary: 'hsl(262.1 83.3% 57.8%)',
    secondary: 'hsl(270 95% 98%)',
    accent: 'hsl(270 95% 98%)',
  },
  orange: {
    primary: 'hsl(24.6 95% 53.1%)',
    secondary: 'hsl(24 100% 97%)',
    accent: 'hsl(24 100% 97%)',
  },
  pink: {
    primary: 'hsl(330.4 81.2% 60.4%)',
    secondary: 'hsl(322.2 100% 98%)',
    accent: 'hsl(322.2 100% 98%)',
  },
  cyan: {
    primary: 'hsl(188.7 85% 53.3%)',
    secondary: 'hsl(185 100% 97%)',
    accent: 'hsl(185 100% 97%)',
  },
};

// 创建主题上下文
const ThemeContext = createContext<ThemeContextValue>();

// 主题提供者组件
export const ThemeProvider: ParentComponent = (props) => {
  // 检测系统主题偏好
  const [systemPrefersDark, setSystemPrefersDark] = createSignal(
    window.matchMedia('(prefers-color-scheme: dark)').matches
  );

  // 从本地存储加载主题配置
  const loadThemeConfig = (): ThemeConfig => {
    try {
      const saved = localStorage.getItem('theme-config');
      return saved ? { ...defaultThemeConfig, ...JSON.parse(saved) } : defaultThemeConfig;
    } catch {
      return defaultThemeConfig;
    }
  };

  // 主题状态
  const [state, setState] = createStore<ThemeState>({
    config: loadThemeConfig(),
    isDark: false,
    systemPrefersDark: systemPrefersDark(),
  });

  // 保存主题配置到本地存储
  const saveThemeConfig = (config: ThemeConfig) => {
    try {
      localStorage.setItem('theme-config', JSON.stringify(config));
    } catch (error) {
      console.warn('Failed to save theme config:', error);
    }
  };

  // 应用主题到DOM
  const applyTheme = (config: ThemeConfig, isDark: boolean) => {
    const root = document.documentElement;
    
    // 设置主题模式类
    if (isDark) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }

    // 应用颜色方案
    const colors = config.customColors || colorSchemes[config.colorScheme];
    root.style.setProperty('--primary', colors.primary);
    root.style.setProperty('--secondary', colors.secondary);
    root.style.setProperty('--accent', colors.accent);

    // 设置数据属性用于CSS选择器
    root.setAttribute('data-theme', config.mode);
    root.setAttribute('data-color-scheme', config.colorScheme);
  };

  // 计算当前是否为暗色主题
  const computeIsDark = (mode: ThemeMode, systemPrefersDark: boolean): boolean => {
    switch (mode) {
      case 'dark':
        return true;
      case 'light':
        return false;
      case 'system':
        return systemPrefersDark;
      default:
        return false;
    }
  };

  // 监听系统主题变化
  createEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      setSystemPrefersDark(e.matches);
      setState('systemPrefersDark', e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  });

  // 更新isDark状态
  createEffect(() => {
    const isDark = computeIsDark(state.config.mode, state.systemPrefersDark);
    setState('isDark', isDark);
  });

  // 应用主题变化
  createEffect(() => {
    applyTheme(state.config, state.isDark);
  });

  // 主题操作方法
  const actions = {
    setThemeMode: (mode: ThemeMode) => {
      const newConfig = { ...state.config, mode };
      setState('config', newConfig);
      saveThemeConfig(newConfig);
    },

    setColorScheme: (scheme: ColorScheme) => {
      const newConfig = { ...state.config, colorScheme: scheme, customColors: undefined };
      setState('config', newConfig);
      saveThemeConfig(newConfig);
    },

    setCustomColors: (colors: ThemeConfig['customColors']) => {
      const newConfig = { ...state.config, customColors: colors };
      setState('config', newConfig);
      saveThemeConfig(newConfig);
    },

    toggleTheme: () => {
      const currentMode = state.config.mode;
      let newMode: ThemeMode;
      
      if (currentMode === 'system') {
        newMode = state.systemPrefersDark ? 'light' : 'dark';
      } else {
        newMode = currentMode === 'light' ? 'dark' : 'light';
      }
      
      actions.setThemeMode(newMode);
    },

    resetTheme: () => {
      setState('config', defaultThemeConfig);
      saveThemeConfig(defaultThemeConfig);
    },
  };

  return (
    <ThemeContext.Provider value={{ state, actions }}>
      {props.children}
    </ThemeContext.Provider>
  );
};

// 主题Hook
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within ThemeProvider');
  }
  return context;
};

// 主题工具函数
export const getThemeColors = (scheme: ColorScheme) => colorSchemes[scheme];

export const isValidColorScheme = (scheme: string): scheme is ColorScheme => {
  return Object.keys(colorSchemes).includes(scheme);
};

export const isValidThemeMode = (mode: string): mode is ThemeMode => {
  return ['light', 'dark', 'system'].includes(mode);
};
