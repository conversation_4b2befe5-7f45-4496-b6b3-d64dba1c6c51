D:\0DevelopeRepository\RustProgram\BubbleSay\src-tauri\target\debug\deps\bubblesay_lib-f0b229fc50c15ba8.d: src\lib.rs src\api\mod.rs src\api\commands\mod.rs src\api\commands\user_commands.rs src\api\commands\project_commands.rs src\api\commands\task_commands.rs src\api\commands\area_commands.rs src\api\commands\system_commands.rs src\api\dto\mod.rs src\api\dto\user_dto.rs src\api\dto\project_dto.rs src\api\dto\task_dto.rs src\api\dto\area_dto.rs src\api\handlers\mod.rs src\api\middleware\mod.rs src\application\mod.rs src\application\services\mod.rs src\application\services\user_app_service.rs src\application\services\project_app_service.rs src\application\services\task_app_service.rs src\application\services\area_app_service.rs src\application\use_cases\mod.rs src\application\dto\mod.rs src\domain\mod.rs src\domain\entities\mod.rs src\domain\entities\user.rs src\domain\entities\project.rs src\domain\entities\task.rs src\domain\entities\area.rs src\domain\entities\habit.rs src\domain\entities\resource.rs src\domain\entities\inbox.rs src\domain\entities\review.rs src\domain\entities\notification.rs src\domain\entities\checklist.rs src\domain\entities\template.rs src\domain\entities\file.rs src\domain\value_objects\mod.rs src\domain\value_objects\common.rs src\domain\value_objects\user_types.rs src\domain\value_objects\project_types.rs src\domain\value_objects\task_types.rs src\domain\value_objects\area_types.rs src\domain\value_objects\resource_types.rs src\domain\services\mod.rs src\domain\services\user_service.rs src\domain\services\project_service.rs src\domain\services\task_service.rs src\domain\services\area_service.rs src\domain\services\search_service.rs src\domain\services\notification_service.rs src\domain\services\backup_service.rs src\domain\repositories\mod.rs src\domain\repositories\user_repository.rs src\domain\repositories\project_repository.rs src\domain\repositories\task_repository.rs src\domain\repositories\area_repository.rs src\domain\events\mod.rs src\infrastructure\mod.rs src\infrastructure\database\mod.rs src\infrastructure\database\connection.rs src\infrastructure\database\migrations.rs src\infrastructure\database\models.rs src\infrastructure\database\initializer.rs src\infrastructure\database\health_check.rs src\infrastructure\database\manager.rs src\infrastructure\repositories\mod.rs src\infrastructure\repositories\user_repository_impl.rs src\infrastructure\repositories\project_repository_impl.rs src\infrastructure\repositories\task_repository_impl.rs src\infrastructure\repositories\area_repository_impl.rs src\infrastructure\external\mod.rs src\infrastructure\config\mod.rs src\infrastructure\search\mod.rs src\infrastructure\file_system\mod.rs src\infrastructure\cache\mod.rs src\infrastructure\events\mod.rs src\shared\mod.rs src\shared\errors\mod.rs src\shared\types\mod.rs src\shared\utils\mod.rs src\shared\utils\date_utils.rs src\shared\utils\string_utils.rs src\shared\utils\validation.rs src\shared\utils\crypto.rs D:\0DevelopeRepository\RustProgram\BubbleSay\src-tauri\target\debug\build\bubblesay-5cee9958b0c8282c\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

D:\0DevelopeRepository\RustProgram\BubbleSay\src-tauri\target\debug\deps\libbubblesay_lib-f0b229fc50c15ba8.rmeta: src\lib.rs src\api\mod.rs src\api\commands\mod.rs src\api\commands\user_commands.rs src\api\commands\project_commands.rs src\api\commands\task_commands.rs src\api\commands\area_commands.rs src\api\commands\system_commands.rs src\api\dto\mod.rs src\api\dto\user_dto.rs src\api\dto\project_dto.rs src\api\dto\task_dto.rs src\api\dto\area_dto.rs src\api\handlers\mod.rs src\api\middleware\mod.rs src\application\mod.rs src\application\services\mod.rs src\application\services\user_app_service.rs src\application\services\project_app_service.rs src\application\services\task_app_service.rs src\application\services\area_app_service.rs src\application\use_cases\mod.rs src\application\dto\mod.rs src\domain\mod.rs src\domain\entities\mod.rs src\domain\entities\user.rs src\domain\entities\project.rs src\domain\entities\task.rs src\domain\entities\area.rs src\domain\entities\habit.rs src\domain\entities\resource.rs src\domain\entities\inbox.rs src\domain\entities\review.rs src\domain\entities\notification.rs src\domain\entities\checklist.rs src\domain\entities\template.rs src\domain\entities\file.rs src\domain\value_objects\mod.rs src\domain\value_objects\common.rs src\domain\value_objects\user_types.rs src\domain\value_objects\project_types.rs src\domain\value_objects\task_types.rs src\domain\value_objects\area_types.rs src\domain\value_objects\resource_types.rs src\domain\services\mod.rs src\domain\services\user_service.rs src\domain\services\project_service.rs src\domain\services\task_service.rs src\domain\services\area_service.rs src\domain\services\search_service.rs src\domain\services\notification_service.rs src\domain\services\backup_service.rs src\domain\repositories\mod.rs src\domain\repositories\user_repository.rs src\domain\repositories\project_repository.rs src\domain\repositories\task_repository.rs src\domain\repositories\area_repository.rs src\domain\events\mod.rs src\infrastructure\mod.rs src\infrastructure\database\mod.rs src\infrastructure\database\connection.rs src\infrastructure\database\migrations.rs src\infrastructure\database\models.rs src\infrastructure\database\initializer.rs src\infrastructure\database\health_check.rs src\infrastructure\database\manager.rs src\infrastructure\repositories\mod.rs src\infrastructure\repositories\user_repository_impl.rs src\infrastructure\repositories\project_repository_impl.rs src\infrastructure\repositories\task_repository_impl.rs src\infrastructure\repositories\area_repository_impl.rs src\infrastructure\external\mod.rs src\infrastructure\config\mod.rs src\infrastructure\search\mod.rs src\infrastructure\file_system\mod.rs src\infrastructure\cache\mod.rs src\infrastructure\events\mod.rs src\shared\mod.rs src\shared\errors\mod.rs src\shared\types\mod.rs src\shared\utils\mod.rs src\shared\utils\date_utils.rs src\shared\utils\string_utils.rs src\shared\utils\validation.rs src\shared\utils\crypto.rs D:\0DevelopeRepository\RustProgram\BubbleSay\src-tauri\target\debug\build\bubblesay-5cee9958b0c8282c\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

src\lib.rs:
src\api\mod.rs:
src\api\commands\mod.rs:
src\api\commands\user_commands.rs:
src\api\commands\project_commands.rs:
src\api\commands\task_commands.rs:
src\api\commands\area_commands.rs:
src\api\commands\system_commands.rs:
src\api\dto\mod.rs:
src\api\dto\user_dto.rs:
src\api\dto\project_dto.rs:
src\api\dto\task_dto.rs:
src\api\dto\area_dto.rs:
src\api\handlers\mod.rs:
src\api\middleware\mod.rs:
src\application\mod.rs:
src\application\services\mod.rs:
src\application\services\user_app_service.rs:
src\application\services\project_app_service.rs:
src\application\services\task_app_service.rs:
src\application\services\area_app_service.rs:
src\application\use_cases\mod.rs:
src\application\dto\mod.rs:
src\domain\mod.rs:
src\domain\entities\mod.rs:
src\domain\entities\user.rs:
src\domain\entities\project.rs:
src\domain\entities\task.rs:
src\domain\entities\area.rs:
src\domain\entities\habit.rs:
src\domain\entities\resource.rs:
src\domain\entities\inbox.rs:
src\domain\entities\review.rs:
src\domain\entities\notification.rs:
src\domain\entities\checklist.rs:
src\domain\entities\template.rs:
src\domain\entities\file.rs:
src\domain\value_objects\mod.rs:
src\domain\value_objects\common.rs:
src\domain\value_objects\user_types.rs:
src\domain\value_objects\project_types.rs:
src\domain\value_objects\task_types.rs:
src\domain\value_objects\area_types.rs:
src\domain\value_objects\resource_types.rs:
src\domain\services\mod.rs:
src\domain\services\user_service.rs:
src\domain\services\project_service.rs:
src\domain\services\task_service.rs:
src\domain\services\area_service.rs:
src\domain\services\search_service.rs:
src\domain\services\notification_service.rs:
src\domain\services\backup_service.rs:
src\domain\repositories\mod.rs:
src\domain\repositories\user_repository.rs:
src\domain\repositories\project_repository.rs:
src\domain\repositories\task_repository.rs:
src\domain\repositories\area_repository.rs:
src\domain\events\mod.rs:
src\infrastructure\mod.rs:
src\infrastructure\database\mod.rs:
src\infrastructure\database\connection.rs:
src\infrastructure\database\migrations.rs:
src\infrastructure\database\models.rs:
src\infrastructure\database\initializer.rs:
src\infrastructure\database\health_check.rs:
src\infrastructure\database\manager.rs:
src\infrastructure\repositories\mod.rs:
src\infrastructure\repositories\user_repository_impl.rs:
src\infrastructure\repositories\project_repository_impl.rs:
src\infrastructure\repositories\task_repository_impl.rs:
src\infrastructure\repositories\area_repository_impl.rs:
src\infrastructure\external\mod.rs:
src\infrastructure\config\mod.rs:
src\infrastructure\search\mod.rs:
src\infrastructure\file_system\mod.rs:
src\infrastructure\cache\mod.rs:
src\infrastructure\events\mod.rs:
src\shared\mod.rs:
src\shared\errors\mod.rs:
src\shared\types\mod.rs:
src\shared\utils\mod.rs:
src\shared\utils\date_utils.rs:
src\shared\utils\string_utils.rs:
src\shared\utils\validation.rs:
src\shared\utils\crypto.rs:
D:\0DevelopeRepository\RustProgram\BubbleSay\src-tauri\target\debug\build\bubblesay-5cee9958b0c8282c\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=bubblesay
# env-dep:CARGO_PKG_VERSION=0.1.0
# env-dep:OUT_DIR=D:\\0DevelopeRepository\\RustProgram\\BubbleSay\\src-tauri\\target\\debug\\build\\bubblesay-5cee9958b0c8282c\\out
