import { RouterProvider } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { router } from './lib/router'
import { ErrorBoundary } from './components/shared'
import { LanguageProvider } from './contexts/LanguageContext'
import { FirstTimeSetup } from './components/features/FirstTimeSetup'
import { useUserSettingsStore } from './store/userSettingsStore'
import { useProjectStore } from './store/projectStore'
import { useAreaStore } from './store/areaStore'
import { useUIStore } from './store/uiStore'
import { useGlobalShortcutsStore } from './store/globalShortcutsStore'
import { fileSystemApi } from './lib/api'



function App(): React.JSX.Element {
  const { settings, isInitialized, updateSettings, completeFirstTimeSetup } = useUserSettingsStore()
  const { fetchProjects } = useProjectStore()
  const { fetchAreas } = useAreaStore()
  const { theme } = useUIStore()
  const { initializeGlobalShortcuts } = useGlobalShortcutsStore()
  const [showFirstTimeSetup, setShowFirstTimeSetup] = useState(false)
  const [hasInitialized, setHasInitialized] = useState(false)
  const [configSynced, setConfigSynced] = useState(false)



  // 主题应用逻辑
  useEffect(() => {
    const applyTheme = () => {
      const root = document.documentElement

      if (theme === 'system') {
        // 检测系统主题
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
        root.classList.toggle('dark', systemTheme === 'dark')
      } else {
        root.classList.toggle('dark', theme === 'dark')
      }
    }

    applyTheme()

    // 监听系统主题变化
    if (theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const handleChange = () => applyTheme()
      mediaQuery.addEventListener('change', handleChange)
      return () => mediaQuery.removeEventListener('change', handleChange)
    }
  }, [theme])



  // 同步主进程配置状态
  useEffect(() => {
    const syncConfigFromMainProcess = async () => {
      if (window.electronAPI?.config?.getStatus && !configSynced) {
        try {
          console.log('🔄 [App.tsx] Syncing config from main process...')
          const result = await window.electronAPI.config.getStatus()

          if (result.success && result.data) {
            console.log('📄 [App.tsx] Main process config status:', result.data)

            // 如果主进程有配置但前端没有，同步配置
            if (!result.data.isFirstTime && result.data.workspaceDirectory && settings.isFirstTime) {
              console.log('🔄 [App.tsx] Syncing config from main process to frontend')
              updateSettings({
                workspaceDirectory: result.data.workspaceDirectory,
                username: result.data.username || '',
                isFirstTime: false
              })
              completeFirstTimeSetup()
            }
          }

          setConfigSynced(true)
        } catch (error) {
          console.error('❌ [App.tsx] Failed to sync config from main process:', error)
          setConfigSynced(true) // 即使失败也标记为已尝试同步
        }
      } else if (!window.electronAPI?.config?.getStatus) {
        // 浏览器环境，直接标记为已同步
        setConfigSynced(true)
      }
    }

    syncConfigFromMainProcess()
  }, [configSynced, settings.isFirstTime, updateSettings, completeFirstTimeSetup])

  useEffect(() => {
    // 检查是否是首次使用（只在配置同步完成后执行）
    if (!configSynced) return

    if (settings.isFirstTime && !isInitialized) {
      setShowFirstTimeSetup(true)
    } else if (!settings.isFirstTime && settings.workspaceDirectory && !hasInitialized) {
      // 只在第一次时初始化，避免重复执行
      setHasInitialized(true)

      // 如果不是首次使用且有工作目录设置，重新初始化文件系统
      if (window.electronAPI?.fileSystem?.reinitialize) {
        fileSystemApi.reinitialize(settings.workspaceDirectory).catch((error) => {
          console.error('Failed to reinitialize file system on startup:', error)
        })
      }

      // 对于非首次启动，数据库已经在主进程启动时使用正确的工作目录初始化了
      // 这里不需要再发送工作目录设置
      console.log('=== [App.tsx] NON-FIRST-TIME STARTUP ===')
      console.log('[App.tsx] Database should already be initialized with workspace directory:', settings.workspaceDirectory)

      // 尝试紧急初始化数据库（如果主进程没有正确初始化）
      console.log('🚨 [App.tsx] Attempting emergency database initialization...')
      window.electronAPI.window.emergencyInitDatabase().then((result) => {
        console.log('🚨 [App.tsx] Emergency database initialization result:', result)

        // 加载项目和领域数据
        fetchProjects().catch((error) => {
          console.error('Failed to fetch projects on startup:', error)
        })
        fetchAreas().catch((error) => {
          console.error('Failed to fetch areas on startup:', error)
        })
      }).catch((error) => {
        console.error('🚨 [App.tsx] Emergency database initialization failed:', error)

        // 即使紧急初始化失败，也尝试加载数据
        fetchProjects().catch((error) => {
          console.error('Failed to fetch projects on startup:', error)
        })
        fetchAreas().catch((error) => {
          console.error('Failed to fetch areas on startup:', error)
        })
      })

      // Initialize global shortcuts
      initializeGlobalShortcuts().catch((error) => {
        console.error('Failed to initialize global shortcuts:', error)
      })

    }
  }, [
    configSynced,
    settings.isFirstTime,
    settings.workspaceDirectory,
    isInitialized,
    hasInitialized,
    fetchProjects,
    fetchAreas,
    initializeGlobalShortcuts
  ])

  const handleFirstTimeSetupComplete = () => {
    setShowFirstTimeSetup(false)
  }

  return (
    <ErrorBoundary>
      <LanguageProvider>
        <RouterProvider router={router} />
        <FirstTimeSetup isOpen={showFirstTimeSetup} onComplete={handleFirstTimeSetupComplete} />
      </LanguageProvider>
    </ErrorBoundary>
  )
}

export default App
