// Area Application Service - 领域应用服务

use crate::domain::entities::{Area, AreaStatus};
use crate::domain::repositories::{AreaRepository, ProjectRepository, HabitRepository};
use crate::domain::services::AreaDomainService;
use crate::shared::errors::{AppError, Result};
use crate::shared::types::{Id, QueryParams};
use std::sync::Arc;

/// 领域应用服务
/// 负责领域相关的业务流程编排
pub struct AreaAppService {
    area_repository: Arc<dyn AreaRepository>,
    project_repository: Arc<dyn ProjectRepository>,
    habit_repository: Option<Arc<dyn HabitRepository>>, // 暂时可选，因为还未实现
    area_domain_service: AreaDomainService,
}

impl AreaAppService {
    pub fn new(
        area_repository: Arc<dyn AreaRepository>,
        project_repository: Arc<dyn ProjectRepository>,
        area_domain_service: AreaDomainService,
    ) -> Self {
        Self {
            area_repository,
            project_repository,
            habit_repository: None,
            area_domain_service,
        }
    }

    /// 创建新领域
    pub async fn create_area(
        &self,
        name: String,
        description: Option<String>,
        standards: Option<Vec<String>>,
        color: Option<String>,
        icon: Option<String>,
    ) -> Result<Area> {
        // 验证领域名称
        self.area_domain_service.validate_area_name(&name)?;

        // 检查名称是否已存在
        if self.area_repository.name_exists(&name).await? {
            return Err(AppError::ValidationError("Area name already exists".to_string()));
        }

        // 创建领域实体
        let mut area = Area::new(name, description);
        if let Some(standards) = standards {
            area.standards = standards;
        }
        area.color = color;
        area.icon = icon;

        // 保存领域
        self.area_repository.save(&area).await?;

        Ok(area)
    }

    /// 根据ID获取领域
    pub async fn get_area_by_id(&self, id: &Id) -> Result<Area> {
        self.area_repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| AppError::NotFound("Area not found".to_string()))
    }

    /// 根据名称获取领域
    pub async fn get_area_by_name(&self, name: &str) -> Result<Area> {
        self.area_repository
            .find_by_name(name)
            .await?
            .ok_or_else(|| AppError::NotFound("Area not found".to_string()))
    }

    /// 更新领域信息
    pub async fn update_area(
        &self,
        id: &Id,
        name: Option<String>,
        description: Option<String>,
        standards: Option<Vec<String>>,
        color: Option<String>,
        icon: Option<String>,
    ) -> Result<Area> {
        // 获取现有领域
        let mut area = self.get_area_by_id(id).await?;

        // 验证领域名称（如果提供且与当前不同）
        if let Some(ref new_name) = name {
            if area.name != *new_name {
                self.area_domain_service.validate_area_name(new_name)?;
                
                // 检查新名称是否已被其他领域使用
                if self.area_repository.name_exists(new_name).await? {
                    return Err(AppError::ValidationError("Area name already exists".to_string()));
                }
                area.name = new_name.clone();
            }
        }

        // 更新其他字段
        if let Some(desc) = description {
            area.description = Some(desc);
        }
        if let Some(standards) = standards {
            area.standards = standards;
        }
        if let Some(color) = color {
            area.color = Some(color);
        }
        if let Some(icon) = icon {
            area.icon = Some(icon);
        }

        area.update_metadata();

        // 保存更新
        self.area_repository.update(&area).await?;

        Ok(area)
    }

    /// 更新领域状态
    pub async fn update_area_status(&self, id: &Id, status: AreaStatus) -> Result<Area> {
        let mut area = self.get_area_by_id(id).await?;

        area.status = status;
        area.update_metadata();

        self.area_repository.update(&area).await?;

        Ok(area)
    }

    /// 激活领域
    pub async fn activate_area(&self, id: &Id) -> Result<Area> {
        let mut area = self.get_area_by_id(id).await?;

        if !self.area_domain_service.can_activate_area(&area) {
            return Err(AppError::ValidationError("Area cannot be activated".to_string()));
        }

        area.status = AreaStatus::Active;
        area.update_metadata();

        self.area_repository.update(&area).await?;

        Ok(area)
    }

    /// 设置领域为维护状态
    pub async fn set_area_maintenance(&self, id: &Id) -> Result<Area> {
        let mut area = self.get_area_by_id(id).await?;

        area.status = AreaStatus::Maintenance;
        area.update_metadata();

        self.area_repository.update(&area).await?;

        Ok(area)
    }

    /// 设置领域为休眠状态
    pub async fn set_area_dormant(&self, id: &Id) -> Result<Area> {
        let mut area = self.get_area_by_id(id).await?;

        area.status = AreaStatus::Dormant;
        area.update_metadata();

        self.area_repository.update(&area).await?;

        Ok(area)
    }

    /// 删除领域（软删除）
    pub async fn delete_area(&self, id: &Id) -> Result<()> {
        // 检查领域是否存在
        let _area = self.get_area_by_id(id).await?;

        // 检查是否有关联的项目
        let projects = self.project_repository.find_by_area_id(id).await?;
        if !projects.is_empty() {
            return Err(AppError::ValidationError(
                "Cannot delete area with associated projects".to_string(),
            ));
        }

        // 执行软删除
        self.area_repository.delete(id).await?;

        Ok(())
    }

    /// 获取所有活跃领域
    pub async fn get_active_areas(&self) -> Result<Vec<Area>> {
        self.area_repository.find_all_active().await
    }

    /// 根据状态获取领域
    pub async fn get_areas_by_status(&self, status: &AreaStatus) -> Result<Vec<Area>> {
        self.area_repository.find_by_status(status).await
    }

    /// 分页获取领域列表
    pub async fn get_areas_with_pagination(&self, params: &QueryParams) -> Result<(Vec<Area>, u64)> {
        self.area_repository.find_with_pagination(params).await
    }

    /// 搜索领域
    pub async fn search_areas(&self, query: &str) -> Result<Vec<Area>> {
        if query.trim().is_empty() {
            return Ok(Vec::new());
        }

        self.area_repository.search(query).await
    }

    /// 获取领域统计信息
    pub async fn get_area_statistics(&self) -> Result<crate::domain::repositories::AreaStatistics> {
        self.area_repository.get_statistics().await
    }

    /// 获取领域的项目数量
    pub async fn get_area_project_count(&self, id: &Id) -> Result<u64> {
        self.area_repository.get_project_count(id).await
    }

    /// 获取领域的习惯数量
    pub async fn get_area_habit_count(&self, id: &Id) -> Result<u64> {
        self.area_repository.get_habit_count(id).await
    }

    /// 获取领域详细信息（包含关联数据）
    pub async fn get_area_details(&self, id: &Id) -> Result<AreaDetails> {
        let area = self.get_area_by_id(id).await?;
        let projects = self.project_repository.find_by_area_id(id).await?;
        let project_count = projects.len() as u64;
        let habit_count = self.get_area_habit_count(id).await?;

        // 计算健康分数
        let health_score = self.area_domain_service.calculate_health_score(&area);

        Ok(AreaDetails {
            area,
            project_count,
            habit_count,
            health_score,
            recent_projects: projects.into_iter().take(5).collect(), // 最近5个项目
        })
    }

    /// 检查领域名称是否可用
    pub async fn is_area_name_available(&self, name: &str) -> Result<bool> {
        // 验证名称格式
        self.area_domain_service.validate_area_name(name)?;
        
        // 检查是否已存在
        let exists = self.area_repository.name_exists(name).await?;
        Ok(!exists)
    }

    /// 归档领域
    pub async fn archive_area(&self, id: &Id) -> Result<Area> {
        let mut area = self.get_area_by_id(id).await?;
        
        // 检查是否有活跃的项目
        let active_projects = self.project_repository.find_by_area_id(id).await?;
        let has_active_projects = active_projects.iter().any(|p| {
            p.entity_status == crate::shared::types::EntityStatus::Active
        });

        if has_active_projects {
            return Err(AppError::ValidationError(
                "Cannot archive area with active projects".to_string(),
            ));
        }

        area.entity_status = crate::shared::types::EntityStatus::Archived;
        area.update_metadata();

        self.area_repository.update(&area).await?;

        Ok(area)
    }

    /// 获取需要关注的领域（健康分数低的领域）
    pub async fn get_areas_needing_attention(&self) -> Result<Vec<Area>> {
        let all_areas = self.area_repository.find_all_active().await?;
        
        let areas_needing_attention = all_areas
            .into_iter()
            .filter(|area| {
                let health_score = self.area_domain_service.calculate_health_score(area);
                health_score < 0.5 // 健康分数低于50%
            })
            .collect();

        Ok(areas_needing_attention)
    }

    /// 批量更新领域状态
    pub async fn batch_update_area_status(&self, area_ids: Vec<Id>, status: AreaStatus) -> Result<Vec<Area>> {
        let mut updated_areas = Vec::new();

        for area_id in area_ids {
            match self.update_area_status(&area_id, status.clone()).await {
                Ok(area) => updated_areas.push(area),
                Err(e) => {
                    // 记录错误但继续处理其他领域
                    eprintln!("Failed to update area {}: {}", area_id, e);
                }
            }
        }

        Ok(updated_areas)
    }
}

/// 领域详细信息
#[derive(Debug, Clone)]
pub struct AreaDetails {
    pub area: Area,
    pub project_count: u64,
    pub habit_count: u64,
    pub health_score: f32,
    pub recent_projects: Vec<crate::domain::entities::Project>,
}

// 占位符习惯仓储接口
pub trait HabitRepository: Send + Sync {
    // 这里将在后续实现习惯功能时添加方法
}
