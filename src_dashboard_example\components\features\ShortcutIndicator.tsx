import React, { useState, useEffect } from 'react'
import { Badge } from '../ui/badge'
import { useShortcutsStore } from '../../store/shortcutsStore'

export function ShortcutIndicator() {
  const { shortcuts } = useShortcutsStore()
  const [pressedKeys, setPressedKeys] = useState<string[]>([])
  const [matchedShortcut, setMatchedShortcut] = useState<string | null>(null)

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const keys: string[] = []
      if (e.ctrlKey) keys.push('Ctrl')
      if (e.altKey) keys.push('Alt')
      if (e.shiftKey) keys.push('Shift')
      if (e.metaKey) keys.push('Meta')

      if (e.key !== 'Control' && e.key !== 'Alt' && e.key !== 'Shift' && e.key !== 'Meta') {
        keys.push(e.key.length === 1 ? e.key.toUpperCase() : e.key)
      }

      setPressedKeys(keys)

      // 查找匹配的快捷键
      const matched = shortcuts.find(shortcut => 
        shortcut.enabled && 
        shortcut.currentKeys.length === keys.length &&
        shortcut.currentKeys.every((key, index) => key === keys[index])
      )

      setMatchedShortcut(matched ? matched.name : null)
    }

    const handleKeyUp = () => {
      setPressedKeys([])
      setMatchedShortcut(null)
    }

    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('keyup', handleKeyUp)

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.removeEventListener('keyup', handleKeyUp)
    }
  }, [shortcuts])

  if (pressedKeys.length === 0) return null

  return (
    <div className="fixed bottom-4 right-4 bg-background border rounded-lg p-3 shadow-lg z-50">
      <div className="flex items-center gap-2 text-sm">
        <span>按键:</span>
        {pressedKeys.map((key, index) => (
          <Badge key={index} variant="outline">
            {key}
          </Badge>
        ))}
        {matchedShortcut && (
          <>
            <span>→</span>
            <Badge variant="secondary">{matchedShortcut}</Badge>
          </>
        )}
      </div>
    </div>
  )
}
