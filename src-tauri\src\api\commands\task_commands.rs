// Task Commands - 任务相关的 Tauri 命令

use crate::api::dto::*;
use crate::application::services::TaskAppService;
use crate::shared::errors::AppError;
use crate::shared::types::{Id, QueryParams, Pagination};
use std::sync::Arc;
use tauri::State;

/// 创建任务
#[tauri::command]
pub async fn create_task(
    request: CreateTaskRequest,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<TaskResponse>, String> {
    // 验证请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    // 调用应用服务
    match task_service.create_task(
        request.title,
        request.description,
        request.project_id,
        request.parent_task_id,
        request.due_date,
        Some(request.get_priority()),
    ).await {
        Ok(task) => Ok(ApiResponse::success(TaskResponse::from(task))),
        Err(e) => Ok(ApiResponse::error(format!("Failed to create task: {}", e))),
    }
}

/// 根据ID获取任务
#[tauri::command]
pub async fn get_task_by_id(
    id: Id,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<TaskResponse>, String> {
    match task_service.get_task_by_id(&id).await {
        Ok(task) => Ok(ApiResponse::success(TaskResponse::from(task))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Task not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to get task: {}", e))),
    }
}

/// 更新任务信息
#[tauri::command]
pub async fn update_task(
    id: Id,
    request: UpdateTaskRequest,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<TaskResponse>, String> {
    // 验证请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    // 调用应用服务
    match task_service.update_task(
        &id,
        request.title,
        request.description,
        request.project_id,
        request.parent_task_id,
        request.due_date,
        request.get_priority(),
    ).await {
        Ok(task) => Ok(ApiResponse::success(TaskResponse::from(task))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Task not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to update task: {}", e))),
    }
}

/// 更新任务状态
#[tauri::command]
pub async fn update_task_status(
    id: Id,
    request: UpdateTaskStatusRequest,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<TaskResponse>, String> {
    // 验证请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    match task_service.update_task_status(&id, request.get_status()).await {
        Ok(task) => Ok(ApiResponse::success(TaskResponse::from(task))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Task not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to update task status: {}", e))),
    }
}

/// 更新任务进度
#[tauri::command]
pub async fn update_task_progress(
    id: Id,
    request: UpdateTaskProgressRequest,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<TaskResponse>, String> {
    // 验证请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    match task_service.update_task_progress(&id, request.progress).await {
        Ok(task) => Ok(ApiResponse::success(TaskResponse::from(task))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Task not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to update task progress: {}", e))),
    }
}

/// 完成任务
#[tauri::command]
pub async fn complete_task(
    id: Id,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<TaskResponse>, String> {
    match task_service.complete_task(&id).await {
        Ok(task) => Ok(ApiResponse::success(TaskResponse::from(task))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Task not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to complete task: {}", e))),
    }
}

/// 取消任务
#[tauri::command]
pub async fn cancel_task(
    id: Id,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<TaskResponse>, String> {
    match task_service.cancel_task(&id).await {
        Ok(task) => Ok(ApiResponse::success(TaskResponse::from(task))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Task not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to cancel task: {}", e))),
    }
}

/// 重新打开任务
#[tauri::command]
pub async fn reopen_task(
    id: Id,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<TaskResponse>, String> {
    match task_service.reopen_task(&id).await {
        Ok(task) => Ok(ApiResponse::success(TaskResponse::from(task))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Task not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to reopen task: {}", e))),
    }
}

/// 删除任务
#[tauri::command]
pub async fn delete_task(
    id: Id,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<EmptyResponse>, String> {
    match task_service.delete_task(&id).await {
        Ok(_) => Ok(ApiResponse::success(EmptyResponse)),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Task not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to delete task: {}", e))),
    }
}

/// 获取所有活跃任务
#[tauri::command]
pub async fn get_active_tasks(
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<Vec<TaskResponse>>, String> {
    match task_service.get_active_tasks().await {
        Ok(tasks) => {
            let task_responses: Vec<TaskResponse> = tasks.into_iter()
                .map(TaskResponse::from)
                .collect();
            Ok(ApiResponse::success(task_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get active tasks: {}", e))),
    }
}

/// 根据项目获取任务
#[tauri::command]
pub async fn get_tasks_by_project(
    project_id: Id,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<Vec<TaskResponse>>, String> {
    match task_service.get_tasks_by_project(&project_id).await {
        Ok(tasks) => {
            let task_responses: Vec<TaskResponse> = tasks.into_iter()
                .map(TaskResponse::from)
                .collect();
            Ok(ApiResponse::success(task_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get tasks by project: {}", e))),
    }
}

/// 获取子任务
#[tauri::command]
pub async fn get_subtasks(
    parent_task_id: Id,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<Vec<TaskResponse>>, String> {
    match task_service.get_subtasks(&parent_task_id).await {
        Ok(tasks) => {
            let task_responses: Vec<TaskResponse> = tasks.into_iter()
                .map(TaskResponse::from)
                .collect();
            Ok(ApiResponse::success(task_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get subtasks: {}", e))),
    }
}

/// 根据状态获取任务
#[tauri::command]
pub async fn get_tasks_by_status(
    status: String,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<Vec<TaskResponse>>, String> {
    let status_request = UpdateTaskStatusRequest { status };
    if let Err(e) = status_request.validate() {
        return Ok(ApiResponse::error(e));
    }

    match task_service.get_tasks_by_status(&status_request.get_status()).await {
        Ok(tasks) => {
            let task_responses: Vec<TaskResponse> = tasks.into_iter()
                .map(TaskResponse::from)
                .collect();
            Ok(ApiResponse::success(task_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get tasks by status: {}", e))),
    }
}

/// 获取今日任务
#[tauri::command]
pub async fn get_today_tasks(
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<Vec<TaskResponse>>, String> {
    match task_service.get_today_tasks().await {
        Ok(tasks) => {
            let task_responses: Vec<TaskResponse> = tasks.into_iter()
                .map(TaskResponse::from)
                .collect();
            Ok(ApiResponse::success(task_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get today tasks: {}", e))),
    }
}

/// 获取即将到期的任务
#[tauri::command]
pub async fn get_tasks_due_soon(
    days: u32,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<Vec<TaskResponse>>, String> {
    match task_service.get_tasks_due_soon(days).await {
        Ok(tasks) => {
            let task_responses: Vec<TaskResponse> = tasks.into_iter()
                .map(TaskResponse::from)
                .collect();
            Ok(ApiResponse::success(task_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get tasks due soon: {}", e))),
    }
}

/// 获取已逾期的任务
#[tauri::command]
pub async fn get_overdue_tasks(
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<Vec<TaskResponse>>, String> {
    match task_service.get_overdue_tasks().await {
        Ok(tasks) => {
            let task_responses: Vec<TaskResponse> = tasks.into_iter()
                .map(TaskResponse::from)
                .collect();
            Ok(ApiResponse::success(task_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get overdue tasks: {}", e))),
    }
}

/// 分页获取任务列表
#[tauri::command]
pub async fn get_tasks_with_pagination(
    pagination: PaginationRequest,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<TaskListResponse>, String> {
    // 验证分页参数
    if let Err(e) = pagination.validate() {
        return Ok(ApiResponse::error(e));
    }

    let query_params = QueryParams {
        pagination: Some(Pagination {
            page: pagination.page,
            size: pagination.size,
        }),
        ..Default::default()
    };

    match task_service.get_tasks_with_pagination(&query_params).await {
        Ok((tasks, total)) => {
            let task_responses: Vec<TaskResponse> = tasks.into_iter()
                .map(TaskResponse::from)
                .collect();
            
            let response = TaskListResponse {
                tasks: task_responses,
                total,
                page: pagination.page,
                size: pagination.size,
            };
            
            Ok(ApiResponse::success(response))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get tasks: {}", e))),
    }
}

/// 搜索任务
#[tauri::command]
pub async fn search_tasks(
    request: SearchRequest,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<Vec<TaskResponse>>, String> {
    // 验证搜索请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    match task_service.search_tasks(&request.query).await {
        Ok(tasks) => {
            let task_responses: Vec<TaskResponse> = tasks.into_iter()
                .map(TaskResponse::from)
                .collect();
            Ok(ApiResponse::success(task_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to search tasks: {}", e))),
    }
}

/// 获取任务统计信息
#[tauri::command]
pub async fn get_task_statistics(
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<TaskStatisticsResponse>, String> {
    match task_service.get_task_statistics().await {
        Ok(stats) => Ok(ApiResponse::success(TaskStatisticsResponse::from(stats))),
        Err(e) => Ok(ApiResponse::error(format!("Failed to get task statistics: {}", e))),
    }
}

/// 获取根任务
#[tauri::command]
pub async fn get_root_tasks(
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<Vec<TaskResponse>>, String> {
    match task_service.get_root_tasks().await {
        Ok(tasks) => {
            let task_responses: Vec<TaskResponse> = tasks.into_iter()
                .map(TaskResponse::from)
                .collect();
            Ok(ApiResponse::success(task_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get root tasks: {}", e))),
    }
}

/// 获取任务树
#[tauri::command]
pub async fn get_task_tree(
    root_task_id: Id,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<Vec<TaskResponse>>, String> {
    match task_service.get_task_tree(&root_task_id).await {
        Ok(tasks) => {
            let task_responses: Vec<TaskResponse> = tasks.into_iter()
                .map(TaskResponse::from)
                .collect();
            Ok(ApiResponse::success(task_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get task tree: {}", e))),
    }
}

/// 计算任务优先级分数
#[tauri::command]
pub async fn calculate_task_priority_score(
    id: Id,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<TaskPriorityScoreResponse>, String> {
    match task_service.calculate_task_priority_score(&id).await {
        Ok(score) => {
            let response = TaskPriorityScoreResponse {
                task_id: id,
                priority_score: score,
            };
            Ok(ApiResponse::success(response))
        },
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Task not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to calculate priority score: {}", e))),
    }
}

/// 获取高优先级任务
#[tauri::command]
pub async fn get_high_priority_tasks(
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<Vec<TaskResponse>>, String> {
    match task_service.get_high_priority_tasks().await {
        Ok(tasks) => {
            let task_responses: Vec<TaskResponse> = tasks.into_iter()
                .map(TaskResponse::from)
                .collect();
            Ok(ApiResponse::success(task_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to get high priority tasks: {}", e))),
    }
}

/// 批量更新任务状态
#[tauri::command]
pub async fn batch_update_task_status(
    request: BatchUpdateTaskStatusRequest,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<Vec<TaskResponse>>, String> {
    // 验证请求
    if let Err(e) = request.validate() {
        return Ok(ApiResponse::error(e));
    }

    match task_service.batch_update_task_status(request.task_ids, request.get_status()).await {
        Ok(tasks) => {
            let task_responses: Vec<TaskResponse> = tasks.into_iter()
                .map(TaskResponse::from)
                .collect();
            Ok(ApiResponse::success(task_responses))
        },
        Err(e) => Ok(ApiResponse::error(format!("Failed to batch update task status: {}", e))),
    }
}

/// 克隆任务
#[tauri::command]
pub async fn clone_task(
    id: Id,
    request: CloneTaskRequest,
    task_service: State<'_, Arc<TaskAppService>>,
) -> Result<ApiResponse<TaskResponse>, String> {
    match task_service.clone_task(&id, request.new_title).await {
        Ok(task) => Ok(ApiResponse::success(TaskResponse::from(task))),
        Err(AppError::NotFound(_)) => Ok(ApiResponse::error("Task not found".to_string())),
        Err(e) => Ok(ApiResponse::error(format!("Failed to clone task: {}", e))),
    }
}
