// Database Health Check - 数据库健康检查

use crate::infrastructure::database::DatabaseConnection;
use crate::shared::errors::{AppError, Result};
use serde::{Deserialize, Serialize};
use std::time::{Duration, Instant};

#[derive(<PERSON>bug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DatabaseHealthStatus {
    pub is_healthy: bool,
    pub connection_status: ConnectionStatus,
    pub performance_metrics: PerformanceMetrics,
    pub integrity_checks: IntegrityChecks,
    pub last_check_time: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionStatus {
    pub can_connect: bool,
    pub connection_time_ms: u64,
    pub active_connections: u32,
    pub max_connections: u32,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub query_response_time_ms: u64,
    pub database_size_bytes: u64,
    pub index_usage_efficiency: f64,
}

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct IntegrityChecks {
    pub foreign_key_violations: u32,
    pub constraint_violations: u32,
    pub orphaned_records: u32,
}

pub struct DatabaseHealthChecker {
    connection: DatabaseConnection,
}

impl DatabaseHealthChecker {
    pub fn new(connection: DatabaseConnection) -> Self {
        Self { connection }
    }

    /// 执行完整的健康检查
    pub async fn check_health(&self) -> Result<DatabaseHealthStatus> {
        let start_time = Instant::now();
        
        // 连接状态检查
        let connection_status = self.check_connection_status().await?;
        
        // 性能指标检查
        let performance_metrics = self.check_performance_metrics().await?;
        
        // 完整性检查
        let integrity_checks = self.check_data_integrity().await?;
        
        let is_healthy = connection_status.can_connect 
            && integrity_checks.foreign_key_violations == 0
            && integrity_checks.constraint_violations == 0
            && performance_metrics.query_response_time_ms < 1000; // 1秒阈值

        Ok(DatabaseHealthStatus {
            is_healthy,
            connection_status,
            performance_metrics,
            integrity_checks,
            last_check_time: chrono::Utc::now(),
        })
    }

    /// 检查连接状态
    async fn check_connection_status(&self) -> Result<ConnectionStatus> {
        let start = Instant::now();
        
        // 测试基础连接
        let can_connect = match self.connection.health_check().await {
            Ok(_) => true,
            Err(_) => false,
        };
        
        let connection_time_ms = start.elapsed().as_millis() as u64;

        // 获取连接池信息
        let pool = self.connection.pool();
        let active_connections = pool.size() as u32;
        let max_connections = 10; // 从配置获取

        Ok(ConnectionStatus {
            can_connect,
            connection_time_ms,
            active_connections,
            max_connections,
        })
    }

    /// 检查性能指标
    async fn check_performance_metrics(&self) -> Result<PerformanceMetrics> {
        let start = Instant::now();
        
        // 执行测试查询来测量响应时间
        let _ = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM users")
            .fetch_one(&**self.connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Performance test query failed: {}", e)))?;
        
        let query_response_time_ms = start.elapsed().as_millis() as u64;

        // 获取数据库大小
        let database_size_bytes = self.get_database_size().await?;

        // 计算索引使用效率（简化版）
        let index_usage_efficiency = self.calculate_index_efficiency().await?;

        Ok(PerformanceMetrics {
            query_response_time_ms,
            database_size_bytes,
            index_usage_efficiency,
        })
    }

    /// 检查数据完整性
    async fn check_data_integrity(&self) -> Result<IntegrityChecks> {
        // 检查外键约束违反
        let foreign_key_violations = self.check_foreign_key_violations().await?;
        
        // 检查约束违反
        let constraint_violations = self.check_constraint_violations().await?;
        
        // 检查孤立记录
        let orphaned_records = self.check_orphaned_records().await?;

        Ok(IntegrityChecks {
            foreign_key_violations,
            constraint_violations,
            orphaned_records,
        })
    }

    /// 获取数据库大小
    async fn get_database_size(&self) -> Result<u64> {
        let size = sqlx::query_scalar::<_, i64>("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
            .fetch_one(&**self.connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to get database size: {}", e)))?;

        Ok(size as u64)
    }

    /// 计算索引使用效率
    async fn calculate_index_efficiency(&self) -> Result<f64> {
        // 简化的索引效率计算
        // 实际实现中可以分析查询计划和索引使用情况
        Ok(0.85) // 假设85%的效率
    }

    /// 检查外键约束违反
    async fn check_foreign_key_violations(&self) -> Result<u32> {
        // 启用外键检查
        sqlx::query("PRAGMA foreign_keys = ON")
            .execute(&**self.connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to enable foreign key checks: {}", e)))?;

        // 检查外键完整性
        let violations = sqlx::query_scalar::<_, i64>("PRAGMA foreign_key_check")
            .fetch_optional(&**self.connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Foreign key check failed: {}", e)))?;

        Ok(violations.unwrap_or(0) as u32)
    }

    /// 检查约束违反
    async fn check_constraint_violations(&self) -> Result<u32> {
        // 检查完整性约束
        let violations = sqlx::query_scalar::<_, i64>("PRAGMA integrity_check")
            .fetch_optional(&**self.connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Integrity check failed: {}", e)))?;

        // 如果返回 "ok"，则没有违反
        Ok(0) // 简化实现
    }

    /// 检查孤立记录
    async fn check_orphaned_records(&self) -> Result<u32> {
        let mut orphaned_count = 0;

        // 检查项目中的孤立任务（项目不存在但任务引用了项目ID）
        let orphaned_tasks = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM tasks WHERE project_id IS NOT NULL AND project_id NOT IN (SELECT id FROM projects)"
        )
        .fetch_one(&**self.connection.pool())
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to check orphaned tasks: {}", e)))?;

        orphaned_count += orphaned_tasks as u32;

        // 检查习惯中的孤立记录（领域不存在但习惯引用了领域ID）
        let orphaned_habits = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM habits WHERE area_id NOT IN (SELECT id FROM areas)"
        )
        .fetch_one(&**self.connection.pool())
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to check orphaned habits: {}", e)))?;

        orphaned_count += orphaned_habits as u32;

        Ok(orphaned_count)
    }

    /// 快速健康检查（仅基础连接测试）
    pub async fn quick_health_check(&self) -> Result<bool> {
        match self.connection.health_check().await {
            Ok(_) => Ok(true),
            Err(_) => Ok(false),
        }
    }

    /// 修复数据完整性问题
    pub async fn repair_integrity_issues(&self) -> Result<u32> {
        let mut repaired_count = 0;

        // 清理孤立的任务记录
        let deleted_tasks = sqlx::query(
            "DELETE FROM tasks WHERE project_id IS NOT NULL AND project_id NOT IN (SELECT id FROM projects)"
        )
        .execute(&**self.connection.pool())
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to clean orphaned tasks: {}", e)))?;

        repaired_count += deleted_tasks.rows_affected() as u32;

        // 清理孤立的习惯记录
        let deleted_habits = sqlx::query(
            "DELETE FROM habits WHERE area_id NOT IN (SELECT id FROM areas)"
        )
        .execute(&**self.connection.pool())
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to clean orphaned habits: {}", e)))?;

        repaired_count += deleted_habits.rows_affected() as u32;

        Ok(repaired_count)
    }
}
