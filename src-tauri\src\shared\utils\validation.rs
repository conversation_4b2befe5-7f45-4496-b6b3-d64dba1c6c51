// Validation Utilities - 验证工具函数

use crate::shared::errors::{AppError, Result};

pub fn validate_required(value: &str, field_name: &str) -> Result<()> {
    if value.trim().is_empty() {
        Err(AppError::ValidationError(format!("{} is required", field_name)))
    } else {
        Ok(())
    }
}

pub fn validate_length(value: &str, field_name: &str, min: usize, max: usize) -> Result<()> {
    let len = value.len();
    if len < min {
        Err(AppError::ValidationError(format!(
            "{} must be at least {} characters long",
            field_name, min
        )))
    } else if len > max {
        Err(AppError::ValidationError(format!(
            "{} must be no more than {} characters long",
            field_name, max
        )))
    } else {
        Ok(())
    }
}

pub fn validate_email(email: &str) -> Result<()> {
    if email.contains('@') && email.contains('.') && email.len() > 5 {
        Ok(())
    } else {
        Err(AppError::ValidationError("Invalid email format".to_string()))
    }
}

pub fn validate_url(url: &str) -> Result<()> {
    if url.starts_with("http://") || url.starts_with("https://") {
        Ok(())
    } else {
        Err(AppError::ValidationError("Invalid URL format".to_string()))
    }
}

pub fn validate_range<T>(value: T, field_name: &str, min: T, max: T) -> Result<()>
where
    T: PartialOrd + std::fmt::Display,
{
    if value < min {
        Err(AppError::ValidationError(format!(
            "{} must be at least {}",
            field_name, min
        )))
    } else if value > max {
        Err(AppError::ValidationError(format!(
            "{} must be no more than {}",
            field_name, max
        )))
    } else {
        Ok(())
    }
}

pub fn validate_date_range(
    start: chrono::DateTime<chrono::Utc>,
    end: chrono::DateTime<chrono::Utc>,
) -> Result<()> {
    if start >= end {
        Err(AppError::ValidationError(
            "Start date must be before end date".to_string(),
        ))
    } else {
        Ok(())
    }
}

pub fn validate_progress(progress: f32) -> Result<()> {
    if progress < 0.0 || progress > 1.0 {
        Err(AppError::ValidationError(
            "Progress must be between 0.0 and 1.0".to_string(),
        ))
    } else {
        Ok(())
    }
}

pub fn validate_id_format(id: &str) -> Result<()> {
    if id.is_empty() {
        Err(AppError::ValidationError("ID cannot be empty".to_string()))
    } else if id.len() > 100 {
        Err(AppError::ValidationError(
            "ID must be less than 100 characters".to_string(),
        ))
    } else {
        Ok(())
    }
}

pub fn validate_color_hex(color: &str) -> Result<()> {
    if color.starts_with('#') && color.len() == 7 {
        // Check if all characters after # are valid hex
        if color[1..].chars().all(|c| c.is_ascii_hexdigit()) {
            Ok(())
        } else {
            Err(AppError::ValidationError(
                "Invalid hex color format".to_string(),
            ))
        }
    } else {
        Err(AppError::ValidationError(
            "Color must be in hex format (#RRGGBB)".to_string(),
        ))
    }
}
