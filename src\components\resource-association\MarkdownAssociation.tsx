/**
 * Markdown关联组件
 * 支持Markdown文档关联、创建、浏览等功能
 */

import { createSignal, createEffect, For, Show } from 'solid-js'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Textarea } from '../ui/textarea'
import { Badge } from '../ui/badge'
import { Card, CardContent } from '../ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { 
  FileText, 
  Plus, 
  Search,
  Folder,
  File as FileIcon,
  AlertCircle,
  CheckCircle,
  ExternalLink
} from 'lucide-solid'
import { cn } from '../../lib/utils'

import type { MarkdownAssociationProps, MarkdownAssociationData } from './types'

// 模拟文档数据
interface DocumentItem {
  path: string
  title: string
  description?: string
  lastModified: Date
  size: number
  isDirectory: boolean
}

export function MarkdownAssociation(props: MarkdownAssociationProps) {
  // 状态管理
  const [activeTab, setActiveTab] = createSignal<'browse' | 'create'>('browse')
  const [searchQuery, setSearchQuery] = createSignal('')
  const [selectedPath, setSelectedPath] = createSignal('')
  const [currentPath, setCurrentPath] = createSignal('/')
  const [documents, setDocuments] = createSignal<DocumentItem[]>([])
  const [loading, setLoading] = createSignal(false)
  
  // 创建表单状态
  const [newDocTitle, setNewDocTitle] = createSignal('')
  const [newDocPath, setNewDocPath] = createSignal('')
  const [newDocContent, setNewDocContent] = createSignal('')
  const [description, setDescription] = createSignal('')
  const [tags, setTags] = createSignal('')
  const [errors, setErrors] = createSignal<string[]>([])

  // 基础路径
  const basePath = () => props.basePath || props.config?.markdownBasePath || '/documents'

  // 加载文档列表
  const loadDocuments = async (path: string = currentPath()) => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const mockDocuments: DocumentItem[] = [
        {
          path: `${path}/project-notes.md`,
          title: 'Project Notes',
          description: 'General project documentation',
          lastModified: new Date(),
          size: 1024,
          isDirectory: false
        },
        {
          path: `${path}/meeting-minutes`,
          title: 'Meeting Minutes',
          lastModified: new Date(),
          size: 0,
          isDirectory: true
        },
        {
          path: `${path}/requirements.md`,
          title: 'Requirements',
          description: 'Project requirements and specifications',
          lastModified: new Date(Date.now() - 86400000),
          size: 2048,
          isDirectory: false
        }
      ]
      
      setDocuments(mockDocuments)
    } catch (error) {
      console.error('Failed to load documents:', error)
    } finally {
      setLoading(false)
    }
  }

  // 搜索文档
  const searchDocuments = async (query: string) => {
    if (!query.trim()) {
      loadDocuments()
      return
    }
    
    setLoading(true)
    try {
      // 模拟搜索API调用
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const filteredDocs = documents().filter(doc =>
        doc.title.toLowerCase().includes(query.toLowerCase()) ||
        doc.description?.toLowerCase().includes(query.toLowerCase())
      )
      
      setDocuments(filteredDocs)
    } catch (error) {
      console.error('Search failed:', error)
    } finally {
      setLoading(false)
    }
  }

  // 导航到目录
  const navigateToPath = (path: string) => {
    setCurrentPath(path)
    loadDocuments(path)
  }

  // 选择文档
  const selectDocument = (doc: DocumentItem) => {
    if (doc.isDirectory) {
      navigateToPath(doc.path)
    } else {
      setSelectedPath(doc.path)
    }
  }

  // 验证创建表单
  const validateCreateForm = (): boolean => {
    const newErrors: string[] = []
    
    if (!newDocTitle()) {
      newErrors.push('Title is required')
    }
    
    if (!newDocPath()) {
      newErrors.push('File path is required')
    } else if (!newDocPath().endsWith('.md')) {
      newErrors.push('File path must end with .md')
    }
    
    setErrors(newErrors)
    return newErrors.length === 0
  }

  // 关联现有文档
  const handleAssociateExisting = async () => {
    if (!selectedPath()) return
    
    try {
      const data: MarkdownAssociationData = {
        filePath: selectedPath(),
        description: description() || undefined,
        tags: tags().split(',').map(tag => tag.trim()).filter(Boolean)
      }
      
      await props.onAssociate(data)
    } catch (error) {
      console.error('Failed to associate document:', error)
    }
  }

  // 创建新文档
  const handleCreateNew = async () => {
    if (!validateCreateForm()) return
    
    try {
      const fullPath = `${basePath()}/${newDocPath().replace(/^\/+/, '')}`
      
      const data: MarkdownAssociationData = {
        filePath: fullPath,
        title: newDocTitle(),
        description: description() || undefined,
        tags: tags().split(',').map(tag => tag.trim()).filter(Boolean),
        createIfNotExists: true
      }
      
      await props.onAssociate(data)
    } catch (error) {
      console.error('Failed to create document:', error)
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  // 格式化日期
  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  // 初始化
  createEffect(() => {
    loadDocuments()
  })

  // 搜索防抖
  createEffect(() => {
    const query = searchQuery()
    const timeoutId = setTimeout(() => {
      searchDocuments(query)
    }, 300)
    
    return () => clearTimeout(timeoutId)
  })

  return (
    <Dialog open={true} onOpenChange={() => props.onCancel?.()}>
      <DialogContent class="sm:max-w-[700px] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle class="flex items-center gap-2">
            <FileText class="h-5 w-5" />
            Associate Markdown Document
          </DialogTitle>
          <DialogDescription>
            Associate an existing document or create a new one
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab()} onValueChange={setActiveTab}>
          <TabsList class="grid w-full grid-cols-2">
            <TabsTrigger value="browse">Browse Existing</TabsTrigger>
            <TabsTrigger value="create">Create New</TabsTrigger>
          </TabsList>

          {/* 浏览现有文档 */}
          <TabsContent value="browse" class="space-y-4">
            {/* 搜索栏 */}
            <div class="relative">
              <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search documents..."
                value={searchQuery()}
                onInput={(e) => setSearchQuery(e.currentTarget.value)}
                class="pl-10"
              />
            </div>

            {/* 路径导航 */}
            <div class="flex items-center gap-2 text-sm text-muted-foreground">
              <Folder class="h-4 w-4" />
              <span>{currentPath()}</span>
            </div>

            {/* 文档列表 */}
            <div class="border rounded-lg max-h-60 overflow-y-auto">
              <Show when={loading()}>
                <div class="flex items-center justify-center py-8">
                  <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
              </Show>
              
              <Show when={!loading()}>
                <Show when={documents().length === 0}>
                  <div class="text-center py-8 text-muted-foreground">
                    <FileText class="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No documents found</p>
                  </div>
                </Show>
                
                <Show when={documents().length > 0}>
                  <For each={documents()}>
                    {(doc) => (
                      <div
                        class={cn(
                          "flex items-center gap-3 p-3 border-b last:border-b-0 cursor-pointer hover:bg-gray-50 transition-colors",
                          selectedPath() === doc.path && "bg-blue-50 border-blue-200"
                        )}
                        onClick={() => selectDocument(doc)}
                      >
                        <div class="flex-shrink-0">
                          <Show when={doc.isDirectory}>
                            <Folder class="h-5 w-5 text-blue-500" />
                          </Show>
                          <Show when={!doc.isDirectory}>
                            <FileIcon class="h-5 w-5 text-gray-500" />
                          </Show>
                        </div>
                        
                        <div class="flex-1 min-w-0">
                          <p class="font-medium text-sm truncate">{doc.title}</p>
                          <Show when={doc.description}>
                            <p class="text-xs text-muted-foreground truncate">
                              {doc.description}
                            </p>
                          </Show>
                          <div class="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
                            <span>{formatDate(doc.lastModified)}</span>
                            <Show when={!doc.isDirectory}>
                              <span>•</span>
                              <span>{formatFileSize(doc.size)}</span>
                            </Show>
                          </div>
                        </div>
                        
                        <Show when={!doc.isDirectory}>
                          <ExternalLink class="h-4 w-4 text-muted-foreground" />
                        </Show>
                      </div>
                    )}
                  </For>
                </Show>
              </Show>
            </div>

            {/* 选中的文档 */}
            <Show when={selectedPath()}>
              <Card class="border-l-4 border-l-blue-500">
                <CardContent class="p-3">
                  <div class="flex items-center gap-2">
                    <CheckCircle class="h-4 w-4 text-green-500" />
                    <span class="text-sm font-medium">Selected:</span>
                    <span class="text-sm">{selectedPath()}</span>
                  </div>
                </CardContent>
              </Card>
            </Show>
          </TabsContent>

          {/* 创建新文档 */}
          <TabsContent value="create" class="space-y-4">
            <div class="space-y-4">
              <div class="space-y-2">
                <Label for="new-title">Title *</Label>
                <Input
                  id="new-title"
                  placeholder="Enter document title"
                  value={newDocTitle()}
                  onInput={(e) => setNewDocTitle(e.currentTarget.value)}
                  class={cn(errors().some(e => e.includes('Title')) && 'border-red-500')}
                />
              </div>
              
              <div class="space-y-2">
                <Label for="new-path">File Path *</Label>
                <div class="flex items-center gap-2">
                  <span class="text-sm text-muted-foreground">{basePath()}/</span>
                  <Input
                    id="new-path"
                    placeholder="document-name.md"
                    value={newDocPath()}
                    onInput={(e) => setNewDocPath(e.currentTarget.value)}
                    class={cn(errors().some(e => e.includes('path')) && 'border-red-500')}
                  />
                </div>
              </div>
              
              <div class="space-y-2">
                <Label for="new-content">Initial Content (optional)</Label>
                <Textarea
                  id="new-content"
                  placeholder="# Document Title

Add your content here..."
                  value={newDocContent()}
                  onInput={(e) => setNewDocContent(e.currentTarget.value)}
                  rows={4}
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* 通用字段 */}
        <div class="space-y-4 border-t pt-4">
          <div class="space-y-2">
            <Label for="description">Description (optional)</Label>
            <Textarea
              id="description"
              placeholder="Add a description for this association..."
              value={description()}
              onInput={(e) => setDescription(e.currentTarget.value)}
              rows={2}
            />
          </div>
          
          <div class="space-y-2">
            <Label for="tags">Tags (optional)</Label>
            <Input
              id="tags"
              placeholder="Enter tags separated by commas"
              value={tags()}
              onInput={(e) => setTags(e.currentTarget.value)}
            />
            <Show when={tags()}>
              <div class="flex flex-wrap gap-1">
                <For each={tags().split(',').map(tag => tag.trim()).filter(Boolean)}>
                  {(tag) => (
                    <Badge variant="secondary" class="text-xs">
                      {tag}
                    </Badge>
                  )}
                </For>
              </div>
            </Show>
          </div>
        </div>

        {/* 错误信息 */}
        <Show when={errors().length > 0}>
          <div class="space-y-2">
            <For each={errors()}>
              {(error) => (
                <div class="flex items-center gap-2 text-sm text-red-600">
                  <AlertCircle class="h-4 w-4" />
                  <span>{error}</span>
                </div>
              )}
            </For>
          </div>
        </Show>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={props.onCancel}
          >
            Cancel
          </Button>
          <Show when={activeTab() === 'browse'}>
            <Button
              onClick={handleAssociateExisting}
              disabled={!selectedPath() || props.disabled}
            >
              Associate Document
            </Button>
          </Show>
          <Show when={activeTab() === 'create'}>
            <Button
              onClick={handleCreateNew}
              disabled={!newDocTitle() || !newDocPath() || props.disabled}
            >
              <Plus class="h-4 w-4 mr-2" />
              Create & Associate
            </Button>
          </Show>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default MarkdownAssociation
