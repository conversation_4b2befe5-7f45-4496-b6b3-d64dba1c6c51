/**
 * 资源预览组件
 * 支持不同类型资源的预览和快速操作
 */

import { createSignal, createEffect, Show, Switch, Match } from 'solid-js'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Card, CardContent } from '../ui/card'
import { 
  FileText, 
  ExternalLink, 
  Download,
  Edit,
  Share,
  Eye,
  Globe,
  Image,
  Video,
  Music,
  Archive,
  Loader2,
  AlertCircle
} from 'lucide-solid'
import { cn } from '../../lib/utils'

import type { 
  UnifiedResource, 
  MarkdownResource, 
  LinkResource, 
  FileResource,
  ResourceDataSource
} from './types'

interface ResourcePreviewProps {
  resource: UnifiedResource
  open: boolean
  onClose: () => void
  dataSource: ResourceDataSource
  class?: string
}

// 获取资源图标
function getResourceIcon(resource: UnifiedResource) {
  switch (resource.type) {
    case 'markdown':
      return FileText
    case 'link':
      return Globe
    case 'file':
    case 'attachment':
      const fileResource = resource as FileResource
      if (fileResource.mimeType?.startsWith('image/')) return Image
      if (fileResource.mimeType?.startsWith('video/')) return Video
      if (fileResource.mimeType?.startsWith('audio/')) return Music
      if (fileResource.mimeType?.includes('archive')) return Archive
      return FileText
    default:
      return FileText
  }
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 格式化日期
function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

export function ResourcePreview(props: ResourcePreviewProps) {
  // 状态管理
  const [content, setContent] = createSignal<string>('')
  const [loading, setLoading] = createSignal(false)
  const [error, setError] = createSignal<string | null>(null)

  // 加载资源内容
  const loadContent = async () => {
    if (props.resource.type !== 'markdown') return

    setLoading(true)
    setError(null)
    
    try {
      const markdownContent = await props.dataSource.getMarkdownContent(props.resource.id)
      setContent(markdownContent)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load content')
    } finally {
      setLoading(false)
    }
  }

  // 处理外部链接打开
  const handleOpenLink = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  // 处理文件下载
  const handleDownload = async () => {
    try {
      const blob = await props.dataSource.downloadFile(props.resource.id)
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = (props.resource as FileResource).fileName || props.resource.title
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (err) {
      console.error('Download failed:', err)
    }
  }

  // 生命周期
  createEffect(() => {
    if (props.open && props.resource.type === 'markdown') {
      loadContent()
    }
  })

  const Icon = getResourceIcon(props.resource)

  return (
    <Dialog open={props.open} onOpenChange={(open) => !open && props.onClose()}>
      <DialogContent class="sm:max-w-[800px] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle class="flex items-center gap-3">
            <Icon class="h-6 w-6" />
            <div class="flex-1 min-w-0">
              <div class="truncate">{props.resource.title}</div>
              <div class="flex items-center gap-2 mt-1">
                <Badge variant="outline" class="text-xs">
                  {props.resource.type}
                </Badge>
                <Show when={props.resource.tags.length > 0}>
                  <div class="flex gap-1">
                    {props.resource.tags.slice(0, 3).map(tag => (
                      <Badge variant="secondary" class="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    <Show when={props.resource.tags.length > 3}>
                      <Badge variant="secondary" class="text-xs">
                        +{props.resource.tags.length - 3}
                      </Badge>
                    </Show>
                  </div>
                </Show>
              </div>
            </div>
          </DialogTitle>
          <DialogDescription>
            <Show when={props.resource.description}>
              {props.resource.description}
            </Show>
          </DialogDescription>
        </DialogHeader>

        <div class="space-y-4">
          {/* 资源元数据 */}
          <Card>
            <CardContent class="p-4">
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span class="text-muted-foreground">Created:</span>
                  <div class="font-medium">{formatDate(props.resource.createdAt)}</div>
                </div>
                <div>
                  <span class="text-muted-foreground">Updated:</span>
                  <div class="font-medium">{formatDate(props.resource.updatedAt)}</div>
                </div>
                <div>
                  <span class="text-muted-foreground">Access Count:</span>
                  <div class="font-medium">{props.resource.accessCount}</div>
                </div>
                <Show when={props.resource.lastAccessedAt}>
                  <div>
                    <span class="text-muted-foreground">Last Accessed:</span>
                    <div class="font-medium">{formatDate(props.resource.lastAccessedAt!)}</div>
                  </div>
                </Show>
              </div>
            </CardContent>
          </Card>

          {/* 资源特定信息和预览 */}
          <Switch>
            {/* Markdown文档预览 */}
            <Match when={props.resource.type === 'markdown'}>
              <Card>
                <CardContent class="p-4">
                  <div class="space-y-4">
                    {/* Markdown统计信息 */}
                    <div class="grid grid-cols-3 gap-4 text-sm">
                      <Show when={(props.resource as MarkdownResource).wordCount}>
                        <div>
                          <span class="text-muted-foreground">Words:</span>
                          <div class="font-medium">{(props.resource as MarkdownResource).wordCount}</div>
                        </div>
                      </Show>
                      <Show when={(props.resource as MarkdownResource).linksCount}>
                        <div>
                          <span class="text-muted-foreground">Links:</span>
                          <div class="font-medium">{(props.resource as MarkdownResource).linksCount}</div>
                        </div>
                      </Show>
                      <Show when={(props.resource as MarkdownResource).backlinksCount}>
                        <div>
                          <span class="text-muted-foreground">Backlinks:</span>
                          <div class="font-medium">{(props.resource as MarkdownResource).backlinksCount}</div>
                        </div>
                      </Show>
                    </div>

                    {/* 内容预览 */}
                    <div class="border-t pt-4">
                      <h4 class="text-sm font-medium mb-2">Content Preview</h4>
                      <Show when={loading()}>
                        <div class="flex items-center justify-center py-8">
                          <Loader2 class="h-6 w-6 animate-spin" />
                        </div>
                      </Show>
                      <Show when={error()}>
                        <div class="flex items-center gap-2 text-red-600 py-4">
                          <AlertCircle class="h-4 w-4" />
                          <span>{error()}</span>
                        </div>
                      </Show>
                      <Show when={!loading() && !error() && content()}>
                        <div class="prose prose-sm max-w-none max-h-60 overflow-y-auto bg-gray-50 p-4 rounded">
                          <pre class="whitespace-pre-wrap font-sans text-sm">
                            {content().slice(0, 1000)}
                            {content().length > 1000 && '...'}
                          </pre>
                        </div>
                      </Show>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Match>

            {/* 外部链接预览 */}
            <Match when={props.resource.type === 'link'}>
              {() => {
                const linkResource = props.resource as LinkResource
                return (
                  <Card>
                    <CardContent class="p-4">
                      <div class="space-y-4">
                        <div class="flex items-start gap-3">
                          <Show when={linkResource.favicon}>
                            <img 
                              src={linkResource.favicon} 
                              alt="Favicon"
                              class="w-6 h-6 rounded"
                            />
                          </Show>
                          <div class="flex-1">
                            <div class="flex items-center gap-2 mb-2">
                              <span class="font-medium">{linkResource.domain}</span>
                              <Show when={linkResource.isValidUrl}>
                                <Badge variant="outline" class="text-xs text-green-600">
                                  Valid
                                </Badge>
                              </Show>
                              <Show when={linkResource.isValidUrl === false}>
                                <Badge variant="destructive" class="text-xs">
                                  Invalid
                                </Badge>
                              </Show>
                            </div>
                            <p class="text-sm text-muted-foreground break-all">
                              {linkResource.url}
                            </p>
                          </div>
                        </div>

                        <Show when={linkResource.previewTitle || linkResource.previewDescription}>
                          <div class="border-t pt-4">
                            <Show when={linkResource.previewTitle}>
                              <h4 class="font-medium mb-2">{linkResource.previewTitle}</h4>
                            </Show>
                            <Show when={linkResource.previewDescription}>
                              <p class="text-sm text-muted-foreground">
                                {linkResource.previewDescription}
                              </p>
                            </Show>
                          </div>
                        </Show>

                        <Show when={linkResource.previewImage}>
                          <div class="border-t pt-4">
                            <img 
                              src={linkResource.previewImage} 
                              alt="Preview"
                              class="w-full max-h-40 object-cover rounded"
                            />
                          </div>
                        </Show>
                      </div>
                    </CardContent>
                  </Card>
                )
              }}
            </Match>

            {/* 文件预览 */}
            <Match when={props.resource.type === 'file' || props.resource.type === 'attachment'}>
              {() => {
                const fileResource = props.resource as FileResource
                return (
                  <Card>
                    <CardContent class="p-4">
                      <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span class="text-muted-foreground">File Name:</span>
                            <div class="font-medium">{fileResource.fileName}</div>
                          </div>
                          <div>
                            <span class="text-muted-foreground">File Size:</span>
                            <div class="font-medium">{formatFileSize(fileResource.fileSize)}</div>
                          </div>
                          <div>
                            <span class="text-muted-foreground">MIME Type:</span>
                            <div class="font-medium">{fileResource.mimeType}</div>
                          </div>
                          <div>
                            <span class="text-muted-foreground">Status:</span>
                            <Badge variant={fileResource.uploadStatus === 'completed' ? 'default' : 'secondary'}>
                              {fileResource.uploadStatus}
                            </Badge>
                          </div>
                        </div>

                        {/* 文件预览 */}
                        <Show when={fileResource.previewAvailable && fileResource.thumbnailPath}>
                          <div class="border-t pt-4">
                            <h4 class="text-sm font-medium mb-2">Preview</h4>
                            <img 
                              src={fileResource.thumbnailPath} 
                              alt="File preview"
                              class="max-w-full max-h-40 object-contain rounded border"
                            />
                          </div>
                        </Show>
                      </div>
                    </CardContent>
                  </Card>
                )
              }}
            </Match>
          </Switch>

          {/* 操作按钮 */}
          <div class="flex gap-2 pt-4 border-t">
            <Switch>
              <Match when={props.resource.type === 'link'}>
                <Button 
                  onClick={() => handleOpenLink((props.resource as LinkResource).url)}
                  class="flex-1"
                >
                  <ExternalLink class="h-4 w-4 mr-2" />
                  Open Link
                </Button>
              </Match>
              <Match when={props.resource.type === 'file' || props.resource.type === 'attachment'}>
                <Button 
                  onClick={handleDownload}
                  class="flex-1"
                >
                  <Download class="h-4 w-4 mr-2" />
                  Download
                </Button>
              </Match>
              <Match when={props.resource.type === 'markdown'}>
                <Button class="flex-1">
                  <Edit class="h-4 w-4 mr-2" />
                  Edit Document
                </Button>
              </Match>
            </Switch>
            
            <Button variant="outline">
              <Share class="h-4 w-4 mr-2" />
              Share
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default ResourcePreview
