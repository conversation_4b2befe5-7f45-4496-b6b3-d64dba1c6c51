// Task Entity - 任务实体

use crate::shared::types::{Id, Metadata, EntityStatus, Priority};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Task {
    pub id: Id,
    pub title: String,
    pub description: Option<String>,
    pub project_id: Option<Id>,
    pub parent_task_id: Option<Id>,
    pub due_date: Option<chrono::DateTime<chrono::Utc>>,
    pub status: TaskStatus,
    pub priority: Priority,
    pub progress: f32,
    pub entity_status: EntityStatus,
    pub metadata: Metadata,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub enum TaskStatus {
    Todo,
    InProgress,
    Completed,
    Cancelled,
}

impl Default for TaskStatus {
    fn default() -> Self {
        TaskStatus::Todo
    }
}

impl Task {
    pub fn new(title: String, project_id: Option<Id>) -> Self {
        Self {
            id: crate::shared::utils::IdGenerator::new_id_with_prefix("task"),
            title,
            description: None,
            project_id,
            parent_task_id: None,
            due_date: None,
            status: TaskStatus::default(),
            priority: Priority::default(),
            progress: 0.0,
            entity_status: EntityStatus::Active,
            metadata: Metadata::default(),
        }
    }
}
