import { InputRule } from '@milkdown/kit/prose/inputrules'
import { TextSelection } from '@milkdown/kit/prose/state'
import { $inputRule, $useKeymap } from '@milkdown/kit/utils'

/**
 * 智能输入管理器
 * 解决 Crepe 自动转义问题，提供更好的输入体验
 */

/**
 * 单方括号输入规则：[ → []
 * 当用户输入单个 [ 时，自动补全为 []，光标置于中间
 * 使用延迟检查避免与双方括号输入冲突
 */
export const singleBracketInputRule = $inputRule(() => {
  return new InputRule(
    /\[$/,  // 匹配行尾的单个 [
    (state, match, start, end) => {
      console.log('🔧 单方括号输入规则触发:', { match, start, end })

      // 延迟执行，让双方括号规则有机会先执行
      setTimeout(() => {
        // 检查当前位置是否已经被双方括号规则处理
        const currentText = state.doc.textBetween(start - 1, start + 3)
        if (currentText.includes('[[')) {
          console.log('⏭️ 跳过单方括号补全（已被双方括号规则处理）')
          return
        }

        // 如果没有被处理，则执行单方括号补全
        const tr = state.tr.insertText(']', start + 1)
        const newSelection = TextSelection.create(tr.doc, start + 1)
        state.apply(tr.setSelection(newSelection))
        console.log('✅ 延迟单方括号自动补全完成')
      }, 50)

      // 暂时不做任何操作，让其他规则先执行
      return null
    }
  )
})

/**
 * 双方括号输入规则：[[ → [[]]
 * 当用户输入 [[ 时，自动补全为 [[]]，光标置于中间
 */
/**
 * 基于键盘事件的双方括号处理
 * 监听 [ 键的输入，检查是否形成 [[
 */
export const doubleBracketKeymap = $useKeymap('doubleBracketKeymap', {
  HandleBracket: {
    shortcuts: '[',
    command: (ctx) => () => (state, dispatch) => {
      console.log('🔧 监听到 [ 键输入')

      // 获取当前光标位置
      const { selection } = state
      const { from } = selection

      // 检查光标前一个字符是否是 [
      if (from > 0) {
        const beforeChar = state.doc.textBetween(from - 1, from)
        console.log('🔍 光标前的字符:', beforeChar)

        if (beforeChar === '[') {
          console.log('🎯 检测到双方括号输入，执行自动补全')

          // 插入 [ 和 ]]，然后将光标置于中间
          const tr = state.tr
            .insertText('[', from)  // 插入当前的 [
            .insertText(']]', from + 1)  // 插入 ]]

          const newSelection = TextSelection.create(tr.doc, from + 1)

          if (dispatch) {
            dispatch(tr.setSelection(newSelection))
          }

          console.log('✅ 双方括号自动补全完成')
          return true
        }
      }

      // 如果不是双方括号情况，返回 false 让默认处理继续
      return false
    }
  }
})

// 双方括号输入规则 - 使用更精确的匹配策略
export const doubleBracketInputRule = $inputRule(() => {
  return new InputRule(
    /\[\[$/,  // 匹配以 [[ 结尾
    (state, match, start, end) => {
      console.log('🔧 双方括号输入规则触发:', { match, start, end })
      console.log('🔍 匹配数组:', match)
      console.log('🔍 匹配的完整文本:', match[0])
      console.log('🔍 文档总长度:', state.doc.content.size)

      // 从日志分析：当输入 [[ 时，match[0] 是 "[[" 但 start/end 指向的是最后一个字符
      // 所以我们需要向前查找实际的 [[ 位置

      // 向前搜索 [[ 的实际位置
      let actualStart = -1
      let actualEnd = -1

      // 从当前位置向前搜索，找到最近的 [[
      for (let i = Math.max(0, end - 10); i <= end; i++) {
        const text = state.doc.textBetween(i, Math.min(state.doc.content.size, i + 2))
        if (text === '[[') {
          actualStart = i
          actualEnd = i + 2
          break
        }
      }

      console.log('🔍 搜索到的实际位置:', { actualStart, actualEnd })

      if (actualStart !== -1 && actualEnd !== -1) {
        const actualText = state.doc.textBetween(actualStart, actualEnd)
        console.log('🔍 实际位置的文本:', `"${actualText}"`)

        if (actualText === '[[') {
          console.log('✅ 确认是双方括号，执行自动补全')

          // 在 [[ 后面插入 ]]，光标置于中间
          const tr = state.tr.insertText(']]', actualEnd)
          const newSelection = TextSelection.create(tr.doc, actualEnd)

          console.log('✅ 双方括号自动补全完成，光标位置:', actualEnd)
          return tr.setSelection(newSelection)
        }
      }

      console.log('⏭️ 跳过补全，未找到有效的 [[ 位置')
      return null
    }
  )
})

/**
 * 智能退格处理：删除配对的括号
 * 当光标在 [] 或 [[]] 中间时，退格删除整个结构
 */
export const smartBackspaceInputRule = $inputRule(() => {
  return new InputRule(
    /\[\]$|^\[\[\]\]$/,  // 匹配空的 [] 或 [[]]
    (state, match, start, end) => {
      console.log('🔧 智能退格规则触发:', { match, start, end })
      
      // 删除整个括号结构
      const tr = state.tr.delete(start, end)
      
      console.log('✅ 智能退格完成')
      return tr
    }
  )
})

/**
 * 防止转义的文本处理
 * 在内容序列化时移除不必要的转义字符
 */
export function unescapeContent(content: string): string {
  return content
    .replace(/\\\[/g, '[')    // 移除方括号转义
    .replace(/\\\]/g, ']')    // 移除方括号转义
    .replace(/\\\(/g, '(')    // 移除圆括号转义
    .replace(/\\\)/g, ')')    // 移除圆括号转义
}

/**
 * 智能内容清理
 * 在保存前清理内容中的转义字符
 */
export function cleanContent(content: string): string {
  console.log('🧹 开始清理内容转义字符')
  
  const cleaned = unescapeContent(content)
  
  if (cleaned !== content) {
    console.log('✅ 内容清理完成，移除了转义字符')
    console.log('原始:', content.substring(0, 100))
    console.log('清理后:', cleaned.substring(0, 100))
  }
  
  return cleaned
}

/**
 * 检查是否为 WikiLink 语法
 */
export function isWikiLinkSyntax(text: string, position: number): boolean {
  // 检查光标前后是否为 WikiLink 语法
  const before = text.substring(Math.max(0, position - 2), position)
  const after = text.substring(position, Math.min(text.length, position + 2))
  
  return before === '[[' || after === ']]' || (before.includes('[') && after.includes(']'))
}

/**
 * 获取光标位置的 WikiLink 信息
 */
export function getWikiLinkAtPosition(text: string, position: number): {
  target: string
  display?: string
  start: number
  end: number
} | null {
  // 向前查找 [[
  let start = -1
  for (let i = position; i >= 0; i--) {
    if (text.substring(i, i + 2) === '[[') {
      start = i
      break
    }
    if (text[i] === '\n') break // 不跨行查找
  }
  
  if (start === -1) return null
  
  // 向后查找 ]]
  let end = -1
  for (let i = position; i < text.length - 1; i++) {
    if (text.substring(i, i + 2) === ']]') {
      end = i + 2
      break
    }
    if (text[i] === '\n') break // 不跨行查找
  }
  
  if (end === -1) return null
  
  // 解析内容
  const content = text.substring(start + 2, end - 2)
  const parts = content.split('|')
  
  return {
    target: parts[0].trim(),
    display: parts[1]?.trim(),
    start,
    end
  }
}

/**
 * 智能输入配置
 */
export const smartInputConfig = {
  // 禁用自动转义
  disableAutoEscape: true,
  
  // 启用智能补全
  enableSmartCompletion: true,
  
  // 启用智能退格
  enableSmartBackspace: true,
  
  // 自动清理转义字符
  autoCleanEscape: true
}

export default {
  singleBracketInputRule,
  doubleBracketInputRule,
  doubleBracketKeymap,
  smartBackspaceInputRule,
  unescapeContent,
  cleanContent,
  isWikiLinkSyntax,
  getWikiLinkAtPosition,
  smartInputConfig
}
