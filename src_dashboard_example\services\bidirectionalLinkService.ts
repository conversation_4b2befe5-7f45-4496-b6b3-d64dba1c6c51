import type { DocumentLink } from '@prisma/client'
import type { DatabaseResult } from '../../../shared/types'
import { databaseApi } from '../lib/api'

// 双向链接数据接口
export interface BidirectionalLinkData {
  id: string
  sourceDocPath: string
  sourceDocTitle?: string
  targetDocPath: string
  targetDocTitle?: string
  linkText: string
  displayText?: string
  linkType: string
  startPosition: number
  endPosition: number
  lineNumber: number
  columnNumber: number
  contextBefore?: string
  contextAfter?: string
  isValid: boolean
  linkStrength: number
  createdAt: string
  updatedAt: string
  lastValidated: string
}

// 链接统计接口
export interface LinkStatistics {
  totalLinks: number
  validLinks: number
  invalidLinks: number
  backlinkCount: number
  outlinkCount: number
  linkStrengthAvg: number
}

// 创建链接请求接口
export interface CreateLinkRequest {
  sourceDocPath: string
  sourceDocTitle?: string
  targetDocPath: string
  targetDocTitle?: string
  linkText: string
  displayText?: string
  linkType?: string
  startPosition: number
  endPosition: number
  lineNumber: number
  columnNumber: number
  contextBefore?: string
  contextAfter?: string
  linkStrength?: number
}

/**
 * 双向链接服务
 * 处理文档间的双向链接关系
 */
export class BidirectionalLinkService {
  /**
   * 获取文档的双向链接数据
   */
  async getDocumentLinks(docPath: string): Promise<{
    backlinks: BidirectionalLinkData[]
    outlinks: BidirectionalLinkData[]
    statistics: LinkStatistics
  }> {
    try {
      const result: DatabaseResult<{
        backlinks: DocumentLink[]
        outlinks: DocumentLink[]
      }> = await databaseApi.getDocumentLinks(docPath)

      if (!result.success || !result.data) {
        return {
          backlinks: [],
          outlinks: [],
          statistics: this.createEmptyStatistics()
        }
      }

      const { backlinks, outlinks } = result.data

      // 转换数据格式
      const backlinkData = backlinks.map(this.convertToLinkData)
      const outlinkData = outlinks.map(this.convertToLinkData)

      // 计算统计信息
      const statistics = this.calculateStatistics(backlinkData, outlinkData)

      return {
        backlinks: backlinkData,
        outlinks: outlinkData,
        statistics
      }
    } catch (error) {
      console.error('获取文档链接失败:', error)
      return {
        backlinks: [],
        outlinks: [],
        statistics: this.createEmptyStatistics()
      }
    }
  }

  /**
   * 创建新的链接
   */
  async createLink(linkData: CreateLinkRequest): Promise<BidirectionalLinkData | null> {
    try {
      const result: DatabaseResult<DocumentLink> = await databaseApi.createDocumentLink(linkData)

      if (result.success && result.data) {
        return this.convertToLinkData(result.data)
      }

      return null
    } catch (error) {
      console.error('创建链接失败:', error)
      return null
    }
  }

  /**
   * 批量替换文档的链接
   */
  async replaceDocumentLinks(
    sourceDocPath: string,
    links: CreateLinkRequest[]
  ): Promise<BidirectionalLinkData[]> {
    try {
      const result: DatabaseResult<DocumentLink[]> = await databaseApi.replaceDocumentLinks({ sourceDocPath, links })

      if (result.success && result.data) {
        return result.data.map(this.convertToLinkData)
      }

      return []
    } catch (error) {
      console.error('替换文档链接失败:', error)
      return []
    }
  }

  /**
   * 验证链接有效性
   */
  async validateLinks(docPath: string): Promise<void> {
    try {
      await databaseApi.markLinksAsValid(docPath)
    } catch (error) {
      console.error('验证链接失败:', error)
    }
  }

  /**
   * 获取链接统计信息
   */
  async getLinkStatistics(docPath: string): Promise<LinkStatistics> {
    try {
      const result: DatabaseResult<LinkStatistics> = await databaseApi.getLinkStatistics(docPath)

      if (result.success && result.data) {
        return result.data
      }

      return this.createEmptyStatistics()
    } catch (error) {
      console.error('获取链接统计失败:', error)
      return this.createEmptyStatistics()
    }
  }

  /**
   * 转换数据库记录为前端数据格式
   */
  private convertToLinkData(link: DocumentLink): BidirectionalLinkData {
    return {
      id: link.id,
      sourceDocPath: link.sourceDocPath,
      sourceDocTitle: link.sourceDocTitle || undefined,
      targetDocPath: link.targetDocPath,
      targetDocTitle: link.targetDocTitle || undefined,
      linkText: link.linkText,
      displayText: link.displayText || undefined,
      linkType: link.linkType,
      startPosition: link.startPosition,
      endPosition: link.endPosition,
      lineNumber: link.lineNumber,
      columnNumber: link.columnNumber,
      contextBefore: link.contextBefore || undefined,
      contextAfter: link.contextAfter || undefined,
      isValid: link.isValid,
      linkStrength: link.linkStrength,
      createdAt: link.createdAt.toISOString(),
      updatedAt: link.updatedAt.toISOString(),
      lastValidated: link.lastValidated.toISOString()
    }
  }

  /**
   * 计算链接统计信息
   */
  private calculateStatistics(
    backlinks: BidirectionalLinkData[],
    outlinks: BidirectionalLinkData[]
  ): LinkStatistics {
    const allLinks = [...backlinks, ...outlinks]
    const validLinks = allLinks.filter(link => link.isValid)
    const invalidLinks = allLinks.filter(link => !link.isValid)

    const linkStrengthSum = allLinks.reduce((sum, link) => sum + link.linkStrength, 0)
    const linkStrengthAvg = allLinks.length > 0 ? linkStrengthSum / allLinks.length : 0

    return {
      totalLinks: allLinks.length,
      validLinks: validLinks.length,
      invalidLinks: invalidLinks.length,
      backlinkCount: backlinks.length,
      outlinkCount: outlinks.length,
      linkStrengthAvg
    }
  }

  /**
   * 创建空的统计信息
   */
  private createEmptyStatistics(): LinkStatistics {
    return {
      totalLinks: 0,
      validLinks: 0,
      invalidLinks: 0,
      backlinkCount: 0,
      outlinkCount: 0,
      linkStrengthAvg: 0
    }
  }
}

// 导出单例实例
export const bidirectionalLinkService = new BidirectionalLinkService()
