/**
 * KPI配置文件
 * 定义项目KPI和领域指标的配置
 */

import type { KPIConfig } from '../../../shared/types/kpi'

// 通用单位列表
export const COMMON_UNITS = [
  'none', // 无单位
  '%', // 百分比
  '个', // 数量
  '人', // 人数
  '次', // 次数
  '分', // 评分
  '元', // 金额
  'kg', // 重量
  'h', // 小时
  'min', // 分钟
  'days', // 天数
  'ms', // 毫秒
  'MB', // 存储
  'GB', // 存储
]

// 项目KPI配置
export const PROJECT_KPI_CONFIG: KPIConfig = {
  allowedUnits: COMMON_UNITS,
  defaultFrequency: 'weekly',
  validationRules: {
    nameMaxLength: 100,
    valueRequired: true,
    targetRequired: false
  },
  templates: [
    {
      name: 'Bug修复数',
      unit: '个',
      target: '10',
      direction: 'increase'
    },
    {
      name: '新增用户',
      unit: '人',
      target: '100',
      direction: 'increase'
    },
    {
      name: '完成率',
      unit: '%',
      target: '100',
      direction: 'increase'
    },
    {
      name: '代码覆盖率',
      unit: '%',
      target: '80',
      direction: 'increase'
    },
    {
      name: '响应时间',
      unit: 'ms',
      target: '200',
      direction: 'decrease'
    },
    {
      name: '用户满意度',
      unit: '分',
      target: '4.5',
      direction: 'increase'
    },
    {
      name: '销售额',
      unit: '元',
      target: '10000',
      direction: 'increase'
    },
    {
      name: '页面访问量',
      unit: '次',
      target: '1000',
      direction: 'increase'
    },
    {
      name: '错误率',
      unit: '%',
      target: '1',
      direction: 'decrease'
    },
    {
      name: '加载时间',
      unit: 'ms',
      target: '500',
      direction: 'decrease'
    }
  ]
}

// 领域指标配置
export const AREA_METRIC_CONFIG: KPIConfig = {
  allowedUnits: COMMON_UNITS,
  defaultFrequency: 'daily',
  validationRules: {
    nameMaxLength: 100,
    valueRequired: true,
    targetRequired: false
  },
  templates: [
    {
      name: '体重',
      unit: 'kg',
      target: '65',
      direction: 'decrease'
    },
    {
      name: '运动时长',
      unit: 'min',
      target: '30',
      direction: 'increase'
    },
    {
      name: '阅读页数',
      unit: '页',
      target: '20',
      direction: 'increase'
    },
    {
      name: '学习时长',
      unit: 'h',
      target: '2',
      direction: 'increase'
    },
    {
      name: '睡眠时长',
      unit: 'h',
      target: '8',
      direction: 'increase'
    },
    {
      name: '水分摄入',
      unit: 'L',
      target: '2',
      direction: 'increase'
    },
    {
      name: '步数',
      unit: '步',
      target: '10000',
      direction: 'increase'
    },
    {
      name: '冥想时长',
      unit: 'min',
      target: '15',
      direction: 'increase'
    },
    {
      name: '屏幕时间',
      unit: 'h',
      target: '4',
      direction: 'decrease'
    },
    {
      name: '咖啡摄入',
      unit: '杯',
      target: '2',
      direction: 'decrease'
    }
  ]
}

// 频率选项
export const FREQUENCY_OPTIONS = [
  { value: 'daily', label: 'Daily', description: 'Track every day' },
  { value: 'weekly', label: 'Weekly', description: 'Track once per week' },
  { value: 'monthly', label: 'Monthly', description: 'Track once per month' },
  { value: 'quarterly', label: 'Quarterly', description: 'Track once per quarter' }
]

// 方向选项
export const DIRECTION_OPTIONS = [
  {
    value: 'increase',
    label: 'Increase',
    description: 'Higher values are better',
    icon: 'TrendingUp',
    color: 'text-green-600'
  },
  {
    value: 'decrease',
    label: 'Decrease', 
    description: 'Lower values are better',
    icon: 'TrendingDown',
    color: 'text-red-600'
  }
]

// 状态颜色配置
export const STATUS_COLORS = {
  achieved: {
    bg: 'bg-green-100',
    text: 'text-green-800',
    border: 'border-green-200',
    progress: 'bg-green-500'
  },
  'on-track': {
    bg: 'bg-blue-100',
    text: 'text-blue-800',
    border: 'border-blue-200',
    progress: 'bg-blue-500'
  },
  'at-risk': {
    bg: 'bg-yellow-100',
    text: 'text-yellow-800',
    border: 'border-yellow-200',
    progress: 'bg-yellow-500'
  },
  behind: {
    bg: 'bg-red-100',
    text: 'text-red-800',
    border: 'border-red-200',
    progress: 'bg-red-500'
  },
  'no-target': {
    bg: 'bg-gray-100',
    text: 'text-gray-800',
    border: 'border-gray-200',
    progress: 'bg-gray-500'
  }
}

// 进度阈值
export const PROGRESS_THRESHOLDS = {
  achieved: 100,
  onTrack: 75,
  atRisk: 50,
  behind: 0
}

// 缓存配置
export const CACHE_CONFIG = {
  defaultTTL: 5 * 60 * 1000, // 5分钟
  metricsTTL: 2 * 60 * 1000, // 2分钟
  recordsTTL: 1 * 60 * 1000, // 1分钟
  maxCacheSize: 1000, // 最大缓存条目数
  cleanupInterval: 10 * 60 * 1000 // 10分钟清理一次
}

// 查询配置
export const QUERY_CONFIG = {
  defaultRecordLimit: 20,
  maxRecordLimit: 100,
  defaultSortBy: 'updatedAt',
  defaultSortOrder: 'desc' as const
}

// 验证配置
export const VALIDATION_CONFIG = {
  minNameLength: 1,
  maxNameLength: 100,
  maxUnitLength: 20,
  maxNoteLength: 500,
  allowNegativeValues: false,
  requireTarget: false
}

// 导出默认配置
export const DEFAULT_KPI_CONFIG = PROJECT_KPI_CONFIG
export const DEFAULT_AREA_CONFIG = AREA_METRIC_CONFIG

// 配置工厂函数
export function createKPIConfig(overrides: Partial<KPIConfig> = {}): KPIConfig {
  return {
    ...PROJECT_KPI_CONFIG,
    ...overrides
  }
}

export function createAreaConfig(overrides: Partial<KPIConfig> = {}): KPIConfig {
  return {
    ...AREA_METRIC_CONFIG,
    ...overrides
  }
}

// 根据类型获取配置
export function getConfigByType(type: 'project' | 'area'): KPIConfig {
  return type === 'project' ? PROJECT_KPI_CONFIG : AREA_METRIC_CONFIG
}

// 获取单位显示名称
export function getUnitDisplayName(unit: string): string {
  const unitMap: Record<string, string> = {
    '': 'No unit',
    '%': 'Percentage',
    '个': 'Count',
    '人': 'People',
    '次': 'Times',
    '分': 'Points',
    '元': 'Yuan',
    'kg': 'Kilograms',
    'h': 'Hours',
    'min': 'Minutes',
    'days': 'Days',
    'ms': 'Milliseconds',
    'MB': 'Megabytes',
    'GB': 'Gigabytes'
  }
  
  return unitMap[unit] || unit
}

// 获取频率显示名称
export function getFrequencyDisplayName(frequency: string): string {
  const option = FREQUENCY_OPTIONS.find(opt => opt.value === frequency)
  return option?.label || frequency
}
