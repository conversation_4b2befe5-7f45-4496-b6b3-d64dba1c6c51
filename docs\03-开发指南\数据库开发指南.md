# PaoLife 数据库开发指南

## 📋 概述

本文档提供了PaoLife项目数据库开发的完整指南，包括SQLite配置、SQLx使用、迁移管理、查询优化等内容。

## 🗄️ 数据库架构

### 1. SQLite 配置

#### 数据库连接
```rust
// src-tauri/src/infrastructure/database/connection.rs
use sqlx::{sqlite::SqlitePoolOptions, SqlitePool};
use std::time::Duration;

pub async fn create_pool(database_url: &str) -> Result<SqlitePool, sqlx::Error> {
    SqlitePoolOptions::new()
        .max_connections(20)
        .acquire_timeout(Duration::from_secs(30))
        .connect(database_url)
        .await
}

// 启用 WAL 模式和外键约束
pub async fn configure_database(pool: &SqlitePool) -> Result<(), sqlx::Error> {
    sqlx::query("PRAGMA journal_mode = WAL")
        .execute(pool)
        .await?;
    
    sqlx::query("PRAGMA foreign_keys = ON")
        .execute(pool)
        .await?;
    
    sqlx::query("PRAGMA synchronous = NORMAL")
        .execute(pool)
        .await?;
    
    Ok(())
}
```

#### 连接池管理
```rust
// src-tauri/src/infrastructure/database/mod.rs
use once_cell::sync::OnceCell;
use sqlx::SqlitePool;

static DB_POOL: OnceCell<SqlitePool> = OnceCell::new();

pub fn get_pool() -> &'static SqlitePool {
    DB_POOL.get().expect("Database pool not initialized")
}

pub async fn initialize_database(database_url: &str) -> Result<(), Box<dyn std::error::Error>> {
    let pool = create_pool(database_url).await?;
    configure_database(&pool).await?;
    
    DB_POOL.set(pool).map_err(|_| "Failed to set database pool")?;
    Ok(())
}
```

### 2. 迁移管理

#### 迁移文件结构
```
migrations/
├── 20241201000001_initial_schema.sql
├── 20241201000002_add_users_table.sql
├── 20241201000003_add_projects_table.sql
├── 20241201000004_add_tasks_table.sql
└── 20241201000005_add_indexes.sql
```

#### 迁移命令
```bash
# 创建新迁移
sqlx migrate add create_users_table

# 运行迁移
sqlx migrate run

# 回滚迁移
sqlx migrate revert

# 检查迁移状态
sqlx migrate info
```

#### 示例迁移文件
```sql
-- migrations/20241201000002_add_users_table.sql
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    username TEXT NOT NULL UNIQUE,
    email TEXT UNIQUE,
    password_hash TEXT NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    timezone TEXT DEFAULT 'UTC',
    language TEXT DEFAULT 'zh-CN',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);
```

## 🔧 SQLx 使用指南

### 1. 查询构建

#### 基本查询
```rust
// 查询单个记录
pub async fn find_user_by_id(pool: &SqlitePool, id: &str) -> Result<Option<User>, sqlx::Error> {
    let user = sqlx::query_as!(
        User,
        "SELECT * FROM users WHERE id = ?",
        id
    )
    .fetch_optional(pool)
    .await?;
    
    Ok(user)
}

// 查询多个记录
pub async fn find_users_by_status(
    pool: &SqlitePool, 
    is_active: bool
) -> Result<Vec<User>, sqlx::Error> {
    let users = sqlx::query_as!(
        User,
        "SELECT * FROM users WHERE is_active = ? ORDER BY created_at DESC",
        is_active
    )
    .fetch_all(pool)
    .await?;
    
    Ok(users)
}
```

#### 复杂查询
```rust
// 分页查询
pub async fn find_users_paginated(
    pool: &SqlitePool,
    page: i32,
    page_size: i32,
    search: Option<&str>,
) -> Result<(Vec<User>, i64), sqlx::Error> {
    let offset = (page - 1) * page_size;
    
    let mut query = "SELECT * FROM users WHERE 1=1".to_string();
    let mut count_query = "SELECT COUNT(*) as count FROM users WHERE 1=1".to_string();
    
    if let Some(search_term) = search {
        let search_condition = " AND (username LIKE ? OR email LIKE ? OR full_name LIKE ?)";
        query.push_str(search_condition);
        count_query.push_str(search_condition);
    }
    
    query.push_str(" ORDER BY created_at DESC LIMIT ? OFFSET ?");
    
    let users = if let Some(search_term) = search {
        let search_pattern = format!("%{}%", search_term);
        sqlx::query_as::<_, User>(&query)
            .bind(&search_pattern)
            .bind(&search_pattern)
            .bind(&search_pattern)
            .bind(page_size)
            .bind(offset)
            .fetch_all(pool)
            .await?
    } else {
        sqlx::query_as::<_, User>(&query)
            .bind(page_size)
            .bind(offset)
            .fetch_all(pool)
            .await?
    };
    
    let total: (i64,) = if let Some(search_term) = search {
        let search_pattern = format!("%{}%", search_term);
        sqlx::query_as(&count_query)
            .bind(&search_pattern)
            .bind(&search_pattern)
            .bind(&search_pattern)
            .fetch_one(pool)
            .await?
    } else {
        sqlx::query_as(&count_query)
            .fetch_one(pool)
            .await?
    };
    
    Ok((users, total.0))
}
```

### 2. 事务处理

#### 基本事务
```rust
pub async fn create_project_with_tasks(
    pool: &SqlitePool,
    project_data: CreateProjectData,
    tasks_data: Vec<CreateTaskData>,
) -> Result<Project, sqlx::Error> {
    let mut tx = pool.begin().await?;
    
    // 创建项目
    let project = sqlx::query_as!(
        Project,
        "INSERT INTO projects (id, name, description, status, created_by, created_at) 
         VALUES (?, ?, ?, ?, ?, ?) 
         RETURNING *",
        project_data.id,
        project_data.name,
        project_data.description,
        project_data.status,
        project_data.created_by,
        project_data.created_at
    )
    .fetch_one(&mut *tx)
    .await?;
    
    // 创建任务
    for task_data in tasks_data {
        sqlx::query!(
            "INSERT INTO tasks (id, title, project_id, created_by, created_at) 
             VALUES (?, ?, ?, ?, ?)",
            task_data.id,
            task_data.title,
            project.id,
            task_data.created_by,
            task_data.created_at
        )
        .execute(&mut *tx)
        .await?;
    }
    
    tx.commit().await?;
    Ok(project)
}
```

#### 错误处理和回滚
```rust
pub async fn update_project_progress(
    pool: &SqlitePool,
    project_id: &str,
) -> Result<(), AppError> {
    let mut tx = pool.begin().await?;
    
    match calculate_and_update_progress(&mut tx, project_id).await {
        Ok(_) => {
            tx.commit().await?;
            Ok(())
        }
        Err(e) => {
            tx.rollback().await?;
            Err(AppError::Database(e.to_string()))
        }
    }
}
```

### 3. 类型映射

#### 自定义类型
```rust
// 枚举类型映射
#[derive(Debug, Clone, sqlx::Type)]
#[sqlx(type_name = "project_status", rename_all = "snake_case")]
pub enum ProjectStatus {
    NotStarted,
    InProgress,
    AtRisk,
    Paused,
    Completed,
    Archived,
}

// JSON 类型映射
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectMetadata {
    pub tags: Vec<String>,
    pub custom_fields: HashMap<String, String>,
}

impl sqlx::Type<sqlx::Sqlite> for ProjectMetadata {
    fn type_info() -> sqlx::sqlite::SqliteTypeInfo {
        <String as sqlx::Type<sqlx::Sqlite>>::type_info()
    }
}

impl<'r> sqlx::Decode<'r, sqlx::Sqlite> for ProjectMetadata {
    fn decode(value: sqlx::sqlite::SqliteValueRef<'r>) -> Result<Self, sqlx::error::BoxDynError> {
        let json_str = <String as sqlx::Decode<sqlx::Sqlite>>::decode(value)?;
        Ok(serde_json::from_str(&json_str)?)
    }
}

impl<'q> sqlx::Encode<'q, sqlx::Sqlite> for ProjectMetadata {
    fn encode_by_ref(&self, args: &mut Vec<sqlx::sqlite::SqliteArgumentValue<'q>>) -> sqlx::encode::IsNull {
        let json_str = serde_json::to_string(self).unwrap();
        args.push(sqlx::sqlite::SqliteArgumentValue::Text(json_str.into()));
        sqlx::encode::IsNull::No
    }
}
```

## 🚀 性能优化

### 1. 索引策略

#### 单列索引
```sql
-- 频繁查询的列
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);
CREATE INDEX idx_users_email ON users(email);
```

#### 复合索引
```sql
-- 多条件查询优化
CREATE INDEX idx_tasks_project_status ON tasks(project_id, status);
CREATE INDEX idx_projects_area_status ON projects(area_id, status);
CREATE INDEX idx_habit_records_habit_date ON habit_records(habit_id, completed_date);
```

#### 部分索引
```sql
-- 只为活跃记录创建索引
CREATE INDEX idx_active_projects ON projects(created_at) WHERE status != 'archived';
CREATE INDEX idx_pending_tasks ON tasks(due_date) WHERE status IN ('todo', 'in_progress');
```

### 2. 查询优化

#### 使用 EXPLAIN QUERY PLAN
```rust
pub async fn analyze_query_performance(pool: &SqlitePool, query: &str) -> Result<(), sqlx::Error> {
    let explain_query = format!("EXPLAIN QUERY PLAN {}", query);
    let rows = sqlx::query(&explain_query)
        .fetch_all(pool)
        .await?;
    
    for row in rows {
        println!("Query plan: {:?}", row);
    }
    
    Ok(())
}
```

#### 批量操作优化
```rust
pub async fn batch_insert_tasks(
    pool: &SqlitePool,
    tasks: Vec<CreateTaskData>,
) -> Result<(), sqlx::Error> {
    let mut tx = pool.begin().await?;
    
    // 使用批量插入
    for chunk in tasks.chunks(100) {
        let mut query_builder = sqlx::QueryBuilder::new(
            "INSERT INTO tasks (id, title, project_id, status, created_at) "
        );
        
        query_builder.push_values(chunk, |mut b, task| {
            b.push_bind(&task.id)
             .push_bind(&task.title)
             .push_bind(&task.project_id)
             .push_bind(&task.status)
             .push_bind(&task.created_at);
        });
        
        query_builder.build().execute(&mut *tx).await?;
    }
    
    tx.commit().await?;
    Ok(())
}
```

### 3. 连接池优化

#### 配置调优
```rust
pub fn create_optimized_pool(database_url: &str) -> SqlitePoolOptions {
    SqlitePoolOptions::new()
        .max_connections(20)                    // 最大连接数
        .min_connections(5)                     // 最小连接数
        .acquire_timeout(Duration::from_secs(30)) // 获取连接超时
        .idle_timeout(Duration::from_secs(600))   // 空闲连接超时
        .max_lifetime(Duration::from_secs(1800))  // 连接最大生命周期
}
```

## 🧪 测试策略

### 1. 数据库测试

#### 测试数据库设置
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::SqlitePool;
    use tempfile::NamedTempFile;
    
    async fn setup_test_db() -> SqlitePool {
        let temp_file = NamedTempFile::new().unwrap();
        let database_url = format!("sqlite:{}", temp_file.path().display());
        
        let pool = SqlitePoolOptions::new()
            .connect(&database_url)
            .await
            .unwrap();
        
        // 运行迁移
        sqlx::migrate!("./migrations")
            .run(&pool)
            .await
            .unwrap();
        
        pool
    }
    
    #[tokio::test]
    async fn test_create_user() {
        let pool = setup_test_db().await;
        
        let user_data = CreateUserData {
            id: "test_user_1".to_string(),
            username: "testuser".to_string(),
            email: "<EMAIL>".to_string(),
            password_hash: "hashed_password".to_string(),
            full_name: Some("Test User".to_string()),
        };
        
        let result = create_user(&pool, user_data).await;
        assert!(result.is_ok());
        
        let user = result.unwrap();
        assert_eq!(user.username, "testuser");
        assert_eq!(user.email, Some("<EMAIL>".to_string()));
    }
}
```

### 2. 集成测试

#### 事务测试
```rust
#[tokio::test]
async fn test_project_creation_with_tasks() {
    let pool = setup_test_db().await;
    
    let project_data = CreateProjectData {
        name: "Test Project".to_string(),
        description: Some("Test Description".to_string()),
        // ... 其他字段
    };
    
    let tasks_data = vec![
        CreateTaskData {
            title: "Task 1".to_string(),
            // ... 其他字段
        },
        CreateTaskData {
            title: "Task 2".to_string(),
            // ... 其他字段
        },
    ];
    
    let result = create_project_with_tasks(&pool, project_data, tasks_data).await;
    assert!(result.is_ok());
    
    // 验证项目和任务都已创建
    let project = result.unwrap();
    let tasks = find_tasks_by_project(&pool, &project.id).await.unwrap();
    assert_eq!(tasks.len(), 2);
}
```

---

**文档版本**: 1.0  
**创建日期**: 2024年12月  
**维护者**: 开发团队  
**下次更新**: 根据数据库架构演进需要
