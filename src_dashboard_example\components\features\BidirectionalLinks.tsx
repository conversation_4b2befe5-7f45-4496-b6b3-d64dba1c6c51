import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { ScrollArea } from '../ui/scroll-area'
import { cn } from '../../lib/utils'
import type { BidirectionalLinkData, LinkStatistics } from '../../services/bidirectionalLinkService'

interface BidirectionalBacklinksProps {
  currentPath: string
  backlinks: BidirectionalLinkData[]
  onLinkClick?: (path: string) => void
  className?: string
}

interface BidirectionalOutlinksProps {
  currentPath: string
  outlinks: BidirectionalLinkData[]
  onLinkClick?: (path: string) => void
  className?: string
}

interface LinkStatisticsProps {
  statistics: LinkStatistics | null
  className?: string
}

/**
 * 双向反向链接组件
 * 显示指向当前文件的所有链接，包含上下文信息
 */
export function BidirectionalBacklinks({
  currentPath,
  backlinks,
  onLinkClick,
  className
}: BidirectionalBacklinksProps) {
  // 调试日志
  console.log('🔍 BidirectionalBacklinks 组件渲染:', {
    currentPath,
    backlinksLength: backlinks.length,
    backlinks: backlinks
  })

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base flex items-center gap-2">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
            />
          </svg>
          <span>反向链接</span>
          <Badge variant="secondary" className="text-xs">
            {backlinks.length}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        {backlinks.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            <div className="text-2xl mb-2">🔗</div>
            <p className="text-sm">暂无反向链接</p>
            <p className="text-xs mt-1">其他文档链接到此文档时会显示在这里</p>
          </div>
        ) : (
          <ScrollArea className="h-[300px]">
            <div className="space-y-3">
              {backlinks.map((link) => (
                <div
                  key={link.id}
                  className="group p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors cursor-pointer"
                  onClick={() => onLinkClick?.(link.sourceDocPath)}
                >
                  <div className="flex items-start justify-between gap-2 mb-2">
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium truncate">
                        {link.sourceDocTitle ||
                          link.sourceDocPath.split('/').pop()?.replace('.md', '')}
                      </h4>
                      <p className="text-xs text-muted-foreground">{link.sourceDocPath}</p>
                    </div>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Badge variant={link.isValid ? 'default' : 'destructive'} className="text-xs">
                        {link.linkStrength.toFixed(1)}
                      </Badge>
                    </div>
                  </div>

                  {/* 链接上下文 */}
                  {(link.contextBefore || link.contextAfter) && (
                    <div className="text-xs text-muted-foreground bg-muted/30 rounded p-2 mt-2">
                      <span className="opacity-70">{link.contextBefore}</span>
                      <span className="font-medium text-primary mx-1">
                        [[{link.displayText || link.linkText}]]
                      </span>
                      <span className="opacity-70">{link.contextAfter}</span>
                    </div>
                  )}

                  <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                    <span>第 {link.lineNumber} 行</span>
                    <span>{new Date(link.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * 双向出链组件
 * 显示当前文件链接到的所有文件
 */
export function BidirectionalOutlinks({
  currentPath,
  outlinks,
  onLinkClick,
  className
}: BidirectionalOutlinksProps) {
  const validLinks = outlinks.filter((link) => link.isValid)
  const invalidLinks = outlinks.filter((link) => !link.isValid)

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base flex items-center gap-2">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
            />
          </svg>
          <span>出链</span>
          <Badge variant="secondary" className="text-xs">
            {outlinks.length}
          </Badge>
          {invalidLinks.length > 0 && (
            <Badge variant="destructive" className="text-xs">
              {invalidLinks.length} 失效
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        {outlinks.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            <div className="text-2xl mb-2">📝</div>
            <p className="text-sm">暂无出链</p>
            <p className="text-xs mt-1">在文档中使用 [[文档名]] 创建链接</p>
          </div>
        ) : (
          <ScrollArea className="h-[300px]">
            <div className="space-y-3">
              {/* 有效链接 */}
              {validLinks.map((link) => (
                <div
                  key={link.id}
                  className="group p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors cursor-pointer"
                  onClick={() => onLinkClick?.(link.targetDocPath)}
                >
                  <div className="flex items-start justify-between gap-2 mb-2">
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium truncate">
                        {link.displayText || link.linkText}
                      </h4>
                      <p className="text-xs text-muted-foreground">{link.targetDocPath}</p>
                    </div>
                    <Badge variant="default" className="text-xs">
                      {link.linkStrength.toFixed(1)}
                    </Badge>
                  </div>

                  {/* 链接上下文 */}
                  {(link.contextBefore || link.contextAfter) && (
                    <div className="text-xs text-muted-foreground bg-muted/30 rounded p-2 mt-2">
                      <span className="opacity-70">{link.contextBefore}</span>
                      <span className="font-medium text-primary mx-1">
                        [[{link.displayText || link.linkText}]]
                      </span>
                      <span className="opacity-70">{link.contextAfter}</span>
                    </div>
                  )}

                  <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                    <span>第 {link.lineNumber} 行</span>
                    <span>{new Date(link.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
              ))}

              {/* 失效链接 */}
              {invalidLinks.map((link) => (
                <div
                  key={link.id}
                  className="group p-3 rounded-lg border border-destructive/20 bg-destructive/5 opacity-75"
                >
                  <div className="flex items-start justify-between gap-2 mb-2">
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium truncate text-destructive">
                        {link.displayText || link.linkText}
                      </h4>
                      <p className="text-xs text-muted-foreground">目标文档不存在</p>
                    </div>
                    <Badge variant="destructive" className="text-xs">
                      失效
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                    <span>第 {link.lineNumber} 行</span>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-6 text-xs"
                      onClick={() => {
                        // TODO: 实现创建目标文档功能
                        console.log('Create target document:', link.linkText)
                      }}
                    >
                      创建文档
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * 链接统计组件
 * 显示当前文档的链接统计信息
 */
export function LinkStatisticsCard({ statistics, className }: LinkStatisticsProps) {
  if (!statistics) {
    return null
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base flex items-center gap-2">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
            />
          </svg>
          <span>链接统计</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 rounded-lg bg-muted/30">
            <div className="text-2xl font-bold text-primary">{statistics.backlinkCount}</div>
            <div className="text-xs text-muted-foreground">反向链接</div>
          </div>
          <div className="text-center p-3 rounded-lg bg-muted/30">
            <div className="text-2xl font-bold text-primary">{statistics.outlinkCount}</div>
            <div className="text-xs text-muted-foreground">出链</div>
          </div>
          <div className="text-center p-3 rounded-lg bg-muted/30">
            <div className="text-2xl font-bold text-green-600">{statistics.validLinks}</div>
            <div className="text-xs text-muted-foreground">有效链接</div>
          </div>
          <div className="text-center p-3 rounded-lg bg-muted/30">
            <div className="text-2xl font-bold text-destructive">{statistics.invalidLinks}</div>
            <div className="text-xs text-muted-foreground">失效链接</div>
          </div>
        </div>

        {statistics.linkStrengthAvg > 0 && (
          <div className="mt-4 p-3 rounded-lg bg-muted/30">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">平均链接强度</span>
              <Badge variant="outline">{statistics.linkStrengthAvg.toFixed(2)}</Badge>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
