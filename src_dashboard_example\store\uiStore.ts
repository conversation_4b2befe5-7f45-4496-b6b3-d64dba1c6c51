import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'

export interface UIState {
  // Theme and appearance
  theme: 'light' | 'dark' | 'system'
  sidebarCollapsed: boolean
  sidebarWidth: number

  // Navigation
  currentPage: string
  breadcrumbs: Array<{ label: string; path: string }>

  // Modals and dialogs
  modals: {
    createProject: boolean
    createArea: boolean
    createTask: boolean
    createResource: boolean
    settings: boolean
    about: boolean
  }

  // Notifications
  notifications: Array<{
    id: string
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message?: string
    duration?: number
    timestamp: Date
  }>

  // Loading states
  globalLoading: boolean
  loadingStates: Record<string, boolean>

  // Layout preferences
  layout: {
    showPreview: boolean
    previewWidth: number
    showSidebar: boolean
    showToolbar: boolean
    compactMode: boolean
  }

  // Search
  globalSearch: {
    query: string
    isOpen: boolean
    results: Array<{
      id: string
      type: 'project' | 'area' | 'task' | 'resource' | 'note'
      title: string
      description?: string
      path: string
    }>
  }
}

export interface UIActions {
  // Theme actions
  setTheme: (theme: UIState['theme']) => void
  toggleTheme: () => void

  // Sidebar actions
  toggleSidebar: () => void
  setSidebarCollapsed: (collapsed: boolean) => void
  setSidebarWidth: (width: number) => void

  // Navigation actions
  setCurrentPage: (page: string) => void
  setBreadcrumbs: (breadcrumbs: UIState['breadcrumbs']) => void
  addBreadcrumb: (breadcrumb: { label: string; path: string }) => void

  // Modal actions
  openModal: (modal: keyof UIState['modals']) => void
  closeModal: (modal: keyof UIState['modals']) => void
  closeAllModals: () => void

  // Notification actions
  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void

  // Loading actions
  setGlobalLoading: (loading: boolean) => void
  setLoadingState: (key: string, loading: boolean) => void
  clearLoadingState: (key: string) => void

  // Layout actions
  setLayout: (layout: Partial<UIState['layout']>) => void
  togglePreview: () => void
  toggleCompactMode: () => void
  setPreviewWidth: (width: number) => void

  // Search actions
  setGlobalSearch: (search: Partial<UIState['globalSearch']>) => void
  openGlobalSearch: () => void
  closeGlobalSearch: () => void
  clearSearchResults: () => void

  // Utility actions
  resetUI: () => void
}

export type UIStore = UIState & UIActions

const initialState: UIState = {
  theme: 'system',
  sidebarCollapsed: false,
  sidebarWidth: 280,
  currentPage: 'dashboard',
  breadcrumbs: [],
  modals: {
    createProject: false,
    createArea: false,
    createTask: false,
    createResource: false,
    settings: false,
    about: false
  },
  notifications: [],
  globalLoading: false,
  loadingStates: {},
  layout: {
    showPreview: true,
    previewWidth: 400,
    showSidebar: true,
    showToolbar: true,
    compactMode: false
  },
  globalSearch: {
    query: '',
    isOpen: false,
    results: []
  }
}

export const useUIStore = create<UIStore>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // Theme actions
        setTheme: (theme) => set({ theme }),

        toggleTheme: () => {
          const currentTheme = get().theme
          const newTheme = currentTheme === 'light' ? 'dark' : 'light'
          set({ theme: newTheme })
        },

        // Sidebar actions
        toggleSidebar: () =>
          set((state) => ({
            sidebarCollapsed: !state.sidebarCollapsed
          })),

        setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),

        setSidebarWidth: (width) => set({ sidebarWidth: width }),

        // Navigation actions
        setCurrentPage: (page) => set({ currentPage: page }),

        setBreadcrumbs: (breadcrumbs) => set({ breadcrumbs }),

        addBreadcrumb: (breadcrumb) =>
          set((state) => ({
            breadcrumbs: [...state.breadcrumbs, breadcrumb]
          })),

        // Modal actions
        openModal: (modal) =>
          set((state) => ({
            modals: { ...state.modals, [modal]: true }
          })),

        closeModal: (modal) =>
          set((state) => ({
            modals: { ...state.modals, [modal]: false }
          })),

        closeAllModals: () =>
          set((state) => ({
            modals: Object.keys(state.modals).reduce(
              (acc, key) => ({ ...acc, [key]: false }),
              {} as UIState['modals']
            )
          })),

        // Notification actions
        addNotification: (notification) => {
          const id = `notification-${Date.now()}-${Math.random()}`
          const newNotification = {
            ...notification,
            id,
            timestamp: new Date()
          }

          set((state) => ({
            notifications: [newNotification, ...state.notifications]
          }))

          // Auto-remove notification after duration
          if (notification.duration !== 0) {
            const duration = notification.duration || 5000
            setTimeout(() => {
              get().removeNotification(id)
            }, duration)
          }
        },

        removeNotification: (id) =>
          set((state) => ({
            notifications: state.notifications.filter((n) => n.id !== id)
          })),

        clearNotifications: () => set({ notifications: [] }),

        // Loading actions
        setGlobalLoading: (loading) => set({ globalLoading: loading }),

        setLoadingState: (key, loading) =>
          set((state) => ({
            loadingStates: { ...state.loadingStates, [key]: loading }
          })),

        clearLoadingState: (key) =>
          set((state) => {
            const { [key]: _, ...rest } = state.loadingStates
            return { loadingStates: rest }
          }),

        // Layout actions
        setLayout: (layout) =>
          set((state) => ({
            layout: { ...state.layout, ...layout }
          })),

        togglePreview: () =>
          set((state) => ({
            layout: { ...state.layout, showPreview: !state.layout.showPreview }
          })),

        toggleCompactMode: () =>
          set((state) => ({
            layout: { ...state.layout, compactMode: !state.layout.compactMode }
          })),

        setPreviewWidth: (width) =>
          set((state) => ({
            layout: { ...state.layout, previewWidth: width }
          })),

        // Search actions
        setGlobalSearch: (search) =>
          set((state) => ({
            globalSearch: { ...state.globalSearch, ...search }
          })),

        openGlobalSearch: () =>
          set((state) => ({
            globalSearch: { ...state.globalSearch, isOpen: true }
          })),

        closeGlobalSearch: () =>
          set((state) => ({
            globalSearch: { ...state.globalSearch, isOpen: false, query: '' }
          })),

        clearSearchResults: () =>
          set((state) => ({
            globalSearch: { ...state.globalSearch, results: [] }
          })),

        // Utility actions
        resetUI: () => set(initialState)
      }),
      {
        name: 'ui-store',
        partialize: (state) => ({
          theme: state.theme,
          sidebarCollapsed: state.sidebarCollapsed,
          sidebarWidth: state.sidebarWidth,
          layout: state.layout
        })
      }
    ),
    {
      name: 'ui-store'
    }
  )
)
