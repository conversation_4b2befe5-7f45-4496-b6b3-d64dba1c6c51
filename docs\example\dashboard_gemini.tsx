import { createSignal, createMemo, For, Show, onCleanup, onMount, type Component } from 'solid-js';
import { createStore, produce } from 'solid-js/store';

// --- 1. 类型定义 (Type Definitions) ---
interface UserProfile {
  id: string;
  displayName: string;
  lastReviewAt: string; // ISO Date String
}

type ProjectStatus = 'not-started' | 'in-progress' | 'at-risk' | 'paused' | 'completed' | 'archived';
interface Project {
  id: string;
  name: string;
  status: ProjectStatus;
  due?: string; // ISO Date String
  tasksCompleted: number;
  tasksTotal: number;
  updatedAt: string; // ISO Date String
}

interface Habit {
  id: string;
  name:string;
  freq: 'daily' | 'weekly';
  streak: number;
  history: { date: string; done: boolean }[]; // ISO Date String
}

interface Area {
  id: string;
  name: string;
  active: boolean;
  habits: Habit[];
}

type TaskPriority = 'low' | 'medium' | 'high';
interface Task {
  id: string;
  title: string;
  projectId: string;
  due: string; // ISO Date String
  completed: boolean;
  priority: TaskPriority;
  updatedAt: string; // ISO Date String
}

type ResourceType = 'document' | 'link' | 'image' | 'note';
interface Resource {
  id: string;
  type: ResourceType;
  createdAt: string; // ISO Date String
}

interface Tag {
  id: string;
  name: string;
  usage: number;
}

interface InboxItem {
  id: string;
  content: string;
  tags: string[];
  createdAt: string; // ISO Date String
  processed: boolean;
}

// --- 2. 模拟数据与状态管理 (Mock Data & State Management) ---

const [store, setStore] = createStore({
  userProfile: {
    id: 'user-01',
    displayName: '开发者',
    lastReviewAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(),
  } as UserProfile,
  projects: [
    { id: 'proj-1', name: 'Tauri 应用重构', status: 'in-progress', due: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), tasksCompleted: 5, tasksTotal: 12, updatedAt: new Date().toISOString() },
    { id: 'proj-2', name: '设计系统 V2', status: 'at-risk', due: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(), tasksCompleted: 18, tasksTotal: 20, updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString() },
    { id: 'proj-3', name: '2025 Q4 市场规划', status: 'not-started', due: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(), tasksCompleted: 0, tasksTotal: 15, updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString() },
    { id: 'proj-4', name: 'API 文档站', status: 'completed', due: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(), tasksCompleted: 10, tasksTotal: 10, updatedAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString() },
    { id: 'proj-5', name: '用户研究项目', status: 'paused', due: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), tasksCompleted: 2, tasksTotal: 8, updatedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString() },
    { id: 'proj-6', name: '移动端适配', status: 'in-progress', due: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(), tasksCompleted: 3, tasksTotal: 9, updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() },
  ] as Project[],
  areas: [
    { id: 'area-1', name: '个人成长', active: true, habits: [
        { id: 'habit-1', name: '每日阅读', freq: 'daily', streak: 12, history: Array.from({ length: 15 }).map((_, i) => ({ date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0], done: Math.random() > 0.3 })) },
        { id: 'habit-2', name: '周复盘', freq: 'weekly', streak: 4, history: Array.from({ length: 4 }).map((_, i) => ({ date: new Date(Date.now() - i * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], done: true })) },
    ]},
    { id: 'area-2', name: '健康生活', active: true, habits: [
        { id: 'habit-3', name: '健身', freq: 'daily', streak: 5, history: Array.from({ length: 7 }).map((_, i) => ({ date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0], done: [true, true, false, true, true, false, true][i] ?? false })) },
    ]},
    { id: 'area-3', name: '职业发展', active: false, habits: [] },
  ] as Area[],
  tasks: [
    { id: 'task-1', title: '完成 Dashboard UI 实现', projectId: 'proj-1', due: new Date().toISOString(), completed: false, priority: 'high', updatedAt: new Date().toISOString() },
    { id: 'task-2', title: '修复数据同步 Bug', projectId: 'proj-1', due: new Date().toISOString(), completed: true, priority: 'high', updatedAt: new Date().toISOString() },
    { id: 'task-3', title: '与 PM 同步项目风险', projectId: 'proj-2', due: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), completed: false, priority: 'medium', updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString() },
    { id: 'task-4', title: '撰写初步市场分析报告', projectId: 'proj-3', due: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), completed: false, priority: 'low', updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() },
    { id: 'task-5', title: '更新 Logo 资源', projectId: 'proj-2', due: new Date().toISOString(), completed: false, priority: 'medium', updatedAt: new Date().toISOString() },
    { id: 'task-6', title: '学习 SolidJS 进阶', projectId: 'proj-1', due: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(), completed: true, priority: 'low', updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString() },
    { id: 'task-7', title: '起草会议纪要', projectId: 'proj-5', due: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), completed: false, priority: 'low', updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() },
  ] as Task[],
  resources: Array.from({ length: 58 }).map((_, i) => ({
    id: `res-${i}`,
    type: (['document', 'link', 'image', 'note'] as ResourceType[])[i % 4],
    createdAt: new Date(Date.now() - i * 2 * 24 * 60 * 60 * 1000).toISOString(),
  })) as Resource[],
  tags: [
    { id: 'tag-1', name: '灵感', usage: 5 },
    { id: 'tag-2', name: '待办', usage: 12 },
    { id: 'tag-3', name: '重要', usage: 3 },
    { id: 'tag-4', name: '阅读', usage: 8 },
  ] as Tag[],
  inbox: [
      { id: 'inbox-1', content: '研究一下 tauri-plugin-store', tags: [], createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), processed: false },
      { id: 'inbox-2', content: '购买新的机械键盘', tags: ['待办'], createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), processed: true },
  ] as InboxItem[],
});

const [appState, setAppState] = createStore({
    status: 'ready' as 'loading' | 'ready' | 'error',
    errorMessage: '',
});

const reloadData = () => {
    setAppState('status', 'loading');
    setTimeout(() => {
        // In a real app, you would fetch data here.
        // We just simulate a successful load.
        setAppState('status', 'ready');
    }, 1000);
};

// --- 3. 图标组件 (Icon Components) ---
const IconProject: Component<{ class?: string }> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class={props.class}><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><line x1="10" y1="9" x2="8" y2="9"></line></svg>
);
const IconArea: Component<{ class?: string }> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class={props.class}><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line></svg>
);
const IconTask: Component<{ class?: string }> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class={props.class}><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>
);
const IconResource: Component<{ class?: string }> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class={props.class}><path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"></path></svg>
);
const IconChevronRight: Component<{ class?: string }> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class={props.class}><polyline points="9 18 15 12 9 6"></polyline></svg>
);
const IconPlus: Component<{ class?: string }> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class={props.class}><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
);
const IconSparkles: Component<{ class?: string }> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class={props.class}><path d="M9.94 14.36c.26.26.42.62.42 1s-.16.74-.42 1c-.26.26-.62.42-1 .42s-.74-.16-1-.42c-.26-.26-.42-.62-.42-1s.16-.74.42-1c.26-.26.62-.42 1-.42s.74.16 1 .42zM12 6.02c.33 0 .64.13.88.36.24.24.36.55.36.88s-.12.64-.36.88c-.24.24-.55.36-.88.36s-.64-.12-.88-.36c-.24-.24-.36-.55-.36-.88s.12-.64.36-.88c.24-.24.55-.36.88.36zm8.06 8.34c.26.26.42.62.42 1s-.16.74-.42 1c-.26.26-.62.42-1 .42s-.74-.16-1-.42c-.26-.26-.42-.62-.42-1s.16-.74.42-1c.26-.26.62-.42 1-.42s.74.16 1 .42zM12 18.02c.33 0 .64.13.88.36.24.24.36.55.36.88s-.12.64-.36.88c-.24.24-.55.36-.88.36s-.64-.12-.88-.36c-.24-.24-.36-.55-.36-.88s.12-.64.36-.88c.24-.24.55-.36.88.36zm-5-10c.24.24.55.36.88.36s.64-.12.88-.36c.24-.24.36-.55.36-.88s-.12-.64-.36-.88c-.24-.24-.55-.36-.88-.36s-.64.12-.88.36c-.24.24-.36.55-.36.88s.12.64.36.88z"></path></svg>
);


// --- 4. 辅助函数 (Helper Functions) ---
const formatDate = (date: Date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const getGreeting = () => {
  const hour = new Date().getHours();
  if (hour < 12) return '早上好';
  if (hour < 18) return '中午好';
  return '晚上好';
};

const getDayOfWeek = () => {
  return `星期${['日', '一', '二', '三', '四', '五', '六'][new Date().getDay()]}`;
};

// --- 5. 子组件 (Sub-Components) ---

// 5.1 快速捕捉输入框
const QuickCaptureInput: Component = () => {
    const [inputValue, setInputValue] = createSignal('');
    const [showSuggestions, setShowSuggestions] = createSignal(false);
    const [activeIndex, setActiveIndex] = createSignal(0);
    let inputRef: HTMLInputElement | undefined;

    const filteredTags = createMemo(() => {
        const query = inputValue().match(/#(\w*)$/);
        if (!query) return [];
        return store.tags.filter(tag => tag.name.toLowerCase().startsWith(query[1].toLowerCase()));
    });
    
    const onInput = (e: Event) => {
        const value = (e.currentTarget as HTMLInputElement).value;
        setInputValue(value);
        if (value.includes('#') && value.match(/#(\w*)$/)) {
            setShowSuggestions(true);
            setActiveIndex(0);
        } else {
            setShowSuggestions(false);
        }
    };

    const onKeyDown = (e: KeyboardEvent) => {
        if (showSuggestions() && filteredTags().length > 0) {
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                setActiveIndex(i => (i + 1) % filteredTags().length);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                setActiveIndex(i => (i - 1 + filteredTags().length) % filteredTags().length);
            } else if (e.key === 'Enter' || e.key === 'Tab') {
                e.preventDefault();
                selectTag(filteredTags()[activeIndex()]);
            } else if (e.key === 'Escape') {
                setShowSuggestions(false);
            }
        }
        
        if (e.key === 'Enter' && e.shiftKey) {
            e.preventDefault();
            handleSubmit();
        }
    };

    const selectTag = (tag: Tag) => {
        const currentValue = inputValue();
        const newValue = currentValue.replace(/#\w*$/, `#${tag.name} `);
        setInputValue(newValue);
        setShowSuggestions(false);
        inputRef?.focus();
    };

    const handleSubmit = () => {
        const content = inputValue().trim();
        if (!content) return; // 为空时阻止提交
        
        const tags = [...content.matchAll(/#(\w+)/g)].map(match => match[1]);
        const cleanContent = content.replace(/#\w+/g, '').trim();

        setStore('inbox', produce(inbox => {
            inbox.unshift({
                id: `inbox-${Date.now()}`,
                content: cleanContent,
                tags,
                createdAt: new Date().toISOString(),
                processed: false,
            });
        }));
        setInputValue('');
    };
    
    return (
        <div class="relative w-full">
            <div role="combobox" aria-expanded={showSuggestions()} aria-haspopup="listbox" class="flex items-center">
                <input
                    ref={inputRef}
                    type="text"
                    value={inputValue()}
                    onInput={onInput}
                    onKeyDown={onKeyDown}
                    onBlur={() => setTimeout(() => setShowSuggestions(false), 150)}
                    placeholder="有什么新想法？(Shift+Enter 提交)"
                    class="w-full bg-gray-100 dark:bg-gray-700/50 rounded-lg px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
                    aria-autocomplete="list"
                    aria-controls="tag-suggestions"
                    aria-activedescendant={showSuggestions() && filteredTags().length > 0 ? `tag-suggestion-${activeIndex()}` : undefined}
                />
            </div>
            <Show when={showSuggestions() && filteredTags().length > 0}>
                <ul
                    id="tag-suggestions"
                    role="listbox"
                    class="absolute z-10 w-full mt-2 bg-white dark:bg-gray-800 rounded-md shadow-lg max-h-60 overflow-auto text-sm border border-gray-200 dark:border-gray-700"
                >
                    <For each={filteredTags()}>
                        {(tag, index) => (
                            <li
                                id={`tag-suggestion-${index()}`}
                                role="option"
                                aria-selected={index() === activeIndex()}
                                class="px-4 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                                classList={{ 'bg-blue-500 text-white dark:bg-blue-600': index() === activeIndex() }}
                                onMouseDown={() => selectTag(tag)}
                            >
                                {tag.name}
                            </li>
                        )}
                    </For>
                </ul>
            </Show>
        </div>
    );
};

// 5.2 统计概览卡片
const StatCard: Component<{
  title: string;
  icon: Component<{ class?: string }>;
  onClick?: () => void;
  children: any;
  ariaLabel: string;
}> = (props) => {
    return (
        <button
            onClick={props.onClick}
            aria-label={props.ariaLabel}
            class="w-full text-left p-5 bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md focus-visible:shadow-md dark:border dark:border-gray-700/50 hover:bg-gray-50 dark:hover:bg-gray-700/50 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 dark:focus-visible:ring-offset-gray-900 transition-all duration-200 group"
        >
            <div class="flex items-center justify-between">
                <h3 class="font-semibold text-gray-700 dark:text-gray-300">{props.title}</h3>
                <props.icon class="w-6 h-6 text-gray-400 dark:text-gray-500 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-colors" />
            </div>
            <div class="mt-4">
                {props.children}
            </div>
        </button>
    );
};

// 5.3 主体标签页
const MainTabs: Component = () => {
    const [activeTab, setActiveTab] = createSignal<'tasks' | 'projects'>('tasks');

    const todaysDate = new Date();
    const todaysDateString = todaysDate.toISOString().split('T')[0];

    const todaysTasks = createMemo(() => 
        store.tasks
            .filter(t => !t.completed && new Date(t.due).toISOString().split('T')[0] <= todaysDateString)
            .sort((a, b) => new Date(a.due).getTime() - new Date(b.due).getTime())
    );

    const upcomingProjects = createMemo(() => 
        store.projects
            .filter(p => {
                if (!p.due || p.status === 'completed' || p.status === 'archived') return false;
                const dueDate = new Date(p.due);
                const diffDays = (dueDate.getTime() - todaysDate.getTime()) / (1000 * 3600 * 24);
                return diffDays >= 0 && diffDays <= 7;
            })
            .sort((a,b) => new Date(a.due || 0).getTime() - new Date(b.due || 0).getTime())
    );

    const toggleTask = (taskId: string) => {
        setStore('tasks', t => t.id === taskId, 'completed', c => !c);
    };

    const priorityClasses = {
        high: 'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300',
        medium: 'bg-yellow-100 dark:bg-yellow-800/50 text-yellow-700 dark:text-yellow-300',
        low: 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300',
    };

    const projectStatusClasses: Record<ProjectStatus, string> = {
      'in-progress': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'at-risk': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      'not-started': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
      'paused': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      'completed': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      'archived': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
    }

    const getProjectName = (projectId: string) => store.projects.find(p => p.id === projectId)?.name || '未知项目';

    return (
        <section class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 dark:border dark:border-gray-700/50">
            <div role="tablist" aria-label="主要任务与项目" class="flex border-b border-gray-200 dark:border-gray-700">
                <button
                    role="tab"
                    id="tab-tasks"
                    aria-selected={activeTab() === 'tasks'}
                    aria-controls="tabpanel-tasks"
                    onClick={() => setActiveTab('tasks')}
                    class="px-4 py-2 text-sm font-medium border-b-2 transition-colors"
                    classList={{
                        'border-blue-500 text-blue-600 dark:text-blue-400': activeTab() === 'tasks',
                        'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200': activeTab() !== 'tasks'
                    }}
                >
                    今日任务 ({todaysTasks().length})
                </button>
                <button
                    role="tab"
                    id="tab-projects"
                    aria-selected={activeTab() === 'projects'}
                    aria-controls="tabpanel-projects"
                    onClick={() => setActiveTab('projects')}
                    class="px-4 py-2 text-sm font-medium border-b-2 transition-colors"
                    classList={{
                        'border-blue-500 text-blue-600 dark:text-blue-400': activeTab() === 'projects',
                        'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200': activeTab() !== 'projects'
                    }}
                >
                    即将到期 ({upcomingProjects().length})
                </button>
            </div>
            <div class="mt-4">
                <div
                    id="tabpanel-tasks"
                    role="tabpanel"
                    aria-labelledby="tab-tasks"
                    hidden={activeTab() !== 'tasks'}
                    class="space-y-3"
                >
                    <For each={todaysTasks()} fallback={<p class="text-sm text-gray-500 dark:text-gray-400">今日无待办任务，太棒了！</p>}>
                        {task => {
                            const isOverdue = new Date(task.due).toISOString().split('T')[0] < todaysDateString;
                            return (
                                <div class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                                    <input type="checkbox" checked={task.completed} onChange={() => toggleTask(task.id)} class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                                    <div class="ml-3 flex-1">
                                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100" classList={{ 'line-through text-gray-500 dark:text-gray-400': task.completed }}>{task.title}</p>
                                        <div class="flex items-center space-x-2 mt-1">
                                            <span class="text-xs text-gray-500 dark:text-gray-400">{getProjectName(task.projectId)}</span>
                                            <span class="text-xs px-2 py-0.5 rounded-full" classList={{ [priorityClasses[task.priority]]: true }}>{task.priority}</span>
                                            <Show when={isOverdue}>
                                                <span class="text-xs text-red-600 dark:text-red-400 font-semibold">已逾期</span>
                                            </Show>
                                        </div>
                                    </div>
                                </div>
                            );
                        }}
                    </For>
                </div>
                <div
                    id="tabpanel-projects"
                    role="tabpanel"
                    aria-labelledby="tab-projects"
                    hidden={activeTab() !== 'projects'}
                    class="space-y-4"
                >
                    <For each={upcomingProjects()} fallback={<p class="text-sm text-gray-500 dark:text-gray-400">未来 7 天内无到期项目。</p>}>
                        {project => {
                            const progress = project.tasksTotal > 0 ? (project.tasksCompleted / project.tasksTotal) * 100 : 0;
                            const daysRemaining = Math.ceil((new Date(project.due!).getTime() - todaysDate.getTime()) / (1000 * 3600 * 24));
                            return (
                                <div class="p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-center">
                                        <p class="text-sm font-semibold text-gray-900 dark:text-gray-100">{project.name}</p>
                                        <span class="text-xs px-2 py-1 rounded-md" classList={{[projectStatusClasses[project.status]]: true}}>{project.status}</span>
                                    </div>
                                    <div class="mt-2">
                                        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                                            <span>进度</span>
                                            <span>{project.tasksCompleted}/{project.tasksTotal}</span>
                                        </div>
                                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                                            <div class="bg-blue-500 h-1.5 rounded-full" style={{ width: `${progress}%` }}></div>
                                        </div>
                                    </div>
                                    <div class="mt-3 flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
                                        <span>{daysRemaining} 天后到期</span>
                                        <a href="#" onClick={(e) => { e.preventDefault(); /* TODO: handle navigation */ }} class="font-medium text-blue-600 hover:underline dark:text-blue-400">查看详情</a>
                                    </div>
                                </div>
                            );
                        }}
                    </For>
                </div>
            </div>
        </section>
    );
};


// 5.4 周复盘提醒卡片
const WeeklyReviewCard: Component = () => {
    const shouldShow = createMemo(() => {
        const lastReview = new Date(store.userProfile.lastReviewAt);
        const diffDays = (new Date().getTime() - lastReview.getTime()) / (1000 * 3600 * 24);
        return diffDays > 7;
    });

    const startReview = () => {
        setStore('userProfile', 'lastReviewAt', new Date().toISOString());
    };

    return (
        <Show when={shouldShow()}>
            <div class="p-5 rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg relative overflow-hidden">
                <div class="relative z-10">
                  <div class="flex items-center gap-3">
                    <IconSparkles class="w-6 h-6 text-yellow-300" />
                    <h3 class="font-bold text-lg">是时候进行周复盘了！</h3>
                  </div>
                  <p class="text-sm mt-2 opacity-90">回顾过去一周的进展，规划下一周的目标。</p>
                  <button onClick={startReview} class="mt-4 bg-white/20 hover:bg-white/30 text-white font-semibold py-2 px-4 rounded-lg text-sm transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-white">
                      开始复盘
                  </button>
                </div>
            </div>
        </Show>
    );
};

// 5.5 右侧边栏
const RightSidebar: Component = () => {
    const activeAreas = createMemo(() => store.areas.filter(a => a.active).slice(0, 2));
    const unprocessedInboxCount = createMemo(() => store.inbox.filter(item => !item.processed).length);
    const today = new Date().toISOString().split('T')[0];
    const newInboxTodayCount = createMemo(() => store.inbox.filter(item => item.createdAt.startsWith(today)).length);
    const inboxProgress = createMemo(() => {
        const total = store.inbox.length;
        if (total === 0) return 0;
        return ((total - unprocessedInboxCount()) / total) * 100;
    });
    
    const recentlyActive = createMemo(() => 
        [...store.projects, ...store.tasks]
            .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
            .slice(0, 5)
    );

    const HabitHeatmap: Component<{habit: Habit}> = (props) => {
        const last7Days = createMemo(() => {
            return Array.from({length: 7}).map((_, i) => {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const dateString = date.toISOString().split('T')[0];
                const historyEntry = props.habit.history.find(h => h.date === dateString);
                return { date: dateString, done: historyEntry?.done ?? false };
            }).reverse();
        });
        
        return (
            <div class="grid grid-cols-7 gap-1.5">
                <For each={last7Days()}>
                    {(day) => (
                        <div
                            class="w-4 h-4 rounded"
                            classList={{
                                'bg-green-500': day.done,
                                'bg-gray-200 dark:bg-gray-700': !day.done,
                            }}
                            title={day.date}
                        />
                    )}
                </For>
            </div>
        );
    };

    return (
        <aside class="space-y-6">
            {/* 领域与习惯追踪 */}
            <section aria-labelledby="area-habit-title">
                <h3 id="area-habit-title" class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">领域与习惯</h3>
                <div class="space-y-5">
                    <For each={activeAreas()}>
                        {area => (
                            <div class="p-4 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700/50">
                                <h4 class="font-semibold text-sm text-gray-900 dark:text-gray-100">{area.name}</h4>
                                <For each={area.habits}>
                                    {habit => (
                                        <div class="mt-3">
                                            <div class="flex justify-between items-center">
                                                <span class="text-xs text-gray-600 dark:text-gray-300">{habit.name}</span>
                                                <span class="text-xs font-mono font-medium text-green-600 dark:text-green-400">{habit.streak} 天</span>
                                            </div>
                                            <div class="mt-2">
                                                <HabitHeatmap habit={habit} />
                                            </div>
                                        </div>
                                    )}
                                </For>
                            </div>
                        )}
                    </For>
                </div>
            </section>
            
            {/* 收件箱状态 */}
            <section aria-labelledby="inbox-title">
                <h3 id="inbox-title" class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">收件箱</h3>
                <div class="p-4 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700/50">
                    <div class="flex justify-between items-baseline">
                        <span class="text-2xl font-bold text-gray-900 dark:text-gray-100">{unprocessedInboxCount()}</span>
                        <span class="text-xs text-gray-500 dark:text-gray-400">待处理</span>
                    </div>
                    <Show when={newInboxTodayCount() > 0}>
                        <p class="text-xs text-green-600 dark:text-green-400 mt-1">今日新增 {newInboxTodayCount()} 条</p>
                    </Show>
                    <div class="mt-3">
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                           <div class="bg-indigo-500 h-1.5 rounded-full" style={{width: `${inboxProgress()}%`}}></div>
                        </div>
                    </div>
                     <button class="mt-4 w-full text-sm font-semibold text-indigo-600 dark:text-indigo-400 hover:underline">
                        进入收件箱
                    </button>
                </div>
            </section>

            {/* 最近活跃 */}
            <section aria-labelledby="recent-activity-title">
                <h3 id="recent-activity-title" class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">最近活跃</h3>
                <div class="space-y-3">
                   <For each={recentlyActive()}>
                       {(item) => (
                           <a href="#" class="block p-3 rounded-lg bg-white dark:bg-gray-800/50 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                               <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                                   {'title' in item ? item.title : item.name}
                               </p>
                               <div class="text-xs text-gray-500 dark:text-gray-400 mt-1 flex justify-between">
                                   <span>{'title' in item ? '任务' : '项目'}</span>
                                   <span>{new Date(item.updatedAt).toLocaleDateString()}</span>
                               </div>
                           </a>
                       )}
                   </For>
                </div>
            </section>
        </aside>
    );
};


// --- 6. 页面主组件 (Main Page Component) ---

const Dashboard: Component = () => {
  const [currentDate, setCurrentDate] = createSignal(new Date());

  // Memoized stats for cards
  const projectStats = createMemo(() => {
    const total = store.projects.length;
    const inProgress = store.projects.filter(p => p.status === 'in-progress').length;
    const completed = store.projects.filter(p => p.status === 'completed').length;
    return {
      total,
      inProgress,
      completionRate: total > 0 ? (completed / total) * 100 : 0,
    };
  });
  
  const areaStats = createMemo(() => {
      const total = store.areas.length;
      const active = store.areas.filter(a => a.active).length;
      const habitsTotal = store.areas.reduce((sum, area) => sum + area.habits.length, 0);
      return { total, active, habitsTotal };
  });

  const taskStats = createMemo(() => {
    const total = store.tasks.length;
    const completed = store.tasks.filter(t => t.completed).length;
    const today = new Date().toISOString().split('T')[0];
    const dueToday = store.tasks.filter(t => !t.completed && t.due.startsWith(today)).length;
    const overdue = store.tasks.filter(t => !t.completed && t.due < today).length;
    return { total, completed, dueToday, overdue, completionRate: total > 0 ? (completed/total) * 100 : 0 };
  });

  const resourceStats = createMemo(() => {
    const total = store.resources.length;
    const last7days = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
    const recent = store.resources.filter(r => r.createdAt > last7days).length;
    return { total, recent };
  });

  onMount(() => {
    const timer = setInterval(() => setCurrentDate(new Date()), 60000); // Update date every minute
    onCleanup(() => clearInterval(timer));
  });

  return (
    <div class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen font-sans">
      <Show when={appState.status === 'loading'}>
          <div class="fixed inset-0 bg-gray-900/50 flex items-center justify-center z-50">
              <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
      </Show>
      <Show when={appState.status === 'error'}>
          <div class="fixed inset-0 bg-gray-100 dark:bg-gray-800 flex flex-col items-center justify-center z-50 p-8 text-center">
              <h2 class="text-xl font-semibold text-red-600 dark:text-red-400">加载失败</h2>
              <p class="mt-2 text-gray-600 dark:text-gray-300">{appState.errorMessage || '无法加载仪表盘数据，请检查网络连接。'}</p>
              <button onClick={reloadData} class="mt-6 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800">
                  重试
              </button>
          </div>
      </Show>

      <main class="p-4 sm:p-6 lg:p-8 max-w-[1600px] mx-auto">
        {/* 1. 顶部头部栏 */}
        <header class="mb-8">
            <div class="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-gray-100">{getGreeting()}，{store.userProfile.displayName}</h1>
                    <p class="text-gray-500 dark:text-gray-400 mt-1">{formatDate(currentDate())} {getDayOfWeek()}</p>
                </div>
                <div class="w-full sm:max-w-xs lg:max-w-md">
                   <QuickCaptureInput />
                </div>
            </div>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-[1fr_360px] gap-8">
          <div class="flex flex-col gap-8">
            {/* 2. 统计概览卡片区 */}
            <section aria-labelledby="stats-title">
              <h2 id="stats-title" class="sr-only">统计概览</h2>
              <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-6">
                <StatCard title="项目" icon={IconProject} ariaLabel="查看所有项目">
                    <div class="text-3xl font-bold">{projectStats().total}</div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">{projectStats().inProgress} 个进行中</p>
                    <div class="mt-4">
                      <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                          <span>完成率</span><span>{projectStats().completionRate.toFixed(0)}%</span>
                      </div>
                      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5"><div class="bg-blue-500 h-1.5 rounded-full" style={{width: `${projectStats().completionRate}%`}}></div></div>
                    </div>
                </StatCard>
                <StatCard title="领域" icon={IconArea} ariaLabel="管理所有领域">
                   <div class="text-3xl font-bold">{areaStats().total}</div>
                   <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">{areaStats().active} 个活跃领域</p>
                   <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">{areaStats().habitsTotal} 个习惯追踪中</p>
                </StatCard>
                <StatCard title="任务" icon={IconTask} ariaLabel="查看所有任务">
                    <div class="text-3xl font-bold">{taskStats().total}</div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">{taskStats().completed} 个已完成</p>
                    <div class="mt-2 text-sm">
                      <span classList={{"text-yellow-600 dark:text-yellow-400 font-semibold": taskStats().dueToday > 0}}>{taskStats().dueToday} 今日到期</span>, <span classList={{"text-red-600 dark:text-red-400 font-semibold": taskStats().overdue > 0}}>{taskStats().overdue} 已逾期</span>
                    </div>
                </StatCard>
                <StatCard title="资源" icon={IconResource} ariaLabel="查看资源库">
                    <div class="text-3xl font-bold">{resourceStats().total}</div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">最近 7 天新增 {resourceStats().recent} 个</p>
                    {/* TODO: 类型分布图 */}
                </StatCard>
              </div>
            </section>
            
            {/* 智能提醒卡片 */}
            <WeeklyReviewCard />

            {/* 3. 中部标签页 */}
            <MainTabs />
          </div>

          {/* 4. 右侧辅助信息栏 */}
          <RightSidebar />
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
