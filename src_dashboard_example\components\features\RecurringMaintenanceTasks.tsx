import { useState, useMemo, useEffect, useImperativeHandle, forwardRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Checkbox } from '../ui/checkbox'
import { Plus, Calendar, RotateCcw, Trash2 } from 'lucide-react'
import { useTaskStore } from '../../store/taskStore'
import { useLanguage } from '../../contexts/LanguageContext'
import { useUIStore } from '../../store/uiStore'
import { useConfirmDialog } from '../shared/ConfirmDialog'
// {{ AURA-X: Add - 导入数据库API. Approval: 寸止(ID:1738157400). }}
import { databaseApi } from '../../lib/api'

interface RecurringMaintenanceTasksProps {
  areaId: string
  onAddTask?: () => void
  onTaskCreated?: (task: RecurringTask) => void
  refreshKey?: number
  className?: string
}

export interface RecurringMaintenanceTasksRef {
  addTask: (task: RecurringTask) => void
}

interface RecurringTask {
  id: string
  title: string
  description?: string
  repeatRule: 'daily' | 'weekly' | 'monthly' | 'yearly'
  repeatInterval?: number // 每隔多少个周期
  specificDay?: number // 每月的第几天，或每周的第几天
  nextDueDate: Date
  lastCompletedDate?: Date
  areaId: string
  createdAt: Date
}

// 计算下一次截止日期
const calculateNextDueDate = (
  repeatRule: string,
  repeatInterval: number = 1,
  specificDay?: number,
  fromDate: Date = new Date()
): Date => {
  const nextDate = new Date(fromDate)
  
  switch (repeatRule) {
    case 'daily':
      nextDate.setDate(nextDate.getDate() + repeatInterval)
      break
    case 'weekly':
      if (specificDay !== undefined) {
        // 找到下一个指定的星期几
        const currentDay = nextDate.getDay()
        let daysUntilTarget = (specificDay - currentDay + 7) % 7

        // 如果今天就是目标日期，或者已经过了目标日期，则跳到下一周
        if (daysUntilTarget === 0) {
          daysUntilTarget = 7 * repeatInterval
        } else {
          daysUntilTarget += (repeatInterval - 1) * 7
        }

        nextDate.setDate(nextDate.getDate() + daysUntilTarget)
      } else {
        nextDate.setDate(nextDate.getDate() + repeatInterval * 7)
      }
      break
    case 'monthly':
      if (specificDay !== undefined) {
        nextDate.setMonth(nextDate.getMonth() + repeatInterval)
        nextDate.setDate(specificDay)
      } else {
        nextDate.setMonth(nextDate.getMonth() + repeatInterval)
      }
      break
    case 'yearly':
      nextDate.setFullYear(nextDate.getFullYear() + repeatInterval)
      break
  }
  
  return nextDate
}

// 格式化重复规则显示
const formatRepeatRule = (task: RecurringTask, t: any): string => {
  const { repeatRule, repeatInterval = 1, specificDay } = task

  switch (repeatRule) {
    case 'daily':
      return repeatInterval === 1 ? t('recurringTasks.repeatRules.daily') : `${t('recurringTasks.repeatRules.daily')}${repeatInterval}天`
    case 'weekly':
      if (specificDay !== undefined) {
        const dayNames = [
          t('recurringTasks.weekdays.sunday'),
          t('recurringTasks.weekdays.monday'),
          t('recurringTasks.weekdays.tuesday'),
          t('recurringTasks.weekdays.wednesday'),
          t('recurringTasks.weekdays.thursday'),
          t('recurringTasks.weekdays.friday'),
          t('recurringTasks.weekdays.saturday')
        ]
        return repeatInterval === 1
          ? `${t('recurringTasks.repeatRules.weekly')}${dayNames[specificDay]}`
          : `每${repeatInterval}周的${dayNames[specificDay]}`
      }
      return repeatInterval === 1 ? t('recurringTasks.repeatRules.weekly') : `每${repeatInterval}周`
    case 'monthly':
      if (specificDay !== undefined) {
        return repeatInterval === 1
          ? `${t('recurringTasks.repeatRules.monthly')}${specificDay}号`
          : `每${repeatInterval}个月的${specificDay}号`
      }
      return repeatInterval === 1 ? t('recurringTasks.repeatRules.monthly') : `每${repeatInterval}个月`
    case 'yearly':
      return repeatInterval === 1 ? t('recurringTasks.repeatRules.yearly') : `每${repeatInterval}年`
    default:
      return t('recurringTasks.repeatRules.unknown')
  }
}

export const RecurringMaintenanceTasks = forwardRef<RecurringMaintenanceTasksRef, RecurringMaintenanceTasksProps>(
  ({ areaId, onAddTask, onTaskCreated, refreshKey, className }, ref) => {
  const { t } = useLanguage()
  const { addNotification } = useUIStore()
  const { confirm, ConfirmDialog } = useConfirmDialog()
  
  // {{ AURA-X: Modify - 使用数据库状态管理. Approval: 寸止(ID:1738157400). }}
  const [recurringTasks, setRecurringTasks] = useState<RecurringTask[]>([])

  // 从数据库加载任务
  const loadTasks = async () => {
    try {
      const result = await databaseApi.getRecurringTasks(areaId)
      if (result.success) {
        setRecurringTasks(result.data || [])
      } else {
        console.error('Failed to load recurring tasks:', result.error)
      }
    } catch (error) {
      console.error('Failed to load recurring tasks:', error)
    }
  }

  // 监听refreshKey变化，重新加载任务
  useEffect(() => {
    loadTasks()
  }, [refreshKey, areaId])

  // 添加新任务
  const handleAddTask = (newTask: RecurringTask) => {
    setRecurringTasks(prev => [...prev, newTask])
    onTaskCreated?.(newTask)
  }

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    addTask: handleAddTask,
    getCounts: getCountsInternal
  }))

  // 按下一次截止日期排序
  const sortedTasks = useMemo(() => {
    return [...recurringTasks].sort((a, b) => {
      const dateA = a.nextDueDate instanceof Date ? a.nextDueDate : new Date(a.nextDueDate)
      const dateB = b.nextDueDate instanceof Date ? b.nextDueDate : new Date(b.nextDueDate)
      return dateA.getTime() - dateB.getTime()
    })
  }, [recurringTasks])

  // {{ AURA-X: Add - 统计定期任务数量. Confirmed via 寸止 }}
  const getCountsInternal = () => {
    const total = recurringTasks.length
    const todayStr = new Date().toISOString().split('T')[0]
    let dueToday = 0
    let overdue = 0
    recurringTasks.forEach(t => {
      const next = t.nextDueDate instanceof Date ? t.nextDueDate : new Date(t.nextDueDate)
      const nextStr = next.toISOString().split('T')[0]
      if (nextStr === todayStr) dueToday += 1
      if (next < new Date(todayStr)) overdue += 1
    })
    return { total, dueToday, overdue }
  }

  // {{ AURA-X: Modify - 完成任务时更新数据库. Approval: 寸止(ID:1738157400). }}
  // 完成任务处理 - 直接更新到下一个周期，保持未完成状态
  const handleCompleteTask = async (taskId: string) => {
    try {
      const task = recurringTasks.find(t => t.id === taskId)
      if (!task) return

      const now = new Date()
      const nextDueDate = calculateNextDueDate(
        task.repeatRule,
        task.repeatInterval,
        task.specificDay,
        now
      )

      // 更新数据库
      const result = await databaseApi.updateRecurringTask({
        id: taskId,
        updates: {
          lastCompletedDate: now,
          nextDueDate
        }
      })

      if (result.success) {
        // 更新本地状态
        setRecurringTasks(prev => prev.map(task => {
          if (task.id === taskId) {
            return {
              ...task,
              lastCompletedDate: now,
              nextDueDate
            }
          }
          return task
        }))

        addNotification({
          type: 'success',
          title: '维护任务已完成',
          message: '下次截止日期已自动更新，任务将继续重复'
        })
      } else {
        throw new Error(result.error || 'Failed to update task')
      }
    } catch (error) {
      console.error('Failed to complete task:', error)
      addNotification({
        type: 'error',
        title: '操作失败',
        message: '无法完成任务，请重试'
      })
    }
  }

  // {{ AURA-X: Modify - 删除任务时更新数据库. Approval: 寸止(ID:1738157400). }}
  // 删除任务
  const handleDeleteTask = async (taskId: string, taskTitle: string) => {
    const confirmed = await confirm({
      title: t('recurringTasks.deleteTask'),
      message: t('recurringTasks.deleteConfirm', { title: taskTitle }),
      confirmText: t('recurringTasks.deleteButton'),
      cancelText: t('recurringTasks.cancelButton')
    })

    if (confirmed) {
      try {
        const result = await databaseApi.deleteRecurringTask(taskId)

        if (result.success) {
          setRecurringTasks(prev => prev.filter(task => task.id !== taskId))
          addNotification({
            type: 'success',
            title: t('recurringTasks.taskDeleted'),
            message: t('recurringTasks.taskDeletedMessage', { title: taskTitle })
          })
        } else {
          throw new Error(result.error || 'Failed to delete task')
        }
      } catch (error) {
        console.error('Failed to delete task:', error)
        addNotification({
          type: 'error',
          title: '删除失败',
          message: '无法删除任务，请重试'
        })
      }
    }
  }

  // 判断任务是否逾期
  const isOverdue = (dueDate: Date): boolean => {
    return dueDate < new Date()
  }

  // 判断任务是否即将到期（3天内）
  const isDueSoon = (dueDate: Date): boolean => {
    const threeDaysFromNow = new Date()
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3)
    return dueDate <= threeDaysFromNow && dueDate >= new Date()
  }

  return (
    <>
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <RotateCcw className="h-5 w-5" />
              {t('recurringTasks.title')}
            </CardTitle>
            <CardDescription>
              {t('recurringTasks.description')}
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={onAddTask}>
            <Plus className="h-4 w-4 mr-2" />
            {t('recurringTasks.addTask')}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {sortedTasks.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <RotateCcw className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">{t('recurringTasks.noTasks')}</p>
            <p className="text-xs mt-1">{t('recurringTasks.createFirstTask')}</p>
          </div>
        ) : (
          <div className="space-y-3">
            {sortedTasks.map((task) => (
              <div
                key={task.id}
                className="flex items-center gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors"
              >
                <Checkbox
                  id={`task-${task.id}`}
                  onCheckedChange={() => handleCompleteTask(task.id)}
                />
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <label
                      htmlFor={`task-${task.id}`}
                      className="text-sm font-medium cursor-pointer truncate"
                    >
                      {task.title}
                    </label>
                    <Badge variant="outline" className="text-xs">
                      {formatRepeatRule(task, t)}
                    </Badge>
                  </div>
                  {task.description && (
                    <p className="text-xs text-muted-foreground truncate">
                      {task.description}
                    </p>
                  )}
                </div>

                <div className="flex items-center gap-2 text-right">
                  <div className="text-xs">
                    <div className={`font-medium ${
                      isOverdue(task.nextDueDate)
                        ? 'text-red-600'
                        : isDueSoon(task.nextDueDate)
                        ? 'text-amber-600'
                        : 'text-muted-foreground'
                    }`}>
                      {(task.nextDueDate instanceof Date ? task.nextDueDate : new Date(task.nextDueDate)).toLocaleDateString()}
                    </div>
                    {task.lastCompletedDate && (
                      <div className="text-muted-foreground">
                        {t('recurringTasks.lastCompleted')}: {(task.lastCompletedDate instanceof Date ? task.lastCompletedDate : new Date(task.lastCompletedDate)).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteTask(task.id, task.title)}
                    className="text-muted-foreground hover:text-destructive p-1 h-auto ml-2"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>

    <ConfirmDialog />
  </>
  )
})

RecurringMaintenanceTasks.displayName = 'RecurringMaintenanceTasks'

export default RecurringMaintenanceTasks
