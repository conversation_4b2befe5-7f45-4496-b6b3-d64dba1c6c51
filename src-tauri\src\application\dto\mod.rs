// Data Transfer Objects - 数据传输对象
// 应用层的数据传输和转换

use serde::{Deserialize, Serialize};

pub mod user_dto;
pub mod project_dto;
pub mod task_dto;
pub mod area_dto;
pub mod inbox_dto;
pub mod resource_dto;
pub mod review_dto;
pub mod common_dto;

// 重新导出 DTO 类型
pub use user_dto::*;
pub use project_dto::*;
pub use task_dto::*;
pub use area_dto::*;
pub use inbox_dto::*;
pub use resource_dto::*;
pub use review_dto::*;
pub use common_dto::*;

// 基础响应结构
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: Option<String>,
    pub error: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            error: None,
        }
    }

    pub fn error(error: String) -> Self {
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
        }
    }

    pub fn message(message: String) -> Self {
        Self {
            success: true,
            data: None,
            message: Some(message),
            error: None,
        }
    }
}

// 分页请求
#[derive(Debug, Serialize, Deserialize)]
pub struct PageRequest {
    pub page: u32,
    pub size: u32,
    pub sort_by: Option<String>,
    pub sort_order: Option<String>,
}

// 分页响应
#[derive(Debug, Serialize, Deserialize)]
pub struct PageResponse<T> {
    pub items: Vec<T>,
    pub total: u64,
    pub page: u32,
    pub size: u32,
    pub total_pages: u32,
}
