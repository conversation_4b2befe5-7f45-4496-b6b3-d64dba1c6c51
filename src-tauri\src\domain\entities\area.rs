// Area Entity - 领域实体

use crate::shared::types::{Id, Metadata, EntityStatus};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Area {
    pub id: Id,
    pub name: String,
    pub description: Option<String>,
    pub standards: Vec<String>,
    pub color: Option<String>,
    pub icon: Option<String>,
    pub status: AreaStatus,
    pub entity_status: EntityStatus,
    pub metadata: Metadata,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AreaStatus {
    Active,
    Maintenance,
    Dormant,
}

impl Default for AreaStatus {
    fn default() -> Self {
        AreaStatus::Active
    }
}

impl Area {
    pub fn new(name: String) -> Self {
        Self {
            id: crate::shared::utils::IdGenerator::new_id_with_prefix("area"),
            name,
            description: None,
            standards: Vec::new(),
            color: None,
            icon: None,
            status: AreaStatus::default(),
            entity_status: EntityStatus::Active,
            metadata: Metadata::default(),
        }
    }
}
