# KPI指标跟踪组件库

一个可复用的KPI指标跟踪组件库，支持项目KPI和领域指标的统一管理。基于SolidJS构建，提供完整的TypeScript类型支持。

## ✨ 特性

- 🎯 **统一接口** - 项目KPI和领域指标使用相同的组件接口
- 📊 **多种图表** - 支持进度条、线性图、环形图等多种可视化方式
- ⚡ **高性能** - 基于SolidJS的响应式更新，性能优异
- 🎨 **主题支持** - 支持亮色/暗色主题自动切换
- 🔧 **高度可配置** - 丰富的配置选项适应不同使用场景
- 📱 **响应式设计** - 完美适配桌面端和移动端
- 🛡️ **类型安全** - 完整的TypeScript类型定义
- 🔌 **易于集成** - 简单的API设计，快速集成到现有项目

## 🚀 快速开始

### 安装

```bash
# 组件已内置在项目中，无需额外安装
```

### 基本使用

```tsx
import { KPITracker, createKPIDataSource } from '@/components/kpi'

function MyComponent() {
  const dataSource = createKPIDataSource('project')

  return (
    <KPITracker
      parentId="project-123"
      parentType="project"
      dataSource={dataSource}
    />
  )
}
```

### 项目KPI示例

```tsx
import { 
  KPITracker, 
  createKPIDataSource, 
  PROJECT_KPI_CONFIG 
} from '@/components/kpi'

function ProjectDetailPage({ projectId }: { projectId: string }) {
  const dataSource = createKPIDataSource('project')
  
  const config = {
    showChart: true,
    showQuickInput: true,
    allowCreate: true,
    layout: 'grid'
  }
  
  const eventHandlers = {
    onCreate: (kpi) => console.log('KPI created:', kpi),
    onUpdate: (kpi) => console.log('KPI updated:', kpi),
    onRecord: (record) => console.log('Record created:', record)
  }
  
  return (
    <KPITracker
      parentId={projectId}
      parentType="project"
      dataSource={dataSource}
      config={config}
      kpiConfig={PROJECT_KPI_CONFIG}
      eventHandlers={eventHandlers}
    />
  )
}
```

### 领域指标示例

```tsx
import { 
  KPITracker, 
  createKPIDataSource, 
  AREA_METRIC_CONFIG 
} from '@/components/kpi'

function AreaDetailPage({ areaId }: { areaId: string }) {
  const dataSource = createKPIDataSource('area')
  
  const config = {
    showChart: true,
    showHistory: true,
    allowBatchRecord: true,
    layout: 'list',
    chartType: 'line'
  }
  
  return (
    <KPITracker
      parentId={areaId}
      parentType="area"
      dataSource={dataSource}
      config={config}
      kpiConfig={AREA_METRIC_CONFIG}
    />
  )
}
```

## 📦 组件说明

### KPITracker - 主要组件

完整的KPI跟踪和管理组件，包含统计、图表、输入等功能。

**Props:**
- `parentId: string` - 父实体ID（项目ID或领域ID）
- `parentType: 'project' | 'area'` - 父实体类型
- `dataSource: KPIDataSource` - 数据源接口
- `config?: KPIComponentConfig` - 组件配置
- `kpiConfig?: KPIConfig` - KPI配置
- `eventHandlers?: KPIEventHandlers` - 事件处理器

### KPIChart - 图表组件

KPI数据可视化组件，支持多种图表类型。

**Props:**
- `kpis: BaseKPI[]` - KPI数据数组
- `type?: 'bar' | 'line' | 'pie' | 'donut'` - 图表类型
- `showLegend?: boolean` - 是否显示图例
- `onKPIClick?: (kpi: BaseKPI) => void` - KPI点击回调

### KPIInput - 输入组件

单个KPI的快速输入和管理组件。

**Props:**
- `kpi: BaseKPI` - KPI数据
- `onRecord?: (record: KPIRecord) => void` - 记录回调
- `onEdit?: (kpi: BaseKPI) => void` - 编辑回调
- `allowEdit?: boolean` - 是否允许编辑

### KPIDialog - 对话框组件

KPI创建和编辑对话框。

**Props:**
- `open: boolean` - 是否打开
- `mode: 'create' | 'edit'` - 模式
- `kpi?: BaseKPI` - 编辑的KPI数据
- `onSubmit: (data: CreateKPIData) => Promise<void>` - 提交回调

## ⚙️ 配置选项

### KPIComponentConfig

```tsx
interface KPIComponentConfig {
  // 显示选项
  showChart?: boolean          // 显示图表
  showQuickInput?: boolean     // 显示快速输入
  showHistory?: boolean        // 显示历史记录
  showStatistics?: boolean     // 显示统计信息
  
  // 交互选项
  allowCreate?: boolean        // 允许创建
  allowEdit?: boolean          // 允许编辑
  allowDelete?: boolean        // 允许删除
  allowBatchRecord?: boolean   // 允许批量记录
  
  // 布局选项
  layout?: 'grid' | 'list' | 'compact'  // 布局模式
  chartType?: 'bar' | 'line' | 'pie'    // 图表类型
  
  // 数据选项
  maxRecords?: number          // 最大记录数
  autoRefresh?: boolean        // 自动刷新
  refreshInterval?: number     // 刷新间隔(ms)
}
```

### KPIConfig

```tsx
interface KPIConfig {
  type: 'project' | 'area'     // KPI类型
  templates: KPITemplate[]     // 模板列表
  allowedUnits: string[]       // 允许的单位
  validationRules: ValidationRules  // 验证规则
  defaultFrequency: KPIFrequency    // 默认频率
  defaultDirection: KPIDirection    // 默认方向
}
```

## 🔌 数据源接口

### 创建数据源

```tsx
import { createKPIDataSource } from '@/components/kpi'

// 项目KPI数据源
const projectDataSource = createKPIDataSource('project')

// 领域指标数据源
const areaDataSource = createKPIDataSource('area')
```

### 自定义数据源

```tsx
import { KPIDataSource } from '@/components/kpi'

class CustomKPIDataSource implements KPIDataSource {
  async getKPIs(parentId: string): Promise<BaseKPI[]> {
    // 实现获取KPI列表
  }
  
  async createKPI(parentId: string, data: CreateKPIData): Promise<BaseKPI> {
    // 实现创建KPI
  }
  
  // ... 其他方法
}
```

## 🎯 事件处理

```tsx
const eventHandlers: KPIEventHandlers = {
  onCreate: (kpi) => {
    console.log('KPI created:', kpi)
    // 发送通知、更新缓存等
  },
  
  onUpdate: (kpi, changes) => {
    console.log('KPI updated:', kpi, changes)
    // 触发相关计算、同步数据等
  },
  
  onRecord: (record) => {
    console.log('New record:', record)
    // 检查目标达成、发送提醒等
  },
  
  onProgressChange: (kpiId, progress) => {
    console.log('Progress changed:', kpiId, progress)
    // 更新整体进度、发送成就通知等
  },
  
  onError: (error, context) => {
    console.error('KPI error:', error, context)
    // 错误处理、用户提示等
  }
}
```

## 🎨 主题和样式

组件支持自动主题切换和自定义样式：

```tsx
<KPITracker
  config={{
    theme: 'auto', // 'light' | 'dark' | 'auto'
  }}
  className="custom-kpi-tracker"
/>
```

## 📊 预设模板

### 项目KPI模板

- Bug修复数、代码覆盖率、响应时间
- 新增用户、用户满意度、销售额
- 页面访问量、加载时间、完成率

### 领域指标模板

- 健康：体重、运动时长、步数、睡眠时长
- 学习：学习时长、阅读页数、练习题数
- 生活：冥想时长、屏幕时间、社交时间

## 🔧 工具函数

```tsx
import { 
  calculateKPIProgress,
  getKPIStatus,
  formatKPIValue,
  validateKPIData
} from '@/components/kpi'

// 计算进度
const progress = calculateKPIProgress(kpi)

// 获取状态
const status = getKPIStatus(progress)

// 格式化值
const formatted = formatKPIValue(1234.56, 'kg', 1) // "1.2K kg"

// 验证数据
const validation = validateKPIData(data, rules)
```

## 🚀 最佳实践

1. **选择合适的配置**：根据使用场景选择合适的组件配置
2. **处理事件**：实现必要的事件处理器以提供良好的用户体验
3. **错误处理**：妥善处理数据加载和操作错误
4. **性能优化**：合理设置刷新间隔和记录数量限制
5. **用户体验**：提供清晰的反馈和引导信息

## 📝 类型定义

完整的TypeScript类型定义请参考 `types.ts` 文件。
