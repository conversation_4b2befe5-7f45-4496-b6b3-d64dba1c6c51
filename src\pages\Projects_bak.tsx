// Projects Page - 项目管理页面
// 项目列表、创建、编辑和管理功能

import { createSignal, createEffect, Show, For } from 'solid-js';
import { PageContainer } from '../components/layout/Layout';
import { Card, CardHeader, CardTitle, CardContent, ProjectCard } from '../components/ui/Card';
import { Button } from '../components/ui/button';
import { ProjectModal } from '../components/projects/ProjectModal';
import { ProjectDetailModal } from '../components/projects/ProjectDetailModal';
import { cn } from '@/lib/utils';
import { Project } from '../types/business';
import { getProjects, getProjectStatistics } from '../services/apiFactory';

// 项目过滤器类型
interface ProjectFilters {
  search: string;
  status: string;
  priority: string;
  area: string;
}

// 项目视图类型
type ProjectView = 'grid' | 'list' | 'kanban';

export function Projects() {
  const [projects, setProjects] = createSignal<Project[]>([]);
  const [loading, setLoading] = createSignal(true);
  const [error, setError] = createSignal<string | null>(null);
  const [view, setView] = createSignal<ProjectView>('grid');
  const [filters, setFilters] = createSignal<ProjectFilters>({
    search: '',
    status: 'all',
    priority: 'all',
    area: 'all',
  });
  const [showCreateModal, setShowCreateModal] = createSignal(false);
  const [editingProject, setEditingProject] = createSignal<Project | undefined>(undefined);
  const [showDetailModal, setShowDetailModal] = createSignal(false);
  const [selectedProjectId, setSelectedProjectId] = createSignal<string | null>(null);

  // 解析 URL 查询参数，支持 /projects?detail=:id 深链打开模态
  createEffect(() => {
    const url = new URL(window.location.href);
    const detailId = url.searchParams.get('detail');
    if (detailId) {
      setSelectedProjectId(detailId);
      setShowDetailModal(true);
    }
  });

  // 初始加载项目数据
  createEffect(() => {
    loadProjects();
  });

  // 过滤项目
  const filteredProjects = () => {
    const currentFilters = filters();
    return projects().filter(project => {
      const matchesSearch = !currentFilters.search || 
        project.name.toLowerCase().includes(currentFilters.search.toLowerCase()) ||
        project.description?.toLowerCase().includes(currentFilters.search.toLowerCase());
      
      const matchesStatus = currentFilters.status === 'all' || project.status === currentFilters.status;
      const matchesPriority = currentFilters.priority === 'all' || project.priority === currentFilters.priority;
      
      return matchesSearch && matchesStatus && matchesPriority;
    });
  };

  // 项目统计
  const [stats, setStats] = createSignal({
    total: 0,
    active: 0,
    completed: 0,
    overdue: 0,
  });

  // 加载项目统计
  createEffect(() => {
    const loadStats = async () => {
      try {
        const projectStats = await getProjectStatistics();
        setStats(projectStats);
      } catch (err) {
        console.error('Failed to load project statistics:', err);
        // 如果API失败，使用本地计算的统计
        const allProjects = projects();
        setStats({
          total: allProjects.length,
          active: allProjects.filter(p => p.status === 'in_progress').length,
          completed: allProjects.filter(p => p.status === 'completed').length,
          overdue: allProjects.filter(p => p.dueDate && new Date(p.dueDate) < new Date()).length,
        });
      }
    };

    if (projects().length > 0) {
      loadStats();
    }
  });

  const handleProjectClick = (projectId: string) => {
    // 打开项目详情模态窗 + 写入 URL 查询参数，支持分享/回退
    setSelectedProjectId(projectId);
    setShowDetailModal(true);
    const url = new URL(window.location.href);
    url.searchParams.set('detail', projectId);
    window.history.pushState({}, '', url.pathname + '?' + url.searchParams.toString());
  };

  const handleCreateProject = () => {
    setEditingProject(undefined);
    setShowCreateModal(true);
  };

  const handleEditProject = (project: Project) => {
    setEditingProject(project);
    setShowCreateModal(true);
  };

  const handleProjectSuccess = (_project: Project) => {
    // 刷新项目列表
    loadProjects();
    setShowCreateModal(false);
    setEditingProject(undefined);
  };

  const handleCloseModal = () => {
    setShowCreateModal(false);
    setEditingProject(undefined);
  };

  // 重新加载项目数据的函数
  const loadProjects = async () => {
    try {
      setLoading(true);
      setError(null);

      const projectList = await getProjects();
      setProjects(projectList);
    } catch (err) {
      console.error('Failed to load projects:', err);
      setError('加载项目失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 状态显示辅助函数
  const getStatusLabel = (status: string) => {
    const statusMap: Record<string, string> = {
      'not_started': '未开始',
      'in_progress': '进行中',
      'completed': '已完成',
      'on_hold': '暂停',
      'at_risk': '有风险',
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'not_started': 'bg-muted text-muted-foreground',
      'in_progress': 'bg-blue-100 text-blue-700',
      'completed': 'bg-green-100 text-green-700',
      'on_hold': 'bg-yellow-100 text-yellow-700',
      'at_risk': 'bg-red-100 text-red-700',
    };
    return colorMap[status] || 'bg-muted text-muted-foreground';
  };

  const getPriorityLabel = (priority: string) => {
    const priorityMap: Record<string, string> = {
      'low': '低',
      'medium': '中',
      'high': '高',
      'critical': '紧急',
    };
    return priorityMap[priority] || priority;
  };

  const handleFilterChange = (key: keyof ProjectFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };





  return (
    <PageContainer>
      <Show when={loading()}>
        <div class="flex items-center justify-center py-12">
          <div class="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
        </div>
      </Show>

      <Show when={error()}>
        <div class="text-center py-12">
          <p class="text-destructive">{error()}</p>
          <Button variant="outline" class="mt-4" onClick={() => window.location.reload()}>
            重试
          </Button>
        </div>
      </Show>

      <Show when={!loading() && !error()}>
        <div class="space-y-6">
          {/* 项目统计 */}
          <div class="grid gap-4 md:grid-cols-4">
            <Card>
              <CardContent class="p-3">
                <div class="text-lg font-bold">{stats().total}</div>
                <p class="text-xs text-muted-foreground">总项目数</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent class="p-3">
                <div class="text-lg font-bold text-info">{stats().active}</div>
                <p class="text-xs text-muted-foreground">进行中</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent class="p-3">
                <div class="text-lg font-bold text-success">{stats().completed}</div>
                <p class="text-xs text-muted-foreground">已完成</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent class="p-3">
                <div class="text-lg font-bold text-warning">{stats().overdue}</div>
                <p class="text-xs text-muted-foreground">逾期项目</p>
              </CardContent>
            </Card>
          </div>

          {/* 过滤器和视图控制 */}
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <select
                class="flex h-9 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                value={filters().status}
                onChange={(e) => handleFilterChange('status', e.currentTarget.value)}
                aria-label="按状态过滤项目"
              >
                <option value="all">所有状态</option>
                <option value="not_started">未开始</option>
                <option value="in_progress">进行中</option>
                <option value="completed">已完成</option>
                <option value="on_hold">暂停</option>
                <option value="at_risk">有风险</option>
              </select>
              <select
                class="flex h-9 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                value={filters().priority}
                onChange={(e) => handleFilterChange('priority', e.currentTarget.value)}
                aria-label="按优先级过滤项目"
              >
                <option value="all">所有优先级</option>
                <option value="low">低</option>
                <option value="medium">中</option>
                <option value="high">高</option>
                <option value="critical">紧急</option>
              </select>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFilters({ search: '', status: 'all', priority: 'all', area: 'all' })}
              >
                清除过滤器
              </Button>
            </div>

            <div class="flex items-center space-x-4">
              {/* 视图切换 */}
              <div class="flex items-center border border-border rounded-md">
                <Button
                  variant={view() === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setView('grid')}
                  class="rounded-r-none border-r"
                >
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <rect x="3" y="3" width="7" height="7" />
                    <rect x="14" y="3" width="7" height="7" />
                    <rect x="14" y="14" width="7" height="7" />
                    <rect x="3" y="14" width="7" height="7" />
                  </svg>
                </Button>
                <Button
                  variant={view() === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setView('list')}
                  class="rounded-none border-r"
                >
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <line x1="8" y1="6" x2="21" y2="6" />
                    <line x1="8" y1="12" x2="21" y2="12" />
                    <line x1="8" y1="18" x2="21" y2="18" />
                    <line x1="3" y1="6" x2="3.01" y2="6" />
                    <line x1="3" y1="12" x2="3.01" y2="12" />
                    <line x1="3" y1="18" x2="3.01" y2="18" />
                  </svg>
                </Button>
                <Button
                  variant={view() === 'kanban' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setView('kanban')}
                  class="rounded-l-none"
                >
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <rect x="3" y="3" width="6" height="18" />
                    <rect x="11" y="3" width="6" height="10" />
                    <rect x="19" y="3" width="2" height="6" />
                  </svg>
                </Button>
              </div>

              <Button onClick={handleCreateProject}>
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                新建项目
              </Button>
            </div>
          </div>

          {/* 项目列表 */}
          <Show when={filteredProjects().length > 0}>
            {/* 网格视图 */}
            <Show when={view() === 'grid'}>
              <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                <For each={filteredProjects()}>
                  {(project) => (
                    <ProjectCard
                      project={{
                        id: project.id,
                        name: project.name,
                        description: project.description,
                        progress: project.progress,
                        status: project.status,
                        startDate: project.startDate,
                        dueDate: project.dueDate,
                        updatedAt: project.updatedAt,
                        priority: project.priority,
                        tags: project.tags,
                      }}
                      onClick={handleProjectClick}
                      onEdit={handleEditProject}
                    />
                  )}
                </For>
              </div>
            </Show>

            {/* 列表视图 */}
            <Show when={view() === 'list'}>
              <Card>
                <CardContent class="p-0">
                  <div class="overflow-x-auto">
                    <table class="w-full">
                      <thead class="border-b border-border">
                        <tr class="text-left">
                          <th class="p-4 font-medium text-muted-foreground">项目名称</th>
                          <th class="p-4 font-medium text-muted-foreground">状态</th>
                          <th class="p-4 font-medium text-muted-foreground">优先级</th>
                          <th class="p-4 font-medium text-muted-foreground">进度</th>
                          <th class="p-4 font-medium text-muted-foreground">截止日期</th>
                          <th class="p-4 font-medium text-muted-foreground">操作</th>
                        </tr>
                      </thead>
                      <tbody>
                        <For each={filteredProjects()}>
                          {(project) => (
                            <tr class="border-b border-border hover:bg-muted/50 transition-colors">
                              <td class="p-4">
                                <div class="space-y-1">
                                  <div class="font-medium">{project.name}</div>
                                  <Show when={project.description}>
                                    <div class="text-sm text-muted-foreground line-clamp-1">
                                      {project.description}
                                    </div>
                                  </Show>
                                </div>
                              </td>
                              <td class="p-4">
                                <span class={cn('px-2 py-1 rounded-full text-xs font-medium', getStatusColor(project.status))}>
                                  {getStatusLabel(project.status)}
                                </span>
                              </td>
                              <td class="p-4">
                                <span class="text-sm">{project.priority}</span>
                              </td>
                              <td class="p-4">
                                <div class="flex items-center space-x-2">
                                  <div class="w-16 bg-muted rounded-full h-2 relative overflow-hidden">
                                    <div
                                      class="bg-primary h-full rounded-full transition-all duration-300 absolute top-0 left-0"
                                      classList={{
                                        'w-0': project.progress === 0,
                                        'w-1/4': project.progress > 0 && project.progress <= 0.25,
                                        'w-2/4': project.progress > 0.25 && project.progress <= 0.5,
                                        'w-3/4': project.progress > 0.5 && project.progress <= 0.75,
                                        'w-full': project.progress > 0.75,
                                      }}
                                    />
                                  </div>
                                  <span class="text-sm font-medium">{Math.round(project.progress * 100)}%</span>
                                </div>
                              </td>
                              <td class="p-4">
                                <Show when={project.dueDate}>
                                  <span class="text-sm text-muted-foreground">
                                    {new Date(project.dueDate!).toLocaleDateString()}
                                  </span>
                                </Show>
                              </td>
                              <td class="p-4">
                                <div class="flex items-center space-x-2">
                                  <Button
                                    variant="ghost"
                                    size="icon-sm"
                                    onClick={() => handleProjectClick(project.id)}
                                    aria-label="查看项目"
                                  >
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon-sm"
                                    onClick={() => handleEditProject(project)}
                                    aria-label="编辑项目"
                                  >
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                  </Button>
                                </div>
                              </td>
                            </tr>
                          )}
                        </For>
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </Show>

            {/* 看板视图 */}
            <Show when={view() === 'kanban'}>
              <div class="grid gap-6 md:grid-cols-4">
                <For each={['not_started', 'in_progress', 'completed', 'on_hold']}>
                  {(status) => (
                    <Card>
                      <CardHeader class="pb-3">
                        <CardTitle class="text-sm font-medium flex items-center justify-between">
                          <span>{getStatusLabel(status)}</span>
                          <span class="text-xs bg-muted text-muted-foreground px-2 py-1 rounded-full">
                            {filteredProjects().filter(p => p.status === status).length}
                          </span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent class="space-y-3">
                        <For each={filteredProjects().filter(p => p.status === status)}>
                          {(project) => (
                            <div class="p-3 border border-border rounded-lg hover:shadow-sm transition-shadow cursor-pointer">
                              <div class="space-y-2">
                                <div class="font-medium text-sm">{project.name}</div>
                                <Show when={project.description}>
                                  <div class="text-xs text-muted-foreground line-clamp-2">
                                    {project.description}
                                  </div>
                                </Show>
                                <div class="flex items-center justify-between">
                                  <span class="text-xs text-muted-foreground">{getPriorityLabel(project.priority)}</span>
                                  <div class="flex items-center space-x-1">
                                    <Button
                                      variant="ghost"
                                      size="icon-sm"
                                      onClick={() => handleProjectClick(project.id)}
                                      class="h-6 w-6"
                                    >
                                      <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                      </svg>
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="icon-sm"
                                      onClick={() => handleEditProject(project)}
                                      class="h-6 w-6"
                                    >
                                      <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                      </svg>
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </For>
                      </CardContent>
                    </Card>
                  )}
                </For>
              </div>
            </Show>
          </Show>

          <Show when={filteredProjects().length === 0}>
            <Card>
              <CardContent class="p-12 text-center">
                <svg class="mx-auto h-12 w-12 text-muted-foreground mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="text-lg font-semibold mb-2">没有找到项目</h3>
                <p class="text-muted-foreground mb-4">
                  {filters().search || filters().status !== 'all' || filters().priority !== 'all'
                    ? '尝试调整过滤条件或创建新项目'
                    : '开始创建您的第一个项目吧！'
                  }
                </p>
                <Button onClick={handleCreateProject}>
                  创建项目
                </Button>
              </CardContent>
            </Card>
          </Show>
        </div>
      </Show>

      {/* 项目创建/编辑模态框 */}
      <ProjectModal
        isOpen={showCreateModal()}
        onClose={handleCloseModal}
        onSuccess={handleProjectSuccess}
        project={editingProject()}
      />

      {/* 项目详情模态窗 */}
      <ProjectDetailModal
        projectId={selectedProjectId()}
        isOpen={showDetailModal()}
        onClose={() => {
          // 关闭模态
          setShowDetailModal(false);
          setSelectedProjectId(null);
          // 清理 URL 中的 detail 查询参数，保持停留在 /projects
          const url = new URL(window.location.href);
          if (url.searchParams.has('detail')) {
            url.searchParams.delete('detail');
            // 使用 replaceState 避免新增历史记录
            window.history.replaceState({}, '', url.pathname + (url.searchParams.toString() ? `?${url.searchParams.toString()}` : ''));
          }
        }}
      />
    </PageContainer>
  );
}
