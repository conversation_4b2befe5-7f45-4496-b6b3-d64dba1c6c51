# PaoLife 部署指南

## 📋 概述

本文档提供了PaoLife应用的完整部署指南，包括开发环境、测试环境和生产环境的部署配置。

## 🏗️ 构建配置

### 1. 开发构建

#### 前端开发构建
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 类型检查
pnpm type-check

# 代码检查
pnpm lint
```

#### 后端开发构建
```bash
# 进入后端目录
cd src-tauri

# 构建开发版本
cargo build

# 运行测试
cargo test

# 代码检查
cargo clippy
```

#### 完整开发环境
```bash
# 启动完整开发环境（前端+后端）
pnpm tauri dev

# 或者分别启动
# 终端1：前端
pnpm dev

# 终端2：后端
cd src-tauri && cargo tauri dev
```

### 2. 生产构建

#### 构建配置
```bash
# 构建生产版本
pnpm tauri build

# 构建特定平台
pnpm tauri build --target x86_64-pc-windows-msvc    # Windows
pnpm tauri build --target x86_64-apple-darwin       # macOS Intel
pnpm tauri build --target aarch64-apple-darwin      # macOS Apple Silicon
pnpm tauri build --target x86_64-unknown-linux-gnu  # Linux
```

#### 构建优化
```toml
# src-tauri/Cargo.toml
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true
```

```javascript
// vite.config.ts
export default defineConfig({
  build: {
    target: 'esnext',
    minify: 'esbuild',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['solid-js'],
          ui: ['@kobalte/core'],
        },
      },
    },
  },
});
```

## 🖥️ 桌面应用部署

### 1. Windows 部署

#### 系统要求
- Windows 10 版本 1903 或更高
- WebView2 运行时
- Visual C++ Redistributable

#### 安装包配置
```json
// src-tauri/tauri.conf.json
{
  "tauri": {
    "bundle": {
      "targets": ["msi", "nsis"],
      "windows": {
        "certificateThumbprint": null,
        "digestAlgorithm": "sha256",
        "timestampUrl": "",
        "wix": {
          "language": ["zh-CN", "en-US"],
          "template": "templates/main.wxs"
        },
        "nsis": {
          "displayLanguageSelector": true,
          "languages": ["SimpChinese", "English"],
          "installerIcon": "icons/installer.ico",
          "installMode": "perMachine",
          "allowDowngrades": false
        }
      }
    }
  }
}
```

#### 代码签名
```bash
# 使用证书签名
$env:TAURI_PRIVATE_KEY = "path/to/private.key"
$env:TAURI_KEY_PASSWORD = "your_password"
pnpm tauri build
```

### 2. macOS 部署

#### 系统要求
- macOS 10.15 或更高
- 已安装 Xcode Command Line Tools

#### 应用签名和公证
```json
// src-tauri/tauri.conf.json
{
  "tauri": {
    "bundle": {
      "targets": ["dmg", "app"],
      "macOS": {
        "frameworks": [],
        "minimumSystemVersion": "10.15",
        "exceptionDomain": "",
        "signingIdentity": "Developer ID Application: Your Name",
        "providerShortName": "YourTeamID",
        "entitlements": "entitlements.plist"
      }
    }
  }
}
```

```xml
<!-- entitlements.plist -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>com.apple.security.cs.allow-jit</key>
    <true/>
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <true/>
    <key>com.apple.security.cs.disable-library-validation</key>
    <true/>
    <key>com.apple.security.network.client</key>
    <true/>
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
</dict>
</plist>
```

#### 公证流程
```bash
# 构建应用
pnpm tauri build

# 公证应用（需要Apple Developer账号）
xcrun notarytool submit target/release/bundle/dmg/PaoLife.dmg \
  --apple-id "<EMAIL>" \
  --password "app-specific-password" \
  --team-id "YOUR_TEAM_ID" \
  --wait

# 装订公证票据
xcrun stapler staple target/release/bundle/dmg/PaoLife.dmg
```

### 3. Linux 部署

#### 系统要求
- Ubuntu 20.04+ / Debian 11+ / Fedora 35+
- GTK 3.24+
- WebKitGTK 2.36+

#### 包格式配置
```json
// src-tauri/tauri.conf.json
{
  "tauri": {
    "bundle": {
      "targets": ["deb", "appimage", "rpm"],
      "linux": {
        "deb": {
          "depends": [
            "libwebkit2gtk-4.0-37",
            "libgtk-3-0",
            "libayatana-appindicator3-1"
          ],
          "section": "utils",
          "priority": "optional"
        },
        "rpm": {
          "epoch": 0,
          "release": "1",
          "license": "MIT"
        }
      }
    }
  }
}
```

#### AppImage 配置
```bash
# 构建 AppImage
pnpm tauri build --target x86_64-unknown-linux-gnu

# 验证 AppImage
./target/release/bundle/appimage/paolife_*.AppImage --appimage-extract-and-run
```

## 🚀 自动化部署

### 1. GitHub Actions 配置

#### 完整 CI/CD 流程
```yaml
# .github/workflows/release.yml
name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
      
      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        
      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.0-dev build-essential curl wget file libssl-dev libgtk-3-dev libayatana-appindicator3-dev librsvg2-dev
          
      - name: Install frontend dependencies
        run: pnpm install
        
      - name: Run tests
        run: |
          pnpm test
          cd src-tauri && cargo test

  build:
    needs: test
    strategy:
      matrix:
        platform: [ubuntu-latest, windows-latest, macos-latest]
    runs-on: ${{ matrix.platform }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'
          
      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        
      - name: Install Linux dependencies
        if: matrix.platform == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.0-dev build-essential curl wget file libssl-dev libgtk-3-dev libayatana-appindicator3-dev librsvg2-dev
          
      - name: Install frontend dependencies
        run: pnpm install
        
      - name: Build application
        run: pnpm tauri build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          TAURI_PRIVATE_KEY: ${{ secrets.TAURI_PRIVATE_KEY }}
          TAURI_KEY_PASSWORD: ${{ secrets.TAURI_KEY_PASSWORD }}
          
      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: release-${{ matrix.platform }}
          path: |
            src-tauri/target/release/bundle/
            !src-tauri/target/release/bundle/**/.*

  release:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Download artifacts
        uses: actions/download-artifact@v4
        
      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          files: |
            release-ubuntu-latest/**/*
            release-windows-latest/**/*
            release-macos-latest/**/*
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
```

### 2. 自动更新配置

#### Tauri 更新器配置
```json
// src-tauri/tauri.conf.json
{
  "tauri": {
    "updater": {
      "active": true,
      "endpoints": [
        "https://releases.example.com/paolife/{{target}}/{{current_version}}"
      ],
      "dialog": true,
      "pubkey": "YOUR_PUBLIC_KEY_HERE"
    }
  }
}
```

#### 更新服务器实现
```rust
// src-tauri/src/updater.rs
use tauri::api::version;
use tauri::{AppHandle, Manager};

#[tauri::command]
pub async fn check_for_updates(app: AppHandle) -> Result<(), String> {
    let update = app.updater().check().await.map_err(|e| e.to_string())?;
    
    if update.is_update_available() {
        let confirmation = tauri::api::dialog::blocking::confirm(
            Some(&app.get_window("main").unwrap()),
            "Update Available",
            &format!(
                "A new version {} is available. Would you like to update now?",
                update.latest_version()
            ),
        );
        
        if confirmation {
            update.download_and_install().await.map_err(|e| e.to_string())?;
        }
    }
    
    Ok(())
}
```

## 📦 分发策略

### 1. 官方分发渠道

#### Windows
- Microsoft Store
- 官方网站下载
- Chocolatey 包管理器
- Winget 包管理器

#### macOS
- Mac App Store
- 官方网站下载
- Homebrew Cask

#### Linux
- 各发行版软件仓库
- Snap Store
- Flatpak
- AppImage

### 2. 企业部署

#### 静默安装
```bash
# Windows NSIS
PaoLife-Setup.exe /S /D=C:\Program Files\PaoLife

# Windows MSI
msiexec /i PaoLife.msi /quiet INSTALLDIR="C:\Program Files\PaoLife"

# macOS
sudo installer -pkg PaoLife.pkg -target /

# Linux DEB
sudo dpkg -i paolife.deb

# Linux RPM
sudo rpm -i paolife.rpm
```

#### 配置管理
```json
// 企业配置文件 config/enterprise.json
{
  "deployment": {
    "autoUpdate": false,
    "telemetry": false,
    "defaultSettings": {
      "theme": "light",
      "language": "zh-CN",
      "dataPath": "%APPDATA%/PaoLife"
    },
    "restrictions": {
      "allowExternalSync": false,
      "allowPlugins": false
    }
  }
}
```

## 🔧 部署后配置

### 1. 数据迁移

#### 数据库迁移脚本
```rust
// src-tauri/src/migration.rs
use sqlx::SqlitePool;

pub async fn migrate_user_data(pool: &SqlitePool) -> Result<(), Box<dyn std::error::Error>> {
    // 检查是否需要迁移
    let version = get_schema_version(pool).await?;
    
    if version < CURRENT_SCHEMA_VERSION {
        println!("Migrating database from version {} to {}", version, CURRENT_SCHEMA_VERSION);
        
        // 备份现有数据
        backup_database(pool).await?;
        
        // 执行迁移
        sqlx::migrate!("./migrations").run(pool).await?;
        
        // 更新版本号
        update_schema_version(pool, CURRENT_SCHEMA_VERSION).await?;
        
        println!("Migration completed successfully");
    }
    
    Ok(())
}
```

### 2. 性能监控

#### 应用性能监控
```rust
// src-tauri/src/telemetry.rs
use serde_json::json;
use std::time::Instant;

pub struct PerformanceMonitor {
    start_time: Instant,
}

impl PerformanceMonitor {
    pub fn new() -> Self {
        Self {
            start_time: Instant::now(),
        }
    }
    
    pub fn record_startup_time(&self) {
        let startup_time = self.start_time.elapsed();
        
        // 记录启动时间
        log::info!("Application startup time: {:?}", startup_time);
        
        // 发送遥测数据（如果启用）
        if cfg!(feature = "telemetry") {
            self.send_telemetry(json!({
                "event": "app_startup",
                "duration_ms": startup_time.as_millis(),
                "platform": std::env::consts::OS,
                "version": env!("CARGO_PKG_VERSION")
            }));
        }
    }
    
    fn send_telemetry(&self, data: serde_json::Value) {
        // 实现遥测数据发送
        tokio::spawn(async move {
            // 发送到分析服务
        });
    }
}
```

### 3. 错误报告

#### 崩溃报告配置
```rust
// src-tauri/src/crash_reporter.rs
use std::panic;

pub fn setup_crash_reporter() {
    panic::set_hook(Box::new(|panic_info| {
        let crash_report = format!(
            "Panic occurred: {}\nLocation: {:?}\nBacktrace: {:?}",
            panic_info.payload().downcast_ref::<&str>().unwrap_or(&"Unknown"),
            panic_info.location(),
            std::backtrace::Backtrace::capture()
        );
        
        // 保存崩溃报告到本地
        if let Err(e) = std::fs::write("crash_report.txt", &crash_report) {
            eprintln!("Failed to write crash report: {}", e);
        }
        
        // 发送崩溃报告（如果用户同意）
        if cfg!(feature = "crash_reporting") {
            send_crash_report(crash_report);
        }
    }));
}

fn send_crash_report(report: String) {
    // 实现崩溃报告发送
}
```

---

**文档版本**: 1.0  
**创建日期**: 2024年12月  
**维护者**: 开发团队  
**下次更新**: 根据部署需求变化
