// File System Infrastructure - 文件系统基础设施

use crate::shared::errors::{AppError, Result};
use std::path::{Path, PathBuf};

pub mod file_manager;
pub mod path_resolver;

// 重新导出文件系统模块
pub use file_manager::*;
pub use path_resolver::*;

pub trait FileSystemService {
    fn read_file(&self, path: &Path) -> Result<String>;
    fn write_file(&self, path: &Path, content: &str) -> Result<()>;
    fn delete_file(&self, path: &Path) -> Result<()>;
    fn create_directory(&self, path: &Path) -> Result<()>;
    fn list_directory(&self, path: &Path) -> Result<Vec<PathBuf>>;
    fn file_exists(&self, path: &Path) -> bool;
    fn get_file_metadata(&self, path: &Path) -> Result<FileMetadata>;
}

#[derive(Debug, Clone)]
pub struct FileMetadata {
    pub size: u64,
    pub created: chrono::DateTime<chrono::Utc>,
    pub modified: chrono::DateTime<chrono::Utc>,
    pub is_directory: bool,
}

pub struct LocalFileSystemService;

impl FileSystemService for LocalFileSystemService {
    fn read_file(&self, path: &Path) -> Result<String> {
        std::fs::read_to_string(path)
            .map_err(|e| AppError::FileSystemError(format!("Failed to read file {:?}: {}", path, e)))
    }

    fn write_file(&self, path: &Path, content: &str) -> Result<()> {
        if let Some(parent) = path.parent() {
            std::fs::create_dir_all(parent)
                .map_err(|e| AppError::FileSystemError(format!("Failed to create directory {:?}: {}", parent, e)))?;
        }
        
        std::fs::write(path, content)
            .map_err(|e| AppError::FileSystemError(format!("Failed to write file {:?}: {}", path, e)))
    }

    fn delete_file(&self, path: &Path) -> Result<()> {
        std::fs::remove_file(path)
            .map_err(|e| AppError::FileSystemError(format!("Failed to delete file {:?}: {}", path, e)))
    }

    fn create_directory(&self, path: &Path) -> Result<()> {
        std::fs::create_dir_all(path)
            .map_err(|e| AppError::FileSystemError(format!("Failed to create directory {:?}: {}", path, e)))
    }

    fn list_directory(&self, path: &Path) -> Result<Vec<PathBuf>> {
        let entries = std::fs::read_dir(path)
            .map_err(|e| AppError::FileSystemError(format!("Failed to read directory {:?}: {}", path, e)))?;

        let mut paths = Vec::new();
        for entry in entries {
            let entry = entry
                .map_err(|e| AppError::FileSystemError(format!("Failed to read directory entry: {}", e)))?;
            paths.push(entry.path());
        }
        
        Ok(paths)
    }

    fn file_exists(&self, path: &Path) -> bool {
        path.exists()
    }

    fn get_file_metadata(&self, path: &Path) -> Result<FileMetadata> {
        let metadata = std::fs::metadata(path)
            .map_err(|e| AppError::FileSystemError(format!("Failed to get metadata for {:?}: {}", path, e)))?;

        let created = metadata.created()
            .map(|t| chrono::DateTime::from(t))
            .unwrap_or_else(|_| chrono::Utc::now());

        let modified = metadata.modified()
            .map(|t| chrono::DateTime::from(t))
            .unwrap_or_else(|_| chrono::Utc::now());

        Ok(FileMetadata {
            size: metadata.len(),
            created,
            modified,
            is_directory: metadata.is_dir(),
        })
    }
}
