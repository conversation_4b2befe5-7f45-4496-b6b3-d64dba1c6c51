// Crypto Utilities - 加密工具函数

use crate::shared::errors::{AppError, Result};
use std::collections::hash_map::DefaultHasher;
use std::hash::{Hash, Hasher};

pub fn generate_hash<T: Hash>(data: &T) -> u64 {
    let mut hasher = DefaultHasher::new();
    data.hash(&mut hasher);
    hasher.finish()
}

pub fn generate_checksum(data: &[u8]) -> String {
    format!("{:x}", generate_hash(&data))
}

pub fn generate_file_checksum(file_path: &str) -> Result<String> {
    match std::fs::read(file_path) {
        Ok(data) => Ok(generate_checksum(&data)),
        Err(e) => Err(AppError::FileSystemError(format!(
            "Failed to read file for checksum: {}",
            e
        ))),
    }
}

pub fn verify_checksum(data: &[u8], expected_checksum: &str) -> bool {
    let actual_checksum = generate_checksum(data);
    actual_checksum == expected_checksum
}

// Simple obfuscation for non-sensitive data (not cryptographically secure)
pub fn simple_encode(data: &str) -> String {
    data.chars()
        .map(|c| ((c as u8).wrapping_add(13)) as char)
        .collect()
}

pub fn simple_decode(encoded: &str) -> String {
    encoded
        .chars()
        .map(|c| ((c as u8).wrapping_sub(13)) as char)
        .collect()
}

// Generate a simple token for session management
pub fn generate_session_token() -> String {
    let timestamp = chrono::Utc::now().timestamp();
    let random_part = crate::shared::utils::IdGenerator::new_short_id();
    format!("{}_{}", timestamp, random_part)
}

pub fn validate_session_token(token: &str) -> bool {
    let parts: Vec<&str> = token.split('_').collect();
    if parts.len() != 2 {
        return false;
    }

    // Check if timestamp part is valid
    if let Ok(timestamp) = parts[0].parse::<i64>() {
        let now = chrono::Utc::now().timestamp();
        let age = now - timestamp;
        // Token valid for 24 hours
        age >= 0 && age < 86400
    } else {
        false
    }
}
