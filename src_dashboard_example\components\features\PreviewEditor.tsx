import { Crepe } from '@milkdown/crepe'
import '@milkdown/crepe/theme/common/style.css'
import '@milkdown/crepe/theme/frame.css'
import '../../plugins/wikilink/styles.css'
import {
  wikiLinkConfigCtx,
  wikiLinkNode,
  wikiLinkRemarkPlugin,
  wikiLinkCompletionRule,
  wikiLinkViewPlugin,
  type WikiLinkConfig
} from '../../plugins/wikilink'
import { Plugin, PluginKey } from '@milkdown/kit/prose/state'
import { $prose } from '@milkdown/kit/utils'

/**
 * 创建嵌套预览插件 - 使用 Milkdown 插件格式
 */
const createNestedPreviewPlugin = (config: WikiLinkConfig, parentLevel: number) =>
  $prose(() => {
    const pluginKey = new PluginKey(`nested-preview-${parentLevel}`)

    return new Plugin({
      key: pluginKey,

      props: {
        handleDOMEvents: {
          mouseover(_view, event) {
            const target = event.target as HTMLElement
            // 只在悬浮 WikiLink 时记录日志，减少噪音

            if (target.hasAttribute('data-wikilink')) {
              console.log(`🔍 [嵌套L${parentLevel}] mouseover 事件，目标:`, target.tagName, target.getAttribute('data-wikilink'))
              const linkTarget = target.getAttribute('data-target')
              console.log(`🎯 [嵌套L${parentLevel}] 悬浮 WikiLink: ${linkTarget}`)
              console.log(`📍 [嵌套L${parentLevel}] 元素信息:`, {
                tagName: target.tagName,
                className: target.className,
                textContent: target.textContent,
                dataWikilink: target.getAttribute('data-wikilink'),
                dataTarget: target.getAttribute('data-target')
              })

              if (linkTarget) {
                const rect = target.getBoundingClientRect()
                console.log(`📐 [嵌套L${parentLevel}] 位置信息:`, rect)

                // 立即显示嵌套预览，移除延迟和hover检查
                import('../../plugins/wikilink/preview')
                  .then(({ NestedPreviewManager }) => {
                    console.log(`🏗️ [嵌套L${parentLevel}] NestedPreviewManager 加载成功`)
                    const manager = NestedPreviewManager.getInstance()
                    const result = manager.createPreview(
                      linkTarget,
                      rect.left + rect.width / 2,
                      rect.bottom + 5,
                      config,
                      parentLevel
                    )
                    console.log(`✅ [嵌套L${parentLevel}] 预览创建结果:`, result)
                  })
                  .catch((error) => {
                    console.error(`❌ [嵌套L${parentLevel}] 嵌套预览创建失败:`, error)
                  })
              } else {
                console.warn(`⚠️ [嵌套L${parentLevel}] WikiLink 缺少 data-target 属性`)
              }
            }
            // 移除非 WikiLink 元素的日志记录，减少噪音

            return false
          },

          mouseout(_view, event) {
            const target = event.target as HTMLElement

            // 检查鼠标是否离开了 WikiLink
            if (target.hasAttribute('data-wikilink')) {
              const linkTarget = target.getAttribute('data-target')
              console.log(`🎯 [嵌套L${parentLevel}] 离开 WikiLink: ${linkTarget}`)

              // 不再主动触发层级隐藏，让全局鼠标跟踪处理
              // 这避免了过度频繁的层级隐藏检查
              console.log(`📝 [嵌套L${parentLevel}] 层级隐藏交给全局管理器处理`)
            }

            return false
          }
        }
      }
    })
  })

/**
 * 创建预览编辑器实例
 * 复用主编辑器的完整配置，确保功能一致性
 */
export async function createPreviewEditor(
  container: HTMLElement,
  content: string,
  config: WikiLinkConfig,
  level: number
): Promise<Crepe> {
  console.log(`🏗️ 创建预览编辑器 (层级: ${level}, 内容长度: ${content.length})`)
  
  // 复用当前编辑器的完整配置
  const crepe = new Crepe({
    root: container,
    defaultValue: content,
    features: {
      [Crepe.Feature.Toolbar]: false,        // 预览模式禁用工具栏
      [Crepe.Feature.BlockEdit]: false,      // 预览模式禁用块编辑
      [Crepe.Feature.Cursor]: true,
      [Crepe.Feature.ListItem]: true,
      [Crepe.Feature.LinkTooltip]: false,    // 禁用内置链接提示，使用自定义WikiLink
      [Crepe.Feature.ImageBlock]: true,
      [Crepe.Feature.Placeholder]: false,    // 预览模式不需要占位符
      [Crepe.Feature.Table]: true,
      [Crepe.Feature.Latex]: false,
      [Crepe.Feature.CodeMirror]: true
    }
  })

  // 配置 WikiLink 上下文（与主编辑器一致的方式）
  crepe.editor.config((ctx) => {
    const mergedConfig = {
      ...config,
      // 嵌套预览的点击处理
      onPageClick: (pageName: string) => {
        console.log(`🔗 嵌套 WikiLink 点击: ${pageName} (层级: ${level})`)
        if (config.onPageClick) {
          config.onPageClick(pageName)
        }
      }
    }

    console.log(`📋 [DEBUG] 预览编辑器配置 (层级: ${level}):`, mergedConfig)
    ctx.set(wikiLinkConfigCtx.key, mergedConfig)
  })

  // 注入 WikiLink 插件到预览编辑器（禁用原有预览插件避免冲突）
  console.log('🔌 注入 WikiLink 插件到预览编辑器')
  crepe.editor.use(wikiLinkConfigCtx)      // 配置上下文（必须先注入）
  crepe.editor.use(wikiLinkNode)           // 节点定义
  crepe.editor.use(wikiLinkRemarkPlugin)   // Remark 解析
  crepe.editor.use(wikiLinkCompletionRule) // WikiLink 完成规则（预览模式只需要完成规则）
  // 注意：不使用 wikiLinkPreviewPlugin，避免与嵌套预览插件冲突
  crepe.editor.use(wikiLinkViewPlugin)     // 视图渲染（提供点击功能）

  // 添加嵌套预览插件（替代原有预览插件）
  crepe.editor.use(createNestedPreviewPlugin(config, level))

  try {
    console.log(`🔄 开始创建 Crepe 实例 (层级: ${level})`)

    // 添加超时处理，避免无限等待
    const createPromise = crepe.create()
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('编辑器创建超时')), 10000) // 10秒超时
    })

    await Promise.race([createPromise, timeoutPromise])
    console.log(`✅ Crepe 实例创建完成 (层级: ${level})`)

    // 设置为只读模式
    crepe.setReadonly(true)
    console.log(`🔒 设置为只读模式 (层级: ${level})`)

    console.log(`✅ 预览编辑器创建成功 (层级: ${level})`)
    return crepe

  } catch (error) {
    console.error(`❌ 预览编辑器创建失败 (层级: ${level}):`, error)
    // 清理可能的残留状态
    try {
      crepe.destroy()
    } catch (destroyError) {
      console.warn(`清理编辑器时出错:`, destroyError)
    }
    throw error
  }
}

/**
 * 销毁预览编辑器实例
 */
export function destroyPreviewEditor(editor: Crepe): void {
  try {
    editor.destroy()
    console.log('🗑️ 预览编辑器已销毁')
  } catch (error) {
    console.error('销毁预览编辑器失败:', error)
  }
}
