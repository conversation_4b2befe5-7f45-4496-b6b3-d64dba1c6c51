// Input Component - 输入框组件
// 基于设计规范的统一输入框组件

import { JSX, splitProps, mergeProps, Show, createSignal } from 'solid-js';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';

// 输入框变体样式
const inputVariants = cva(
  'flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-colors',
  {
    variants: {
      variant: {
        default: 'border-input',
        error: 'border-destructive focus-visible:ring-destructive',
        success: 'border-success focus-visible:ring-success',
        warning: 'border-warning focus-visible:ring-warning',
      },
      size: {
        default: 'h-10 px-3 py-2',
        sm: 'h-9 px-3 py-2 text-xs',
        lg: 'h-11 px-4 py-3',
        xl: 'h-12 px-4 py-3 text-base',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface InputProps extends JSX.InputHTMLAttributes<HTMLInputElement> {
  variant?: 'default' | 'error' | 'success' | 'warning';
  size?: 'default' | 'sm' | 'lg' | 'xl';
  label?: string;
  helperText?: string;
  error?: string;
  leftIcon?: JSX.Element;
  rightIcon?: JSX.Element;
  leftAddon?: JSX.Element;
  rightAddon?: JSX.Element;
  fullWidth?: boolean;
  clearable?: boolean;
  onClear?: () => void;
}

export function Input(props: InputProps) {
  const merged = mergeProps(
    {
      variant: 'default' as const,
      size: 'default' as const,
      type: 'text' as const,
    },
    props
  );

  const [local, others] = splitProps(merged, [
    'variant',
    'size',
    'label',
    'helperText',
    'error',
    'leftIcon',
    'rightIcon',
    'leftAddon',
    'rightAddon',
    'fullWidth',
    'clearable',
    'onClear',
    'class',
    'value',
  ]);

  const [isFocused, setIsFocused] = createSignal(false);
  const hasValue = () => local.value !== undefined && local.value !== '';
  const showClearButton = () => local.clearable && hasValue() && !others.disabled && !others.readonly;

  const inputVariant = () => {
    if (local.error) return 'error';
    return local.variant;
  };

  const handleClear = () => {
    local.onClear?.();
  };

  const handleFocus = (e: FocusEvent) => {
    setIsFocused(true);
    others.onFocus?.(e);
  };

  const handleBlur = (e: FocusEvent) => {
    setIsFocused(false);
    others.onBlur?.(e);
  };

  return (
    <div class={cn('space-y-2', local.fullWidth && 'w-full')}>
      {/* 标签 */}
      <Show when={local.label}>
        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {local.label}
          {others.required && <span class="text-destructive ml-1">*</span>}
        </label>
      </Show>

      {/* 输入框容器 */}
      <div class="relative">
        {/* 左侧插件 */}
        <Show when={local.leftAddon}>
          <div class="absolute left-0 top-0 h-full flex items-center px-3 border-r border-input bg-muted text-muted-foreground text-sm">
            {local.leftAddon}
          </div>
        </Show>

        {/* 左侧图标 */}
        <Show when={local.leftIcon && !local.leftAddon}>
          <div class="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground">
            {local.leftIcon}
          </div>
        </Show>

        {/* 输入框 */}
        <input
          class={cn(
            inputVariants({ variant: inputVariant(), size: local.size }),
            local.leftIcon && !local.leftAddon && 'pl-10',
            local.leftAddon && 'pl-16',
            (local.rightIcon || showClearButton()) && !local.rightAddon && 'pr-10',
            local.rightAddon && 'pr-16',
            local.class
          )}
          onFocus={handleFocus}
          onBlur={handleBlur}
          {...others}
        />

        {/* 清除按钮 */}
        <Show when={showClearButton()}>
          <button
            type="button"
            class="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"
            onClick={handleClear}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="h-4 w-4"
            >
              <path d="m18 6-12 12" />
              <path d="m6 6 12 12" />
            </svg>
          </button>
        </Show>

        {/* 右侧图标 */}
        <Show when={local.rightIcon && !showClearButton() && !local.rightAddon}>
          <div class="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground">
            {local.rightIcon}
          </div>
        </Show>

        {/* 右侧插件 */}
        <Show when={local.rightAddon}>
          <div class="absolute right-0 top-0 h-full flex items-center px-3 border-l border-input bg-muted text-muted-foreground text-sm">
            {local.rightAddon}
          </div>
        </Show>
      </div>

      {/* 帮助文本或错误信息 */}
      <Show when={local.error || local.helperText}>
        <p class={cn(
          'text-sm',
          local.error ? 'text-destructive' : 'text-muted-foreground'
        )}>
          {local.error || local.helperText}
        </p>
      </Show>
    </div>
  );
}

// 文本域组件
export interface TextareaProps extends JSX.TextareaHTMLAttributes<HTMLTextAreaElement> {
  variant?: 'default' | 'error' | 'success' | 'warning';
  label?: string;
  helperText?: string;
  error?: string;
  fullWidth?: boolean;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

export function Textarea(props: TextareaProps) {
  const merged = mergeProps(
    {
      variant: 'default' as const,
      resize: 'vertical' as const,
    },
    props
  );

  const [local, others] = splitProps(merged, [
    'variant',
    'label',
    'helperText',
    'error',
    'fullWidth',
    'resize',
    'class',
  ]);

  const textareaVariant = () => {
    if (local.error) return 'error';
    return local.variant;
  };

  const resizeClass = () => {
    switch (local.resize) {
      case 'none':
        return 'resize-none';
      case 'vertical':
        return 'resize-y';
      case 'horizontal':
        return 'resize-x';
      case 'both':
        return 'resize';
      default:
        return 'resize-y';
    }
  };

  return (
    <div class={cn('space-y-2', local.fullWidth && 'w-full')}>
      {/* 标签 */}
      <Show when={local.label}>
        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {local.label}
          {others.required && <span class="text-destructive ml-1">*</span>}
        </label>
      </Show>

      {/* 文本域 */}
      <textarea
        class={cn(
          inputVariants({ variant: textareaVariant(), size: 'default' }),
          'min-h-[80px]',
          resizeClass(),
          local.class
        )}
        {...others}
      />

      {/* 帮助文本或错误信息 */}
      <Show when={local.error || local.helperText}>
        <p class={cn(
          'text-sm',
          local.error ? 'text-destructive' : 'text-muted-foreground'
        )}>
          {local.error || local.helperText}
        </p>
      </Show>
    </div>
  );
}

// 输入框组
export interface InputGroupProps {
  children: JSX.Element;
  class?: string;
  orientation?: 'horizontal' | 'vertical';
}

export function InputGroup(props: InputGroupProps) {
  const merged = mergeProps(
    {
      orientation: 'vertical' as const,
    },
    props
  );

  return (
    <div
      class={cn(
        'space-y-4',
        merged.orientation === 'horizontal' && 'flex space-x-4 space-y-0',
        merged.class
      )}
    >
      {merged.children}
    </div>
  );
}
