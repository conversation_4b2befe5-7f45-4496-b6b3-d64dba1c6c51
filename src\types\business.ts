// Business Types - 业务类型定义

import { Id, EntityStatus, Priority, Tag } from './common';

// 用户相关类型
export interface User {
  id: Id;
  username: string;
  email?: string;
  displayName?: string;
  avatarUrl?: string;
  preferences: UserPreferences;
  status: EntityStatus;
  createdAt: string;
  updatedAt: string;
  version: number;
}

export interface UserPreferences {
  theme: string;
  language: string;
  timezone: string;
  dateFormat: string;
  timeFormat: string;
  notificationsEnabled: boolean;
  autoSaveEnabled: boolean;
}

// 项目相关类型
export interface Project {
  id: Id;
  name: string;
  description?: string;
  goals: string[];
  deliverables: string[];
  startDate?: string;
  dueDate?: string;
  areaId?: Id;
  status: ProjectStatus;
  priority: Priority;
  progress: number;
  tags: string[];
  entityStatus: EntityStatus;
  createdAt: string;
  updatedAt: string;
  version: number;
}

export type ProjectStatus = 'not_started' | 'in_progress' | 'at_risk' | 'on_hold' | 'completed' | 'cancelled';

export interface ProjectStatistics {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  overdueProjects: number;
  averageProgress: number;
  projectsByStatus: Array<{ status: ProjectStatus; count: number }>;
  projectsByPriority: Array<{ priority: Priority; count: number }>;
}

// 任务相关类型
export interface Task {
  id: Id;
  title: string;
  description?: string;
  projectId?: Id;
  parentTaskId?: Id;
  dueDate?: string;
  status: TaskStatus;
  priority: Priority;
  progress: number;
  entityStatus: EntityStatus;
  createdAt: string;
  updatedAt: string;
  version: number;
}

export type TaskStatus = 'todo' | 'in_progress' | 'completed' | 'cancelled';

export interface TaskStatistics {
  totalTasks: number;
  activeTasks: number;
  completedTasks: number;
  overdueTasks: number;
  todayTasks: number;
  averageProgress: number;
  completionRate: number;
  tasksByStatus: Array<{ status: TaskStatus; count: number }>;
  tasksByPriority: Array<{ priority: Priority; count: number }>;
}

// 领域相关类型
export interface Area {
  id: Id;
  name: string;
  description?: string;
  standards: string[];
  color?: string;
  icon?: string;
  status: AreaStatus;
  entityStatus: EntityStatus;
  createdAt: string;
  updatedAt: string;
  version: number;
}

export type AreaStatus = 'active' | 'maintenance' | 'dormant';

export interface AreaDetails {
  area: Area;
  projectCount: number;
  habitCount: number;
  healthScore: number;
  recentProjects: Project[];
}

export interface AreaStatistics {
  totalAreas: number;
  activeAreas: number;
  areasByStatus: Array<{ status: AreaStatus; count: number }>;
  areasWithProjects: number;
  areasWithHabits: number;
  averageProjectsPerArea: number;
  averageHabitsPerArea: number;
}

// 收件箱相关类型
export interface InboxItem {
  id: Id;
  title: string;
  content?: string;
  type: InboxItemType;
  source?: string;
  priority: Priority;
  status: InboxItemStatus;
  tags: string[];
  processedAt?: string;
  processedBy?: Id;
  createdAt: string;
  updatedAt: string;
}

export type InboxItemType = 'note' | 'task' | 'idea' | 'reference' | 'email' | 'link' | 'file';
export type InboxItemStatus = 'unprocessed' | 'processing' | 'processed' | 'archived';

// 资源相关类型
export interface Resource {
  id: Id;
  title: string;
  description?: string;
  type: ResourceType;
  url?: string;
  filePath?: string;
  content?: string;
  tags: string[];
  areaId?: Id;
  projectId?: Id;
  status: EntityStatus;
  accessCount: number;
  lastAccessedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export type ResourceType = 'document' | 'link' | 'file' | 'note' | 'template' | 'reference';

// 习惯相关类型
export interface Habit {
  id: Id;
  name: string;
  description?: string;
  areaId?: Id;
  frequency: HabitFrequency;
  target: number;
  unit: string;
  status: EntityStatus;
  streak: number;
  longestStreak: number;
  completionRate: number;
  createdAt: string;
  updatedAt: string;
}

export interface HabitFrequency {
  type: 'daily' | 'weekly' | 'monthly';
  daysOfWeek?: number[]; // 0-6, 0 = Sunday
  daysOfMonth?: number[]; // 1-31
}

export interface HabitRecord {
  id: Id;
  habitId: Id;
  date: string;
  value: number;
  note?: string;
  createdAt: string;
}

// 复盘相关类型
export interface Review {
  id: Id;
  title: string;
  type: ReviewType;
  period: ReviewPeriod;
  startDate: string;
  endDate: string;
  content: ReviewContent;
  status: ReviewStatus;
  createdAt: string;
  updatedAt: string;
}

export type ReviewType = 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'project';
export type ReviewStatus = 'draft' | 'completed' | 'archived';

export interface ReviewPeriod {
  year: number;
  month?: number;
  week?: number;
  day?: number;
}

export interface ReviewContent {
  achievements: string[];
  challenges: string[];
  lessons: string[];
  improvements: string[];
  goals: string[];
  metrics?: ReviewMetrics;
}

export interface ReviewMetrics {
  projectsCompleted: number;
  tasksCompleted: number;
  habitsCompleted: number;
  productivityScore: number;
  satisfactionScore: number;
}

// 仪表盘相关类型
export interface DashboardData {
  overview: DashboardOverview;
  recentActivities: Activity[];
  upcomingTasks: Task[];
  projectProgress: ProjectProgress[];
  habitStreaks: HabitStreak[];
  statistics: DashboardStatistics;
}

export interface DashboardOverview {
  totalProjects: number;
  activeProjects: number;
  completedTasks: number;
  todayTasks: number;
  inboxItems: number;
  habitStreak: number;
}

export interface Activity {
  id: Id;
  type: ActivityType;
  title: string;
  description?: string;
  entityId: Id;
  entityType: string;
  timestamp: string;
  userId?: Id;
}

export type ActivityType = 'created' | 'updated' | 'completed' | 'deleted' | 'archived';

export interface ProjectProgress {
  projectId: Id;
  projectName: string;
  progress: number;
  status: ProjectStatus;
  dueDate?: string;
}

export interface HabitStreak {
  habitId: Id;
  habitName: string;
  streak: number;
  target: number;
  completionRate: number;
}

export interface DashboardStatistics {
  productivity: {
    score: number;
    trend: 'up' | 'down' | 'stable';
    change: number;
  };
  completion: {
    tasks: number;
    projects: number;
    habits: number;
  };
  time: {
    totalFocusTime: number;
    averageSessionTime: number;
    mostProductiveHour: number;
  };
}

// 搜索相关类型
export interface SearchFilters {
  type?: string[];
  status?: string[];
  priority?: Priority[];
  dateRange?: {
    start: string;
    end: string;
  };
  tags?: string[];
  areaId?: Id;
  projectId?: Id;
}

export interface SearchOptions {
  query: string;
  filters?: SearchFilters;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}
