// API Factory - API工厂
// 根据环境变量决定使用真实API还是Mock API

import { apiService } from './api';
import { mockApiService } from './mockApi';

// 环境配置
const isDevelopment = true; // 暂时硬编码为开发模式
const useMockApi = true; // 暂时硬编码使用Mock API
const forceMockApi = false;

// API服务选择逻辑
function createApiService() {
  // 强制使用Mock API（优先级最高）
  if (forceMockApi) {
    console.log('🔧 Using Mock API (forced by VITE_FORCE_MOCK_API)');
    return mockApiService;
  }

  // 开发环境且启用Mock API
  if (isDevelopment && useMockApi) {
    console.log('🔧 Using Mock API (development mode)');
    return mockApiService;
  }

  // 默认使用真实API
  console.log('🚀 Using Real Tauri API');
  return apiService;
}

// 创建API服务实例
export const api = createApiService();

// 导出所有API方法
export const getUsers = () => api.getUsers();
export const getUserById = (id: string) => api.getUserById(id);
export const createUser = (data: any) => api.createUser(data);
export const updateUser = (id: string, data: any) => api.updateUser(id, data);
export const deleteUser = (id: string) => api.deleteUser(id);

export const getProjects = () => api.getProjects();
export const getProjectById = (id: string) => api.getProjectById(id);
export const createProject = (data: any) => api.createProject(data);
export const updateProject = (id: string, data: any) => api.updateProject(id, data);
export const updateProjectStatus = (id: string, status: string) => api.updateProjectStatus(id, status);
export const updateProjectProgress = (id: string, progress: number) => api.updateProjectProgress(id, progress);
export const deleteProject = (id: string) => api.deleteProject(id);
export const getProjectStatistics = () => api.getProjectStatistics();

export const getTasks = () => api.getTasks();
export const getTaskById = (id: string) => api.getTaskById(id);
export const getTasksByProject = (projectId: string) => api.getTasksByProject(projectId);
export const getTodayTasks = () => api.getTodayTasks();
export const getOverdueTasks = () => api.getOverdueTasks();
export const createTask = (data: any) => api.createTask(data);
export const updateTask = (id: string, data: any) => api.updateTask(id, data);
export const updateTaskStatus = (id: string, status: string) => api.updateTaskStatus(id, status);
export const updateTaskProgress = (id: string, progress: number) => api.updateTaskProgress(id, progress);
export const completeTask = (id: string) => api.completeTask(id);
export const deleteTask = (id: string) => api.deleteTask(id);
export const getTaskStatistics = () => api.getTaskStatistics();

export const getAreas = () => api.getAreas();
export const getAreaById = (id: string) => api.getAreaById(id);
export const createArea = (data: any) => api.createArea(data);
export const updateArea = (id: string, data: any) => api.updateArea(id, data);

export const healthCheck = () => api.healthCheck();
export const getSystemInfo = () => api.getSystemInfo();

export const searchProjects = (query: string) => api.searchProjects(query);
export const searchTasks = (query: string) => api.searchTasks(query);
export const searchAreas = (query: string) => api.searchAreas(query);

// 工具函数：检查当前使用的API类型
export function getApiType(): 'mock' | 'real' {
  return api === mockApiService ? 'mock' : 'real';
}

// 工具函数：动态切换API（仅开发环境）
export function switchToMockApi() {
  if (!isDevelopment) {
    console.warn('API switching is only available in development mode');
    return false;
  }

  // 这里可以实现动态切换逻辑
  console.log('Switching to Mock API...');
  return true;
}

export function switchToRealApi() {
  if (!isDevelopment) {
    console.warn('API switching is only available in development mode');
    return false;
  }

  console.log('Switching to Real API...');
  return true;
}
