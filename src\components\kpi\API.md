# KPI组件库 API 文档

## 核心组件

### KPITracker

主要的KPI跟踪和管理组件，提供完整的KPI管理功能。

#### Props

```tsx
interface KPITrackerProps {
  // 必需属性
  parentId: string                    // 父实体ID（项目ID或领域ID）
  parentType: KPIType                 // 父实体类型：'project' | 'area'
  dataSource: KPIDataSource          // 数据源接口实现
  
  // 可选配置
  config?: Partial<KPIComponentConfig>     // 组件配置选项
  kpiConfig?: Partial<KPIConfig>           // KPI配置选项
  eventHandlers?: KPIEventHandlers         // 事件处理器
  
  // 初始数据
  initialKPIs?: BaseKPI[]             // 初始KPI数据
  
  // 样式
  theme?: 'light' | 'dark' | 'auto'   // 主题
  className?: string                   // CSS类名
}
```

#### 使用示例

```tsx
<KPITracker
  parentId="project-123"
  parentType="project"
  dataSource={createKPIDataSource('project')}
  config={{
    showChart: true,
    allowCreate: true,
    layout: 'grid'
  }}
  eventHandlers={{
    onCreate: (kpi) => console.log('Created:', kpi),
    onUpdate: (kpi) => console.log('Updated:', kpi)
  }}
/>
```

### KPIChart

KPI数据可视化组件，支持多种图表类型。

#### Props

```tsx
interface KPIChartProps {
  kpis: BaseKPI[]                     // KPI数据数组
  type?: 'bar' | 'line' | 'pie' | 'donut' | 'progress'  // 图表类型
  showLegend?: boolean                // 是否显示图例
  showTooltip?: boolean               // 是否显示工具提示
  height?: number                     // 图表高度
  onKPIClick?: (kpi: BaseKPI) => void // KPI点击回调
  class?: string                      // CSS类名
}
```

#### 使用示例

```tsx
<KPIChart
  kpis={kpiList}
  type="donut"
  showLegend={true}
  height={300}
  onKPIClick={(kpi) => setSelectedKPI(kpi)}
/>
```

### KPIInput

单个KPI的快速输入和管理组件。

#### Props

```tsx
interface KPIInputProps {
  kpi: BaseKPI                        // KPI数据
  onRecord?: (record: KPIRecord) => void      // 记录创建回调
  onEdit?: (kpi: BaseKPI) => void             // 编辑回调
  onDelete?: (kpiId: string) => void          // 删除回调
  showHistory?: boolean               // 是否显示历史记录
  allowEdit?: boolean                 // 是否允许编辑
  allowDelete?: boolean               // 是否允许删除
  class?: string                      // CSS类名
}
```

### KPIDialog

KPI创建和编辑对话框组件。

#### Props

```tsx
interface KPIDialogProps {
  open: boolean                       // 是否打开对话框
  mode: 'create' | 'edit'            // 模式：创建或编辑
  kpi?: BaseKPI                       // 编辑时的KPI数据
  config?: KPIConfig                  // KPI配置
  onSubmit: (data: CreateKPIData) => Promise<void>  // 提交回调
  onCancel: () => void                // 取消回调
  class?: string                      // CSS类名
}
```

## 数据类型

### BaseKPI

基础KPI数据结构。

```tsx
interface BaseKPI {
  id: string                          // 唯一标识
  name: string                        // KPI名称
  description?: string                // 描述
  value: number                       // 当前值
  target?: number                     // 目标值
  unit?: string                       // 单位
  direction: KPIDirection             // 方向：'increase' | 'decrease'
  frequency?: KPIFrequency            // 频率
  isActive: boolean                   // 是否活跃
  createdAt: Date                     // 创建时间
  updatedAt: Date                     // 更新时间
}
```

### KPIRecord

KPI记录数据结构。

```tsx
interface KPIRecord {
  id: string                          // 记录ID
  kpiId: string                       // 关联的KPI ID
  value: number                       // 记录值
  note?: string                       // 备注
  recordedAt: Date                    // 记录时间
  source: 'manual' | 'auto' | 'import'  // 数据来源
}
```

### KPIStatistics

KPI统计信息。

```tsx
interface KPIStatistics {
  total: number                       // 总数
  achieved: number                    // 已达成数量
  onTrack: number                     // 进展良好数量
  atRisk: number                      // 有风险数量
  behind: number                      // 落后数量
  noTarget: number                    // 无目标数量
  averageProgress: number             // 平均进度
}
```

## 配置选项

### KPIComponentConfig

组件配置选项。

```tsx
interface KPIComponentConfig {
  // 显示选项
  showChart?: boolean                 // 显示图表（默认：true）
  showQuickInput?: boolean            // 显示快速输入（默认：true）
  showHistory?: boolean               // 显示历史记录（默认：false）
  showStatistics?: boolean            // 显示统计信息（默认：true）
  
  // 交互选项
  allowCreate?: boolean               // 允许创建（默认：true）
  allowEdit?: boolean                 // 允许编辑（默认：true）
  allowDelete?: boolean               // 允许删除（默认：true）
  allowBatchRecord?: boolean          // 允许批量记录（默认：false）
  
  // 布局选项
  layout?: 'grid' | 'list' | 'compact'      // 布局模式（默认：'grid'）
  chartType?: 'bar' | 'line' | 'pie' | 'donut'  // 图表类型（默认：'bar'）
  
  // 数据选项
  maxRecords?: number                 // 最大记录数（默认：20）
  autoRefresh?: boolean               // 自动刷新（默认：false）
  refreshInterval?: number            // 刷新间隔毫秒（默认：30000）
  
  // 样式选项
  className?: string                  // CSS类名
  theme?: 'light' | 'dark' | 'auto'  // 主题（默认：'auto'）
}
```

### KPIConfig

KPI配置选项。

```tsx
interface KPIConfig {
  type: KPIType                       // KPI类型
  templates: KPITemplate[]            // 模板列表
  allowedUnits: string[]              // 允许的单位
  validationRules: ValidationRules    // 验证规则
  defaultFrequency: KPIFrequency      // 默认频率
  defaultDirection: KPIDirection      // 默认方向
}
```

## 事件处理

### KPIEventHandlers

事件处理器接口。

```tsx
interface KPIEventHandlers {
  onCreate?: (kpi: BaseKPI) => void | Promise<void>
  onUpdate?: (kpi: BaseKPI, changes: Partial<BaseKPI>) => void | Promise<void>
  onDelete?: (kpiId: string) => void | Promise<void>
  onRecord?: (record: KPIRecord) => void | Promise<void>
  onProgressChange?: (kpiId: string, progress: KPIProgress) => void | Promise<void>
  onStatusChange?: (kpiId: string, oldStatus: KPIStatus, newStatus: KPIStatus) => void | Promise<void>
  onError?: (error: Error, context?: string) => void
}
```

### 事件类型

- `onCreate` - KPI创建时触发
- `onUpdate` - KPI更新时触发
- `onDelete` - KPI删除时触发
- `onRecord` - 创建记录时触发
- `onProgressChange` - 进度变化时触发
- `onStatusChange` - 状态变化时触发
- `onError` - 发生错误时触发

## 数据源接口

### KPIDataSource

数据源接口定义。

```tsx
interface KPIDataSource {
  // KPI CRUD操作
  getKPIs(parentId: string, options?: QueryOptions): Promise<BaseKPI[]>
  getKPI(id: string): Promise<BaseKPI | null>
  createKPI(parentId: string, data: CreateKPIData): Promise<BaseKPI>
  updateKPI(id: string, data: Partial<CreateKPIData>): Promise<BaseKPI>
  deleteKPI(id: string): Promise<void>
  
  // 记录操作
  getRecords(kpiId: string, options?: QueryOptions): Promise<KPIRecord[]>
  createRecord(kpiId: string, data: CreateRecordData): Promise<KPIRecord>
  deleteRecord(recordId: string): Promise<void>
  
  // 统计信息
  getStatistics(parentId: string): Promise<KPIStatistics>
}
```

### 创建数据源

```tsx
// 使用工厂函数创建数据源
const projectDataSource = createKPIDataSource('project')
const areaDataSource = createKPIDataSource('area')

// 自定义数据源实现
class CustomKPIDataSource implements KPIDataSource {
  async getKPIs(parentId: string): Promise<BaseKPI[]> {
    // 自定义实现
  }
  // ... 其他方法
}
```

## 工具函数

### calculateKPIProgress

计算KPI进度百分比。

```tsx
function calculateKPIProgress(kpi: BaseKPI): number
```

### getKPIStatus

获取KPI状态信息。

```tsx
function getKPIStatus(progress: number): {
  status: KPIStatus
  color: string
  label: string
}
```

### formatKPIValue

格式化KPI值显示。

```tsx
function formatKPIValue(
  value: number, 
  unit?: string, 
  precision?: number
): string
```

### validateKPIData

验证KPI数据。

```tsx
function validateKPIData(
  data: Partial<CreateKPIData>,
  rules?: ValidationRules,
  isUpdate?: boolean
): {
  isValid: boolean
  errors: Record<string, string>
}
```

## 预设配置

### 项目KPI配置

```tsx
import { PROJECT_KPI_CONFIG } from '@/components/kpi'
```

### 领域指标配置

```tsx
import { AREA_METRIC_CONFIG } from '@/components/kpi'
```

### 模板

```tsx
import { 
  PROJECT_KPI_TEMPLATES, 
  AREA_METRIC_TEMPLATES 
} from '@/components/kpi'
```

## 类型定义

所有类型定义都可以从主入口导入：

```tsx
import type {
  BaseKPI,
  KPIRecord,
  KPIStatistics,
  KPIComponentConfig,
  KPIEventHandlers,
  KPIDataSource,
  // ... 其他类型
} from '@/components/kpi'
```
