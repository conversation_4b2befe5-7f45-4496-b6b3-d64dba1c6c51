// System Commands - 系统相关的 Tauri 命令

use crate::api::dto::*;
use crate::infrastructure::database::DatabaseManager;
use std::sync::Arc;
use tauri::State;

/// 健康检查
#[tauri::command]
pub async fn health_check(
    db_manager: State<'_, Arc<DatabaseManager>>,
) -> Result<ApiResponse<HealthCheckResponse>, String> {
    // 检查数据库连接状态
    let database_status = match db_manager.health_check().await {
        Ok(_) => "healthy".to_string(),
        Err(_) => "unhealthy".to_string(),
    };

    let response = HealthCheckResponse {
        status: "ok".to_string(),
        timestamp: chrono::Utc::now(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        database_status,
    };

    Ok(ApiResponse::success(response))
}

/// 获取系统信息
#[tauri::command]
pub async fn get_system_info() -> Result<ApiResponse<SystemInfoResponse>, String> {
    let response = SystemInfoResponse {
        app_name: "BubbleSay".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        environment: std::env::var("ENVIRONMENT").unwrap_or_else(|_| "development".to_string()),
        uptime: get_uptime_seconds(),
        memory_usage: get_memory_usage(),
    };

    Ok(ApiResponse::success(response))
}

/// 初始化数据库
#[tauri::command]
pub async fn initialize_database(
    db_manager: State<'_, Arc<DatabaseManager>>,
) -> Result<ApiResponse<EmptyResponse>, String> {
    match db_manager.initialize().await {
        Ok(_) => Ok(ApiResponse::success(EmptyResponse)),
        Err(e) => Ok(ApiResponse::error(format!("Failed to initialize database: {}", e))),
    }
}

/// 备份数据库
#[tauri::command]
pub async fn backup_database(
    backup_path: String,
    db_manager: State<'_, Arc<DatabaseManager>>,
) -> Result<ApiResponse<EmptyResponse>, String> {
    match db_manager.backup(&backup_path).await {
        Ok(_) => Ok(ApiResponse::success(EmptyResponse)),
        Err(e) => Ok(ApiResponse::error(format!("Failed to backup database: {}", e))),
    }
}

/// 恢复数据库
#[tauri::command]
pub async fn restore_database(
    backup_path: String,
    db_manager: State<'_, Arc<DatabaseManager>>,
) -> Result<ApiResponse<EmptyResponse>, String> {
    match db_manager.restore(&backup_path).await {
        Ok(_) => Ok(ApiResponse::success(EmptyResponse)),
        Err(e) => Ok(ApiResponse::error(format!("Failed to restore database: {}", e))),
    }
}

/// 获取应用程序运行时间（秒）
fn get_uptime_seconds() -> u64 {
    // 简化实现，实际应用中可以记录启动时间
    use std::time::{SystemTime, UNIX_EPOCH};
    
    static mut START_TIME: Option<u64> = None;
    
    unsafe {
        if START_TIME.is_none() {
            START_TIME = Some(
                SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs()
            );
        }
        
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
            
        current_time - START_TIME.unwrap()
    }
}

/// 获取内存使用量（字节）
fn get_memory_usage() -> u64 {
    // 简化实现，实际应用中可以使用系统API获取真实内存使用量
    #[cfg(target_os = "windows")]
    {
        use std::mem;
        use winapi::um::processthreadsapi::GetCurrentProcess;
        use winapi::um::psapi::{GetProcessMemoryInfo, PROCESS_MEMORY_COUNTERS};
        
        unsafe {
            let mut pmc: PROCESS_MEMORY_COUNTERS = mem::zeroed();
            let result = GetProcessMemoryInfo(
                GetCurrentProcess(),
                &mut pmc,
                mem::size_of::<PROCESS_MEMORY_COUNTERS>() as u32,
            );
            
            if result != 0 {
                pmc.WorkingSetSize as u64
            } else {
                0
            }
        }
    }
    
    #[cfg(not(target_os = "windows"))]
    {
        // 对于非Windows系统，返回一个估计值
        0
    }
}

/// 系统信息响应
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct SystemInfoResponse {
    pub app_name: String,
    pub version: String,
    pub environment: String,
    pub uptime: u64, // 秒
    pub memory_usage: u64, // 字节
}
