/**
 * 项目资源关联使用示例
 * 展示如何在项目详情页面中集成资源关联组件
 */

import { createSignal, onMount } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card'
import { Button } from '../../ui/button'
import { Badge } from '../../ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../ui/tabs'
import { 
  FileText, 
  Settings, 
  RefreshCw,
  BarChart3,
  Network
} from 'lucide-solid'

// 导入资源关联组件
import { 
  ResourceAssociation,
  BidirectionalLinks,
  createResourceDataSource,
  type ResourceEventHandlers,
  type ResourceAssociationConfig
} from '../index'

interface ProjectResourceExampleProps {
  projectId: string
  projectName?: string
  className?: string
}

export function ProjectResourceExample(props: ProjectResourceExampleProps) {
  // 状态管理
  const [refreshKey, setRefreshKey] = createSignal(0)
  const [showSettings, setShowSettings] = createSignal(false)
  const [activeTab, setActiveTab] = createSignal('resources')

  // 创建数据源
  const dataSource = createResourceDataSource('project')

  // 组件配置
  const componentConfig: ResourceAssociationConfig = {
    enableMarkdownAssociation: true,
    enableLinkManagement: true,
    enableFileUpload: true,
    enableBidirectionalLinks: true,
    showResourcePreview: true,
    showResourceStats: true,
    showRecentResources: true,
    showResourceSearch: true,
    allowCreate: true,
    allowEdit: true,
    allowDelete: true,
    allowBulkOperations: true,
    allowDragDrop: true,
    layout: 'list',
    groupBy: 'type',
    sortBy: 'date',
    sortOrder: 'desc',
    maxFileSize: 50 * 1024 * 1024, // 50MB
    allowedFileTypes: ['*'],
    enableThumbnails: true,
    enableLinkPreview: true,
    enableFaviconFetch: true,
    linkValidationTimeout: 5000,
    enableWikiLinks: true,
    enableAutoLinking: true,
    theme: 'auto'
  }

  // 事件处理器
  const eventHandlers: ResourceEventHandlers = {
    onCreate: (resource) => {
      console.log('Resource created:', resource)
      // 可以在这里添加通知、日志记录等
    },
    
    onUpdate: (resource, changes) => {
      console.log('Resource updated:', resource, changes)
      // 可以在这里触发项目更新通知
    },
    
    onDelete: (resourceId) => {
      console.log('Resource deleted:', resourceId)
      // 可以在这里清理相关数据
    },
    
    onAccess: (resource) => {
      console.log('Resource accessed:', resource)
      // 可以在这里记录访问日志
    },
    
    onUploadStart: (uploadData) => {
      console.log('Upload started:', uploadData)
      // 可以在这里显示上传进度通知
    },
    
    onUploadProgress: (resourceId, progress) => {
      console.log('Upload progress:', resourceId, progress)
      // 可以在这里更新进度条
    },
    
    onUploadComplete: (resource) => {
      console.log('Upload completed:', resource)
      // 可以在这里显示成功通知
    },
    
    onUploadError: (error, uploadData) => {
      console.error('Upload error:', error, uploadData)
      // 可以在这里显示错误通知
    },
    
    onLinkCreate: (link) => {
      console.log('Bidirectional link created:', link)
      // 可以在这里更新链接图谱
    },
    
    onLinkUpdate: (link) => {
      console.log('Bidirectional link updated:', link)
      // 可以在这里刷新链接视图
    },
    
    onLinkValidate: (link, isValid) => {
      console.log('Link validated:', link, isValid)
      // 可以在这里更新链接状态
    },
    
    onError: (error, context) => {
      console.error('Resource error:', error, context)
      // 可以在这里显示错误通知
    }
  }

  // 手动刷新
  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1)
  }

  return (
    <div class="space-y-6">
      {/* 页面头部 */}
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <div>
              <CardTitle class="flex items-center gap-2">
                <FileText class="h-5 w-5" />
                Project Resources
                <Badge variant="outline">
                  {props.projectName || `Project ${props.projectId}`}
                </Badge>
              </CardTitle>
              <CardDescription>
                Manage documents, links, files and references for this project
              </CardDescription>
            </div>
            <div class="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
              >
                <RefreshCw class="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(!showSettings())}
              >
                <Settings class="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 标签页导航 */}
      <Tabs value={activeTab()} onValueChange={setActiveTab}>
        <TabsList class="grid w-full grid-cols-3">
          <TabsTrigger value="resources" class="flex items-center gap-2">
            <FileText class="h-4 w-4" />
            Resources
          </TabsTrigger>
          <TabsTrigger value="links" class="flex items-center gap-2">
            <Network class="h-4 w-4" />
            Links
          </TabsTrigger>
          <TabsTrigger value="analytics" class="flex items-center gap-2">
            <BarChart3 class="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* 资源管理标签页 */}
        <TabsContent value="resources" class="space-y-6">
          <ResourceAssociation
            key={refreshKey()} // 用于强制刷新
            entityType="project"
            entityId={props.projectId}
            dataSource={dataSource}
            config={componentConfig}
            eventHandlers={eventHandlers}
            className={props.className}
          />
        </TabsContent>

        {/* 双向链接标签页 */}
        <TabsContent value="links" class="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Document Links</CardTitle>
              <CardDescription>
                Explore bidirectional links between project documents
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* 这里需要获取项目的主要文档路径 */}
              <BidirectionalLinks
                documentPath={`/projects/${props.projectId}/main.md`}
                links={[]} // 这里应该从数据源获取链接
                onLinkClick={(link) => console.log('Link clicked:', link)}
                onLinkCreate={(sourceDoc, targetDoc) => console.log('Create link:', sourceDoc, targetDoc)}
                showBacklinks={true}
                showOutlinks={true}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* 分析标签页 */}
        <TabsContent value="analytics" class="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Resource Analytics</CardTitle>
              <CardDescription>
                Insights about your project resources
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                  <div class="text-2xl font-bold text-blue-600">12</div>
                  <div class="text-sm text-blue-700">Total Resources</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg">
                  <div class="text-2xl font-bold text-green-600">8</div>
                  <div class="text-sm text-green-700">Documents</div>
                </div>
                <div class="text-center p-4 bg-purple-50 rounded-lg">
                  <div class="text-2xl font-bold text-purple-600">4</div>
                  <div class="text-sm text-purple-700">Files</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default ProjectResourceExample
