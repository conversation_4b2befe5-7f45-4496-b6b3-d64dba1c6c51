/**
 * 领域指标数据缓存服务
 * 提供高性能的数据缓存和智能预加载功能
 */

import type { AreaMetric, AreaMetricRecord } from '../../../shared/types'

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number // Time to live in milliseconds
}

interface MetricCacheData {
  metrics: AreaMetric[]
  records: { [metricId: string]: AreaMetricRecord[] }
  lastUpdated: number
}

class AreaMetricCacheService {
  private cache = new Map<string, CacheEntry<any>>()
  private readonly DEFAULT_TTL = 5 * 60 * 1000 // 5 minutes
  private readonly METRICS_TTL = 2 * 60 * 1000 // 2 minutes for metrics
  private readonly RECORDS_TTL = 1 * 60 * 1000 // 1 minute for records

  /**
   * 生成缓存键
   */
  private getCacheKey(type: string, id: string, params?: any): string {
    const paramStr = params ? JSON.stringify(params) : ''
    return `${type}:${id}:${paramStr}`
  }

  /**
   * 检查缓存项是否过期
   */
  private isExpired(entry: CacheEntry<any>): boolean {
    return Date.now() - entry.timestamp > entry.ttl
  }

  /**
   * 设置缓存
   */
  private setCache<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  /**
   * 获取缓存
   */
  private getCache<T>(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry || this.isExpired(entry)) {
      this.cache.delete(key)
      return null
    }
    return entry.data
  }

  /**
   * 缓存领域指标列表
   */
  cacheAreaMetrics(areaId: string, metrics: AreaMetric[]): void {
    const key = this.getCacheKey('metrics', areaId)
    this.setCache(key, metrics, this.METRICS_TTL)
  }

  /**
   * 获取缓存的领域指标列表
   */
  getCachedAreaMetrics(areaId: string): AreaMetric[] | null {
    const key = this.getCacheKey('metrics', areaId)
    return this.getCache<AreaMetric[]>(key)
  }

  /**
   * 缓存指标记录
   */
  cacheMetricRecords(metricId: string, records: AreaMetricRecord[], limit?: number): void {
    const key = this.getCacheKey('records', metricId, { limit })
    this.setCache(key, records, this.RECORDS_TTL)
  }

  /**
   * 获取缓存的指标记录
   */
  getCachedMetricRecords(metricId: string, limit?: number): AreaMetricRecord[] | null {
    const key = this.getCacheKey('records', metricId, { limit })
    return this.getCache<AreaMetricRecord[]>(key)
  }

  /**
   * 缓存聚合数据（用于分析）
   */
  cacheAggregatedData(areaId: string, data: MetricCacheData): void {
    const key = this.getCacheKey('aggregated', areaId)
    this.setCache(key, data, this.METRICS_TTL)
  }

  /**
   * 获取缓存的聚合数据
   */
  getCachedAggregatedData(areaId: string): MetricCacheData | null {
    const key = this.getCacheKey('aggregated', areaId)
    return this.getCache<MetricCacheData>(key)
  }

  /**
   * 缓存统计数据
   */
  cacheStatistics(areaId: string, stats: any): void {
    const key = this.getCacheKey('stats', areaId)
    this.setCache(key, stats, this.METRICS_TTL)
  }

  /**
   * 获取缓存的统计数据
   */
  getCachedStatistics(areaId: string): any | null {
    const key = this.getCacheKey('stats', areaId)
    return this.getCache<any>(key)
  }

  /**
   * 使指定领域的所有缓存失效
   */
  invalidateAreaCache(areaId: string): void {
    const keysToDelete: string[] = []
    
    for (const [key] of this.cache) {
      if (key.includes(`:${areaId}:`)) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key))
  }

  /**
   * 使指定指标的缓存失效
   */
  invalidateMetricCache(metricId: string): void {
    const keysToDelete: string[] = []
    
    for (const [key] of this.cache) {
      if (key.includes(`records:${metricId}:`)) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key))
  }

  /**
   * 预加载领域数据
   */
  async preloadAreaData(areaId: string, databaseApi: any): Promise<void> {
    try {
      // 检查是否已有缓存
      const cachedMetrics = this.getCachedAreaMetrics(areaId)
      if (cachedMetrics) {
        return // 已有缓存，无需预加载
      }

      // 加载指标列表
      const metricsResult = await databaseApi.getAreaMetrics(areaId)
      if (metricsResult.success) {
        const metrics = metricsResult.data || []
        this.cacheAreaMetrics(areaId, metrics)

        // 预加载前5个指标的记录
        const topMetrics = metrics.slice(0, 5)
        const recordPromises = topMetrics.map(async (metric) => {
          const recordsResult = await databaseApi.getAreaMetricRecords(metric.id, 20)
          if (recordsResult.success) {
            this.cacheMetricRecords(metric.id, recordsResult.data || [], 20)
          }
        })

        await Promise.all(recordPromises)
      }
    } catch (error) {
      console.error('Failed to preload area data:', error)
    }
  }

  /**
   * 智能缓存更新
   * 当数据发生变化时，智能地更新相关缓存
   */
  smartCacheUpdate(areaId: string, metricId: string, newRecord?: AreaMetricRecord): void {
    // 使指标记录缓存失效
    this.invalidateMetricCache(metricId)
    
    // 如果有新记录，更新聚合数据缓存
    if (newRecord) {
      const aggregatedData = this.getCachedAggregatedData(areaId)
      if (aggregatedData) {
        // 更新记录到聚合数据中
        if (!aggregatedData.records[metricId]) {
          aggregatedData.records[metricId] = []
        }
        aggregatedData.records[metricId].unshift(newRecord)
        
        // 限制记录数量以控制内存使用
        if (aggregatedData.records[metricId].length > 50) {
          aggregatedData.records[metricId] = aggregatedData.records[metricId].slice(0, 50)
        }
        
        aggregatedData.lastUpdated = Date.now()
        this.cacheAggregatedData(areaId, aggregatedData)
      }
    }
    
    // 使统计数据缓存失效
    const statsKey = this.getCacheKey('stats', areaId)
    this.cache.delete(statsKey)
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): {
    totalEntries: number
    memoryUsage: number
    hitRate: number
    expiredEntries: number
  } {
    let expiredCount = 0
    let totalSize = 0
    
    for (const [key, entry] of this.cache) {
      if (this.isExpired(entry)) {
        expiredCount++
      }
      
      // 估算内存使用（简单估算）
      totalSize += JSON.stringify(entry.data).length
    }
    
    return {
      totalEntries: this.cache.size,
      memoryUsage: totalSize,
      hitRate: 0, // 需要额外的计数器来跟踪命中率
      expiredEntries: expiredCount
    }
  }

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache(): void {
    const keysToDelete: string[] = []
    
    for (const [key, entry] of this.cache) {
      if (this.isExpired(entry)) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key))
  }

  /**
   * 清空所有缓存
   */
  clearAllCache(): void {
    this.cache.clear()
  }

  /**
   * 获取缓存大小
   */
  getCacheSize(): number {
    return this.cache.size
  }

  /**
   * 批量预加载多个领域的数据
   */
  async batchPreloadAreas(areaIds: string[], databaseApi: any): Promise<void> {
    const preloadPromises = areaIds.map(areaId => this.preloadAreaData(areaId, databaseApi))
    await Promise.allSettled(preloadPromises)
  }
}

// 创建单例实例
export const areaMetricCache = new AreaMetricCacheService()

// 定期清理过期缓存
setInterval(() => {
  areaMetricCache.cleanupExpiredCache()
}, 5 * 60 * 1000) // 每5分钟清理一次

export default areaMetricCache
