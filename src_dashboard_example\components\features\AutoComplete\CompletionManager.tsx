import React, { useState, useCallback, useRef, useEffect } from 'react'
import type {
  CompletionProvider,
  CompletionContext,
  CompletionItem,
  CompletionConfig
} from './types'
import { CompletionUI } from './CompletionUI'
import { emojiCompleteProvider } from './EmojiComplete'
import { mentionCompleteProvider } from './MentionComplete'
import { hashtagCompleteProvider } from './HashtagComplete'
import { wikiLinkCompleteProvider } from './WikiLinkComplete'

/**
 * 补全管理器属性
 */
interface CompletionManagerProps {
  /** 编辑器视图 */
  view: any
  /** 补全配置 */
  config: CompletionConfig
  /** 是否启用 */
  enabled?: boolean
}

/**
 * 补全管理器
 * 统一管理所有类型的智能补全功能
 */
export function CompletionManager({ view, config, enabled = true }: CompletionManagerProps) {
  const [completions, setCompletions] = useState<CompletionItem[]>([])
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [visible, setVisible] = useState(false)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [currentContext, setCurrentContext] = useState<CompletionContext | null>(null)

  const debounceTimerRef = useRef<NodeJS.Timeout>()
  const providersRef = useRef<CompletionProvider[]>([])

  // 初始化提供者
  useEffect(() => {
    const providers: CompletionProvider[] = []

    if (config.emoji) providers.push(emojiCompleteProvider)
    if (config.mention) providers.push(mentionCompleteProvider)
    if (config.hashtag) providers.push(hashtagCompleteProvider)
    if (config.wikilink) providers.push(wikiLinkCompleteProvider)

    providersRef.current = providers
  }, [config])

  /**
   * 检测补全触发
   */
  const detectTrigger = useCallback(
    (text: string, cursorPos: number): CompletionContext | null => {
      if (!enabled || !view) return null

      // 检查每个提供者的触发字符
      for (const provider of providersRef.current) {
        const triggerIndex = text.lastIndexOf(provider.trigger, cursorPos)

        if (triggerIndex >= 0) {
          // 确保触发字符前是空格或行首
          const beforeTrigger = triggerIndex > 0 ? text[triggerIndex - 1] : ' '
          if (beforeTrigger !== ' ' && beforeTrigger !== '\n' && triggerIndex !== 0) {
            continue
          }

          const query = text.substring(triggerIndex + provider.trigger.length, cursorPos)

          // 查询不能包含空格（除了双链可以包含空格）
          if (provider.trigger !== '[[' && query.includes(' ')) {
            continue
          }

          return {
            trigger: provider.trigger,
            query,
            position: {
              from: triggerIndex,
              to: cursorPos
            },
            view
          }
        }
      }

      return null
    },
    [enabled, view]
  )

  /**
   * 获取补全建议
   */
  const getCompletions = useCallback(async (context: CompletionContext) => {
    const provider = providersRef.current.find((p) => p.trigger === context.trigger)
    if (!provider) return []

    try {
      return await provider.getCompletions(context)
    } catch (error) {
      console.error('获取补全建议失败:', error)
      return []
    }
  }, [])

  /**
   * 应用补全
   */
  const applyCompletion = useCallback(
    (item: CompletionItem) => {
      if (!currentContext) return

      const provider = providersRef.current.find((p) => p.trigger === currentContext.trigger)
      if (!provider) return

      try {
        provider.applyCompletion(item, currentContext)
        setVisible(false)
        setCompletions([])
        setCurrentContext(null)
      } catch (error) {
        console.error('应用补全失败:', error)
      }
    },
    [currentContext]
  )

  // 暂时禁用自动触发，等待后续完善
  // TODO: 实现与Milkdown的深度集成
  useEffect(() => {
    // 占位符，后续实现编辑器集成
    console.log('CompletionManager initialized with config:', config)
  }, [config])

  /**
   * 处理键盘导航
   */
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!visible || completions.length === 0) return false

      switch (event.key) {
        case 'ArrowUp':
          event.preventDefault()
          setSelectedIndex((prev) => (prev > 0 ? prev - 1 : completions.length - 1))
          return true

        case 'ArrowDown':
          event.preventDefault()
          setSelectedIndex((prev) => (prev < completions.length - 1 ? prev + 1 : 0))
          return true

        case 'Enter':
        case 'Tab':
          event.preventDefault()
          if (completions[selectedIndex]) {
            applyCompletion(completions[selectedIndex])
          }
          return true

        case 'Escape':
          event.preventDefault()
          setVisible(false)
          setCompletions([])
          setCurrentContext(null)
          return true

        default:
          return false
      }
    },
    [visible, completions, selectedIndex, applyCompletion]
  )

  /**
   * 处理选择
   */
  const handleSelect = useCallback(
    (item: CompletionItem) => {
      applyCompletion(item)
    },
    [applyCompletion]
  )

  /**
   * 关闭补全
   */
  const handleClose = useCallback(() => {
    setVisible(false)
    setCompletions([])
    setCurrentContext(null)
  }, [])

  // 暂时禁用键盘监听，等待后续完善
  // TODO: 实现键盘事件处理

  // 清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
    }
  }, [])

  return (
    <CompletionUI
      items={completions}
      selectedIndex={selectedIndex}
      position={position}
      visible={visible}
      onSelect={handleSelect}
      onClose={handleClose}
    />
  )
}

/**
 * 补全管理器钩子
 * 提供便捷的补全功能集成方法
 */
export function useCompletion(view: any, config: CompletionConfig) {
  const [manager, setManager] = useState<React.ReactElement | null>(null)

  useEffect(() => {
    if (!view) return

    const managerElement = <CompletionManager view={view} config={config} />

    setManager(managerElement)

    return () => {
      setManager(null)
    }
  }, [view, config])

  return {
    manager
  }
}
