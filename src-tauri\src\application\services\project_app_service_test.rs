#[cfg(test)]
mod tests {
    use super::*;
    use crate::domain::entities::{Project, ProjectStatus, Area, AreaStatus};
    use crate::domain::repositories::{ProjectRepository, TaskRepository, AreaRepository};
    use crate::domain::services::ProjectDomainService;
    use crate::shared::errors::{AppError, Result};
    use crate::shared::types::{Id, QueryParams, Priority, EntityStatus};
    use async_trait::async_trait;
    use std::collections::HashMap;
    use std::sync::{Arc, Mutex};

    // Mock Project Repository
    #[derive(Default)]
    struct MockProjectRepository {
        projects: Arc<Mutex<HashMap<Id, Project>>>,
        names: Arc<Mutex<HashMap<String, Id>>>,
    }

    impl MockProjectRepository {
        fn new() -> Self {
            Self::default()
        }

        fn add_project(&self, project: Project) {
            let mut projects = self.projects.lock().unwrap();
            let mut names = self.names.lock().unwrap();
            names.insert(project.name.clone(), project.id.clone());
            projects.insert(project.id.clone(), project);
        }
    }

    #[async_trait]
    impl ProjectRepository for MockProjectRepository {
        async fn find_by_id(&self, id: &Id) -> Result<Option<Project>> {
            let projects = self.projects.lock().unwrap();
            Ok(projects.get(id).cloned())
        }

        async fn find_by_name(&self, name: &str) -> Result<Option<Project>> {
            let names = self.names.lock().unwrap();
            let projects = self.projects.lock().unwrap();
            
            if let Some(id) = names.get(name) {
                Ok(projects.get(id).cloned())
            } else {
                Ok(None)
            }
        }

        async fn save(&self, project: &Project) -> Result<()> {
            self.add_project(project.clone());
            Ok(())
        }

        async fn update(&self, project: &Project) -> Result<()> {
            let mut projects = self.projects.lock().unwrap();
            if projects.contains_key(&project.id) {
                projects.insert(project.id.clone(), project.clone());
                Ok(())
            } else {
                Err(AppError::NotFound("Project not found".to_string()))
            }
        }

        async fn delete(&self, id: &Id) -> Result<()> {
            let mut projects = self.projects.lock().unwrap();
            if let Some(mut project) = projects.get(id).cloned() {
                project.entity_status = EntityStatus::Deleted;
                projects.insert(id.clone(), project);
                Ok(())
            } else {
                Err(AppError::NotFound("Project not found".to_string()))
            }
        }

        async fn find_all_active(&self) -> Result<Vec<Project>> {
            let projects = self.projects.lock().unwrap();
            Ok(projects.values()
                .filter(|p| p.entity_status == EntityStatus::Active)
                .cloned()
                .collect())
        }

        async fn find_by_area_id(&self, area_id: &Id) -> Result<Vec<Project>> {
            let projects = self.projects.lock().unwrap();
            Ok(projects.values()
                .filter(|p| p.area_id.as_ref() == Some(area_id) && p.entity_status != EntityStatus::Deleted)
                .cloned()
                .collect())
        }

        async fn find_by_status(&self, status: &ProjectStatus) -> Result<Vec<Project>> {
            let projects = self.projects.lock().unwrap();
            Ok(projects.values()
                .filter(|p| p.status == *status && p.entity_status != EntityStatus::Deleted)
                .cloned()
                .collect())
        }

        async fn find_by_priority(&self, priority: &Priority) -> Result<Vec<Project>> {
            let projects = self.projects.lock().unwrap();
            Ok(projects.values()
                .filter(|p| p.priority == *priority && p.entity_status != EntityStatus::Deleted)
                .cloned()
                .collect())
        }

        async fn find_due_soon(&self, days: u32) -> Result<Vec<Project>> {
            let cutoff = chrono::Utc::now() + chrono::Duration::days(days as i64);
            let projects = self.projects.lock().unwrap();
            Ok(projects.values()
                .filter(|p| {
                    if let Some(due_date) = p.due_date {
                        due_date <= cutoff && p.status != ProjectStatus::Completed && p.status != ProjectStatus::Cancelled
                    } else {
                        false
                    }
                })
                .cloned()
                .collect())
        }

        async fn find_overdue(&self) -> Result<Vec<Project>> {
            let now = chrono::Utc::now();
            let projects = self.projects.lock().unwrap();
            Ok(projects.values()
                .filter(|p| {
                    if let Some(due_date) = p.due_date {
                        due_date < now && p.status != ProjectStatus::Completed && p.status != ProjectStatus::Cancelled
                    } else {
                        false
                    }
                })
                .cloned()
                .collect())
        }

        async fn find_with_pagination(&self, _params: &QueryParams) -> Result<(Vec<Project>, u64)> {
            let projects = self.find_all_active().await?;
            let total = projects.len() as u64;
            Ok((projects, total))
        }

        async fn count_active_projects(&self) -> Result<u64> {
            let projects = self.find_all_active().await?;
            Ok(projects.len() as u64)
        }

        async fn find_by_progress_range(&self, min_progress: f32, max_progress: f32) -> Result<Vec<Project>> {
            let projects = self.projects.lock().unwrap();
            Ok(projects.values()
                .filter(|p| p.progress >= min_progress && p.progress <= max_progress && p.entity_status != EntityStatus::Deleted)
                .cloned()
                .collect())
        }

        async fn search(&self, query: &str) -> Result<Vec<Project>> {
            let projects = self.projects.lock().unwrap();
            Ok(projects.values()
                .filter(|p| {
                    (p.name.contains(query) || 
                     p.description.as_ref().map_or(false, |d| d.contains(query))) &&
                    p.entity_status != EntityStatus::Deleted
                })
                .cloned()
                .collect())
        }

        async fn get_statistics(&self) -> Result<crate::domain::repositories::ProjectStatistics> {
            let projects = self.projects.lock().unwrap();
            let total_projects = projects.len() as u64;
            let active_projects = projects.values().filter(|p| p.entity_status == EntityStatus::Active).count() as u64;
            let completed_projects = projects.values().filter(|p| p.status == ProjectStatus::Completed).count() as u64;
            let overdue_projects = 0; // Simplified
            let average_progress = 0.5; // Simplified

            Ok(crate::domain::repositories::ProjectStatistics {
                total_projects,
                active_projects,
                completed_projects,
                overdue_projects,
                average_progress,
                projects_by_status: vec![],
                projects_by_priority: vec![],
            })
        }
    }

    // Mock Task Repository
    #[derive(Default)]
    struct MockTaskRepository;

    #[async_trait]
    impl TaskRepository for MockTaskRepository {
        async fn find_by_id(&self, _id: &Id) -> Result<Option<crate::domain::entities::Task>> { Ok(None) }
        async fn find_by_title(&self, _title: &str) -> Result<Option<crate::domain::entities::Task>> { Ok(None) }
        async fn save(&self, _task: &crate::domain::entities::Task) -> Result<()> { Ok(()) }
        async fn update(&self, _task: &crate::domain::entities::Task) -> Result<()> { Ok(()) }
        async fn delete(&self, _id: &Id) -> Result<()> { Ok(()) }
        async fn find_all_active(&self) -> Result<Vec<crate::domain::entities::Task>> { Ok(vec![]) }
        async fn find_by_project_id(&self, _project_id: &Id) -> Result<Vec<crate::domain::entities::Task>> { Ok(vec![]) }
        async fn find_by_parent_task_id(&self, _parent_task_id: &Id) -> Result<Vec<crate::domain::entities::Task>> { Ok(vec![]) }
        async fn find_by_status(&self, _status: &crate::domain::entities::TaskStatus) -> Result<Vec<crate::domain::entities::Task>> { Ok(vec![]) }
        async fn find_by_priority(&self, _priority: &Priority) -> Result<Vec<crate::domain::entities::Task>> { Ok(vec![]) }
        async fn find_due_soon(&self, _days: u32) -> Result<Vec<crate::domain::entities::Task>> { Ok(vec![]) }
        async fn find_overdue(&self) -> Result<Vec<crate::domain::entities::Task>> { Ok(vec![]) }
        async fn find_today_tasks(&self) -> Result<Vec<crate::domain::entities::Task>> { Ok(vec![]) }
        async fn find_with_pagination(&self, _params: &QueryParams) -> Result<(Vec<crate::domain::entities::Task>, u64)> { Ok((vec![], 0)) }
        async fn count_active_tasks(&self) -> Result<u64> { Ok(0) }
        async fn find_by_progress_range(&self, _min_progress: f32, _max_progress: f32) -> Result<Vec<crate::domain::entities::Task>> { Ok(vec![]) }
        async fn search(&self, _query: &str) -> Result<Vec<crate::domain::entities::Task>> { Ok(vec![]) }
        async fn get_statistics(&self) -> Result<crate::domain::repositories::TaskStatistics> {
            Ok(crate::domain::repositories::TaskStatistics {
                total_tasks: 0,
                active_tasks: 0,
                completed_tasks: 0,
                overdue_tasks: 0,
                today_tasks: 0,
                average_progress: 0.0,
                tasks_by_status: vec![],
                tasks_by_priority: vec![],
                completion_rate: 0.0,
            })
        }
        async fn get_project_completion_rate(&self, _project_id: &Id) -> Result<f32> { Ok(0.5) }
        async fn find_root_tasks(&self) -> Result<Vec<crate::domain::entities::Task>> { Ok(vec![]) }
        async fn find_task_tree(&self, _root_task_id: &Id) -> Result<Vec<crate::domain::entities::Task>> { Ok(vec![]) }
    }

    // Mock Area Repository
    #[derive(Default)]
    struct MockAreaRepository {
        areas: Arc<Mutex<HashMap<Id, Area>>>,
    }

    impl MockAreaRepository {
        fn new() -> Self {
            Self::default()
        }

        fn add_area(&self, area: Area) {
            let mut areas = self.areas.lock().unwrap();
            areas.insert(area.id.clone(), area);
        }
    }

    #[async_trait]
    impl AreaRepository for MockAreaRepository {
        async fn find_by_id(&self, id: &Id) -> Result<Option<Area>> {
            let areas = self.areas.lock().unwrap();
            Ok(areas.get(id).cloned())
        }

        async fn find_by_name(&self, _name: &str) -> Result<Option<Area>> { Ok(None) }
        async fn save(&self, area: &Area) -> Result<()> { self.add_area(area.clone()); Ok(()) }
        async fn update(&self, _area: &Area) -> Result<()> { Ok(()) }
        async fn delete(&self, _id: &Id) -> Result<()> { Ok(()) }
        async fn find_all_active(&self) -> Result<Vec<Area>> { Ok(vec![]) }
        async fn find_by_status(&self, _status: &AreaStatus) -> Result<Vec<Area>> { Ok(vec![]) }
        async fn find_with_pagination(&self, _params: &QueryParams) -> Result<(Vec<Area>, u64)> { Ok((vec![], 0)) }
        async fn count_active_areas(&self) -> Result<u64> { Ok(0) }
        async fn search(&self, _query: &str) -> Result<Vec<Area>> { Ok(vec![]) }
        async fn name_exists(&self, _name: &str) -> Result<bool> { Ok(false) }
        async fn get_statistics(&self) -> Result<crate::domain::repositories::AreaStatistics> {
            Ok(crate::domain::repositories::AreaStatistics {
                total_areas: 0,
                active_areas: 0,
                areas_by_status: vec![],
                areas_with_projects: 0,
                areas_with_habits: 0,
                average_projects_per_area: 0.0,
                average_habits_per_area: 0.0,
            })
        }
        async fn get_project_count(&self, _area_id: &Id) -> Result<u64> { Ok(0) }
        async fn get_habit_count(&self, _area_id: &Id) -> Result<u64> { Ok(0) }
    }

    fn create_project_app_service() -> ProjectAppService {
        let project_repository = Arc::new(MockProjectRepository::new());
        let task_repository = Arc::new(MockTaskRepository::default());
        let area_repository = Arc::new(MockAreaRepository::new());
        let domain_service = ProjectDomainService::new();
        
        ProjectAppService::new(project_repository, task_repository, area_repository, domain_service)
    }

    #[tokio::test]
    async fn test_create_project_success() {
        let service = create_project_app_service();
        
        let result = service.create_project(
            "Test Project".to_string(),
            Some("A test project".to_string()),
            None,
            Some(Priority::High),
        ).await;

        assert!(result.is_ok());
        let project = result.unwrap();
        assert_eq!(project.name, "Test Project");
        assert_eq!(project.description, Some("A test project".to_string()));
        assert_eq!(project.priority, Priority::High);
        assert_eq!(project.status, ProjectStatus::NotStarted);
    }

    #[tokio::test]
    async fn test_create_project_with_area() {
        let service = create_project_app_service();
        
        // First create an area
        let area = Area::new("Test Area".to_string(), Some("Test area description".to_string()));
        let area_id = area.id.clone();
        service.area_repository.save(&area).await.unwrap();

        let result = service.create_project(
            "Test Project".to_string(),
            Some("A test project".to_string()),
            Some(area_id.clone()),
            None,
        ).await;

        assert!(result.is_ok());
        let project = result.unwrap();
        assert_eq!(project.area_id, Some(area_id));
    }

    #[tokio::test]
    async fn test_update_project_status() {
        let service = create_project_app_service();
        
        let project = service.create_project(
            "Test Project".to_string(),
            None,
            None,
            None,
        ).await.unwrap();

        let result = service.update_project_status(&project.id, ProjectStatus::InProgress).await;
        assert!(result.is_ok());
        
        let updated_project = result.unwrap();
        assert_eq!(updated_project.status, ProjectStatus::InProgress);
    }

    #[tokio::test]
    async fn test_update_project_progress() {
        let service = create_project_app_service();
        
        let project = service.create_project(
            "Test Project".to_string(),
            None,
            None,
            None,
        ).await.unwrap();

        let result = service.update_project_progress(&project.id, 0.75).await;
        assert!(result.is_ok());
        
        let updated_project = result.unwrap();
        assert_eq!(updated_project.progress, 0.75);
    }

    #[tokio::test]
    async fn test_complete_project() {
        let service = create_project_app_service();
        
        let project = service.create_project(
            "Test Project".to_string(),
            None,
            None,
            None,
        ).await.unwrap();

        let result = service.complete_project(&project.id).await;
        assert!(result.is_ok());
        
        let completed_project = result.unwrap();
        assert_eq!(completed_project.status, ProjectStatus::Completed);
        assert_eq!(completed_project.progress, 1.0);
    }

    #[tokio::test]
    async fn test_cancel_project() {
        let service = create_project_app_service();
        
        let project = service.create_project(
            "Test Project".to_string(),
            None,
            None,
            None,
        ).await.unwrap();

        let result = service.cancel_project(&project.id).await;
        assert!(result.is_ok());
        
        let cancelled_project = result.unwrap();
        assert_eq!(cancelled_project.status, ProjectStatus::Cancelled);
    }

    #[tokio::test]
    async fn test_cannot_cancel_completed_project() {
        let service = create_project_app_service();
        
        let project = service.create_project(
            "Test Project".to_string(),
            None,
            None,
            None,
        ).await.unwrap();

        // Complete the project first
        let _completed = service.complete_project(&project.id).await.unwrap();

        // Try to cancel completed project
        let result = service.cancel_project(&project.id).await;
        assert!(result.is_err());
        match result.unwrap_err() {
            AppError::ValidationError(msg) => assert_eq!(msg, "Cannot cancel completed project"),
            _ => panic!("Expected ValidationError"),
        }
    }

    #[tokio::test]
    async fn test_clone_project() {
        let service = create_project_app_service();
        
        let original_project = service.create_project(
            "Original Project".to_string(),
            Some("Original description".to_string()),
            None,
            Some(Priority::High),
        ).await.unwrap();

        let result = service.clone_project(&original_project.id, "Cloned Project".to_string()).await;
        assert!(result.is_ok());
        
        let cloned_project = result.unwrap();
        assert_eq!(cloned_project.name, "Cloned Project");
        assert_eq!(cloned_project.description, Some("Original description".to_string()));
        assert_eq!(cloned_project.priority, Priority::High);
        assert_ne!(cloned_project.id, original_project.id);
    }
}
