import { Link } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { Progress } from '../ui/progress'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { cn } from '../../lib/utils'
import { useLanguage } from '../../contexts/LanguageContext'
import type { Area } from '../../../../shared/types'

interface AreaCardProps {
  area: Area
  onEdit?: (area: Area) => void
  onDelete?: (area: Area) => void
  onArchive?: (area: Area) => void
  className?: string
  relatedProjectsCount?: number
  habitCompletionRate?: number
}

export function AreaCard({
  area,
  onEdit,
  onDelete,
  onArchive,
  className,
  relatedProjectsCount = 0,
  habitCompletionRate = 0
}: AreaCardProps) {
  const { t } = useLanguage()

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'Needs Attention':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'On Hold':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'Review Required':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getCompletionColor = (rate: number) => {
    if (rate >= 80) return 'text-green-600'
    if (rate >= 60) return 'text-yellow-600'
    if (rate >= 40) return 'text-orange-600'
    return 'text-red-600'
  }

  return (
    <Card
      className={cn(
        'group hover:shadow-md transition-all duration-200 border-l-4 border-area',
        area.archived && 'opacity-75',
        className
      )}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <CardTitle className="text-lg truncate">
                <Link to={`/areas/${area.id}`} className="hover:text-primary transition-colors">
                  {area.name}
                </Link>
              </CardTitle>
              <Badge
                variant="outline"
                className={cn('text-xs', getStatusColor(area.status || 'Active'))}
              >
                {t(
                  `pages.areas.filters.status.${(area.status || 'Active').toLowerCase().replace(' ', '')}`
                ) ||
                  area.status ||
                  'Active'}
              </Badge>
            </div>
            {area.description && (
              <CardDescription className="line-clamp-2">{area.description}</CardDescription>
            )}
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 5v.01M12 12v.01M12 19v.01"
                  />
                </svg>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit?.(area)}>
                {t('pages.areas.card.editArea')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onArchive?.(area)}>
                {t('pages.areas.card.archiveArea')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete?.(area)}
                className="text-red-600 focus:text-red-600"
              >
                {t('pages.areas.card.deleteArea')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Standard */}
        {area.standard && (
          <div className="space-y-1">
            <div className="text-xs font-medium text-muted-foreground">
              {t('pages.areas.card.standard')}
            </div>
            <p className="text-sm line-clamp-2">{area.standard}</p>
          </div>
        )}

        {/* Habit Completion Rate */}
        {habitCompletionRate > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">{t('pages.areas.card.habitCompletion')}</span>
              <span className={cn('font-medium', getCompletionColor(habitCompletionRate))}>
                {habitCompletionRate}%
              </span>
            </div>
            <Progress value={habitCompletionRate} className="h-2" />
          </div>
        )}

        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-4 text-center">
          <div className="space-y-1">
            <div className="text-lg font-bold text-blue-600">{relatedProjectsCount}</div>
            <div className="text-xs text-muted-foreground">
              {t('pages.areas.card.relatedProjects')}
            </div>
          </div>
          <div className="space-y-1">
            <div className="text-lg font-bold text-green-600">
              {(area as any).habits?.length || 0}
            </div>
            <div className="text-xs text-muted-foreground">
              {t('pages.areas.card.activeHabits')}
            </div>
          </div>
        </div>

        {/* Review Information */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div>
            {area.reviewFrequency && (
              <span>
                {t('pages.areas.card.review')}:{' '}
                {t(
                  `pages.areas.dialog.reviewFrequencyOptions.${area.reviewFrequency.toLowerCase()}`
                ) || area.reviewFrequency}
              </span>
            )}
          </div>
          <div>
            {t('pages.areas.card.updated')} {new Date(area.updatedAt).toLocaleDateString()}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center gap-2">
            {(area as any).habits && (area as any).habits.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {(area as any).habits.length} {t('pages.areas.card.habits')}
              </Badge>
            )}
          </div>
          <Button asChild variant="ghost" size="sm" className="h-7 px-2 text-xs">
            <Link to={`/areas/${area.id}`}>{t('pages.areas.card.viewDetails')}</Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default AreaCard
