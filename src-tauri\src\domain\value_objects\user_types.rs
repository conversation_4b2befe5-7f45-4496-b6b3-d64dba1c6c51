// User Value Objects - 用户相关值对象

use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub struct Username {
    pub value: String,
}

impl Username {
    pub fn new(username: String) -> Result<Self, String> {
        if username.len() >= 3 && username.len() <= 50 {
            Ok(Self { value: username })
        } else {
            Err("Username must be between 3 and 50 characters".to_string())
        }
    }
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct DisplayName {
    pub value: String,
}

impl DisplayName {
    pub fn new(name: String) -> Result<Self, String> {
        if !name.trim().is_empty() && name.len() <= 100 {
            Ok(Self { value: name })
        } else {
            Err("Display name cannot be empty and must be less than 100 characters".to_string())
        }
    }
}
