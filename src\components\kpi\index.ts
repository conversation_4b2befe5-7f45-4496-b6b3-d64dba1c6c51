/**
 * KPI组件库入口文件
 * 导出所有KPI相关的组件、类型和工具函数
 */

// 主要组件
export { default as KPITracker } from './KPITracker'
export { default as KPIChart } from './KPIChart'
export { default as KPIInput } from './KPIInput'
export { default as KPIDialog } from './KPIDialog'
export { default as KPIStatisticsCard } from './KPIStatisticsCard'

// 数据源适配器
export { 
  ProjectKPIDataSource, 
  AreaMetricDataSource, 
  createKPIDataSource 
} from './adapters'

// 类型定义
export type {
  // 基础数据类型
  BaseKPI,
  KPIRecord,
  KPIProgress,
  KPIStatistics,
  KPIDirection,
  KPIFrequency,
  KPIStatus,
  KPIType,
  
  // 配置类型
  KPIComponentConfig,
  KPITemplate,
  ValidationRules,
  KPIConfig,
  
  // 事件类型
  KPIEvent,
  KPIEventType,
  KPIEventHandlers,
  
  // API接口类型
  KPIDataSource,
  QueryOptions,
  CreateKPIData,
  CreateRecordData,
  
  // 组件Props类型
  KPITrackerProps,
  KPIChartProps,
  KPIInputProps,
  KPIDialogProps,
  
  // 工具类型
  DeepPartial,
  RequireFields,
  StatusColorMap
} from './types'

// 常量
export { DEFAULT_KPI_CONFIG } from './types'

// 工具函数
export {
  calculateKPIProgress,
  getKPIStatus,
  getStatusColor,
  formatKPIValue,
  validateKPIData
} from './utils'

// 预设配置
export {
  PROJECT_KPI_TEMPLATES,
  AREA_METRIC_TEMPLATES,
  DEFAULT_UNITS,
  FREQUENCY_OPTIONS,
  DIRECTION_OPTIONS
} from './presets'
