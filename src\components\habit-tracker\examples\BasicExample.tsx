/**
 * 习惯追踪基础使用示例
 * 展示如何在应用中集成习惯追踪组件
 */

import { createSignal, onMount } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card'
import { But<PERSON> } from '../../ui/button'
import { Badge } from '../../ui/badge'
import { 
  Target, 
  Settings, 
  RefreshCw,
  TrendingUp
} from 'lucide-solid'

// 导入习惯追踪组件
import { 
  HabitTracker,
  createHabitDataSource,
  type HabitEventHandlers,
  type HabitTrackerConfig
} from '../index'

interface BasicExampleProps {
  areaId: string
  areaName?: string
  className?: string
}

export function BasicExample(props: BasicExampleProps) {
  // 状态管理
  const [refreshKey, setRefreshKey] = createSignal(0)
  const [showSettings, setShowSettings] = createSignal(false)

  // 创建数据源
  const dataSource = createHabitDataSource()

  // 组件配置
  const config: HabitTrackerConfig = {
    // 显示选项
    showStatistics: true,
    showProgress: true,
    showCalendar: true,
    showChart: true,
    showStreak: true,
    
    // 交互选项
    allowCreate: true,
    allowEdit: true,
    allowDelete: true,
    allowBulkOperations: false,
    
    // 布局选项
    layout: 'grid',
    calendarView: 'month',
    chartType: 'line',
    
    // 功能选项
    enableReminders: true,
    enableNotes: true,
    enableStreaks: true,
    enableGoals: true,
    
    // 样式选项
    compactMode: false,
    showColors: true,
    animateProgress: true,
    
    // 数据选项
    maxRecords: 365,
    defaultView: 'today',
    
    // 主题
    theme: 'auto'
  }

  // 事件处理器
  const eventHandlers: HabitEventHandlers = {
    onCreate: (habit) => {
      console.log('Habit created:', habit)
      // 可以在这里添加通知、分析等
      showNotification(`Habit "${habit.name}" created successfully!`)
    },
    
    onUpdate: (habit, changes) => {
      console.log('Habit updated:', habit, changes)
      showNotification(`Habit "${habit.name}" updated`)
    },
    
    onDelete: (habitId) => {
      console.log('Habit deleted:', habitId)
      showNotification('Habit deleted successfully')
    },
    
    onRecord: (record) => {
      console.log('Record created:', record)
      if (record.completed) {
        showNotification('Great job! Keep up the good work! 🎉')
      }
    },
    
    onStreakAchieved: (habitId, streak) => {
      console.log('Streak achieved:', habitId, streak)
      showNotification(`🔥 Amazing! You've reached a ${streak}-day streak!`)
    },
    
    onGoalReached: (habitId, progress) => {
      console.log('Goal reached:', habitId, progress)
      showNotification('🎯 Goal reached! You\'re doing fantastic!')
    },
    
    onReminder: (habitId, reminderData) => {
      console.log('Reminder:', habitId, reminderData)
      // 可以在这里实现提醒逻辑
    },
    
    onError: (error, context) => {
      console.error('Habit tracker error:', error, context)
      showNotification(`Error: ${error.message}`, 'error')
    }
  }

  // 显示通知（简单实现）
  const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
    // 这里可以集成实际的通知系统
    console.log(`[${type.toUpperCase()}] ${message}`)
    
    // 简单的浏览器通知
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('Habit Tracker', {
        body: message,
        icon: type === 'success' ? '✅' : '❌'
      })
    }
  }

  // 请求通知权限
  const requestNotificationPermission = async () => {
    if ('Notification' in window && Notification.permission === 'default') {
      await Notification.requestPermission()
    }
  }

  // 手动刷新
  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1)
  }

  // 初始化
  onMount(() => {
    requestNotificationPermission()
  })

  return (
    <div class="space-y-6">
      {/* 页面头部 */}
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <div>
              <CardTitle class="flex items-center gap-2">
                <Target class="h-5 w-5" />
                Habit Tracker
                <Badge variant="outline">
                  {props.areaName || `Area ${props.areaId}`}
                </Badge>
              </CardTitle>
              <CardDescription>
                Track your daily habits and build lasting routines
              </CardDescription>
            </div>
            <div class="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
              >
                <RefreshCw class="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(!showSettings())}
              >
                <Settings class="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 设置面板 */}
      {showSettings() && (
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Settings class="h-5 w-5" />
              Settings
            </CardTitle>
            <CardDescription>
              Customize your habit tracking experience
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="space-y-2">
                <h4 class="font-medium">Display Options</h4>
                <div class="space-y-1 text-sm text-muted-foreground">
                  <div>✓ Statistics Dashboard</div>
                  <div>✓ Progress Charts</div>
                  <div>✓ Calendar View</div>
                  <div>✓ Streak Tracking</div>
                </div>
              </div>
              
              <div class="space-y-2">
                <h4 class="font-medium">Features</h4>
                <div class="space-y-1 text-sm text-muted-foreground">
                  <div>✓ Create & Edit Habits</div>
                  <div>✓ Daily Recording</div>
                  <div>✓ Goal Setting</div>
                  <div>✓ Reminders</div>
                </div>
              </div>
              
              <div class="space-y-2">
                <h4 class="font-medium">Layout</h4>
                <div class="space-y-1 text-sm text-muted-foreground">
                  <div>• Grid View</div>
                  <div>• Monthly Calendar</div>
                  <div>• Line Charts</div>
                  <div>• Auto Theme</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 主要的习惯追踪组件 */}
      <HabitTracker
        key={refreshKey()} // 用于强制刷新
        areaId={props.areaId}
        dataSource={dataSource}
        config={config}
        eventHandlers={eventHandlers}
        className={props.className}
      />

      {/* 使用提示 */}
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <TrendingUp class="h-5 w-5" />
            Getting Started
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-3">
              <h4 class="font-medium">Quick Tips</h4>
              <ul class="space-y-2 text-sm text-muted-foreground">
                <li>• Start with 2-3 simple habits</li>
                <li>• Set realistic daily targets</li>
                <li>• Track consistently for best results</li>
                <li>• Celebrate small wins and streaks</li>
                <li>• Review progress weekly</li>
              </ul>
            </div>
            
            <div class="space-y-3">
              <h4 class="font-medium">Habit Ideas</h4>
              <ul class="space-y-2 text-sm text-muted-foreground">
                <li>• Drink 8 glasses of water</li>
                <li>• Exercise for 30 minutes</li>
                <li>• Read 10 pages</li>
                <li>• Meditate for 5 minutes</li>
                <li>• Write in journal</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default BasicExample
