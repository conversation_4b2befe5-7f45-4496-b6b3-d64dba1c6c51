// ProjectDetailModal - 项目详情模态窗组件
// 在浏览器中以模态窗形式展示项目详情

import { createSignal, createEffect, Show, For, onMount } from 'solid-js';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import { But<PERSON> } from '../ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import { cn } from '@/lib/utils';
import { Project, Task } from '../../types/business';
import { getProjectById, getTasksByProject } from '../../services/apiFactory';
import DetailHeader from './detail/Header';
import OverviewSection from './detail/OverviewSection';
import TasksSection from './detail/TasksSection';
import KPISection from './detail/KPISection';
import ResourcesSection from './detail/ResourcesSection';


interface ProjectDetailModalProps {
  projectId: string | null;
  isOpen: boolean;
  onClose: () => void;
}

export function ProjectDetailModal(props: ProjectDetailModalProps) {
  const [project, setProject] = createSignal<Project | null>(null);
  const [tasks, setTasks] = createSignal<Task[]>([]);
  const [loading, setLoading] = createSignal(false);
  const [error, setError] = createSignal<string | null>(null);
  const [activeTab, setActiveTab] = createSignal('overview');

  // 加载项目数据
  const loadProjectData = async () => {
    if (!props.projectId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const [projectData, tasksData] = await Promise.all([
        getProjectById(props.projectId),
        getTasksByProject(props.projectId)
      ]);
      
      setProject(projectData);
      setTasks(tasksData);
    } catch (err) {
      console.error('Failed to load project data:', err);
      setError('加载项目数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 当模态窗打开且有项目ID时加载数据
  createEffect(() => {
    if (props.isOpen && props.projectId) {
      loadProjectData();
    }
  });

  // 状态显示辅助函数
  const getStatusLabel = (status: string) => {
    const statusMap: Record<string, string> = {
      'not_started': '未开始',
      'in_progress': '进行中',
      'completed': '已完成',
      'on_hold': '暂停',
      'at_risk': '有风险',
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'not_started': 'bg-gray-100 text-gray-700',
      'in_progress': 'bg-blue-100 text-blue-700',
      'completed': 'bg-green-100 text-green-700',
      'on_hold': 'bg-yellow-100 text-yellow-700',
      'at_risk': 'bg-red-100 text-red-700',
    };
    return colorMap[status] || 'bg-gray-100 text-gray-700';
  };

  const getPriorityLabel = (priority: string) => {
    const priorityMap: Record<string, string> = {
      'low': '低',
      'medium': '中',
      'high': '高',
      'critical': '紧急',
    };
    return priorityMap[priority] || priority;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const calculateRemainingDays = (dueDate?: string) => {
    if (!dueDate) return null;
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const remainingDays = () => {
    const proj = project();
    return proj ? calculateRemainingDays(proj.dueDate) : null;
  };

  // 标签页配置
  const tabs = [
    { id: 'overview', label: '项目概览', icon: '📊' },
    { id: 'tasks', label: '任务管理', icon: '✅' },
    { id: 'kpi', label: 'KPI指标', icon: '📈' },
    { id: 'resources', label: '资源关联', icon: '📎' },
  ];


  return (
    <Dialog open={props.isOpen} onOpenChange={(open) => { if (!open) props.onClose(); }}>
      <DialogContent class={cn('max-w-6xl w-[min(100vw-2rem,72rem)] max-h-[90vh] p-0 overflow-hidden')}>
        <DetailHeader
          project={project()}
          onClose={props.onClose}
          getStatusLabel={getStatusLabel}
          getStatusColor={getStatusColor}
        />

        {/* 模态窗主体内容 */}
        <div class="flex-1 overflow-hidden">
            <Show when={loading()}>
              <div class="flex items-center justify-center h-64">
                <div class="text-center">
                  <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p class="text-muted-foreground">加载中...</p>
                </div>
              </div>
            </Show>

            <Show when={error()}>
              <div class="p-6">
                <Card>
                  <CardContent class="p-6">
                    <div class="text-center text-red-600">
                      <p>{error()}</p>
                      <Button class="mt-4" onClick={loadProjectData}>重试</Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </Show>

            <Show when={!loading() && !error() && project()}>
              <div class="flex flex-col h-full">
                {/* 项目基本信息 */}
                <div class="p-6 border-b border-border">
                  <div class="space-y-4">
                    <div>
                      <h3 class="text-2xl font-bold">{project()?.name}</h3>
                      <Show when={project()?.description}>
                        <p class="text-muted-foreground mt-2">{project()?.description}</p>
                      </Show>
                    </div>

                    {/* 进度条 */}
                    <div class="space-y-2">
                      <div class="flex justify-between text-sm">
                        <span class="text-muted-foreground">项目进度</span>
                        <span class="font-medium">{Math.round((project()?.progress || 0) * 100)}%</span>
                      </div>
                      <div class="w-full bg-muted rounded-full h-2 relative overflow-hidden">
                        <div
                          class="bg-primary h-full rounded-full transition-all duration-300 absolute top-0 left-0"
                          classList={{
                            'w-0': (project()?.progress || 0) === 0,
                            'w-1/4': (project()?.progress || 0) > 0 && (project()?.progress || 0) <= 0.25,
                            'w-2/4': (project()?.progress || 0) > 0.25 && (project()?.progress || 0) <= 0.5,
                            'w-3/4': (project()?.progress || 0) > 0.5 && (project()?.progress || 0) <= 0.75,
                            'w-full': (project()?.progress || 0) > 0.75,
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* 标签页导航 */}
                <div class="border-b border-border px-6">
                  <nav class="flex space-x-8">
                    <For each={tabs}>
                      {(tab) => (
                        <button
                          type="button"
                          onClick={() => setActiveTab(tab.id)}
                          class={cn(
                            'flex items-center space-x-2 py-3 px-1 border-b-2 font-medium text-sm transition-colors',
                            activeTab() === tab.id
                              ? 'border-primary text-primary'
                              : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
                          )}
                        >
                          <span>{tab.icon}</span>
                          <span>{tab.label}</span>
                        </button>
                      )}
                    </For>
                  </nav>
                </div>

                {/* 标签页内容 */}
                <div class="flex-1 overflow-y-auto p-6">
                  <Show when={activeTab() === 'overview'}>
                    <OverviewSection project={project()!} />
                  </Show>

                  <Show when={activeTab() === 'tasks'}>
                    <TasksSection tasks={tasks()} />
                  </Show>

                  <Show when={activeTab() === 'kpi'}>
                    <KPISection projectId={project()!.id} />
                  </Show>

                  <Show when={activeTab() === 'resources'}>
                    <ResourcesSection projectId={project()!.id} />
                  </Show>
                </div>
              </div>
            </Show>
          </div>
      </DialogContent>
    </Dialog>
  );
}
