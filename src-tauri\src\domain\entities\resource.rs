// Resource Entity - 资源实体

use crate::shared::types::{Id, Metadata, EntityStatus, Tag};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Resource {
    pub id: Id,
    pub title: String,
    pub content: String,
    pub resource_type: ResourceType,
    pub file_path: Option<String>,
    pub tags: Vec<Tag>,
    pub links: Vec<String>,
    pub entity_status: EntityStatus,
    pub metadata: Metadata,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ResourceType {
    Markdown,
    Note,
    Link,
    File,
}

impl Resource {
    pub fn new(title: String, content: String, resource_type: ResourceType) -> Self {
        Self {
            id: crate::shared::utils::IdGenerator::new_id_with_prefix("res"),
            title,
            content,
            resource_type,
            file_path: None,
            tags: Vec::new(),
            links: Vec::new(),
            entity_status: EntityStatus::Active,
            metadata: Metadata::default(),
        }
    }
}
