// Notification Domain Service - 通知领域服务

use crate::domain::entities::{Notification, NotificationType};
use crate::shared::errors::Result;
use crate::shared::types::Priority;

pub struct NotificationDomainService;

impl NotificationDomainService {
    pub fn new() -> Self {
        Self
    }

    pub fn validate_notification(&self, title: &str, message: &str) -> Result<()> {
        crate::shared::utils::validate_required(title, "notification title")?;
        crate::shared::utils::validate_length(title, "notification title", 1, 100)?;
        crate::shared::utils::validate_required(message, "notification message")?;
        crate::shared::utils::validate_length(message, "notification message", 1, 500)?;
        Ok(())
    }

    pub fn determine_priority(&self, notification_type: &NotificationType) -> Priority {
        match notification_type {
            NotificationType::TaskDue => Priority::High,
            NotificationType::ProjectDeadline => Priority::Critical,
            NotificationType::HabitReminder => Priority::Medium,
            NotificationType::ReviewReminder => Priority::Medium,
            NotificationType::System => Priority::Low,
        }
    }

    pub fn should_send_notification(&self, notification: &Notification) -> bool {
        // Business logic for determining if notification should be sent
        !notification.read && notification.entity_status == crate::shared::types::EntityStatus::Active
    }
}

impl Default for NotificationDomainService {
    fn default() -> Self {
        Self::new()
    }
}
