// Dashboard Page - 仪表盘页面
// 应用程序主页，显示概览信息和关键指标

import { JSX, createSignal, createEffect, Show, For } from 'solid-js';
import { PageContainer, ContentArea } from '../components/layout/Layout';
import { Card, CardHeader, CardTitle, CardContent, StatsCard, ProjectCard } from '../components/ui/Card';
import { Button } from '../components/ui/button';
import { cn } from '@/lib/utils';

// 仪表盘数据类型
interface DashboardData {
  overview: {
    totalProjects: number;
    activeProjects: number;
    completedTasks: number;
    todayTasks: number;
    inboxItems: number;
    habitStreak: number;
  };
  recentActivities: Array<{
    id: string;
    type: 'created' | 'updated' | 'completed';
    title: string;
    description?: string;
    timestamp: string;
  }>;
  upcomingTasks: Array<{
    id: string;
    title: string;
    dueDate: string;
    priority: string;
    projectName?: string;
  }>;
  projectProgress: Array<{
    id: string;
    name: string;
    progress: number;
    status: string;
    dueDate?: string;
    priority: string;
  }>;
  productivity: {
    score: number;
    trend: 'up' | 'down' | 'stable';
    change: number;
  };
}

export function Dashboard() {
  const [data, setData] = createSignal<DashboardData | null>(null);
  const [loading, setLoading] = createSignal(true);
  const [error, setError] = createSignal<string | null>(null);

  // 模拟数据加载
  createEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        // 这里将来会调用真实的API
        await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟加载时间
        
        const mockData: DashboardData = {
          overview: {
            totalProjects: 12,
            activeProjects: 8,
            completedTasks: 45,
            todayTasks: 7,
            inboxItems: 3,
            habitStreak: 15,
          },
          recentActivities: [
            {
              id: '1',
              type: 'completed',
              title: '完成项目设计文档',
              description: 'BubbleSay 项目',
              timestamp: '2024-01-15T10:30:00Z',
            },
            {
              id: '2',
              type: 'created',
              title: '创建新任务',
              description: '实现用户认证功能',
              timestamp: '2024-01-15T09:15:00Z',
            },
            {
              id: '3',
              type: 'updated',
              title: '更新项目进度',
              description: '前端开发进度达到 60%',
              timestamp: '2024-01-15T08:45:00Z',
            },
          ],
          upcomingTasks: [
            {
              id: '1',
              title: '完成API文档编写',
              dueDate: '2024-01-16T18:00:00Z',
              priority: 'high',
              projectName: 'BubbleSay',
            },
            {
              id: '2',
              title: '代码审查',
              dueDate: '2024-01-17T12:00:00Z',
              priority: 'medium',
              projectName: 'BubbleSay',
            },
            {
              id: '3',
              title: '用户测试',
              dueDate: '2024-01-18T15:00:00Z',
              priority: 'high',
            },
          ],
          projectProgress: [
            {
              id: '1',
              name: 'BubbleSay 开发',
              progress: 0.75,
              status: 'in_progress',
              dueDate: '2024-02-01T00:00:00Z',
              priority: 'high',
            },
            {
              id: '2',
              name: '个人网站重构',
              progress: 0.45,
              status: 'in_progress',
              dueDate: '2024-01-25T00:00:00Z',
              priority: 'medium',
            },
            {
              id: '3',
              name: '学习 Rust',
              progress: 0.30,
              status: 'in_progress',
              priority: 'low',
            },
          ],
          productivity: {
            score: 85,
            trend: 'up',
            change: 12,
          },
        };
        
        setData(mockData);
      } catch (err) {
        setError('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  });

  const formatRelativeTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return '刚刚';
    if (diffInHours < 24) return `${diffInHours}小时前`;
    return `${Math.floor(diffInHours / 24)}天前`;
  };

  const formatDueDate = (dueDate: string) => {
    const date = new Date(dueDate);
    const now = new Date();
    const diffInDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return '今天';
    if (diffInDays === 1) return '明天';
    if (diffInDays > 0) return `${diffInDays}天后`;
    return `逾期${Math.abs(diffInDays)}天`;
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'completed':
        return (
          <div class="h-2 w-2 rounded-full bg-success" />
        );
      case 'created':
        return (
          <div class="h-2 w-2 rounded-full bg-info" />
        );
      case 'updated':
        return (
          <div class="h-2 w-2 rounded-full bg-warning" />
        );
      default:
        return (
          <div class="h-2 w-2 rounded-full bg-muted-foreground" />
        );
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-destructive';
      case 'medium':
        return 'text-warning';
      case 'low':
        return 'text-muted-foreground';
      default:
        return 'text-muted-foreground';
    }
  };

  return (
    <PageContainer
      title="仪表盘"
      description="欢迎回来！这里是您的工作概览。"
      actions={
        <Button>
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          快速添加
        </Button>
      }
    >
      <Show when={loading()}>
        <div class="flex items-center justify-center py-12">
          <div class="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
        </div>
      </Show>

      <Show when={error()}>
        <div class="text-center py-12">
          <p class="text-destructive">{error()}</p>
          <Button variant="outline" class="mt-4" onClick={() => window.location.reload()}>
            重试
          </Button>
        </div>
      </Show>

      <Show when={data()}>
        <div class="space-y-6">
          {/* 概览统计卡片 */}
          <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
            <StatsCard
              title="总项目数"
              value={data()!.overview.totalProjects}
              icon={
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M8 6h10" />
                  <path d="M6 12h9" />
                  <path d="M11 18h7" />
                </svg>
              }
            />
            <StatsCard
              title="活跃项目"
              value={data()!.overview.activeProjects}
              variant="info"
              icon={
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="10" />
                  <polyline points="12,6 12,12 16,14" />
                </svg>
              }
            />
            <StatsCard
              title="已完成任务"
              value={data()!.overview.completedTasks}
              variant="success"
              trend={{ value: 15, direction: 'up', period: '本周' }}
              icon={
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M9 12l2 2 4-4" />
                  <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3" />
                  <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3" />
                </svg>
              }
            />
            <StatsCard
              title="今日任务"
              value={data()!.overview.todayTasks}
              variant="warning"
              icon={
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
                  <line x1="16" y1="2" x2="16" y2="6" />
                  <line x1="8" y1="2" x2="8" y2="6" />
                  <line x1="3" y1="10" x2="21" y2="10" />
                </svg>
              }
            />
            <StatsCard
              title="收件箱"
              value={data()!.overview.inboxItems}
              icon={
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="22,12 18,12 15,21 9,3 6,12 2,12" />
                </svg>
              }
            />
            <StatsCard
              title="习惯连击"
              value={`${data()!.overview.habitStreak}天`}
              variant="success"
              trend={{ value: 3, direction: 'up' }}
              icon={
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z" />
                </svg>
              }
            />
          </div>

          {/* 主要内容区域 */}
          <div class="grid gap-6 lg:grid-cols-3">
            {/* 左侧：项目进度和即将到期的任务 */}
            <div class="lg:col-span-2 space-y-6">
              {/* 项目进度 */}
              <Card>
                <CardHeader>
                  <CardTitle>项目进度</CardTitle>
                </CardHeader>
                <CardContent class="space-y-4">
                  <For each={data()!.projectProgress}>
                    {(project) => (
                      <ProjectCard
                        project={{
                          id: project.id,
                          name: project.name,
                          progress: project.progress,
                          status: project.status,
                          dueDate: project.dueDate,
                          priority: project.priority,
                        }}
                        onClick={(id) => console.log('Navigate to project:', id)}
                      />
                    )}
                  </For>
                </CardContent>
              </Card>

              {/* 即将到期的任务 */}
              <Card>
                <CardHeader>
                  <CardTitle>即将到期</CardTitle>
                </CardHeader>
                <CardContent>
                  <div class="space-y-3">
                    <For each={data()!.upcomingTasks}>
                      {(task) => (
                        <div class="flex items-center justify-between p-3 rounded-lg border border-border hover:bg-accent/50 transition-colors">
                          <div class="flex-1">
                            <div class="font-medium">{task.title}</div>
                            <Show when={task.projectName}>
                              <div class="text-sm text-muted-foreground">{task.projectName}</div>
                            </Show>
                          </div>
                          <div class="flex items-center space-x-2">
                            <span class={cn('text-sm font-medium', getPriorityColor(task.priority))}>
                              {task.priority}
                            </span>
                            <span class="text-sm text-muted-foreground">
                              {formatDueDate(task.dueDate)}
                            </span>
                          </div>
                        </div>
                      )}
                    </For>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 右侧：生产力指标和最近活动 */}
            <div class="space-y-6">
              {/* 生产力指标 */}
              <Card>
                <CardHeader>
                  <CardTitle>生产力指标</CardTitle>
                </CardHeader>
                <CardContent class="text-center">
                  <div class="text-4xl font-bold text-primary mb-2">
                    {data()!.productivity.score}
                  </div>
                  <div class="text-sm text-muted-foreground mb-4">生产力评分</div>
                  <div class={cn(
                    'inline-flex items-center text-sm font-medium',
                    data()!.productivity.trend === 'up' ? 'text-success' : 
                    data()!.productivity.trend === 'down' ? 'text-destructive' : 'text-muted-foreground'
                  )}>
                    <Show when={data()!.productivity.trend === 'up'}>
                      <svg class="mr-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 17l9.2-9.2M17 17V7H7" />
                      </svg>
                    </Show>
                    <Show when={data()!.productivity.trend === 'down'}>
                      <svg class="mr-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 7l-9.2 9.2M7 7v10h10" />
                      </svg>
                    </Show>
                    {data()!.productivity.change}% 相比上周
                  </div>
                </CardContent>
              </Card>

              {/* 最近活动 */}
              <Card>
                <CardHeader>
                  <CardTitle>最近活动</CardTitle>
                </CardHeader>
                <CardContent>
                  <div class="space-y-4">
                    <For each={data()!.recentActivities}>
                      {(activity) => (
                        <div class="flex items-start space-x-3">
                          <div class="mt-2">
                            {getActivityIcon(activity.type)}
                          </div>
                          <div class="flex-1 min-w-0">
                            <div class="text-sm font-medium">{activity.title}</div>
                            <Show when={activity.description}>
                              <div class="text-sm text-muted-foreground">{activity.description}</div>
                            </Show>
                            <div class="text-xs text-muted-foreground mt-1">
                              {formatRelativeTime(activity.timestamp)}
                            </div>
                          </div>
                        </div>
                      )}
                    </For>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </Show>
    </PageContainer>
  );
}
