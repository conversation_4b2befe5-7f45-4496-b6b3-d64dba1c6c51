// Task Application Service - 任务应用服务

use crate::domain::entities::{Task, TaskStatus};
use crate::domain::repositories::{TaskRepository, ProjectRepository};
use crate::domain::services::TaskDomainService;
use crate::shared::errors::{AppError, Result};
use crate::shared::types::{Id, QueryParams, Priority};
use std::sync::Arc;

/// 任务应用服务
/// 负责任务相关的业务流程编排
pub struct TaskAppService {
    task_repository: Arc<dyn TaskRepository>,
    project_repository: Arc<dyn ProjectRepository>,
    task_domain_service: TaskDomainService,
}

impl TaskAppService {
    pub fn new(
        task_repository: Arc<dyn TaskRepository>,
        project_repository: Arc<dyn ProjectRepository>,
        task_domain_service: TaskDomainService,
    ) -> Self {
        Self {
            task_repository,
            project_repository,
            task_domain_service,
        }
    }

    /// 创建新任务
    pub async fn create_task(
        &self,
        title: String,
        description: Option<String>,
        project_id: Option<Id>,
        parent_task_id: Option<Id>,
        due_date: Option<chrono::DateTime<chrono::Utc>>,
        priority: Option<Priority>,
    ) -> Result<Task> {
        // 验证任务标题
        self.task_domain_service.validate_task_title(&title)?;

        // 验证项目是否存在（如果提供）
        if let Some(ref project_id) = project_id {
            if self.project_repository.find_by_id(project_id).await?.is_none() {
                return Err(AppError::ValidationError("Project not found".to_string()));
            }
        }

        // 验证父任务是否存在（如果提供）
        if let Some(ref parent_id) = parent_task_id {
            if self.task_repository.find_by_id(parent_id).await?.is_none() {
                return Err(AppError::ValidationError("Parent task not found".to_string()));
            }
        }

        // 创建任务实体
        let mut task = Task::new(title, description);
        task.project_id = project_id;
        task.parent_task_id = parent_task_id;
        task.due_date = due_date;
        if let Some(priority) = priority {
            task.priority = priority;
        }

        // 保存任务
        self.task_repository.save(&task).await?;

        Ok(task)
    }

    /// 根据ID获取任务
    pub async fn get_task_by_id(&self, id: &Id) -> Result<Task> {
        self.task_repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| AppError::NotFound("Task not found".to_string()))
    }

    /// 更新任务信息
    pub async fn update_task(
        &self,
        id: &Id,
        title: Option<String>,
        description: Option<String>,
        project_id: Option<Id>,
        parent_task_id: Option<Id>,
        due_date: Option<chrono::DateTime<chrono::Utc>>,
        priority: Option<Priority>,
    ) -> Result<Task> {
        // 获取现有任务
        let mut task = self.get_task_by_id(id).await?;

        // 验证任务标题（如果提供）
        if let Some(ref new_title) = title {
            self.task_domain_service.validate_task_title(new_title)?;
            task.title = new_title.clone();
        }

        // 验证项目是否存在（如果提供）
        if let Some(ref new_project_id) = project_id {
            if self.project_repository.find_by_id(new_project_id).await?.is_none() {
                return Err(AppError::ValidationError("Project not found".to_string()));
            }
            task.project_id = Some(new_project_id.clone());
        }

        // 验证父任务是否存在（如果提供）
        if let Some(ref new_parent_id) = parent_task_id {
            if new_parent_id == id {
                return Err(AppError::ValidationError("Task cannot be its own parent".to_string()));
            }
            if self.task_repository.find_by_id(new_parent_id).await?.is_none() {
                return Err(AppError::ValidationError("Parent task not found".to_string()));
            }
            task.parent_task_id = Some(new_parent_id.clone());
        }

        // 更新其他字段
        if let Some(desc) = description {
            task.description = Some(desc);
        }
        if let Some(due) = due_date {
            task.due_date = Some(due);
        }
        if let Some(priority) = priority {
            task.priority = priority;
        }

        task.update_metadata();

        // 保存更新
        self.task_repository.update(&task).await?;

        Ok(task)
    }

    /// 更新任务状态
    pub async fn update_task_status(&self, id: &Id, status: TaskStatus) -> Result<Task> {
        let mut task = self.get_task_by_id(id).await?;

        task.status = status;
        task.update_metadata();

        // 如果标记为完成，设置进度为100%
        if status == TaskStatus::Completed {
            task.progress = 1.0;
        }

        self.task_repository.update(&task).await?;

        Ok(task)
    }

    /// 更新任务进度
    pub async fn update_task_progress(&self, id: &Id, progress: f32) -> Result<Task> {
        if progress < 0.0 || progress > 1.0 {
            return Err(AppError::ValidationError(
                "Progress must be between 0.0 and 1.0".to_string(),
            ));
        }

        let mut task = self.get_task_by_id(id).await?;
        task.progress = progress;
        task.update_metadata();

        // 如果进度达到100%，自动设置为完成状态
        if progress >= 1.0 && task.status != TaskStatus::Completed {
            task.status = TaskStatus::Completed;
        } else if progress > 0.0 && task.status == TaskStatus::Todo {
            task.status = TaskStatus::InProgress;
        }

        self.task_repository.update(&task).await?;

        Ok(task)
    }

    /// 完成任务
    pub async fn complete_task(&self, id: &Id) -> Result<Task> {
        let mut task = self.get_task_by_id(id).await?;

        if !self.task_domain_service.can_complete_task(&task) {
            return Err(AppError::ValidationError("Task cannot be completed".to_string()));
        }

        task.status = TaskStatus::Completed;
        task.progress = 1.0;
        task.update_metadata();

        self.task_repository.update(&task).await?;

        Ok(task)
    }

    /// 取消任务
    pub async fn cancel_task(&self, id: &Id) -> Result<Task> {
        let mut task = self.get_task_by_id(id).await?;

        if task.status == TaskStatus::Completed {
            return Err(AppError::ValidationError("Cannot cancel completed task".to_string()));
        }

        task.status = TaskStatus::Cancelled;
        task.update_metadata();

        self.task_repository.update(&task).await?;

        Ok(task)
    }

    /// 重新打开任务
    pub async fn reopen_task(&self, id: &Id) -> Result<Task> {
        let mut task = self.get_task_by_id(id).await?;

        if task.status != TaskStatus::Completed && task.status != TaskStatus::Cancelled {
            return Err(AppError::ValidationError("Only completed or cancelled tasks can be reopened".to_string()));
        }

        task.status = if task.progress > 0.0 { TaskStatus::InProgress } else { TaskStatus::Todo };
        task.update_metadata();

        self.task_repository.update(&task).await?;

        Ok(task)
    }

    /// 删除任务（软删除）
    pub async fn delete_task(&self, id: &Id) -> Result<()> {
        // 检查任务是否存在
        let _task = self.get_task_by_id(id).await?;

        // 执行软删除
        self.task_repository.delete(id).await?;

        Ok(())
    }

    /// 获取所有活跃任务
    pub async fn get_active_tasks(&self) -> Result<Vec<Task>> {
        self.task_repository.find_all_active().await
    }

    /// 根据项目获取任务
    pub async fn get_tasks_by_project(&self, project_id: &Id) -> Result<Vec<Task>> {
        self.task_repository.find_by_project_id(project_id).await
    }

    /// 根据父任务获取子任务
    pub async fn get_subtasks(&self, parent_task_id: &Id) -> Result<Vec<Task>> {
        self.task_repository.find_by_parent_task_id(parent_task_id).await
    }

    /// 根据状态获取任务
    pub async fn get_tasks_by_status(&self, status: &TaskStatus) -> Result<Vec<Task>> {
        self.task_repository.find_by_status(status).await
    }

    /// 获取今日任务
    pub async fn get_today_tasks(&self) -> Result<Vec<Task>> {
        self.task_repository.find_today_tasks().await
    }

    /// 获取即将到期的任务
    pub async fn get_tasks_due_soon(&self, days: u32) -> Result<Vec<Task>> {
        self.task_repository.find_due_soon(days).await
    }

    /// 获取已逾期的任务
    pub async fn get_overdue_tasks(&self) -> Result<Vec<Task>> {
        self.task_repository.find_overdue().await
    }

    /// 分页获取任务列表
    pub async fn get_tasks_with_pagination(&self, params: &QueryParams) -> Result<(Vec<Task>, u64)> {
        self.task_repository.find_with_pagination(params).await
    }

    /// 搜索任务
    pub async fn search_tasks(&self, query: &str) -> Result<Vec<Task>> {
        if query.trim().is_empty() {
            return Ok(Vec::new());
        }

        self.task_repository.search(query).await
    }

    /// 获取任务统计信息
    pub async fn get_task_statistics(&self) -> Result<crate::domain::repositories::TaskStatistics> {
        self.task_repository.get_statistics().await
    }

    /// 获取根任务（没有父任务的任务）
    pub async fn get_root_tasks(&self) -> Result<Vec<Task>> {
        self.task_repository.find_root_tasks().await
    }

    /// 获取任务树（包含所有子任务）
    pub async fn get_task_tree(&self, root_task_id: &Id) -> Result<Vec<Task>> {
        self.task_repository.find_task_tree(root_task_id).await
    }

    /// 计算任务优先级分数
    pub async fn calculate_task_priority_score(&self, id: &Id) -> Result<u32> {
        let task = self.get_task_by_id(id).await?;
        Ok(self.task_domain_service.calculate_priority_score(&task))
    }

    /// 获取高优先级任务
    pub async fn get_high_priority_tasks(&self) -> Result<Vec<Task>> {
        let all_tasks = self.task_repository.find_all_active().await?;
        
        let mut high_priority_tasks: Vec<_> = all_tasks
            .into_iter()
            .filter(|task| {
                let score = self.task_domain_service.calculate_priority_score(task);
                score >= 75 // 高优先级阈值
            })
            .collect();

        // 按优先级分数排序
        high_priority_tasks.sort_by(|a, b| {
            let score_a = self.task_domain_service.calculate_priority_score(a);
            let score_b = self.task_domain_service.calculate_priority_score(b);
            score_b.cmp(&score_a)
        });

        Ok(high_priority_tasks)
    }

    /// 批量更新任务状态
    pub async fn batch_update_task_status(&self, task_ids: Vec<Id>, status: TaskStatus) -> Result<Vec<Task>> {
        let mut updated_tasks = Vec::new();

        for task_id in task_ids {
            match self.update_task_status(&task_id, status.clone()).await {
                Ok(task) => updated_tasks.push(task),
                Err(e) => {
                    // 记录错误但继续处理其他任务
                    eprintln!("Failed to update task {}: {}", task_id, e);
                }
            }
        }

        Ok(updated_tasks)
    }

    /// 克隆任务（创建副本）
    pub async fn clone_task(&self, id: &Id, new_title: String) -> Result<Task> {
        let original_task = self.get_task_by_id(id).await?;

        // 验证新任务标题
        self.task_domain_service.validate_task_title(&new_title)?;

        // 创建新任务
        let mut new_task = Task::new(new_title, original_task.description.clone());
        new_task.project_id = original_task.project_id.clone();
        new_task.due_date = original_task.due_date;
        new_task.priority = original_task.priority;

        // 保存新任务
        self.task_repository.save(&new_task).await?;

        Ok(new_task)
    }
}
