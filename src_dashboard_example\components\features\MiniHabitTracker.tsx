/**
 * 迷你习惯追踪器 - 紧凑设计的月历热力图
 * 显示当前月份的完整日历，小像素设计，专注于核心打卡功能
 */

import { useState, useMemo, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '../ui/tooltip'
import { Plus, Target, Flame, Trash2, MoreHorizontal } from 'lucide-react'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu'
import { useAreaStore } from '../../store/areaStore'
import { useLanguage } from '../../contexts/LanguageContext'
import { useConfirmDialog } from '../shared/ConfirmDialog'
import { cn } from '../../lib/utils'
import HabitValueDialog from './HabitValueDialog'
import { databaseApi } from '../../lib/api'

interface MiniHabitTrackerProps {
  areaId: string
  onAddHabit?: () => void
  className?: string
}

interface HabitRecord {
  date: string | Date
  completed: boolean
  value?: number
  note?: string
}

interface Habit {
  id: string
  name: string
  description?: string
  color?: string
  areaId: string
  frequency: string
  target: number
  unit?: string
  records?: HabitRecord[]
  createdAt: Date
  updatedAt?: Date
}

export function MiniHabitTracker({ areaId, onAddHabit, className }: MiniHabitTrackerProps) {
  const { t } = useLanguage()
  const { habits, habitRecords, toggleHabitRecord, deleteHabit, setHabitValue, setHabitRecords, fetchHabitsForArea } = useAreaStore()
  const { confirm, ConfirmDialog: ConfirmDialogComponent } = useConfirmDialog()
  const [valueDialogOpen, setValueDialogOpen] = useState(false)
  const [selectedHabit, setSelectedHabit] = useState<Habit | null>(null)
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)

  // 加载习惯数据
  useEffect(() => {
    if (areaId) {
      fetchHabitsForArea(areaId)
    }
  }, [areaId, fetchHabitsForArea])

  // 加载习惯记录数据
  useEffect(() => {
    const loadHabitRecords = async () => {
      const areaHabits = habits.filter(habit => habit.areaId === areaId)
      const allRecords: any[] = []

      for (const habit of areaHabits) {
        try {
          const result = await databaseApi.getHabitRecords(habit.id)
          if (result.success && result.data) {
            allRecords.push(...result.data)
          }
        } catch (error) {
          console.error(`Failed to load records for habit ${habit.id}:`, error)
        }
      }

      setHabitRecords(allRecords)
    }

    if (areaId && habits.length > 0) {
      loadHabitRecords()
    }
  }, [areaId, habits, setHabitRecords])

  // 获取该领域的习惯，并附加记录信息
  const areaHabits = useMemo(() => {
    if (!habits) return []

    const filteredHabits = habits
      .filter(habit => habit.areaId === areaId)
      .map(habit => ({
        ...habit,
        color: '#3b82f6', // 默认蓝色
        unit: undefined, // 数据库中没有unit字段
        records: habitRecords
          .filter(record => record.habitId === habit.id)
          .map(record => {
            try {
              return {
                date: typeof record.date === 'string' ? record.date : new Date(record.date).toISOString().split('T')[0],
                completed: record.completed,
                value: record.value ?? undefined, // 数据库记录中的value字段，null转为undefined
                note: undefined
              }
            } catch (error) {
              console.warn('Invalid date in habit record:', record.date)
              return {
                date: new Date().toISOString().split('T')[0], // 使用今天作为默认值
                completed: record.completed,
                value: record.value ?? undefined,
                note: undefined
              }
            }
          })
      }))

    return filteredHabits
  }, [habits, habitRecords, areaId])

  // 判断是否为数值型习惯
  //
  // 数值型习惯示例：
  // - { name: "喝水", target: 8, unit: "杯" } → 数值型
  // - { name: "走路", target: 10000, unit: "步" } → 数值型
  // - { name: "阅读", target: 30, unit: "分钟" } → 数值型
  //
  // 布尔型习惯示例：
  // - { name: "冥想", target: 1, unit: "" } → 布尔型
  // - { name: "早起", target: null, unit: null } → 布尔型
  // - { name: "锻炼", target: 0, unit: "次" } → 布尔型
  //
  // 判断标准：
  // target > 1 (目标值大于1，表示需要追踪数量)
  const isNumericHabit = (habit: Habit): boolean => {
    return habit.target > 1
  }
  
  // 生成当前月份的完整日历
  const currentMonth = useMemo(() => {
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()

    const days: Date[] = []
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(new Date(year, month, i))
    }

    return {
      year,
      month,
      days,
      monthName: now.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' }),
      firstDayOfWeek: firstDay.getDay() // 0 = Sunday, 1 = Monday, etc.
    }
  }, [])

  // 获取习惯在指定日期的完成状态
  const getHabitStatus = (habit: Habit, date: Date): boolean => {
    const dateStr = date.toISOString().split('T')[0]
    const record = habitRecords.find(r => {
      try {
        const recordDateStr = typeof r.date === 'string' ? r.date : new Date(r.date).toISOString().split('T')[0]
        return r.habitId === habit.id && recordDateStr === dateStr
      } catch (error) {
        return false
      }
    })

    if (isNumericHabit(habit)) {
      // 数值型习惯：有记录且值大于0就算完成
      return !!(record && record.value != null && record.value > 0)
    } else {
      // 布尔型习惯：按completed字段判断
      return record?.completed || false
    }
  }

  // 获取习惯在指定日期的数值
  const getHabitValue = (habit: Habit, date: Date): number => {
    const dateStr = date.toISOString().split('T')[0]
    const record = habitRecords.find(r => {
      try {
        const recordDateStr = typeof r.date === 'string' ? r.date : new Date(r.date).toISOString().split('T')[0]
        return r.habitId === habit.id && recordDateStr === dateStr
      } catch {
        return false
      }
    })
    return record?.value ?? 0
  }

  // 获取习惯完成百分比（用于数值型习惯的视觉效果）
  const getHabitProgress = (habit: Habit, date: Date): number => {
    if (!isNumericHabit(habit) || !habit.target) return 0
    const value = getHabitValue(habit, date)
    return Math.min((value / habit.target) * 100, 100)
  }

  // 处理习惯点击
  const handleHabitClick = async (habit: Habit, date: Date) => {
    if (isFuture(date)) return

    if (isNumericHabit(habit)) {
      // 数值型习惯：打开输入弹窗
      setSelectedHabit(habit)
      setSelectedDate(date)
      setValueDialogOpen(true)
    } else {
      // 布尔型习惯：直接切换状态
      await toggleHabitRecord(habit.id, date)
    }
  }

  // 保存数值型习惯的值
  const handleSaveHabitValue = async (value: number) => {
    if (!selectedHabit || !selectedDate) return

    try {
      // 使用新的setHabitValue函数保存数值
      await setHabitValue(selectedHabit.id, selectedDate, value)

      // 保存成功后关闭弹窗
      setValueDialogOpen(false)
      setSelectedHabit(null)
      setSelectedDate(null)
    } catch (error) {
      console.error('Failed to save habit value:', error)
      // 可以在这里添加错误提示
    }
  }

  // 获取频率标签
  const getFrequencyLabel = (frequency: string) => {
    switch (frequency) {
      case 'daily':
        return t('pages.habits.dialog.frequencyOptions.daily')
      case 'weekly':
        return t('pages.habits.dialog.frequencyOptions.weekly')
      case 'monthly':
        return t('pages.habits.dialog.frequencyOptions.monthly')
      default:
        return frequency
    }
  }

  // 删除习惯
  const handleDeleteHabit = async (habitId: string) => {
    const confirmed = await confirm({
      title: t('pages.habits.miniTracker.deleteHabit'),
      message: t('pages.habits.miniTracker.deleteConfirm'),
      variant: 'destructive',
      confirmText: t('common.delete'),
      cancelText: t('common.cancel')
    })

    if (confirmed) {
      try {
        await deleteHabit(habitId)
      } catch (error) {
        console.error('Failed to delete habit:', error)
      }
    }
  }

  // 计算习惯的完成率（当前月份）
  const getHabitCompletionRate = (habit: Habit): number => {
    if (!habit.records || habit.records.length === 0) return 0

    const completedDays = currentMonth.days.filter(date => getHabitStatus(habit, date)).length
    return Math.round((completedDays / currentMonth.days.length) * 100)
  }

  // 计算连续天数
  const getCurrentStreak = (habit: Habit): number => {
    let streak = 0
    const today = new Date()
    
    for (let i = 0; i < 30; i++) {
      const date = new Date(today)
      date.setDate(today.getDate() - i)
      
      if (getHabitStatus(habit, date)) {
        streak++
      } else {
        break
      }
    }
    
    return streak
  }

  // 检查是否是今天
  const isToday = (date: Date): boolean => {
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  // 检查是否是未来日期
  const isFuture = (date: Date): boolean => {
    const today = new Date()
    today.setHours(23, 59, 59, 999)
    return date > today
  }

  return (
    <TooltipProvider>
      <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-base">
              <Target className="h-4 w-4" />
              {t('pages.habits.miniTracker.title')}
            </CardTitle>
            <CardDescription className="text-xs">
              {t('pages.habits.miniTracker.description', { monthName: currentMonth.monthName })}
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={onAddHabit}>
            <Plus className="h-4 w-4 mr-1" />
            {t('pages.habits.miniTracker.addButton')}
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        {areaHabits.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            <Target className="h-6 w-6 mx-auto mb-2 opacity-50" />
            <p className="text-sm">{t('pages.habits.miniTracker.noHabits')}</p>
            <p className="text-xs mt-1">{t('pages.habits.miniTracker.createFirstHabitHint')}</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
            {areaHabits.map((habit) => {
              const completionRate = getHabitCompletionRate(habit)
              const currentStreak = getCurrentStreak(habit)

              return (
                <div key={habit.id} className="group p-2 rounded-lg border border-gray-200 bg-white hover:border-gray-300 transition-colors aspect-square flex flex-col">
                  {/* 习惯标题行 - 紧凑版 */}
                  <div className="flex items-center justify-between mb-1 min-h-0">
                    <div className="flex items-center gap-1 flex-1 min-w-0">
                      <div
                        className="w-2 h-2 rounded-full flex-shrink-0"
                        style={{ '--habit-color': habit.color, backgroundColor: 'var(--habit-color)' } as React.CSSProperties}
                      />
                      <div className="flex flex-col min-w-0 flex-1">
                        <span className="font-medium text-xs truncate">{habit.name}</span>
                        <span className="text-xs text-muted-foreground truncate">
                          {habit.target > 1
                            ? `${t('pages.habits.miniTracker.target')}: ${habit.target} · ${getFrequencyLabel(habit.frequency || 'daily')}`
                            : getFrequencyLabel(habit.frequency || 'daily')
                          }
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-1 flex-shrink-0">
                      {currentStreak > 0 && (
                        <Badge variant="secondary" className="text-xs px-1 py-0 h-4 flex items-center gap-0.5">
                          <Flame className="h-2 w-2" />
                          {currentStreak}
                        </Badge>
                      )}
                      <Badge variant="outline" className="text-xs px-1 py-0 h-4">
                        {completionRate}%
                      </Badge>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity">
                            <MoreHorizontal className="h-3 w-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-32">
                          <DropdownMenuItem
                            onClick={() => handleDeleteHabit(habit.id)}
                            className="text-red-600 focus:text-red-600"
                          >
                            <Trash2 className="h-3 w-3 mr-2" />
                            {t('pages.habits.miniTracker.deleteHabit')}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>

                  {/* 月历热力图 - 超紧凑正方形版本 */}
                  <div className="grid grid-cols-7 gap-0.5 flex-1">
                    {/* 星期标题 */}
                    {['日', '一', '二', '三', '四', '五', '六'].map((day) => (
                      <div
                        key={day}
                        className="text-xs text-center text-muted-foreground font-medium h-3 flex items-center justify-center"
                      >
                        {day}
                      </div>
                    ))}

                    {/* 空白填充（月初前的空格） */}
                    {Array.from({ length: currentMonth.firstDayOfWeek }).map((_, index) => (
                      <div key={`empty-${index}`} className="aspect-square" />
                    ))}

                    {/* 日期格子 - 4×4像素 */}
                    {currentMonth.days.map((date) => {
                      const isCompleted = getHabitStatus(habit, date)
                      const isTodayDate = isToday(date)
                      const isFutureDate = isFuture(date)
                      const progress = getHabitProgress(habit, date)
                      const value = getHabitValue(habit, date)

                      // 生成tooltip内容
                      const getTooltipContent = () => {
                        const dateStr = `${date.getMonth() + 1}月${date.getDate()}日`
                        if (isFutureDate) return `${dateStr} - ${t('pages.habits.miniTracker.futureDate')}`
                        if (!isCompleted && value === 0) return `${dateStr} - ${t('pages.habits.miniTracker.noRecord')}`

                        if (isNumericHabit(habit)) {
                          const target = habit.target || 0
                          if (value > 0) {
                            return `${dateStr} - ${t('pages.habits.miniTracker.completedWithValue', {
                              value: value.toString(),
                              unit: '',
                              target: target.toString(),
                              progress: Math.round(progress).toString()
                            })}`
                          }
                          return `${dateStr} - ${t('pages.habits.miniTracker.targetWithUnit', {
                            target: target.toString(),
                            unit: ''
                          })}`
                        } else {
                          return `${dateStr} - ${isCompleted ? t('pages.habits.miniTracker.completed') : t('pages.habits.miniTracker.notCompleted')}`
                        }
                      }

                      return (
                        <Tooltip key={date.toISOString()}>
                          <TooltipTrigger asChild>
                            <button
                              type="button"
                              onClick={() => handleHabitClick(habit, date)}
                              disabled={isFutureDate}
                              className={cn(
                                'aspect-square text-xs rounded-sm transition-all duration-200 border flex-shrink-0 min-w-0 relative overflow-hidden',
                                'hover:scale-110 focus:outline-none focus:ring-1 focus:ring-offset-0',
                                {
                                  // 未来日期 - 最高优先级
                                  'bg-gray-50 text-gray-400 border-gray-200 cursor-not-allowed opacity-60': isFutureDate,
                                  // 数值型习惯100%完成 - 绿色
                                  'bg-green-500 text-white border-green-600 hover:bg-green-600': !isFutureDate && isNumericHabit(habit) && progress >= 100,
                                  // 数值型习惯部分完成 - 蓝色
                                  'bg-blue-500 text-white border-blue-600 hover:bg-blue-600': !isFutureDate && isNumericHabit(habit) && progress > 0 && progress < 100,
                                  // 布尔型习惯完成状态 - 绿色
                                  'bg-green-500 text-white border-green-600 hover:bg-green-600': !isFutureDate && !isNumericHabit(habit) && isCompleted,
                                  // 未完成状态 - 浅灰背景深色文字
                                  'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200': !isFutureDate && !isCompleted && !(isNumericHabit(habit) && progress > 0),
                                  // 今天特殊标识
                                  'ring-2 ring-blue-500 ring-offset-1': isTodayDate,
                                  // 焦点状态
                                  'focus:ring-blue-500': !isFutureDate
                                }
                              )}
                              style={
                                isNumericHabit(habit) && progress > 0 && progress < 100 && !isFutureDate
                                  ? {
                                      // 部分完成：蓝色渐变
                                      background: `linear-gradient(to top, #3b82f6 ${progress}%, #f3f4f6 ${progress}%)`,
                                      color: progress > 50 ? 'white' : '#374151',
                                      borderColor: '#2563eb'
                                    }
                                  : isNumericHabit(habit) && progress >= 100 && !isFutureDate
                                  ? {
                                      // 100%完成：纯绿色
                                      background: '#22c55e',
                                      color: 'white',
                                      borderColor: '#16a34a'
                                    }
                                  : undefined
                              }
                            >
                              {date.getDate()}
                            </button>
                          </TooltipTrigger>
                          <TooltipContent
                            side="top"
                            className="max-w-xs pointer-events-none"
                            sideOffset={8}
                          >
                            <div className="text-center">
                              <div className="font-medium">{getTooltipContent()}</div>
                              {isNumericHabit(habit) && value > 0 && (
                                <div className="text-xs opacity-90 mt-1">
                                  {t('pages.habits.miniTracker.clickToModify')}
                                </div>
                              )}
                              {!isNumericHabit(habit) && !isFutureDate && (
                                <div className="text-xs opacity-90 mt-1">
                                  {t('pages.habits.miniTracker.clickToToggle')}
                                </div>
                              )}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      )
                    })}
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </CardContent>

      {/* 数值输入弹窗 */}
      {selectedHabit && selectedDate && (
        <HabitValueDialog
          isOpen={valueDialogOpen}
          onClose={() => {
            setValueDialogOpen(false)
            setSelectedHabit(null)
            setSelectedDate(null)
          }}
          onSave={handleSaveHabitValue}
          habitName={selectedHabit.name}
          date={selectedDate}
          currentValue={getHabitValue(selectedHabit, selectedDate)}
          targetValue={selectedHabit.target}
          unit=""
        />
      )}
      </Card>

      {/* 确认删除弹窗 */}
      <ConfirmDialogComponent />
    </TooltipProvider>
  )
}

export default MiniHabitTracker
