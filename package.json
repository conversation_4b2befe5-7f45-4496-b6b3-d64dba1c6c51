{"name": "<PERSON>ay", "version": "0.1.0", "description": "", "type": "module", "scripts": {"start": "vite", "dev": "vite", "build": "vite build", "serve": "vite preview", "tauri": "tauri", "preview": "vite preview"}, "license": "MIT", "dependencies": {"@codemirror/lang-markdown": "^6.3.4", "@codemirror/language": "^6.11.3", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.38.2", "@kobalte/core": "^0.13.11", "@tanstack/solid-virtual": "^3.13.12", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "codemirror": "^6.0.2", "solid-codemirror": "^2.3.1", "solid-js": "^1.9.3", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@biomejs/biome": "^2.2.2", "@tailwindcss/vite": "^4.1.12", "@tauri-apps/cli": "^2", "@types/node": "^24.3.0", "tailwindcss": "^4.1.12", "tw-animate-css": "^1.3.7", "typescript": "~5.6.2", "vite": "^7.1.4", "vite-plugin-solid": "^2.11.8"}}