// Mock API Service - 模拟API服务
// 用于前端开发时的数据模拟

import { User, Project, Task, Area } from '../types/business';

// 模拟数据
const mockUsers: User[] = [
  {
    id: 'user-1',
    username: 'demo_user',
    email: '<EMAIL>',
    displayName: '演示用户',
    avatarUrl: '',
    preferences: {
      theme: 'system',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      dateFormat: 'YYYY-MM-DD',
      timeFormat: '24h',
      notificationsEnabled: true,
      autoSaveEnabled: true,
    },
    status: 'active',
    version: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
];

const mockProjects: Project[] = [
  {
    id: 'project-1',
    name: '示例项目',
    description: '这是一个示例项目',
    status: 'in_progress',
    priority: 'high',
    progress: 0.65,
    areaId: 'area-1',
    goals: ['完成核心功能', '优化用户体验'],
    deliverables: ['MVP版本', '用户文档'],
    startDate: '2025-01-01T00:00:00Z',
    dueDate: '2025-03-01T00:00:00Z',
    tags: ['重要', '紧急'],
    entityStatus: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    version: 1,
  },
  {
    id: 'project-2',
    name: '网站重构项目',
    description: '使用新技术栈重构公司网站',
    status: 'not_started',
    priority: 'medium',
    progress: 0,
    areaId: 'area-2',
    goals: ['提升性能', '改善用户体验'],
    deliverables: ['新版网站', 'SEO优化'],
    startDate: '2025-02-01T00:00:00Z',
    dueDate: '2025-04-01T00:00:00Z',
    tags: ['技术', '优化'],
    entityStatus: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    version: 1,
  },
  {
    id: 'project-3',
    name: '移动应用开发',
    description: '开发跨平台移动应用',
    status: 'completed',
    priority: 'low',
    progress: 1.0,
    areaId: 'area-1',
    goals: ['发布应用', '获得用户反馈'],
    deliverables: ['iOS应用', 'Android应用'],
    startDate: '2024-10-01T00:00:00Z',
    dueDate: '2024-12-31T00:00:00Z',
    tags: ['移动', '跨平台'],
    entityStatus: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    version: 1,
  },
  {
    id: 'project-4',
    name: '数据分析平台',
    description: '构建企业级数据分析平台',
    status: 'on_hold',
    priority: 'critical',
    progress: 0.3,
    areaId: 'area-3',
    goals: ['实时数据处理', '可视化报表'],
    deliverables: ['分析引擎', '报表系统'],
    startDate: '2024-11-01T00:00:00Z',
    dueDate: '2025-05-01T00:00:00Z',
    tags: ['数据', '分析', '企业级'],
    entityStatus: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    version: 1,
  }
];

const mockTasks: Task[] = [
  {
    id: 'task-1',
    title: '设计用户界面',
    description: '设计主要页面的用户界面',
    status: 'in_progress',
    priority: 'high',
    progress: 0.4,
    projectId: 'project-1',
    dueDate: '2025-01-15T00:00:00Z',
    entityStatus: 'active',
    version: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'task-2',
    title: '实现API集成',
    description: '集成后端API接口',
    status: 'todo',
    priority: 'medium',
    progress: 0,
    projectId: 'project-1',
    dueDate: '2025-01-20T00:00:00Z',
    entityStatus: 'active',
    version: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
];

const mockAreas: Area[] = [
  {
    id: 'area-1',
    name: '产品开发',
    description: '产品开发相关的所有活动',
    status: 'active',
    standards: ['代码质量', '用户体验', '性能优化'],
    color: '#3B82F6',
    icon: '🚀',
    entityStatus: 'active',
    version: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
];

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Mock API Service 类
class MockApiService {
  private async mockDelay(): Promise<void> {
    await delay(300 + Math.random() * 700); // 300-1000ms 随机延迟
  }

  // 用户相关 Mock API
  async getUsers(): Promise<User[]> {
    await this.mockDelay();
    return [...mockUsers];
  }

  async getUserById(id: string): Promise<User> {
    await this.mockDelay();
    const user = mockUsers.find(u => u.id === id);
    if (!user) throw new Error('User not found');
    return user;
  }

  async createUser(data: {
    username: string;
    email?: string;
    displayName?: string;
  }): Promise<User> {
    await this.mockDelay();
    const newUser: User = {
      id: `user-${Date.now()}`,
      username: data.username,
      email: data.email || '',
      displayName: data.displayName || data.username,
      avatarUrl: '',
      preferences: {
        theme: 'system',
        language: 'zh-CN',
        timezone: 'Asia/Shanghai',
        dateFormat: 'YYYY-MM-DD',
        timeFormat: '24h',
        notificationsEnabled: true,
        autoSaveEnabled: true,
      },
      status: 'active',
      version: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    mockUsers.push(newUser);
    return newUser;
  }

  async updateUser(id: string, data: {
    displayName?: string;
    email?: string;
    avatarUrl?: string;
  }): Promise<User> {
    await this.mockDelay();
    const userIndex = mockUsers.findIndex(u => u.id === id);
    if (userIndex === -1) throw new Error('User not found');

    mockUsers[userIndex] = {
      ...mockUsers[userIndex],
      ...data,
      updatedAt: new Date().toISOString(),
    };
    return mockUsers[userIndex];
  }

  async deleteUser(id: string): Promise<void> {
    await this.mockDelay();
    const userIndex = mockUsers.findIndex(u => u.id === id);
    if (userIndex === -1) throw new Error('User not found');
    mockUsers[userIndex].status = 'inactive';
  }

  // 项目相关 Mock API
  async getProjects(): Promise<Project[]> {
    await this.mockDelay();
    return mockProjects.filter(p => p.entityStatus === 'active');
  }

  async getProjectById(id: string): Promise<Project> {
    await this.mockDelay();
    const project = mockProjects.find(p => p.id === id);
    if (!project) throw new Error('Project not found');
    return project;
  }

  async createProject(data: {
    name: string;
    description?: string;
    areaId?: string;
    priority?: string;
    startDate?: string;
    dueDate?: string;
    goals?: string[];
    deliverables?: string[];
    tags?: string[];
  }): Promise<Project> {
    await this.mockDelay();
    const newProject: Project = {
      id: `project-${Date.now()}`,
      name: data.name,
      description: data.description || '',
      status: 'not_started',
      priority: (data.priority as any) || 'medium',
      progress: 0,
      areaId: data.areaId,
      goals: data.goals || [],
      deliverables: data.deliverables || [],
      startDate: data.startDate,
      dueDate: data.dueDate,
      tags: data.tags || [],
      entityStatus: 'active',
      version: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    mockProjects.push(newProject);
    return newProject;
  }

  async updateProject(id: string, data: any): Promise<Project> {
    await this.mockDelay();
    const projectIndex = mockProjects.findIndex(p => p.id === id);
    if (projectIndex === -1) throw new Error('Project not found');

    mockProjects[projectIndex] = {
      ...mockProjects[projectIndex],
      ...data,
      updatedAt: new Date().toISOString(),
    };
    return mockProjects[projectIndex];
  }

  async updateProjectStatus(id: string, status: string): Promise<Project> {
    return this.updateProject(id, { status });
  }

  async updateProjectProgress(id: string, progress: number): Promise<Project> {
    return this.updateProject(id, { progress });
  }

  async deleteProject(id: string): Promise<void> {
    await this.mockDelay();
    const projectIndex = mockProjects.findIndex(p => p.id === id);
    if (projectIndex === -1) throw new Error('Project not found');
    mockProjects[projectIndex].entityStatus = 'inactive';
  }

  async getProjectStatistics(): Promise<any> {
    await this.mockDelay();
    const activeProjects = mockProjects.filter(p => p.entityStatus === 'active');
    return {
      total: activeProjects.length,
      active: activeProjects.filter(p => p.status === 'in_progress').length,
      completed: activeProjects.filter(p => p.status === 'completed').length,
      overdue: activeProjects.filter(p => p.dueDate && new Date(p.dueDate) < new Date()).length,
    };
  }

  // 任务相关 Mock API
  async getTasks(): Promise<Task[]> {
    await this.mockDelay();
    return mockTasks.filter(t => t.entityStatus === 'active');
  }

  async getTaskById(id: string): Promise<Task> {
    await this.mockDelay();
    const task = mockTasks.find(t => t.id === id);
    if (!task) throw new Error('Task not found');
    return task;
  }

  async getTasksByProject(projectId: string): Promise<Task[]> {
    await this.mockDelay();
    return mockTasks.filter(t => t.entityStatus === 'active' && t.projectId === projectId);
  }

  async getTodayTasks(): Promise<Task[]> {
    await this.mockDelay();
    const today = new Date().toISOString().split('T')[0];
    return mockTasks.filter(t => t.entityStatus === 'active' && t.dueDate === today);
  }

  async getOverdueTasks(): Promise<Task[]> {
    await this.mockDelay();
    const today = new Date();
    return mockTasks.filter(t => t.entityStatus === 'active' && t.dueDate && new Date(t.dueDate) < today);
  }

  // 其他方法的简化实现...
  async createTask(data: any): Promise<Task> {
    await this.mockDelay();
    const newTask: Task = {
      id: `task-${Date.now()}`,
      title: data.title,
      description: data.description || '',
      status: 'todo',
      priority: data.priority || 'medium',
      progress: 0,
      projectId: data.projectId,
      parentTaskId: data.parentTaskId,
      dueDate: data.dueDate,
      entityStatus: 'active',
      version: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    mockTasks.push(newTask);
    return newTask;
  }

  async updateTask(id: string, data: Partial<Task>): Promise<Task> {
    await this.mockDelay();
    const idx = mockTasks.findIndex(t => t.id === id);
    if (idx === -1) throw new Error('Task not found');
    mockTasks[idx] = {
      ...mockTasks[idx],
      ...data,
      updatedAt: new Date().toISOString(),
    };
    return mockTasks[idx];
  }

  async updateTaskStatus(id: string, status: Task['status']): Promise<Task> {
    return this.updateTask(id, {
      status,
      progress: status === 'completed' ? 1 : status === 'in_progress' ? Math.max(0.1, mockTasks.find(t => t.id === id)?.progress || 0) : 0,
    });
  }

  async updateTaskProgress(id: string, progress: number): Promise<Task> {
    progress = Math.max(0, Math.min(1, progress));
    const status: Task['status'] = progress >= 1 ? 'completed' : (mockTasks.find(t => t.id === id)?.status === 'cancelled' ? 'cancelled' : (progress > 0 ? 'in_progress' : 'todo'));
    return this.updateTask(id, { progress, status });
  }

  async completeTask(id: string): Promise<Task> {
    return this.updateTask(id, { status: 'completed', progress: 1 });
  }

  async deleteTask(id: string): Promise<void> {
    await this.mockDelay();
    const idx = mockTasks.findIndex(t => t.id === id);
    if (idx === -1) throw new Error('Task not found');
    mockTasks[idx].entityStatus = 'inactive';
    mockTasks[idx].updatedAt = new Date().toISOString();
  }

  async getTaskStatistics(): Promise<any> {
    await this.mockDelay();
    const active = mockTasks.filter(t => t.entityStatus === 'active');
    const total = active.length;
    const completed = active.filter(t => t.status === 'completed').length;
    const activeTasks = active.filter(t => t.status !== 'completed' && t.status !== 'cancelled').length;
    const overdue = active.filter(t => t.dueDate && new Date(t.dueDate) < new Date() && t.status !== 'completed').length;
    const avgProgress = total ? active.reduce((s, t) => s + (t.progress || 0), 0) / total : 0;
    return {
      totalTasks: total,
      activeTasks,
      completedTasks: completed,
      overdueTasks: overdue,
      todayTasks: active.filter(t => t.dueDate && t.dueDate.slice(0,10) === new Date().toISOString().slice(0,10)).length,
      averageProgress: avgProgress,
      completionRate: total ? completed / total : 0,
      tasksByStatus: [
        { status: 'todo', count: active.filter(t => t.status === 'todo').length },
        { status: 'in_progress', count: active.filter(t => t.status === 'in_progress').length },
        { status: 'completed', count: active.filter(t => t.status === 'completed').length },
        { status: 'cancelled', count: active.filter(t => t.status === 'cancelled').length },
      ],
      tasksByPriority: [
        { priority: 'low', count: active.filter(t => t.priority === 'low').length },
        { priority: 'medium', count: active.filter(t => t.priority === 'medium').length },
        { priority: 'high', count: active.filter(t => t.priority === 'high').length },
        { priority: 'critical', count: active.filter(t => (t as any).priority === 'critical').length },
      ],
    };
  }


  // 领域相关 Mock API
  async getAreas(): Promise<Area[]> {
    await this.mockDelay();
    return mockAreas.filter(a => a.entityStatus === 'active');
  }

  // 系统相关 Mock API
  async healthCheck(): Promise<any> {
    await this.mockDelay();
    return { status: 'ok', timestamp: new Date().toISOString() };
  }

  async getSystemInfo(): Promise<any> {
    await this.mockDelay();
    return {
      version: '0.1.0',
      environment: 'development',
      database: 'mock',
    };
  }

  // 搜索 Mock API
  async searchProjects(query: string): Promise<Project[]> {
    await this.mockDelay();
    return mockProjects.filter(p =>
      p.entityStatus === 'active' && p.name.toLowerCase().includes(query.toLowerCase())
    );
  }

  async searchTasks(query: string): Promise<Task[]> {
    await this.mockDelay();
    return mockTasks.filter(t =>
      t.entityStatus === 'active' && t.title.toLowerCase().includes(query.toLowerCase())
    );
  }

  async searchAreas(query: string): Promise<Area[]> {
    await this.mockDelay();
    return mockAreas.filter(a =>
      a.entityStatus === 'active' && a.name.toLowerCase().includes(query.toLowerCase())
    );
  }
}

export const mockApiService = new MockApiService();
