// Performance monitoring utilities for drag operations and editor testing

export interface EditorPerformanceMetrics {
  startupTime: number
  editLatency: number
  renderTime: number
  memoryUsage: number
  fileSize: number
}

export interface PerformanceTestResult {
  name: string
  metrics: EditorPerformanceMetrics
  passed: boolean
  timestamp: Date
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: Map<string, number[]> = new Map()
  private testResults: PerformanceTestResult[] = []
  private isEnabled = process.env.NODE_ENV === 'development'

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  startTimer(operation: string): () => void {
    if (!this.isEnabled) return () => {}

    const startTime = performance.now()

    return () => {
      const endTime = performance.now()
      const duration = endTime - startTime

      if (!this.metrics.has(operation)) {
        this.metrics.set(operation, [])
      }

      this.metrics.get(operation)!.push(duration)

      // Log slow operations
      if (duration > 16) {
        // More than one frame at 60fps
        console.warn(`Slow operation detected: ${operation} took ${duration.toFixed(2)}ms`)
      }
    }
  }

  getMetrics(operation?: string) {
    if (!this.isEnabled) return null

    if (operation) {
      const times = this.metrics.get(operation) || []
      return {
        operation,
        count: times.length,
        average: times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0,
        min: times.length > 0 ? Math.min(...times) : 0,
        max: times.length > 0 ? Math.max(...times) : 0
      }
    }

    const allMetrics: any = {}
    this.metrics.forEach((times, op) => {
      allMetrics[op] = {
        count: times.length,
        average: times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0,
        min: times.length > 0 ? Math.min(...times) : 0,
        max: times.length > 0 ? Math.max(...times) : 0
      }
    })

    return allMetrics
  }

  clearMetrics() {
    if (this.isEnabled) {
      this.metrics.clear()
      this.testResults = []
    }
  }

  logSummary() {
    if (!this.isEnabled) return

    console.group('🚀 Drag Performance Metrics')
    const metrics = this.getMetrics()

    if (metrics) {
      Object.entries(metrics).forEach(([operation, stats]: [string, any]) => {
        console.log(`${operation}:`, {
          count: stats.count,
          avg: `${stats.average.toFixed(2)}ms`,
          min: `${stats.min.toFixed(2)}ms`,
          max: `${stats.max.toFixed(2)}ms`
        })
      })
    }

    console.groupEnd()
  }

  // Editor performance testing methods
  async testEditorStartup(): Promise<PerformanceTestResult> {
    const startTime = performance.now()

    // Simulate editor initialization
    await new Promise((resolve) => setTimeout(resolve, 100))

    const endTime = performance.now()
    const startupTime = endTime - startTime

    const metrics: EditorPerformanceMetrics = {
      startupTime,
      editLatency: 0,
      renderTime: startupTime,
      memoryUsage: this.getMemoryUsage(),
      fileSize: 0
    }

    const result: PerformanceTestResult = {
      name: 'Editor Startup',
      metrics,
      passed: startupTime < 1000, // Should start within 1 second
      timestamp: new Date()
    }

    this.testResults.push(result)
    return result
  }

  async testEditLatency(textLength: number = 1000): Promise<PerformanceTestResult> {
    const startTime = performance.now()

    // Simulate text input processing
    await new Promise((resolve) => setTimeout(resolve, Math.min(textLength / 100, 50)))

    const endTime = performance.now()
    const editLatency = endTime - startTime

    const metrics: EditorPerformanceMetrics = {
      startupTime: 0,
      editLatency,
      renderTime: editLatency,
      memoryUsage: this.getMemoryUsage(),
      fileSize: textLength / 1024
    }

    const result: PerformanceTestResult = {
      name: `Edit Latency (${textLength} chars)`,
      metrics,
      passed: editLatency < 50, // Should respond within 50ms
      timestamp: new Date()
    }

    this.testResults.push(result)
    return result
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      // @ts-ignore - Chrome specific API
      return Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
    }
    return 0
  }

  async runEditorPerformanceTests(): Promise<PerformanceTestResult[]> {
    console.log('🚀 Running Editor Performance Tests...')

    const tests = [
      await this.testEditorStartup(),
      await this.testEditLatency(500),
      await this.testEditLatency(2000),
      await this.testEditLatency(10000)
    ]

    this.printEditorTestReport()
    return tests
  }

  printEditorTestReport() {
    if (!this.isEnabled || this.testResults.length === 0) return

    console.log('\n📊 Editor Performance Test Results:')
    console.log('===================================')

    const passed = this.testResults.filter((r) => r.passed).length
    const total = this.testResults.length
    console.log(`Pass Rate: ${passed}/${total} (${((passed / total) * 100).toFixed(1)}%)`)

    this.testResults.forEach((result) => {
      const status = result.passed ? '✅' : '❌'
      console.log(`${status} ${result.name}:`)
      console.log(`   Startup: ${result.metrics.startupTime.toFixed(2)}ms`)
      console.log(`   Edit Latency: ${result.metrics.editLatency.toFixed(2)}ms`)
      console.log(`   Render: ${result.metrics.renderTime.toFixed(2)}ms`)
      console.log(`   Memory: ${result.metrics.memoryUsage}MB`)
    })
  }

  getTestResults(): PerformanceTestResult[] {
    return [...this.testResults]
  }
}

// Convenience functions
export const startTimer = (operation: string) =>
  PerformanceMonitor.getInstance().startTimer(operation)

export const getMetrics = (operation?: string) =>
  PerformanceMonitor.getInstance().getMetrics(operation)

export const logPerformanceSummary = () => PerformanceMonitor.getInstance().logSummary()

// Global performance monitor instance
export const performanceMonitor = PerformanceMonitor.getInstance()

// Hook for React components
export const usePerformanceMonitor = (): {
  startTimer: (operation: string) => void
  getMetrics: (
    operation: string
  ) => { count: number; average: number; min: number; max: number } | null
  clearMetrics: () => void
  logSummary: () => void
} => {
  const monitor = PerformanceMonitor.getInstance()

  return {
    startTimer: monitor.startTimer.bind(monitor),
    getMetrics: monitor.getMetrics.bind(monitor),
    clearMetrics: monitor.clearMetrics.bind(monitor),
    logSummary: monitor.logSummary.bind(monitor)
  }
}
