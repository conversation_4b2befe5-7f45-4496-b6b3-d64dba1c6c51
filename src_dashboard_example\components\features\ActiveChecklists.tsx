import { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Checkbox } from '../ui/checkbox'
import { Progress } from '../ui/progress'
import { CheckSquare, Clock, Trash2 } from 'lucide-react'
import { useTaskStore } from '../../store/taskStore'
import { electronApi } from '../../lib/api'
import { useLanguage } from '../../contexts/LanguageContext'
import { useUIStore } from '../../store/uiStore'
import { useConfirmDialog } from '../shared/ConfirmDialog'

interface ActiveChecklistsProps {
  areaId: string
  className?: string
}

export function ActiveChecklists({ areaId, className }: ActiveChecklistsProps) {
  const { t } = useLanguage()
  const { addNotification } = useUIStore()
  const { confirm, ConfirmDialog } = useConfirmDialog()
  
  const {
    checklists,
    checklistInstances,
    updateChecklistInstance,
    deleteChecklistInstance
  } = useTaskStore()

  // 获取该领域的清单模板
  const areaChecklists = checklists ? checklists.filter(checklist =>
    (checklist as any).areaId === areaId
  ) : []

  // 获取进行中的清单实例（未完成的）
  const activeInstances = useMemo(() => {
    if (!checklistInstances) return []

    return checklistInstances
      .filter(instance => {
        const template = areaChecklists.find(c => c.id === instance.checklistId)
        return template && !instance.completedAt
      })
      .map(instance => {
        // 优先使用模板快照信息，如果没有则查找当前模板
        const template = areaChecklists.find(c => c.id === instance.checklistId)
        const templateInfo = instance.templateSnapshot || template

        if (!templateInfo) return null

        // 处理数据库中的status结构：{items: [...], templateSnapshot: {...}}
        const statusItems = Array.isArray(instance.status) ? instance.status :
                           (instance.status as any)?.items || []
        const templateSnapshot = Array.isArray(instance.status) ? null :
                                (instance.status as any)?.templateSnapshot

        const completedItems = statusItems.filter(item => item.completed).length
        const totalItems = statusItems.length
        const progress = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0

        return {
          ...instance,
          status: statusItems, // 使用正确的items数组
          templateSnapshot: templateSnapshot || instance.templateSnapshot, // 使用数据库中的templateSnapshot
          template: templateInfo,
          progress,
          completedItems,
          totalItems
        }
      })
      .filter(Boolean) // 过滤掉null值
      .sort((a, b) => {
        const dateA = a.createdAt instanceof Date ? a.createdAt : new Date(a.createdAt)
        const dateB = b.createdAt instanceof Date ? b.createdAt : new Date(b.createdAt)
        return dateB.getTime() - dateA.getTime()
      })
  }, [checklistInstances, areaChecklists])

  // 切换清单项完成状态
  const handleToggleItem = async (instanceId: string, itemId: string) => {
    if (!checklistInstances) return
    const instance = checklistInstances.find(i => i.id === instanceId)
    if (!instance) return

    // 处理数据库中的status结构
    const statusItems = Array.isArray(instance.status) ? instance.status :
                       (instance.status as any)?.items || []

    const updatedStatus = statusItems.map(item =>
      item.id === itemId ? { ...item, completed: !item.completed } : item
    )

    // 检查是否所有项目都完成了
    const allCompleted = updatedStatus.every(item => item.completed)
    const completedAt = allCompleted ? new Date() : undefined

    try {
      const result = await electronApi.database.updateChecklistInstance({
        id: instanceId,
        updates: {
          status: updatedStatus,
          completedAt: completedAt
        }
      })

      if (result.success) {
        // 更新本地状态 - 保持原始的数据库结构
        const originalStatus = Array.isArray(instance.status) ? updatedStatus : {
          items: updatedStatus,
          templateSnapshot: (instance.status as any)?.templateSnapshot
        }

        updateChecklistInstance(instanceId, {
          status: originalStatus,
          completedAt: completedAt
        })

        if (allCompleted) {
          addNotification({
            type: 'success',
            title: '清单已完成',
            message: `"${instance.template?.name || '清单'}"已全部完成！`
          })
        }
      } else {
        console.error('Failed to update checklist instance:', result.error)
      }
    } catch (error) {
      console.error('Error updating checklist instance:', error)
    }
  }

  // 删除清单实例
  const handleDeleteInstance = async (instanceId: string, templateName: string) => {
    const confirmed = await confirm({
      title: '删除清单实例',
      message: `确定要删除"${templateName}"的这个实例吗？此操作无法撤销。`,
      confirmText: '删除',
      cancelText: '取消'
    })

    if (confirmed) {
      try {
        const result = await electronApi.database.deleteChecklistInstance(instanceId)
        if (result.success) {
          // 从本地状态删除
          deleteChecklistInstance(instanceId)
          addNotification({
            type: 'success',
            title: '已删除',
            message: '清单实例已删除'
          })
        } else {
          addNotification({
            type: 'error',
            title: '删除失败',
            message: result.error || '无法删除清单实例'
          })
        }
      } catch (error) {
        console.error('Failed to delete checklist instance:', error)
        addNotification({
          type: 'error',
          title: '删除失败',
          message: '无法删除清单实例'
        })
      }
    }
  }

  // 获取时间显示
  const getTimeDisplay = (date: Date | string): string => {
    const now = new Date()
    const targetDate = date instanceof Date ? date : new Date(date)
    const diffInHours = Math.floor((now.getTime() - targetDate.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return '刚刚创建'
    if (diffInHours < 24) return `${diffInHours}小时前`

    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}天前`

    return targetDate.toLocaleDateString()
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <CheckSquare className="h-5 w-5" />
                进行中的清单
              </CardTitle>
              <CardDescription>
                当前正在执行的清单实例
              </CardDescription>
            </div>
            {activeInstances.length > 0 && (
              <Badge variant="secondary">
                {activeInstances.length} 个进行中
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {activeInstances.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <CheckSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">暂无进行中的清单</p>
              <p className="text-xs mt-1">从右侧模板库创建清单实例开始执行</p>
            </div>
          ) : (
            <div className="space-y-4">
              {activeInstances.map((instance) => (
                <Card key={instance.id} className="border-l-4 border-l-blue-500">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <CardTitle className="text-base truncate">
                          {instance.template.name}
                        </CardTitle>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {instance.completedItems}/{instance.totalItems} 已完成
                          </Badge>
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            {getTimeDisplay(instance.createdAt)}
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteInstance(instance.id, instance.template.name)}
                        className="text-muted-foreground hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>完成进度</span>
                        <span className="font-medium">{instance.progress}%</span>
                      </div>
                      <Progress value={instance.progress} className="h-2" />
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    <div className="space-y-2">
                      {instance.status.map((item) => (
                        <div
                          key={item.id}
                          className="flex items-center gap-3 p-2 rounded hover:bg-accent/50 transition-colors"
                        >
                          <Checkbox
                            id={`${instance.id}-${item.id}`}
                            checked={item.completed}
                            onCheckedChange={() => handleToggleItem(instance.id, item.id)}
                          />
                          <label
                            htmlFor={`${instance.id}-${item.id}`}
                            className={`flex-1 text-sm cursor-pointer ${
                              item.completed 
                                ? 'line-through text-muted-foreground' 
                                : 'text-foreground'
                            }`}
                          >
                            {item.text}
                          </label>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
      
      <ConfirmDialog />
    </>
  )
}

export default ActiveChecklists
