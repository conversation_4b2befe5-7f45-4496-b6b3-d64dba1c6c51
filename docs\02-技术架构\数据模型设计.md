# PaoLife 数据模型设计

## 📋 概述

本文档定义了PaoLife项目的完整数据模型设计，包括数据库表结构、实体关系、索引优化策略等，确保数据的一致性、完整性和高性能访问。

## 🎯 设计原则

### 核心优化策略

1. **统一指标系统**: 将ProjectKPI和AreaMetric合并为统一的Metric表
2. **多态关联模式**: 使用Reference表统一管理所有实体间的关联关系
3. **通用标签系统**: 用Taggable表替代TaskTag，支持为任何实体打标签
4. **资源中央化**: 建立Resource表作为所有资源的中央仓库
5. **去JSON化**: 将JSON字段拆分为独立表，提升查询性能
6. **索引优化**: 为高频查询字段添加复合索引
7. **数据类型优化**: 使用更精确的数据类型
8. **关系规范化**: 消除数据冗余，提升一致性

### 设计约束

- **数据完整性**: 使用外键约束确保引用完整性
- **数据有效性**: 使用CHECK约束验证数据范围和格式
- **唯一性约束**: 防止重复数据和业务逻辑冲突
- **级联操作**: 合理设置级联删除和更新策略

## 🏗️ 核心表结构设计

### 1. 用户和配置管理

#### 1.1 用户表 (users)
```sql
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    username TEXT NOT NULL UNIQUE,
    email TEXT UNIQUE,
    password_hash TEXT NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    timezone TEXT DEFAULT 'UTC',
    language TEXT DEFAULT 'zh-CN',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP
);
```

**设计说明**:
- 支持本地用户账户管理
- 包含国际化支持（时区、语言）
- 记录用户活动状态和登录历史

#### 1.2 用户设置表 (user_settings)
```sql
CREATE TABLE user_settings (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    setting_key TEXT NOT NULL,
    setting_value TEXT NOT NULL,
    setting_type TEXT NOT NULL CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, setting_key)
);
```

**设计说明**:
- 替代JSON字段，支持类型化查询
- 灵活的键值对存储，支持用户个性化配置
- 类型约束确保数据有效性

#### 1.3 应用配置表 (app_config)
```sql
CREATE TABLE app_config (
    id TEXT PRIMARY KEY,
    config_key TEXT NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    config_type TEXT NOT NULL CHECK (config_type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    is_user_configurable BOOLEAN DEFAULT FALSE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 系统级配置管理
- 区分用户可配置和系统配置
- 支持配置描述和类型验证

### 2. 生活领域管理

#### 2.1 领域表 (areas)
```sql
CREATE TABLE areas (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    standard TEXT,
    icon_name TEXT,
    color_hex TEXT CHECK (color_hex GLOB '#[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]'),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    archived_at TIMESTAMP
);
```

**设计说明**:
- 支持生活领域的分类管理
- 颜色格式验证确保UI一致性
- 软删除机制（archived_at）保留历史数据

#### 2.2 领域指标表 (area_metrics)
```sql
CREATE TABLE area_metrics (
    id TEXT PRIMARY KEY,
    area_id TEXT NOT NULL REFERENCES areas(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    current_value DECIMAL(15,4) NOT NULL DEFAULT 0,
    target_value DECIMAL(15,4),
    unit TEXT,
    tracking_type TEXT NOT NULL DEFAULT 'manual'
        CHECK (tracking_type IN ('manual', 'habit_based', 'automatic')),
    direction TEXT NOT NULL DEFAULT 'higher_better'
        CHECK (direction IN ('higher_better', 'lower_better', 'target_value')),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 支持多种指标跟踪方式
- DECIMAL类型确保数值精度
- 方向性指标支持不同的优化目标

#### 2.3 领域指标记录表 (area_metric_records)
```sql
CREATE TABLE area_metric_records (
    id TEXT PRIMARY KEY,
    metric_id TEXT NOT NULL REFERENCES area_metrics(id) ON DELETE CASCADE,
    recorded_value DECIMAL(15,4) NOT NULL,
    note TEXT,
    source TEXT CHECK (source IN ('manual', 'habit', 'import', 'calculated')),
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    recorded_date DATE NOT NULL,
    recorded_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 记录指标的历史变化
- 置信度评分支持数据质量评估
- 多种数据来源支持自动化和手动录入

#### 2.4 习惯表 (habits)
```sql
CREATE TABLE habits (
    id TEXT PRIMARY KEY,
    area_id TEXT NOT NULL REFERENCES areas(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    target_frequency INTEGER NOT NULL DEFAULT 1,
    frequency_unit TEXT NOT NULL DEFAULT 'daily'
        CHECK (frequency_unit IN ('daily', 'weekly', 'monthly')),
    difficulty_level INTEGER DEFAULT 3 CHECK (difficulty_level >= 1 AND difficulty_level <= 5),
    reward_points INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 支持多种频率的习惯设定
- 难度等级和奖励积分支持游戏化
- 与领域关联，体现习惯的生活分类

#### 2.5 习惯记录表 (habit_records)
```sql
CREATE TABLE habit_records (
    id TEXT PRIMARY KEY,
    habit_id TEXT NOT NULL REFERENCES habits(id) ON DELETE CASCADE,
    completed_date DATE NOT NULL,
    completion_value INTEGER DEFAULT 1,
    note TEXT,
    mood_rating INTEGER CHECK (mood_rating >= 1 AND mood_rating <= 5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(habit_id, completed_date)
);
```

**设计说明**:
- 唯一约束防止同一天重复记录
- 支持完成强度和心情评分
- 为习惯分析提供丰富的数据维度

### 3. 项目管理

#### 3.1 项目表 (projects)
```sql
CREATE TABLE projects (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'not_started'
        CHECK (status IN ('not_started', 'in_progress', 'at_risk', 'paused', 'completed', 'archived')),
    progress INTEGER NOT NULL DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    priority INTEGER DEFAULT 3 CHECK (priority >= 1 AND priority <= 5),
    start_date DATE,
    deadline DATE,
    estimated_hours INTEGER,
    actual_hours INTEGER DEFAULT 0,
    area_id TEXT REFERENCES areas(id) ON DELETE SET NULL,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    archived_at TIMESTAMP
);
```

**设计说明**:
- 支持完整的项目生命周期管理
- 枚举约束确保状态数据一致性
- 进度百分比和优先级范围验证
- 与领域关联，支持项目分类

#### 3.2 项目KPI表 (project_kpis)
```sql
CREATE TABLE project_kpis (
    id TEXT PRIMARY KEY,
    project_id TEXT NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    current_value DECIMAL(15,4) NOT NULL DEFAULT 0,
    target_value DECIMAL(15,4),
    unit TEXT,
    kpi_type TEXT NOT NULL DEFAULT 'increase'
        CHECK (kpi_type IN ('increase', 'decrease', 'maintain')),
    frequency TEXT NOT NULL DEFAULT 'daily'
        CHECK (frequency IN ('daily', 'weekly', 'monthly', 'quarterly')),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 支持项目关键绩效指标管理
- 多种KPI类型和频率设置
- DECIMAL类型确保数值精度

#### 3.3 KPI记录表 (kpi_records)
```sql
CREATE TABLE kpi_records (
    id TEXT PRIMARY KEY,
    kpi_id TEXT NOT NULL REFERENCES project_kpis(id) ON DELETE CASCADE,
    recorded_value DECIMAL(15,4) NOT NULL,
    note TEXT,
    recorded_date DATE NOT NULL,
    recorded_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 记录KPI的历史变化
- 支持备注和记录人追踪
- 为项目分析提供数据基础

#### 3.4 项目交付物表 (deliverables)
```sql
CREATE TABLE deliverables (
    id TEXT PRIMARY KEY,
    project_id TEXT NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    deliverable_type TEXT NOT NULL DEFAULT 'document'
        CHECK (deliverable_type IN ('document', 'software', 'presentation', 'report', 'other')),
    status TEXT NOT NULL DEFAULT 'planned'
        CHECK (status IN ('planned', 'in_progress', 'review', 'completed', 'cancelled')),
    file_path TEXT,
    external_url TEXT,
    planned_date DATE,
    completed_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 支持多种类型的交付物管理
- 完整的交付物状态跟踪
- 支持本地文件和外部链接

#### 3.5 交付物验收标准表 (deliverable_criteria)
```sql
CREATE TABLE deliverable_criteria (
    id TEXT PRIMARY KEY,
    deliverable_id TEXT NOT NULL REFERENCES deliverables(id) ON DELETE CASCADE,
    criterion_text TEXT NOT NULL,
    is_met BOOLEAN DEFAULT FALSE,
    notes TEXT,
    verified_by TEXT REFERENCES users(id),
    verified_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 替代JSON字段，支持结构化查询
- 验收标准的详细跟踪
- 支持验收人和验收时间记录

### 4. 任务管理

#### 4.1 任务表 (tasks)
```sql
CREATE TABLE tasks (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'todo'
        CHECK (status IN ('todo', 'in_progress', 'waiting', 'completed', 'cancelled')),
    priority INTEGER DEFAULT 3 CHECK (priority >= 1 AND priority <= 5),
    parent_task_id TEXT REFERENCES tasks(id) ON DELETE CASCADE,
    project_id TEXT REFERENCES projects(id) ON DELETE SET NULL,
    area_id TEXT REFERENCES areas(id) ON DELETE SET NULL,
    assigned_to TEXT REFERENCES users(id) ON DELETE SET NULL,
    due_date DATE,
    estimated_minutes INTEGER,
    actual_minutes INTEGER DEFAULT 0,
    completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    sort_order INTEGER DEFAULT 0,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,

    CHECK (id != parent_task_id),
    CHECK (project_id IS NOT NULL OR area_id IS NOT NULL)
);
```

**设计说明**:
- 支持无限层级的任务嵌套
- 任务必须关联到项目或领域
- 防止循环引用和自引用
- 完整的任务状态和进度管理

#### 4.2 标签表 (tags)
```sql
CREATE TABLE tags (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    color_hex TEXT CHECK (color_hex GLOB '#[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]'),
    description TEXT,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 通用标签系统，支持多实体标记
- 颜色格式验证确保UI一致性
- 标签名称唯一性约束

#### 4.3 任务标签关联表 (task_tags)
```sql
CREATE TABLE task_tags (
    task_id TEXT NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    tag_id TEXT NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (task_id, tag_id)
);
```

**设计说明**:
- 多对多关系管理任务和标签
- 复合主键防止重复关联
- 级联删除保持数据一致性

#### 4.4 定期任务表 (recurring_tasks)
```sql
CREATE TABLE recurring_tasks (
    id TEXT PRIMARY KEY,
    area_id TEXT NOT NULL REFERENCES areas(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    recurrence_pattern TEXT NOT NULL CHECK (recurrence_pattern IN ('daily', 'weekly', 'monthly', 'yearly')),
    recurrence_interval INTEGER NOT NULL DEFAULT 1,
    recurrence_days TEXT,
    start_date DATE NOT NULL,
    end_date DATE,
    next_due_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    auto_create_tasks BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 支持多种重复模式的任务
- 自动任务创建机制
- 灵活的重复规则配置

### 5. 资源管理

#### 5.1 资源链接表 (resource_links)
```sql
CREATE TABLE resource_links (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    file_path TEXT NOT NULL,
    resource_type TEXT NOT NULL DEFAULT 'file'
        CHECK (resource_type IN ('file', 'folder', 'url', 'reference')),
    mime_type TEXT,
    file_size INTEGER,
    project_id TEXT REFERENCES projects(id) ON DELETE SET NULL,
    area_id TEXT REFERENCES areas(id) ON DELETE SET NULL,
    task_id TEXT REFERENCES tasks(id) ON DELETE SET NULL,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP
);
```

**设计说明**:
- 统一的资源管理系统
- 支持多种资源类型
- 灵活的关联关系（项目、领域、任务）
- 访问时间跟踪

#### 5.2 文档链接表 (document_links)
```sql
CREATE TABLE document_links (
    id TEXT PRIMARY KEY,
    source_path TEXT NOT NULL,
    target_path TEXT NOT NULL,
    link_type TEXT NOT NULL DEFAULT 'wikilink'
        CHECK (link_type IN ('wikilink', 'reference', 'embed', 'citation')),
    display_text TEXT,
    context_text TEXT,
    line_number INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CHECK (source_path != target_path),
    UNIQUE(source_path, target_path, link_type, line_number)
);
```

**设计说明**:
- 支持双向链接系统
- 多种链接类型支持
- 防止自链接和重复链接
- 上下文信息保存

#### 5.3 文档元数据表 (document_metadata)
```sql
CREATE TABLE document_metadata (
    id TEXT PRIMARY KEY,
    file_path TEXT NOT NULL UNIQUE,
    title TEXT,
    word_count INTEGER DEFAULT 0,
    character_count INTEGER DEFAULT 0,
    last_modified TIMESTAMP,
    file_hash TEXT,
    tags_extracted TEXT,
    links_count INTEGER DEFAULT 0,
    backlinks_count INTEGER DEFAULT 0,
    is_orphaned BOOLEAN DEFAULT FALSE,
    sync_status TEXT DEFAULT 'synced' CHECK (sync_status IN ('synced', 'modified', 'conflict', 'missing')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 文档的元数据管理
- 支持文件变更检测和同步状态
- 孤立文档识别
- 自动提取的标签和链接统计
- 为搜索和分析提供数据基础

#### 5.4 文件变更历史表 (file_change_history)
```sql
CREATE TABLE file_change_history (
    id TEXT PRIMARY KEY,
    file_path TEXT NOT NULL,
    change_type TEXT NOT NULL CHECK (change_type IN ('created', 'modified', 'deleted', 'renamed', 'moved')),
    old_path TEXT,
    new_path TEXT,
    file_hash_before TEXT,
    file_hash_after TEXT,
    change_size INTEGER DEFAULT 0,
    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    processing_status TEXT DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processed', 'failed'))
);
```

**设计说明**:
- 文件系统变更的详细记录
- 支持重命名和移动操作跟踪
- 变更大小统计
- 处理状态跟踪

#### 5.5 文档标签提取表 (document_tags)
```sql
CREATE TABLE document_tags (
    id TEXT PRIMARY KEY,
    file_path TEXT NOT NULL REFERENCES document_metadata(file_path) ON DELETE CASCADE,
    tag_text TEXT NOT NULL,
    tag_type TEXT NOT NULL DEFAULT 'hashtag' CHECK (tag_type IN ('hashtag', 'frontmatter', 'inline')),
    line_number INTEGER,
    context_text TEXT,
    extracted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(file_path, tag_text, tag_type, line_number)
);
```

**设计说明**:
- 从文档中自动提取的标签
- 支持多种标签类型
- 保留标签位置和上下文
- 防止重复提取

### 6. 其他功能模块

#### 6.1 收件箱笔记表 (inbox_notes)
```sql
CREATE TABLE inbox_notes (
    id TEXT PRIMARY KEY,
    content TEXT NOT NULL,
    note_type TEXT DEFAULT 'quick_note'
        CHECK (note_type IN ('quick_note', 'idea', 'task', 'reference', 'meeting_note')),
    source TEXT DEFAULT 'manual' CHECK (source IN ('manual', 'email', 'web_clipper', 'voice')),
    processing_status TEXT DEFAULT 'unprocessed'
        CHECK (processing_status IN ('unprocessed', 'processing', 'processed', 'archived')),
    processed_into_type TEXT CHECK (processed_into_type IN ('project', 'task', 'area', 'resource')),
    processed_into_id TEXT,
    priority INTEGER DEFAULT 3 CHECK (priority >= 1 AND priority <= 5),
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    processed_by TEXT REFERENCES users(id)
);
```

**设计说明**:
- 快速捕捉想法和信息
- 多种来源和类型支持
- 完整的处理流程跟踪
- 支持转换为其他实体

#### 6.2 复盘模板表 (review_templates)
```sql
CREATE TABLE review_templates (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    template_type TEXT NOT NULL DEFAULT 'weekly'
        CHECK (template_type IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'project')),
    is_default BOOLEAN DEFAULT FALSE,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 结构化复盘模板管理
- 多种复盘周期支持
- 默认模板机制

#### 6.3 复盘模板问题表 (review_template_questions)
```sql
CREATE TABLE review_template_questions (
    id TEXT PRIMARY KEY,
    template_id TEXT NOT NULL REFERENCES review_templates(id) ON DELETE CASCADE,
    question_text TEXT NOT NULL,
    question_type TEXT NOT NULL DEFAULT 'text'
        CHECK (question_type IN ('text', 'rating', 'boolean', 'number', 'date')),
    sort_order INTEGER DEFAULT 0,
    is_required BOOLEAN DEFAULT FALSE,
    placeholder_text TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 灵活的问题类型支持
- 问题排序和必填设置
- 支持占位符文本

#### 6.4 复盘记录表 (reviews)
```sql
CREATE TABLE reviews (
    id TEXT PRIMARY KEY,
    template_id TEXT NOT NULL REFERENCES review_templates(id),
    title TEXT NOT NULL,
    review_period_start DATE NOT NULL,
    review_period_end DATE NOT NULL,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'completed', 'archived')),
    overall_rating INTEGER CHECK (overall_rating >= 1 AND overall_rating <= 10),
    key_insights TEXT,
    action_items TEXT,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);
```

**设计说明**:
- 基于模板的复盘记录
- 复盘周期和状态管理
- 关键洞察和行动项记录

#### 6.5 复盘答案表 (review_answers)
```sql
CREATE TABLE review_answers (
    id TEXT PRIMARY KEY,
    review_id TEXT NOT NULL REFERENCES reviews(id) ON DELETE CASCADE,
    question_id TEXT NOT NULL REFERENCES review_template_questions(id),
    answer_text TEXT,
    answer_number DECIMAL(15,4),
    answer_boolean BOOLEAN,
    answer_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(review_id, question_id)
);
```

**设计说明**:
- 多类型答案存储
- 防止重复回答
- 支持答案修改历史

### 7. 通知和提醒系统

#### 7.1 通知规则表 (notification_rules)
```sql
CREATE TABLE notification_rules (
    id TEXT PRIMARY KEY,
    rule_name TEXT NOT NULL,
    rule_type TEXT NOT NULL CHECK (rule_type IN ('task_due', 'habit_reminder', 'project_milestone', 'custom')),
    entity_type TEXT NOT NULL CHECK (entity_type IN ('task', 'habit', 'project', 'area')),
    entity_id TEXT,
    trigger_condition TEXT NOT NULL,
    advance_minutes INTEGER DEFAULT 0,
    repeat_interval INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 支持多种类型的通知规则
- 灵活的触发条件配置
- 提前提醒和重复提醒设置

#### 7.2 通知记录表 (notifications)
```sql
CREATE TABLE notifications (
    id TEXT PRIMARY KEY,
    rule_id TEXT REFERENCES notification_rules(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    notification_type TEXT NOT NULL CHECK (notification_type IN ('info', 'warning', 'error', 'success')),
    is_read BOOLEAN DEFAULT FALSE,
    scheduled_at TIMESTAMP NOT NULL,
    sent_at TIMESTAMP,
    user_id TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 通知的发送记录和状态跟踪
- 支持定时发送和已读状态管理
- 多种通知类型支持

### 8. 搜索和索引系统

#### 8.1 搜索索引表 (search_index)
```sql
CREATE TABLE search_index (
    id TEXT PRIMARY KEY,
    entity_type TEXT NOT NULL CHECK (entity_type IN ('project', 'task', 'area', 'habit', 'resource', 'note', 'document')),
    entity_id TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    tags TEXT,
    file_path TEXT,
    indexed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(entity_type, entity_id)
);
```

**设计说明**:
- 全文搜索索引表
- 支持多种实体类型的统一搜索
- 包含文档路径支持
- 标签和内容的索引化

#### 8.2 Tantivy搜索配置表 (search_config)
```sql
CREATE TABLE search_config (
    id TEXT PRIMARY KEY,
    index_name TEXT NOT NULL UNIQUE,
    index_path TEXT NOT NULL,
    schema_version TEXT NOT NULL,
    field_configs TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_rebuild_at TIMESTAMP,
    document_count INTEGER DEFAULT 0,
    index_size_bytes INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- Tantivy搜索引擎配置
- 索引路径和模式版本管理
- 索引统计信息
- 支持多索引配置

#### 8.3 搜索历史表 (search_history)
```sql
CREATE TABLE search_history (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id),
    search_query TEXT NOT NULL,
    search_type TEXT DEFAULT 'global' CHECK (search_type IN ('global', 'project', 'area', 'resource', 'document')),
    search_filters TEXT,
    results_count INTEGER DEFAULT 0,
    response_time_ms INTEGER,
    searched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 用户搜索历史记录
- 支持不同类型的搜索
- 搜索过滤器和性能统计
- 搜索结果统计

#### 8.4 搜索建议表 (search_suggestions)
```sql
CREATE TABLE search_suggestions (
    id TEXT PRIMARY KEY,
    suggestion_text TEXT NOT NULL UNIQUE,
    suggestion_type TEXT NOT NULL CHECK (suggestion_type IN ('query', 'tag', 'title', 'auto_complete')),
    usage_count INTEGER DEFAULT 1,
    last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 搜索建议和自动补全
- 使用频率统计
- 支持多种建议类型

### 9. 项目模板系统

#### 9.1 项目模板表 (project_templates)
```sql
CREATE TABLE project_templates (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    template_data TEXT NOT NULL,
    category TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    usage_count INTEGER DEFAULT 0,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 项目模板的定义和管理
- 支持模板分类和使用统计
- 公共模板和私有模板支持

#### 9.2 模板任务表 (template_tasks)
```sql
CREATE TABLE template_tasks (
    id TEXT PRIMARY KEY,
    template_id TEXT NOT NULL REFERENCES project_templates(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    parent_task_id TEXT REFERENCES template_tasks(id),
    estimated_minutes INTEGER,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 模板中的任务定义
- 支持任务层级关系
- 预设的时间估算

#### 9.3 模板实例化记录表 (template_instances)
```sql
CREATE TABLE template_instances (
    id TEXT PRIMARY KEY,
    template_id TEXT NOT NULL REFERENCES project_templates(id),
    project_id TEXT NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    instance_name TEXT,
    customizations TEXT,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(template_id, project_id)
);
```

**设计说明**:
- 记录模板应用到项目的实例
- 支持自定义配置记录
- 防止重复实例化

#### 9.4 模板清单关联表 (template_checklists)
```sql
CREATE TABLE template_checklists (
    id TEXT PRIMARY KEY,
    template_id TEXT NOT NULL REFERENCES project_templates(id) ON DELETE CASCADE,
    checklist_id TEXT NOT NULL REFERENCES checklists(id),
    is_required BOOLEAN DEFAULT FALSE,
    auto_create BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(template_id, checklist_id)
);
```

**设计说明**:
- 模板与清单的关联关系
- 支持必填和自动创建设置
- 防止重复关联

### 10. 归档系统

#### 10.1 归档记录表 (archive_records)
```sql
CREATE TABLE archive_records (
    id TEXT PRIMARY KEY,
    entity_type TEXT NOT NULL CHECK (entity_type IN ('project', 'area', 'task', 'habit')),
    entity_id TEXT NOT NULL,
    archive_reason TEXT NOT NULL CHECK (archive_reason IN ('completed', 'cancelled', 'inactive', 'manual')),
    archive_note TEXT,
    original_data TEXT NOT NULL,
    archived_by TEXT NOT NULL REFERENCES users(id),
    archived_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    restored_at TIMESTAMP,
    restored_by TEXT REFERENCES users(id)
);
```

**设计说明**:
- 统一的归档记录管理
- 保存原始数据用于恢复
- 归档原因和操作人记录

### 11. 数据备份和同步

#### 11.1 备份记录表 (backup_records)
```sql
CREATE TABLE backup_records (
    id TEXT PRIMARY KEY,
    backup_name TEXT NOT NULL,
    backup_type TEXT NOT NULL CHECK (backup_type IN ('manual', 'automatic', 'scheduled')),
    backup_path TEXT NOT NULL,
    file_size INTEGER,
    backup_status TEXT DEFAULT 'completed' CHECK (backup_status IN ('in_progress', 'completed', 'failed')),
    created_by TEXT REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    restored_at TIMESTAMP,
    restored_by TEXT REFERENCES users(id)
);
```

**设计说明**:
- 备份操作的记录和管理
- 支持手动和自动备份
- 备份状态和恢复记录

#### 11.2 数据同步日志表 (sync_logs)
```sql
CREATE TABLE sync_logs (
    id TEXT PRIMARY KEY,
    sync_type TEXT NOT NULL CHECK (sync_type IN ('export', 'import', 'migration')),
    entity_type TEXT CHECK (entity_type IN ('project', 'task', 'area', 'habit', 'resource', 'all')),
    operation_status TEXT DEFAULT 'in_progress' CHECK (operation_status IN ('in_progress', 'completed', 'failed')),
    records_processed INTEGER DEFAULT 0,
    records_total INTEGER DEFAULT 0,
    error_message TEXT,
    file_path TEXT,
    started_by TEXT NOT NULL REFERENCES users(id),
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);
```

**设计说明**:
- 数据导入导出操作记录
- 进度跟踪和错误处理
- 支持不同实体类型的同步

### 12. 快捷键和用户偏好

#### 12.1 快捷键配置表 (keyboard_shortcuts)
```sql
CREATE TABLE keyboard_shortcuts (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id),
    action_name TEXT NOT NULL,
    key_combination TEXT NOT NULL,
    is_global BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, action_name),
    UNIQUE(user_id, key_combination)
);
```

**设计说明**:
- 用户自定义快捷键配置
- 全局和局部快捷键支持
- 防止快捷键冲突

#### 12.2 用户活动日志表 (user_activity_logs)
```sql
CREATE TABLE user_activity_logs (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id),
    activity_type TEXT NOT NULL CHECK (activity_type IN ('login', 'logout', 'create', 'update', 'delete', 'view')),
    entity_type TEXT CHECK (entity_type IN ('project', 'task', 'area', 'habit', 'resource', 'note')),
    entity_id TEXT,
    activity_details TEXT,
    ip_address TEXT,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 用户操作行为记录
- 支持审计和分析
- 隐私保护的活动跟踪

### 13. 智能推荐和分析

#### 13.1 推荐记录表 (recommendations)
```sql
CREATE TABLE recommendations (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id),
    recommendation_type TEXT NOT NULL CHECK (recommendation_type IN ('related_resource', 'habit_suggestion', 'project_template', 'review_reminder')),
    title TEXT NOT NULL,
    description TEXT,
    target_entity_type TEXT,
    target_entity_id TEXT,
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    is_dismissed BOOLEAN DEFAULT FALSE,
    is_accepted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    dismissed_at TIMESTAMP,
    accepted_at TIMESTAMP
);
```

**设计说明**:
- 智能推荐系统的记录
- 推荐置信度评分
- 用户反馈跟踪

#### 13.2 数据统计缓存表 (statistics_cache)
```sql
CREATE TABLE statistics_cache (
    id TEXT PRIMARY KEY,
    cache_key TEXT NOT NULL UNIQUE,
    cache_data TEXT NOT NULL,
    entity_type TEXT,
    entity_id TEXT,
    calculation_date DATE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 复杂统计计算结果缓存
- 提升仪表板和分析页面性能
- 支持过期时间管理

### 14. 文件和附件管理

#### 14.1 附件表 (attachments)
```sql
CREATE TABLE attachments (
    id TEXT PRIMARY KEY,
    entity_type TEXT NOT NULL CHECK (entity_type IN ('project', 'task', 'area', 'habit', 'note')),
    entity_id TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    mime_type TEXT,
    file_hash TEXT,
    upload_status TEXT DEFAULT 'completed' CHECK (upload_status IN ('uploading', 'completed', 'failed')),
    uploaded_by TEXT NOT NULL REFERENCES users(id),
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 统一的附件管理系统
- 支持多种实体类型的附件
- 文件完整性验证

#### 14.2 文件监控表 (file_watchers)
```sql
CREATE TABLE file_watchers (
    id TEXT PRIMARY KEY,
    watch_path TEXT NOT NULL UNIQUE,
    watch_type TEXT NOT NULL CHECK (watch_type IN ('file', 'directory')),
    is_active BOOLEAN DEFAULT TRUE,
    last_scan_at TIMESTAMP,
    files_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 文件系统监控配置
- 支持文件和目录监控
- 扫描状态和统计信息

### 15. 清单系统

#### 15.1 清单表 (checklists)
```sql
CREATE TABLE checklists (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    checklist_type TEXT NOT NULL DEFAULT 'general'
        CHECK (checklist_type IN ('general', 'project_template', 'area_standard', 'review_criteria')),
    is_template BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 支持多种类型的清单
- 模板机制支持复用
- 与项目、领域关联使用

#### 15.2 清单项表 (checklist_items)
```sql
CREATE TABLE checklist_items (
    id TEXT PRIMARY KEY,
    checklist_id TEXT NOT NULL REFERENCES checklists(id) ON DELETE CASCADE,
    item_text TEXT NOT NULL,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_required BOOLEAN DEFAULT FALSE,
    estimated_minutes INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**设计说明**:
- 清单的具体项目
- 支持排序和必填设置
- 时间估算支持

#### 15.3 清单实例表 (checklist_instances)
```sql
CREATE TABLE checklist_instances (
    id TEXT PRIMARY KEY,
    checklist_id TEXT NOT NULL REFERENCES checklists(id),
    entity_type TEXT NOT NULL CHECK (entity_type IN ('project', 'task', 'area', 'review')),
    entity_id TEXT NOT NULL,
    instance_name TEXT,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
    completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    UNIQUE(checklist_id, entity_type, entity_id)
);
```

**设计说明**:
- 清单在具体实体中的实例化
- 完成状态和进度跟踪
- 防止重复实例化

#### 15.4 清单项完成记录表 (checklist_item_completions)
```sql
CREATE TABLE checklist_item_completions (
    id TEXT PRIMARY KEY,
    instance_id TEXT NOT NULL REFERENCES checklist_instances(id) ON DELETE CASCADE,
    item_id TEXT NOT NULL REFERENCES checklist_items(id),
    is_completed BOOLEAN DEFAULT FALSE,
    completion_note TEXT,
    completed_by TEXT REFERENCES users(id),
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(instance_id, item_id)
);
```

**设计说明**:
- 清单项的完成记录
- 支持完成备注和完成人
- 防止重复完成记录

## 🚀 索引优化策略

### 高频查询索引

```sql
-- 项目相关索引
CREATE INDEX idx_projects_status_area ON projects(status, area_id);
CREATE INDEX idx_projects_created_at ON projects(created_at DESC);
CREATE INDEX idx_projects_deadline ON projects(deadline);

-- 任务相关索引
CREATE INDEX idx_tasks_status_priority ON tasks(status, priority DESC);
CREATE INDEX idx_tasks_parent_sort ON tasks(parent_task_id, sort_order);
CREATE INDEX idx_tasks_project_area ON tasks(project_id, area_id);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);

-- 习惯相关索引
CREATE INDEX idx_habit_records_date ON habit_records(completed_date DESC);
CREATE INDEX idx_habit_records_habit_date ON habit_records(habit_id, completed_date);

-- KPI相关索引
CREATE INDEX idx_kpi_records_date ON kpi_records(recorded_date DESC);
CREATE INDEX idx_area_metric_records_date ON area_metric_records(recorded_date DESC);

-- 文档链接索引
CREATE INDEX idx_document_links_source ON document_links(source_path);
CREATE INDEX idx_document_links_target ON document_links(target_path);

-- 资源相关索引
CREATE INDEX idx_resource_links_type_project ON resource_links(resource_type, project_id);
CREATE INDEX idx_resource_links_accessed ON resource_links(last_accessed_at DESC);
```

### 复合索引优化

```sql
-- 领域指标复合索引
CREATE INDEX idx_area_metrics_active_type ON area_metrics(is_active, tracking_type);

-- 收件箱处理索引
CREATE INDEX idx_inbox_status_priority ON inbox_notes(processing_status, priority DESC);

-- 复盘周期索引
CREATE INDEX idx_reviews_period ON reviews(review_period_start, review_period_end);

-- 用户设置索引
CREATE INDEX idx_user_settings_key ON user_settings(user_id, setting_key);

-- 标签关联索引
CREATE INDEX idx_task_tags_tag ON task_tags(tag_id);

-- 通知系统索引
CREATE INDEX idx_notifications_user_read ON notifications(user_id, is_read);
CREATE INDEX idx_notifications_scheduled ON notifications(scheduled_at);
CREATE INDEX idx_notification_rules_entity ON notification_rules(entity_type, entity_id);

-- 搜索系统索引
CREATE INDEX idx_search_index_entity ON search_index(entity_type, entity_id);
CREATE INDEX idx_search_index_file_path ON search_index(file_path);
CREATE INDEX idx_search_history_user ON search_history(user_id, searched_at DESC);
CREATE INDEX idx_search_config_active ON search_config(is_active, index_name);
CREATE INDEX idx_search_suggestions_type ON search_suggestions(suggestion_type, usage_count DESC);

-- 清单系统索引
CREATE INDEX idx_checklists_type_active ON checklists(checklist_type, is_active);
CREATE INDEX idx_checklist_items_checklist ON checklist_items(checklist_id, sort_order);
CREATE INDEX idx_checklist_instances_entity ON checklist_instances(entity_type, entity_id);
CREATE INDEX idx_checklist_instances_status ON checklist_instances(status, completion_percentage);
CREATE INDEX idx_checklist_completions_instance ON checklist_item_completions(instance_id, is_completed);

-- 模板系统索引
CREATE INDEX idx_template_instances_template ON template_instances(template_id);
CREATE INDEX idx_template_instances_project ON template_instances(project_id);
CREATE INDEX idx_template_checklists_template ON template_checklists(template_id, is_required);

-- 文档系统索引
CREATE INDEX idx_document_metadata_sync_status ON document_metadata(sync_status, last_modified);
CREATE INDEX idx_document_metadata_orphaned ON document_metadata(is_orphaned);
CREATE INDEX idx_file_change_history_path ON file_change_history(file_path, detected_at DESC);
CREATE INDEX idx_file_change_history_status ON file_change_history(processing_status, detected_at);
CREATE INDEX idx_document_tags_file ON document_tags(file_path, tag_type);
CREATE INDEX idx_document_tags_text ON document_tags(tag_text, tag_type);

-- 归档系统索引
CREATE INDEX idx_archive_records_entity ON archive_records(entity_type, entity_id);
CREATE INDEX idx_archive_records_date ON archive_records(archived_at DESC);

-- 备份系统索引
CREATE INDEX idx_backup_records_date ON backup_records(created_at DESC);
CREATE INDEX idx_sync_logs_status ON sync_logs(operation_status, started_at);

-- 活动日志索引
CREATE INDEX idx_activity_logs_user_date ON user_activity_logs(user_id, created_at DESC);
CREATE INDEX idx_activity_logs_entity ON user_activity_logs(entity_type, entity_id);

-- 推荐系统索引
CREATE INDEX idx_recommendations_user_type ON recommendations(user_id, recommendation_type);
CREATE INDEX idx_recommendations_dismissed ON recommendations(is_dismissed, created_at);

-- 统计缓存索引
CREATE INDEX idx_statistics_cache_key ON statistics_cache(cache_key);
CREATE INDEX idx_statistics_cache_expires ON statistics_cache(expires_at);

-- 附件系统索引
CREATE INDEX idx_attachments_entity ON attachments(entity_type, entity_id);
CREATE INDEX idx_attachments_upload_date ON attachments(uploaded_at DESC);
```

**索引优化说明**:
- 为状态查询添加复合索引，提升筛选性能
- 时间相关查询使用降序索引，优化最新数据查询
- 层级关系查询优化，支持快速树形结构遍历
- 双向链接查询优化，支持快速反向查找

## 📊 实体关系图

### 核心实体关系

```mermaid
erDiagram
    users ||--o{ areas : creates
    users ||--o{ projects : creates
    users ||--o{ tasks : creates
    users ||--o{ habits : creates
    users ||--o{ inbox_notes : creates

    areas ||--o{ projects : contains
    areas ||--o{ tasks : contains
    areas ||--o{ habits : contains
    areas ||--o{ area_metrics : has
    areas ||--o{ recurring_tasks : has

    projects ||--o{ tasks : contains
    projects ||--o{ deliverables : has
    projects ||--o{ project_kpis : has
    projects ||--o{ resource_links : references

    tasks ||--o{ tasks : "parent-child"
    tasks ||--o{ task_tags : tagged_with
    tasks ||--o{ resource_links : references

    habits ||--o{ habit_records : records

    tags ||--o{ task_tags : applied_to

    project_kpis ||--o{ kpi_records : records
    area_metrics ||--o{ area_metric_records : records

    review_templates ||--o{ reviews : instantiates
    review_templates ||--o{ review_template_questions : contains
    reviews ||--o{ review_answers : answers
    review_template_questions ||--o{ review_answers : answered_by
```

### 数据流关系

```mermaid
flowchart TD
    A[用户] --> B[生活领域]
    A --> C[项目]
    A --> D[收件箱]

    B --> E[习惯]
    B --> F[领域指标]
    B --> G[定期任务]

    C --> H[项目KPI]
    C --> I[交付物]
    C --> J[项目任务]

    B --> K[领域任务]

    J --> L[子任务]
    K --> L

    D --> M[处理为项目]
    D --> N[处理为任务]
    D --> O[处理为资源]

    E --> P[习惯记录]
    F --> Q[指标记录]
    H --> R[KPI记录]

    S[复盘模板] --> T[复盘记录]
    T --> U[复盘答案]
```

## 🔧 数据完整性约束

### 业务规则约束

1. **任务关联约束**: 任务必须关联到项目或领域之一
2. **循环引用防护**: 任务不能以自己为父任务
3. **日期逻辑约束**: 项目开始日期不能晚于截止日期
4. **数值范围约束**: 进度百分比、优先级、评分等有明确范围
5. **状态转换约束**: 某些状态转换需要满足前置条件

### 数据质量保证

```sql
-- 添加触发器示例（SQLite语法）
CREATE TRIGGER update_project_progress
AFTER UPDATE ON tasks
WHEN NEW.status != OLD.status AND NEW.project_id IS NOT NULL
BEGIN
    UPDATE projects
    SET progress = (
        SELECT ROUND(
            (COUNT(CASE WHEN status = 'completed' THEN 1 END) * 100.0) / COUNT(*)
        )
        FROM tasks
        WHERE project_id = NEW.project_id
    ),
    updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.project_id;
END;
```

## 📈 性能优化建议

### 查询优化

1. **分页查询**: 使用LIMIT和OFFSET进行分页
2. **条件索引**: 为常用的WHERE条件创建索引
3. **连接优化**: 避免不必要的JOIN操作
4. **子查询优化**: 使用EXISTS替代IN子查询

### 数据归档策略

1. **软删除**: 使用archived_at字段标记删除
2. **历史数据**: 定期归档旧的记录数据
3. **清理策略**: 定期清理临时数据和日志

### 缓存策略

1. **查询缓存**: 缓存频繁查询的结果
2. **计算缓存**: 缓存复杂的统计计算结果
3. **元数据缓存**: 缓存配置和设置信息

---

**文档版本**: 1.0
**创建日期**: 2024年12月
**维护者**: 技术团队
**下次更新**: 根据数据模型演进需要