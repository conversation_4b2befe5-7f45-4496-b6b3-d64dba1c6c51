// File: src/pages/Dashboard.tsx
import { Component, For, Show, createMemo, createSignal, onMount, JSX, createEffect } from "solid-js";

/** =========================
 * Types
 * =======================*/
type ID = string;

type ProjectStatus = "not_started" | "in_progress" | "at_risk" | "paused" | "completed" | "archived";
type Priority = "low" | "med" | "high";
type HabitFreq = "daily" | "weekly";

interface UserProfile {
  id: ID;
  displayName: string;
  lastReviewAt: number; // epoch ms
}

interface Task {
  id: ID;
  title: string;
  projectId: ID | null;
  due: number | null; // epoch ms
  completed: boolean;
  priority: Priority;
  updatedAt: number;
}

interface Project {
  id: ID;
  name: string;
  status: ProjectStatus;
  due: number | null;
  tasksCompleted: number;
  tasksTotal: number;
  updatedAt: number;
}

interface Habit {
  id: ID;
  name: string;
  freq: HabitFreq;
  streak: number;
  history: { date: string; done: boolean }[]; // ISO yyyy-MM-dd
}

interface Area {
  id: ID;
  name: string;
  active: boolean;
  habits: Habit[];
  updatedAt: number;
}

interface Resource {
  id: ID;
  type: "doc" | "link" | "image" | "note" | "code";
  createdAt: number;
}

interface Tag {
  id: ID;
  name: string;
  usage: number;
}

interface InboxItem {
  id: ID;
  content: string;
  tags: string[];
  createdAt: number;
  processed: boolean;
}

/** =========================
 * Utilities
 * =======================*/
const now = () => Date.now();
const DAY = 24 * 60 * 60 * 1000;

function fmtDateYMD(ts: number): string {
  const d = new Date(ts);
  const y = d.getFullYear();
  const m = `${d.getMonth() + 1}`.padStart(2, "0");
  const dd = `${d.getDate()}`.padStart(2, "0");
  return `${y}-${m}-${dd}`;
}
function weekdayCN(ts: number): string {
  const w = new Date(ts).getDay();
  return ["星期日","星期一","星期二","星期三","星期四","星期五","星期六"][w];
}
function greetingByTime(ts: number): string {
  const h = new Date(ts).getHours();
  if (h < 6) return "凌晨好";
  if (h < 11) return "早上好";
  if (h < 13) return "中午好";
  if (h < 18) return "下午好";
  return "晚上好";
}
function daysBetween(a: number, b: number): number {
  return Math.floor(Math.abs(a - b) / DAY);
}
function startOfDay(ts: number): number {
  const d = new Date(ts);
  d.setHours(0, 0, 0, 0);
  return d.getTime();
}
function isToday(ts: number | null): boolean {
  if (!ts) return false;
  return startOfDay(ts) === startOfDay(now());
}
function isOverdue(ts: number | null): boolean {
  if (!ts) return false;
  return startOfDay(ts) < startOfDay(now());
}
function withinNextDays(ts: number | null, days: number): boolean {
  if (!ts) return false;
  const sod = startOfDay(now());
  const diff = startOfDay(ts) - sod;
  return diff >= 0 && diff <= days * DAY;
}
function clamp01(n: number): number {
  return Math.max(0, Math.min(1, n));
}
function priorityColor(p: Priority): string {
  switch (p) {
    case "high":
      return "text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/30 border-red-200 dark:border-red-700";
    case "med":
      return "text-amber-600 dark:text-amber-300 bg-amber-50 dark:bg-amber-900/30 border-amber-200 dark:border-amber-700";
    case "low":
    default:
      return "text-emerald-600 dark:text-emerald-300 bg-emerald-50 dark:bg-emerald-900/30 border-emerald-200 dark:border-emerald-700";
  }
}
function statusBadgeColor(s: ProjectStatus): string {
  const map: Record<ProjectStatus, string> = {
    not_started: "bg-slate-100 text-slate-600 dark:bg-slate-800/60 dark:text-slate-300",
    in_progress: "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",
    at_risk: "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300",
    paused: "bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300",
    completed: "bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300",
    archived: "bg-zinc-100 text-zinc-700 dark:bg-zinc-900/30 dark:text-zinc-300",
  };
  return `inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${map[s]}`;
}
function fmtTimeAgo(ts: number): string {
  const d = daysBetween(now(), ts);
  if (d === 0) return "今天";
  if (d === 1) return "昨天";
  return `${d} 天前`;
}
function uuid(): ID {
  return Math.random().toString(36).slice(2, 10);
}
function navigateTo(page: string): void {
  // TODO: integrate your router
  console.log(`[TODO] navigate to ${page}`);
}

/** =========================
 * Mock Data
 * =======================*/
const [loading, setLoading] = createSignal<boolean>(true);
const [error, setError] = createSignal<string | null>(null);

const [user, setUser] = createSignal<UserProfile>({
  id: "u1",
  displayName: "Tayler",
  lastReviewAt: now() - 9 * DAY,
});

const [tags, setTags] = createSignal<Tag[]>([
  { id: "t1", name: "收件箱", usage: 21 },
  { id: "t2", name: "工作", usage: 34 },
  { id: "t3", name: "学习", usage: 18 },
  { id: "t4", name: "健康", usage: 9 },
  { id: "t5", name: "想法", usage: 12 },
  { id: "t6", name: "创意", usage: 8 },
]);

const [projects, setProjects] = createSignal<Project[]>([]);
const [areas, setAreas] = createSignal<Area[]>([]);
const [tasks, setTasks] = createSignal<Task[]>([]);
const [resources, setResources] = createSignal<Resource[]>([]);
const [inbox, setInbox] = createSignal<InboxItem[]>([]);

function loadMock(): void {
  const ts = now();
  const mockProjects: Project[] = [
    { id: "p1", name: "知识库重构", status: "in_progress", due: ts + 2 * DAY, tasksCompleted: 5, tasksTotal: 10, updatedAt: ts - 2 * DAY },
    { id: "p2", name: "移动端探索", status: "at_risk", due: ts + 6 * DAY, tasksCompleted: 2, tasksTotal: 8, updatedAt: ts - DAY },
    { id: "p3", name: "网站改版", status: "paused", due: ts + 30 * DAY, tasksCompleted: 3, tasksTotal: 20, updatedAt: ts - 8 * DAY },
    { id: "p4", name: "发布准备", status: "not_started", due: ts + 4 * DAY, tasksCompleted: 0, tasksTotal: 6, updatedAt: ts - 3 * DAY },
    { id: "p5", name: "归档示例", status: "archived", due: null, tasksCompleted: 12, tasksTotal: 12, updatedAt: ts - 40 * DAY },
    { id: "p6", name: "性能优化", status: "in_progress", due: ts + 1 * DAY, tasksCompleted: 7, tasksTotal: 10, updatedAt: ts - 0.5 * DAY },
  ];
  const mockTasks: Task[] = [
    { id: "tk1", title: "优化启动时间", projectId: "p6", due: ts, completed: false, priority: "high", updatedAt: ts - 2 * 60 * 60 * 1000 },
    { id: "tk2", title: "修复暗色模式细节", projectId: "p1", due: ts + DAY, completed: false, priority: "med", updatedAt: ts - DAY },
    { id: "tk3", title: "组件抽象与复用", projectId: "p1", due: ts - DAY, completed: false, priority: "high", updatedAt: ts - 3 * DAY },
    { id: "tk4", title: "埋点方案讨论", projectId: "p2", due: ts, completed: true, priority: "low", updatedAt: ts - 5 * DAY },
    { id: "tk5", title: "项目文档补齐", projectId: "p4", due: ts + 2 * DAY, completed: false, priority: "med", updatedAt: ts - 2 * DAY },
    { id: "tk6", title: "重构任务列表", projectId: "p1", due: ts - 2 * DAY, completed: false, priority: "high", updatedAt: ts - 12 * 60 * 60 * 1000 },
    { id: "tk7", title: "调研富文本", projectId: null, due: ts, completed: false, priority: "low", updatedAt: ts - 6 * 60 * 60 * 1000 },
  ];
  const todayISO = fmtDateYMD(ts);
  const mkHabit = (id: string, name: string, freq: HabitFreq, streak: number): Habit => {
    // last 14 days history
    const history = Array.from({ length: 14 }).map((_, i) => {
      const d = new Date(ts - (13 - i) * DAY);
      const iso = fmtDateYMD(d.getTime());
      const done = Math.random() > 0.35; // pseudo
      return { date: iso, done };
    });
    return { id, name, freq, streak, history };
  };
  const mockAreas: Area[] = [
    { id: "a1", name: "健康", active: true, habits: [mkHabit("h1", "跑步", "daily", 3), mkHabit("h2", "早睡", "daily", 5)], updatedAt: ts - 2 * DAY },
    { id: "a2", name: "学习", active: true, habits: [mkHabit("h3", "阅读", "daily", 2), mkHabit("h4", "写作", "weekly", 1)], updatedAt: ts - 12 * 60 * 60 * 1000 },
    { id: "a3", name: "家庭", active: false, habits: [mkHabit("h5", "家庭时间", "weekly", 1)], updatedAt: ts - 7 * DAY },
  ];
  const mockResources: Resource[] = [
    { id: "r1", type: "doc", createdAt: ts - 4 * DAY },
    { id: "r2", type: "link", createdAt: ts - 1 * DAY },
    { id: "r3", type: "note", createdAt: ts - 2 * DAY },
    { id: "r4", type: "code", createdAt: ts - 3 * DAY },
    { id: "r5", type: "image", createdAt: ts - 0.2 * DAY },
  ];
  const mockInbox: InboxItem[] = [
    { id: "i1", content: "看 #学习 相关资料", tags: ["学习"], createdAt: ts - 6 * 60 * 60 * 1000, processed: false },
    { id: "i2", content: "记录 #想法 UI 动画", tags: ["想法"], createdAt: ts - 2 * DAY, processed: false },
    { id: "i3", content: "采购跑鞋 #健康", tags: ["健康"], createdAt: ts - 3 * DAY, processed: true },
  ];
  setProjects(mockProjects);
  setTasks(mockTasks);
  setAreas(mockAreas);
  setResources(mockResources);
  setInbox(mockInbox);
}

onMount(() => {
  // simulate async
  setLoading(true);
  setError(null);
  setTimeout(() => {
    try {
      loadMock();
      setLoading(false);
    } catch (e) {
      setError("加载失败，请重试");
      setLoading(false);
    }
  }, 320);
});

/** =========================
 * Derived Data
 * =======================*/
const totalProjects = createMemo(() => projects().length);
const inProgressProjects = createMemo(() => projects().filter(p => p.status === "in_progress").length);
const projectStatusDist = createMemo(() => {
  const dist: Record<ProjectStatus, number> = {
    not_started: 0, in_progress: 0, at_risk: 0, paused: 0, completed: 0, archived: 0
  };
  for (const p of projects()) dist[p.status]++;
  return dist;
});
const projectCompletionRate = createMemo(() => {
  const arr = projects().filter(p => p.tasksTotal > 0);
  if (arr.length === 0) return 0;
  const sum = arr.reduce((acc, p) => acc + p.tasksCompleted / p.tasksTotal, 0);
  return sum / arr.length;
});

const totalAreas = createMemo(() => areas().length);
const activeAreas = createMemo(() => areas().filter(a => a.active).length);
const totalHabits = createMemo(() => areas().reduce((acc, a) => acc + a.habits.length, 0));
const todayHabitDoneCount = createMemo(() => {
  const todayISO = fmtDateYMD(now());
  let cnt = 0;
  for (const a of areas()) for (const h of a.habits) {
    const rec = h.history[h.history.length - 1];
    if (rec && rec.date === todayISO && rec.done) cnt++;
  }
  return cnt;
});
const todayHabitRate = createMemo(() => {
  const todayISO = fmtDateYMD(now());
  const all = areas().flatMap(a => a.habits);
  if (all.length === 0) return 0;
  const done = all.filter(h => {
    const rec = h.history[h.history.length - 1];
    return rec && rec.date === todayISO && rec.done;
  }).length;
  return done / all.length;
});

const totalTasks = createMemo(() => tasks().length);
const activeTasks = createMemo(() => tasks().filter(t => !t.completed).length);
const completedTasks = createMemo(() => tasks().filter(t => t.completed).length);
const todayDueTasks = createMemo(() => tasks().filter(t => !t.completed && (isToday(t.due) || isOverdue(t.due))));
const overdueCount = createMemo(() => tasks().filter(t => !t.completed && isOverdue(t.due)).length);
const taskCompletionRate = createMemo(() => totalTasks() === 0 ? 0 : completedTasks() / totalTasks());

const totalResources = createMemo(() => resources().length);
const recentResourcesCount = createMemo(() => resources().filter(r => daysBetween(now(), r.createdAt) <= 7).length);
const resourceTypeDist = createMemo(() => {
  const dist: Record<Resource["type"], number> = { doc: 0, link: 0, image: 0, note: 0, code: 0 };
  for (const r of resources()) dist[r.type]++;
  return dist;
});

const upcomingProjects = createMemo(() =>
  projects()
    .filter(p => p.due && withinNextDays(p.due, 7) && p.status !== "completed" && p.status !== "archived")
    .sort((a, b) => (a.due! - b.due!))
);

const recentActive = createMemo(() => {
  type Item = { id: ID; name: string; kind: "project" | "area"; status?: ProjectStatus; updatedAt: number };
  const pro: Item[] = projects().map(p => ({ id: p.id, name: p.name, kind: "project", status: p.status, updatedAt: p.updatedAt }));
  const ars: Item[] = areas().map(a => ({ id: a.id, name: a.name, kind: "area", updatedAt: a.updatedAt }));
  return [...pro, ...ars].sort((a, b) => b.updatedAt - a.updatedAt).slice(0, 6);
});

/** =========================
 * QuickCapture (combobox with tag suggestions)
 * =======================*/
interface QuickCapturePayload {
  content: string;
  tags: string[];
}
interface QuickCaptureProps {
  allTags: string[];
  onSubmit: (payload: QuickCapturePayload) => void;
}

const QuickCapture: Component<QuickCaptureProps> = (props) => {
  const [value, setValue] = createSignal<string>("");
  const [open, setOpen] = createSignal<boolean>(false);
  const [activeIndex, setActiveIndex] = createSignal<number>(0);

  const suggestions = createMemo<string[]>(() => {
    // detect query after last '#'
    const text = value();
    const caret = text.length;
    const hashIdx = text.lastIndexOf("#");
    if (hashIdx === -1 || caret <= hashIdx + 1) return [];
    const q = text.slice(hashIdx + 1).trim();
    if (!q) return [];
    const lower = q.toLowerCase();
    const set = new Set(props.allTags);
    // also include case-insensitive matches
    const list = Array.from(set).filter(t => t.toLowerCase().startsWith(lower));
    return list.slice(0, 8);
  });

  createEffect(() => {
    setOpen(suggestions().length > 0);
    if (open()) setActiveIndex(0);
  });

  function insertTag(tag: string): void {
    const text = value();
    const hashIdx = text.lastIndexOf("#");
    if (hashIdx === -1) return;
    const before = text.slice(0, hashIdx);
    // Replace "#partial" to "#tag "
    const replaced = `${before}#${tag} `;
    setValue(replaced);
    setOpen(false);
  }

  function extractTags(text: string): string[] {
    // Chinese + latin + numbers + _ -
    const reg = /#([A-Za-z0-9\u4e00-\u9fa5_-]+)/g;
    const res = new Set<string>();
    let m: RegExpExecArray | null;
    while ((m = reg.exec(text)) !== null) res.add(m[1]);
    return Array.from(res);
  }

  function handleKeyDown(e: KeyboardEvent): void {
    if (e.key === "Escape") {
      setOpen(false);
      return;
    }
    if (open()) {
      if (e.key === "ArrowDown") {
        e.preventDefault();
        setActiveIndex((i) => Math.min(i + 1, Math.max(0, suggestions().length - 1)));
        return;
      }
      if (e.key === "ArrowUp") {
        e.preventDefault();
        setActiveIndex((i) => Math.max(0, i - 1));
        return;
      }
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        const cur = suggestions()[activeIndex()] ?? "";
        if (cur) insertTag(cur);
        return;
      }
    }
    if (e.key === "Enter" && e.shiftKey) {
      e.preventDefault();
      const text = value().trim();
      if (!text) return;
      const tgs = extractTags(text);
      props.onSubmit({ content: text, tags: tgs });
      setValue("");
      setOpen(false);
      return;
    }
  }

  return (
    <div class="w-full">
      <label for="qc" class="sr-only">快速捕捉</label>
      <div
        role="combobox"
        aria-expanded={open() ? "true" : "false"}
        aria-owns="qc-suggest"
        aria-controls="qc-suggest"
        aria-activedescendant={open() ? `opt-${activeIndex()}` : undefined}
        class="relative"
      >
        <textarea
          id="qc"
          class="w-full rounded-xl border border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900 px-4 py-3 text-sm leading-relaxed outline-none focus-visible:ring-2 focus-visible:ring-blue-500/60 resize-none"
          rows={2}
          placeholder="快速记录灵感… 输入 # 选择标签，Shift+Enter 提交"
          value={value()}
          onInput={(e) => setValue((e.currentTarget as HTMLTextAreaElement).value)}
          onKeyDown={handleKeyDown as unknown as JSX.EventHandlerUnion<HTMLTextAreaElement, KeyboardEvent>}
        />
        <Show when={open()}>
          <ul
            id="qc-suggest"
            role="listbox"
            class="absolute z-20 mt-2 max-h-56 w-full overflow-auto rounded-lg border border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900 shadow-lg"
          >
            <For each={suggestions()}>
              {(t, i) => (
                <li
                  id={`opt-${i()}`}
                  role="option"
                  aria-selected={activeIndex() === i()}
                  class={`px-3 py-2 text-sm cursor-pointer ${activeIndex() === i() ? "bg-zinc-100 dark:bg-zinc-800" : ""}`}
                  onMouseDown={(e) => {
                    e.preventDefault();
                    insertTag(t);
                  }}
                  onMouseEnter={() => setActiveIndex(i())}
                >
                  #{t}
                </li>
              )}
            </For>
          </ul>
        </Show>
      </div>
      <p class="mt-2 text-xs text-zinc-500 dark:text-zinc-400">快捷键：Shift+Enter 提交 · Esc 关闭建议 · ↑/↓ 选择建议</p>
    </div>
  );
};

/** =========================
 * Overview Cards
 * =======================*/
const ProgressBar: Component<{ value: number }> = (p) => {
  const pct = () => Math.round(clamp01(p.value) * 100);
  return (
    <div class="w-full h-2 rounded-full bg-zinc-200 dark:bg-zinc-800 overflow-hidden" aria-label={`进度 ${pct()}%`}>
      <div class="h-full bg-blue-500 dark:bg-blue-400" style={{ width: `${pct()}%` }} />
    </div>
  );
};

const StatCard: Component<{
  title: string;
  onActivate?: () => void;
  children: JSX.Element;
}> = (props) => {
  return (
    <button
      type="button"
      onClick={() => props.onActivate?.()}
      class="group text-left rounded-2xl border border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900 p-4 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/60 hover:shadow-sm transition"
    >
      <div class="text-sm font-medium text-zinc-500 dark:text-zinc-400">{props.title}</div>
      <div class="mt-3 space-y-3">{props.children}</div>
      <div class="mt-3 text-xs text-blue-600 dark:text-blue-400 opacity-0 group-hover:opacity-100">查看详情 →</div>
    </button>
  );
};

/** =========================
 * Tabs
 * =======================*/
type TabsKey = "today" | "upcoming";

const DashboardTabs: Component<{
  tasks: Task[];
  projects: Project[];
  onToggleTask: (id: ID, completed: boolean) => void;
}> = (props) => {
  const [tab, setTab] = createSignal<TabsKey>("today");

  function handleKey(e: KeyboardEvent): void {
    if (e.ctrlKey && e.key.toLowerCase() === "tab") {
      e.preventDefault();
      setTab(tab() === "today" ? "upcoming" : "today");
      return;
    }
    if (e.key === "ArrowRight" || e.key === "ArrowLeft") {
      e.preventDefault();
      setTab(tab() === "today" ? "upcoming" : "today");
    }
  }

  const todayList = createMemo(() =>
    props.tasks
      .filter(t => !t.completed && (isToday(t.due) || isOverdue(t.due)))
      .sort((a, b) => {
        const overA = isOverdue(a.due) ? 1 : 0;
        const overB = isOverdue(b.due) ? 1 : 0;
        if (overA !== overB) return overB - overA;
        const da = a.due ?? Number.MAX_SAFE_INTEGER;
        const db = b.due ?? Number.MAX_SAFE_INTEGER;
        return da - db;
      })
  );

  const projectMap = createMemo(() => {
    const m = new Map<ID, Project>();
    for (const p of props.projects) m.set(p.id, p);
    return m;
  });

  const upcoming = createMemo(() =>
    props.projects
      .filter(p => p.due && withinNextDays(p.due, 7) && p.status !== "completed" && p.status !== "archived")
      .sort((a, b) => (a.due! - b.due!))
  );

  return (
    <section class="rounded-2xl border border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900">
      <div
        role="tablist"
        aria-label="仪表盘标签页"
        tabIndex={0}
        onKeyDown={handleKey as unknown as JSX.EventHandlerUnion<HTMLDivElement, KeyboardEvent>}
        class="flex items-center gap-2 border-b border-zinc-200 dark:border-zinc-800 px-3 pt-2"
      >
        <button
          role="tab"
          aria-selected={tab() === "today"}
          class={`px-3 py-2 text-sm rounded-t-md ${tab() === "today" ? "text-blue-600 border-b-2 border-blue-600" : "text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200"}`}
          onClick={() => setTab("today")}
        >
          今日任务
        </button>
        <button
          role="tab"
          aria-selected={tab() === "upcoming"}
          class={`px-3 py-2 text-sm rounded-t-md ${tab() === "upcoming" ? "text-blue-600 border-b-2 border-blue-600" : "text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200"}`}
          onClick={() => setTab("upcoming")}
        >
          即将到期项目
        </button>
        <div class="ml-auto text-xs text-zinc-500 dark:text-zinc-400 pr-2">Ctrl+Tab 切换</div>
      </div>

      <div class="p-4">
        <Show when={tab() === "today"} fallback={
          <div class="space-y-3">
            <For each={upcoming()}>
              {(p) => {
                const total = Math.max(1, p.tasksTotal);
                const progress = p.tasksCompleted / total;
                const remainDays = p.due ? Math.ceil((startOfDay(p.due) - startOfDay(now())) / DAY) : undefined;
                return (
                  <div class="rounded-xl border border-zinc-200 dark:border-zinc-800 p-3">
                    <div class="flex items-center justify-between gap-2">
                      <div class="font-medium">{p.name}</div>
                      <span class={statusBadgeColor(p.status)}>{{
                        not_started: "未开始",
                        in_progress: "进行中",
                        at_risk: "有风险",
                        paused: "暂停",
                        completed: "完成",
                        archived: "归档",
                      }[p.status]}</span>
                    </div>
                    <div class="mt-2">
                      <ProgressBar value={progress} />
                      <div class="mt-1 text-xs text-zinc-500 dark:text-zinc-400">
                        {p.tasksCompleted}/{p.tasksTotal} · {remainDays !== undefined ? `剩余 ${remainDays} 天` : "无截止日期"}
                      </div>
                    </div>
                    <div class="mt-2">
                      <button
                        class="text-xs text-blue-600 dark:text-blue-400 hover:underline focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/60 rounded"
                        onClick={() => navigateTo(`project:${p.id}`)}
                      >
                        查看详情
                      </button>
                    </div>
                  </div>
                );
              }}
            </For>
            <Show when={upcoming().length === 0}>
              <div class="text-sm text-zinc-500 dark:text-zinc-400">未来 7 天内暂无截止的项目。</div>
            </Show>
          </div>
        }>
          <div class="space-y-2">
            <div class="text-xs text-zinc-500 dark:text-zinc-400">
              完成 {props.tasks.filter(t => t.completed && (isToday(t.due) || isOverdue(t.due))).length} /
              {todayList().length + props.tasks.filter(t => t.completed && (isToday(t.due) || isOverdue(t.due))).length}
            </div>
            <For each={todayList()}>
              {(t) => {
                const proj = projectMap().get(t.projectId ?? "");
                return (
                  <div
                    class={`rounded-lg border p-3 flex items-start gap-3 ${isOverdue(t.due) ? "border-red-300 dark:border-red-700 bg-red-50/40 dark:bg-red-900/20" : "border-zinc-200 dark:border-zinc-800"}`}
                  >
                    <input
                      aria-label="完成任务"
                      type="checkbox"
                      class="mt-1 h-4 w-4 rounded border-zinc-300 dark:border-zinc-700"
                      checked={t.completed}
                      onChange={(e) => props.onToggleTask(t.id, (e.currentTarget as HTMLInputElement).checked)}
                    />
                    <div class="flex-1">
                      <div class="flex items-center justify-between gap-2">
                        <div class="font-medium">{t.title}</div>
                        <span class={`text-[11px] border rounded px-1.5 py-0.5 ${priorityColor(t.priority)}`}>
                          {t.priority === "high" ? "高" : t.priority === "med" ? "中" : "低"}
                        </span>
                      </div>
                      <div class="mt-1 text-xs text-zinc-500 dark:text-zinc-400 flex items-center gap-3">
                        <span>{t.due ? (isOverdue(t.due) ? "已逾期" : "今天到期") : "无截止"}</span>
                        <Show when={proj}><span class="truncate max-w-[180px]">所属项目：{proj!.name}</span></Show>
                        <span>更新：{fmtTimeAgo(t.updatedAt)}</span>
                      </div>
                    </div>
                  </div>
                );
              }}
            </For>
            <Show when={todayList().length === 0}>
              <div class="text-sm text-zinc-500 dark:text-zinc-400">今天没有需要处理或逾期的任务。</div>
            </Show>
          </div>
        </Show>
      </div>
    </section>
  );
};

/** =========================
 * Right Rail (Areas/Habits, Inbox, Recent)
 * =======================*/
const HeatDot: Component<{ done: boolean }> = (p) => (
  <div class={`w-4 h-4 rounded ${p.done ? "bg-emerald-500/80 dark:bg-emerald-400/90" : "bg-zinc-200 dark:bg-zinc-800"}`} />
);

function lastNDaysISO(n: number): string[] {
  const arr: string[] = [];
  const t = startOfDay(now());
  for (let i = n - 1; i >= 0; i--) {
    arr.push(fmtDateYMD(t - i * DAY));
  }
  return arr;
}

function habitRate7d(h: Habit): number {
  const days = lastNDaysISO(7);
  const done = days.filter(d => h.history.some(rec => rec.date === d && rec.done)).length;
  // expected count: daily -> 7, weekly -> 1
  const expected = h.freq === "daily" ? 7 : 1;
  return clamp01(done / expected);
}
function streakFromHistory(h: Habit): number {
  const days = lastNDaysISO(30).reverse(); // from today backwards
  let s = 0;
  for (let i = 0; i < days.length; i++) {
    const d = days[i];
    const rec = h.history.find(r => r.date === d);
    if (rec?.done) s++;
    else break;
  }
  return s;
}

const RightRail: Component<{
  areas: Area[];
  inbox: InboxItem[];
  onOpenInbox: () => void;
  recent: { id: ID; name: string; kind: "project" | "area"; status?: ProjectStatus; updatedAt: number }[];
}> = (props) => {
  const topAreas = createMemo(() => props.areas.filter(a => a.active).slice(0, 2));
  const inboxUnprocessed = createMemo(() => props.inbox.filter(i => !i.processed));
  const inboxTodayNew = createMemo(() => props.inbox.filter(i => startOfDay(i.createdAt) === startOfDay(now())));

  const overallProgress = createMemo(() => {
    const all = props.inbox.length;
    if (!all) return 0;
    const processed = props.inbox.filter(i => i.processed).length;
    return processed / all;
  });

  const sevenDays = lastNDaysISO(7);

  return (
    <aside class="space-y-4">
      <section class="rounded-2xl border border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900 p-4">
        <div class="text-sm font-semibold">领域与习惯</div>
        <div class="mt-3 space-y-3">
          <For each={topAreas()}>
            {(a) => (
              <div class="rounded-xl border border-zinc-200 dark:border-zinc-800 p-3">
                <div class="flex items-center justify-between">
                  <div class="font-medium">{a.name}</div>
                  <button
                    class="text-xs text-blue-600 dark:text-blue-400 hover:underline focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/60 rounded"
                    onClick={() => navigateTo(`area:${a.id}`)}
                  >
                    查看
                  </button>
                </div>
                <div class="mt-2 space-y-2">
                  <For each={a.habits.slice(0, 2)}>
                    {(h) => {
                      const rate = habitRate7d(h);
                      const streak = streakFromHistory(h);
                      return (
                        <div class="rounded-lg border border-zinc-200 dark:border-zinc-800 p-2">
                          <div class="flex items-center justify-between">
                            <div class="text-sm">{h.name}</div>
                            <div class="text-xs text-zinc-500 dark:text-zinc-400">
                              7天完成率 {(rate * 100).toFixed(0)}% · 连续 {streak} 天
                            </div>
                          </div>
                          <div class="mt-2 grid grid-cols-7 gap-1">
                            <For each={sevenDays}>
                              {(d) => {
                                const rec = h.history.find(r => r.date === d);
                                return <HeatDot done={!!rec?.done} />;
                              }}
                            </For>
                          </div>
                        </div>
                      );
                    }}
                  </For>
                </div>
              </div>
            )}
          </For>
          <Show when={topAreas().length === 0}>
            <div class="text-sm text-zinc-500 dark:text-zinc-400">暂无活跃领域。</div>
          </Show>
        </div>
      </section>

      <section class="rounded-2xl border border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900 p-4">
        <div class="text-sm font-semibold">收件箱</div>
        <div class="mt-2 text-sm text-zinc-700 dark:text-zinc-300">
          未处理 <span class="font-semibold">{inboxUnprocessed().length}</span> 条 · 今日新增 {inboxTodayNew().length}
        </div>
        <div class="mt-2">
          <ProgressBar value={overallProgress()} />
          <div class="mt-1 text-xs text-zinc-500 dark:text-zinc-400">整体处理进度</div>
        </div>
        <div class="mt-2">
          <button
            class="text-xs text-blue-600 dark:text-blue-400 hover:underline focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/60 rounded"
            onClick={() => props.onOpenInbox()}
          >
            打开收件箱
          </button>
        </div>
      </section>

      <section class="rounded-2xl border border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900 p-4">
        <div class="text-sm font-semibold">最近活跃</div>
        <div class="mt-2 space-y-2">
          <For each={props.recent}>
            {(it) => (
              <div class="flex items-center justify-between rounded-lg border border-zinc-200 dark:border-zinc-800 p-2">
                <div class="min-w-0">
                  <div class="text-sm font-medium truncate max-w-[200px]">{it.name}</div>
                  <div class="text-xs text-zinc-500 dark:text-zinc-400">
                    {it.kind === "project" ? "项目" : "领域"} · 更新于 {fmtTimeAgo(it.updatedAt)}
                  </div>
                </div>
                <div>
                  <Show when={it.kind === "project" && it.status}>
                    <span class={statusBadgeColor(it.status!)}>{{
                      not_started: "未开始",
                      in_progress: "进行中",
                      at_risk: "有风险",
                      paused: "暂停",
                      completed: "完成",
                      archived: "归档",
                    }[it.status!]}</span>
                  </Show>
                </div>
              </div>
            )}
          </For>
          <Show when={props.recent.length === 0}>
            <div class="text-sm text-zinc-500 dark:text-zinc-400">暂无活跃项。</div>
          </Show>
        </div>
      </section>
    </aside>
  );
};

/** =========================
 * Weekly Review Card
 * =======================*/
const WeeklyReviewCard: Component<{ lastReviewAt: number; onReview: () => void }> = (p) => {
  const show = createMemo(() => daysBetween(now(), p.lastReviewAt) > 7);
  return (
    <Show when={show()}>
      <section
        class="rounded-2xl p-4 bg-gradient-to-r from-indigo-500/10 via-fuchsia-500/10 to-amber-500/10 border border-zinc-200 dark:border-zinc-800"
        role="region"
        aria-label="周复盘提醒"
      >
        <div class="flex items-center justify-between gap-2">
          <div>
            <div class="text-sm font-semibold">该复盘啦</div>
            <div class="text-xs text-zinc-600 dark:text-zinc-400">距离上次复盘超过 7 天，进行一次快速复盘以同步全局。</div>
          </div>
          <button
            class="shrink-0 rounded-lg bg-blue-600 hover:bg-blue-700 text-white text-sm px-3 py-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/60"
            onClick={() => p.onReview()}
          >
            开始复盘
          </button>
        </div>
      </section>
    </Show>
  );
};

/** =========================
 * Dashboard Page
 * =======================*/
const Dashboard: Component = () => {
  // toggle task completion
  function toggleTask(id: ID, completed: boolean): void {
    setTasks((prev) => prev.map(t => t.id === id ? { ...t, completed } : t));
    // keep project stats roughly in sync
    const t = tasks().find(x => x.id === id);
    if (t && t.projectId) {
      setProjects((prev) => prev.map(p => {
        if (p.id !== t.projectId) return p;
        const delta = completed ? +1 : -1;
        const tasksCompleted = Math.max(0, Math.min(p.tasksTotal, p.tasksCompleted + delta));
        return { ...p, tasksCompleted, updatedAt: now() };
      }));
    }
  }

  function handleCaptureSubmit(payload: QuickCapturePayload): void {
    const item: InboxItem = { id: uuid(), content: payload.content, tags: payload.tags, createdAt: now(), processed: false };
    setInbox((prev) => [item, ...prev]);
    // update tags usage & add new tags
    if (payload.tags.length) {
      setTags((prev) => {
        const map = new Map(prev.map(t => [t.name, t]));
        for (const name of payload.tags) {
          if (map.has(name)) {
            const t = map.get(name)!;
            map.set(name, { ...t, usage: t.usage + 1 });
          } else {
            map.set(name, { id: uuid(), name, usage: 1 });
          }
        }
        return Array.from(map.values());
      });
    }
  }

  function markReviewed(): void {
    setUser((u) => ({ ...u, lastReviewAt: now() }));
  }

  const allTagNames = createMemo(() => tags().map(t => t.name));

  const dateStr = createMemo(() => fmtDateYMD(now()));
  const weekdayStr = createMemo(() => weekdayCN(now()));
  const greet = createMemo(() => greetingByTime(now()));

  return (
    <main class="container mx-auto px-4 py-5 text-zinc-900 dark:text-zinc-100">
      {/* Header */}
      <header class="rounded-2xl border border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900 p-4">
        <div class="flex flex-col md:flex-row md:items-start gap-4">
          <div class="flex-1">
            <div class="text-lg md:text-xl font-semibold">
              {greet()}，{user().displayName}
            </div>
            <div class="text-sm text-zinc-500 dark:text-zinc-400">{dateStr()} · {weekdayStr()}</div>
            <div class="mt-3">
              <QuickCapture allTags={allTagNames()} onSubmit={handleCaptureSubmit} />
            </div>
          </div>
          <div class="w-full md:w-[360px]">
            <WeeklyReviewCard lastReviewAt={user().lastReviewAt} onReview={markReviewed} />
          </div>
        </div>
      </header>

      {/* Error / Loading */}
      <Show when={!loading() && !error()} fallback={
        <div class="mt-5">
          <Show when={loading()} fallback={
            <div class="rounded-xl border border-red-300 dark:border-red-700 bg-red-50/60 dark:bg-red-900/20 p-4">
              <div class="text-sm font-semibold text-red-700 dark:text-red-300">出错了</div>
              <div class="text-sm text-red-700/80 dark:text-red-300/80 mt-1">{error()}</div>
              <button
                class="mt-2 text-sm rounded-lg bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/60"
                onClick={() => {
                  setError(null);
                  setLoading(true);
                  setTimeout(() => { try { loadMock(); setLoading(false); } catch { setError("加载失败"); setLoading(false); } }, 300);
                }}
              >
                重试
              </button>
            </div>
          }>
            <div class="rounded-xl border border-zinc-200 dark:border-zinc-800 p-4 mt-5">
              <div class="animate-pulse text-sm text-zinc-500 dark:text-zinc-400">加载中…</div>
            </div>
          </Show>
        </div>
      }>
        {/* Content */}
        <div class="mt-5 grid grid-cols-1 lg:grid-cols-[1fr,360px] gap-4">
          {/* Left main column */}
          <div class="space-y-4">
            {/* Overview cards */}
            <section class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4">
              {/* Projects */}
              <StatCard title="项目概览" onActivate={() => navigateTo("projects")}>
                <div class="flex items-baseline gap-2">
                  <div class="text-2xl font-semibold">{totalProjects()}</div>
                  <div class="text-xs text-zinc-500 dark:text-zinc-400">总数</div>
                </div>
                <div class="text-sm text-zinc-600 dark:text-zinc-300">进行中 {inProgressProjects()}</div>
                <div class="mt-2">
                  <ProgressBar value={projectCompletionRate()} />
                  <div class="mt-1 text-xs text-zinc-500 dark:text-zinc-400">平均完成率 {(projectCompletionRate() * 100).toFixed(0)}%</div>
                </div>
                <div class="mt-1 text-xs text-zinc-500 dark:text-zinc-400">
                  分布：未开始 {projectStatusDist().not_started} · 进行中 {projectStatusDist().in_progress} · 风险 {projectStatusDist().at_risk} · 暂停 {projectStatusDist().paused} · 完成 {projectStatusDist().completed} · 归档 {projectStatusDist().archived}
                </div>
              </StatCard>

              {/* Areas */}
              <StatCard title="领域与习惯" onActivate={() => navigateTo("areas")}>
                <div class="flex items-baseline gap-2">
                  <div class="text-2xl font-semibold">{totalAreas()}</div>
                  <div class="text-xs text-zinc-500 dark:text-zinc-400">领域</div>
                </div>
                <div class="text-sm text-zinc-600 dark:text-zinc-300">活跃 {activeAreas()}</div>
                <div class="text-sm text-zinc-600 dark:text-zinc-300">习惯 {totalHabits()}</div>
                <div class="mt-2">
                  <ProgressBar value={todayHabitRate()} />
                  <div class="mt-1 text-xs text-zinc-500 dark:text-zinc-400">今日完成 {todayHabitDoneCount()} / {totalHabits()}</div>
                </div>
              </StatCard>

              {/* Tasks */}
              <StatCard title="任务总览" onActivate={() => navigateTo("tasks")}>
                <div class="flex items-baseline gap-2">
                  <div class="text-2xl font-semibold">{totalTasks()}</div>
                  <div class="text-xs text-zinc-500 dark:text-zinc-400">总数</div>
                </div>
                <div class="text-sm text-zinc-600 dark:text-zinc-300">活跃 {activeTasks()} · 完成 {completedTasks()}</div>
                <div class="text-sm text-zinc-600 dark:text-zinc-300">
                  今日到期/逾期 {todayDueTasks().length} · <span class="text-red-600 dark:text-red-400">逾期 {overdueCount()}</span>
                </div>
                <div class="mt-2">
                  <ProgressBar value={taskCompletionRate()} />
                  <div class="mt-1 text-xs text-zinc-500 dark:text-zinc-400">完成率 {(taskCompletionRate() * 100).toFixed(0)}%</div>
                </div>
              </StatCard>

              {/* Resources */}
              <StatCard title="资源库" onActivate={() => navigateTo("resources")}>
                <div class="flex items-baseline gap-2">
                  <div class="text-2xl font-semibold">{totalResources()}</div>
                  <div class="text-xs text-zinc-500 dark:text-zinc-400">总数</div>
                </div>
                <div class="text-sm text-zinc-600 dark:text-zinc-300">最近 7 天新增 {recentResourcesCount()}</div>
                <div class="text-xs text-zinc-500 dark:text-zinc-400">
                  类型：文档 {resourceTypeDist().doc} · 链接 {resourceTypeDist().link} · 图片 {resourceTypeDist().image} · 笔记 {resourceTypeDist().note} · 代码 {resourceTypeDist().code}
                </div>
              </StatCard>
            </section>

            {/* Tabs */}
            <DashboardTabs
              tasks={tasks()}
              projects={projects()}
              onToggleTask={toggleTask}
            />
          </div>

          {/* Right rail */}
          <RightRail
            areas={areas()}
            inbox={inbox()}
            onOpenInbox={() => navigateTo("inbox")}
            recent={recentActive()}
          />
        </div>
      </Show>
    </main>
  );
};

export default Dashboard;
