/**
 * KPI集成测试工具
 * 用于验证KPI架构重构的完整性和功能
 */

import { featureFlagManager } from '../config/featureFlags'
import { createProjectKPIManager, createAreaMetricManager } from '../lib/kpiApiAdapters'
import type { CreateKPIData } from '../../../shared/types/kpi'

// 测试结果类型
export interface TestResult {
  name: string
  passed: boolean
  error?: string
  details?: any
}

export interface TestSuite {
  name: string
  results: TestResult[]
  passed: boolean
  totalTests: number
  passedTests: number
}

// KPI集成测试类
export class KPIIntegrationTest {
  private results: TestSuite[] = []

  /**
   * 运行所有测试
   */
  async runAllTests(): Promise<TestSuite[]> {
    console.group('🧪 KPI集成测试开始')
    
    this.results = []
    
    // 功能开关测试
    await this.testFeatureFlags()
    
    // 组件导入测试
    await this.testComponentImports()
    
    // API适配器测试
    await this.testApiAdapters()
    
    // 类型系统测试
    await this.testTypeSystem()
    
    console.groupEnd()
    
    return this.results
  }

  /**
   * 测试功能开关系统
   */
  private async testFeatureFlags(): Promise<void> {
    const suite: TestSuite = {
      name: 'Feature Flags System',
      results: [],
      passed: false,
      totalTests: 0,
      passedTests: 0
    }

    // 测试功能开关管理器
    suite.results.push(await this.runTest('Feature Flag Manager Exists', () => {
      if (!featureFlagManager) {
        throw new Error('Feature flag manager not found')
      }
      return { manager: 'exists' }
    }))

    // 测试获取功能开关
    suite.results.push(await this.runTest('Get Feature Flags', () => {
      const flags = featureFlagManager.getFlags()
      if (!flags || typeof flags !== 'object') {
        throw new Error('Failed to get feature flags')
      }
      return { flagCount: Object.keys(flags).length }
    }))

    // 测试更新功能开关
    suite.results.push(await this.runTest('Update Feature Flags', () => {
      const originalFlags = featureFlagManager.getFlags()
      featureFlagManager.updateFlags({ useRefactoredKPIManagement: true })
      const updatedFlags = featureFlagManager.getFlags()
      
      if (updatedFlags.useRefactoredKPIManagement !== true) {
        throw new Error('Failed to update feature flags')
      }
      
      // 恢复原始状态
      featureFlagManager.updateFlags({ useRefactoredKPIManagement: originalFlags.useRefactoredKPIManagement })
      
      return { updated: true }
    }))

    this.finalizeSuite(suite)
  }

  /**
   * 测试组件导入
   */
  private async testComponentImports(): Promise<void> {
    const suite: TestSuite = {
      name: 'Component Imports',
      results: [],
      passed: false,
      totalTests: 0,
      passedTests: 0
    }

    // 测试自适应组件导入
    suite.results.push(await this.runTest('Adaptive Components Import', async () => {
      try {
        const { AdaptiveProjectKPIManagement, AdaptiveAreaKPIManagement } = await import('../components/adaptive/AdaptiveKPIManagement')
        if (!AdaptiveProjectKPIManagement || !AdaptiveAreaKPIManagement) {
          throw new Error('Adaptive components not found')
        }
        return { components: ['AdaptiveProjectKPIManagement', 'AdaptiveAreaKPIManagement'] }
      } catch (error) {
        throw new Error(`Failed to import adaptive components: ${error}`)
      }
    }))

    // 测试通用对话框导入
    suite.results.push(await this.runTest('Universal Dialog Import', async () => {
      try {
        const UniversalKPIDialog = await import('../components/common/UniversalKPIDialog')
        if (!UniversalKPIDialog.default) {
          throw new Error('Universal KPI Dialog not found')
        }
        return { component: 'UniversalKPIDialog' }
      } catch (error) {
        throw new Error(`Failed to import universal dialog: ${error}`)
      }
    }))

    // 测试开发者工具导入
    suite.results.push(await this.runTest('Developer Tools Import', async () => {
      try {
        const DevToolsPanel = await import('../components/dev/DevToolsPanel')
        if (!DevToolsPanel.default) {
          throw new Error('Developer Tools Panel not found')
        }
        return { component: 'DevToolsPanel' }
      } catch (error) {
        throw new Error(`Failed to import developer tools: ${error}`)
      }
    }))

    this.finalizeSuite(suite)
  }

  /**
   * 测试API适配器
   */
  private async testApiAdapters(): Promise<void> {
    const suite: TestSuite = {
      name: 'API Adapters',
      results: [],
      passed: false,
      totalTests: 0,
      passedTests: 0
    }

    // 测试项目KPI管理器创建
    suite.results.push(await this.runTest('Project KPI Manager Creation', () => {
      const manager = createProjectKPIManager()
      if (!manager) {
        throw new Error('Failed to create project KPI manager')
      }
      return { manager: 'created' }
    }))

    // 测试领域指标管理器创建
    suite.results.push(await this.runTest('Area Metric Manager Creation', () => {
      const manager = createAreaMetricManager()
      if (!manager) {
        throw new Error('Failed to create area metric manager')
      }
      return { manager: 'created' }
    }))

    // 测试管理器方法
    suite.results.push(await this.runTest('Manager Methods', () => {
      const manager = createProjectKPIManager()
      const requiredMethods = ['create', 'update', 'delete', 'getKPIs', 'getStatistics']
      
      for (const method of requiredMethods) {
        if (typeof manager[method] !== 'function') {
          throw new Error(`Manager missing method: ${method}`)
        }
      }
      
      return { methods: requiredMethods }
    }))

    this.finalizeSuite(suite)
  }

  /**
   * 测试类型系统
   */
  private async testTypeSystem(): Promise<void> {
    const suite: TestSuite = {
      name: 'Type System',
      results: [],
      passed: false,
      totalTests: 0,
      passedTests: 0
    }

    // 测试类型导入
    suite.results.push(await this.runTest('Type Imports', async () => {
      try {
        const types = await import('../../../shared/types/kpi')
        const requiredTypes = ['BaseKPI', 'NumericKPI', 'CreateKPIData', 'KPIStatistics']
        
        // 检查类型是否存在（通过检查导出）
        const exports = Object.keys(types)
        const missingTypes = requiredTypes.filter(type => !exports.includes(type))
        
        if (missingTypes.length > 0) {
          throw new Error(`Missing types: ${missingTypes.join(', ')}`)
        }
        
        return { types: requiredTypes }
      } catch (error) {
        throw new Error(`Failed to import types: ${error}`)
      }
    }))

    // 测试类型转换函数
    suite.results.push(await this.runTest('Type Conversion Functions', async () => {
      try {
        const { toNumericKPI } = await import('../../../shared/types/kpi')
        
        if (typeof toNumericKPI !== 'function') {
          throw new Error('toNumericKPI function not found')
        }
        
        // 测试转换
        const testKPI = {
          id: 'test',
          name: 'Test KPI',
          value: '100',
          target: '200',
          direction: 'increase' as const,
          updatedAt: new Date()
        }
        
        const numericKPI = toNumericKPI(testKPI)
        
        if (typeof numericKPI.value !== 'number' || numericKPI.value !== 100) {
          throw new Error('Type conversion failed')
        }
        
        return { conversion: 'successful' }
      } catch (error) {
        throw new Error(`Type conversion test failed: ${error}`)
      }
    }))

    this.finalizeSuite(suite)
  }

  /**
   * 运行单个测试
   */
  private async runTest(name: string, testFn: () => any | Promise<any>): Promise<TestResult> {
    try {
      const result = await testFn()
      console.log(`✅ ${name}`)
      return {
        name,
        passed: true,
        details: result
      }
    } catch (error) {
      console.error(`❌ ${name}: ${error}`)
      return {
        name,
        passed: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 完成测试套件
   */
  private finalizeSuite(suite: TestSuite): void {
    suite.totalTests = suite.results.length
    suite.passedTests = suite.results.filter(r => r.passed).length
    suite.passed = suite.passedTests === suite.totalTests
    
    console.log(`📊 ${suite.name}: ${suite.passedTests}/${suite.totalTests} tests passed`)
    
    this.results.push(suite)
  }

  /**
   * 生成测试报告
   */
  generateReport(): string {
    const totalTests = this.results.reduce((sum, suite) => sum + suite.totalTests, 0)
    const totalPassed = this.results.reduce((sum, suite) => sum + suite.passedTests, 0)
    const overallPassed = this.results.every(suite => suite.passed)
    
    let report = '# KPI集成测试报告\n\n'
    report += `## 总体结果\n`
    report += `- 状态: ${overallPassed ? '✅ 通过' : '❌ 失败'}\n`
    report += `- 测试通过率: ${totalPassed}/${totalTests} (${Math.round(totalPassed / totalTests * 100)}%)\n\n`
    
    this.results.forEach(suite => {
      report += `## ${suite.name}\n`
      report += `- 状态: ${suite.passed ? '✅ 通过' : '❌ 失败'}\n`
      report += `- 通过率: ${suite.passedTests}/${suite.totalTests}\n\n`
      
      suite.results.forEach(test => {
        report += `### ${test.name}\n`
        report += `- 结果: ${test.passed ? '✅ 通过' : '❌ 失败'}\n`
        if (test.error) {
          report += `- 错误: ${test.error}\n`
        }
        if (test.details) {
          report += `- 详情: ${JSON.stringify(test.details, null, 2)}\n`
        }
        report += '\n'
      })
    })
    
    return report
  }
}

// 创建全局测试实例
export const kpiIntegrationTest = new KPIIntegrationTest()

// 便捷函数
export async function runKPIIntegrationTests(): Promise<TestSuite[]> {
  return kpiIntegrationTest.runAllTests()
}

export async function generateKPITestReport(): Promise<string> {
  await kpiIntegrationTest.runAllTests()
  return kpiIntegrationTest.generateReport()
}

// 开发者工具函数
export function logKPITestResults(): void {
  if (process.env.NODE_ENV === 'development') {
    runKPIIntegrationTests().then(results => {
      console.group('🧪 KPI集成测试结果')
      results.forEach(suite => {
        console.log(`${suite.name}: ${suite.passedTests}/${suite.totalTests} passed`)
      })
      console.groupEnd()
    })
  }
}

export default KPIIntegrationTest
