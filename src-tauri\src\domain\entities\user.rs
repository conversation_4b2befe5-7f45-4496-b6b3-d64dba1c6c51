// User Entity - 用户实体
// 用户聚合根

use crate::shared::types::{Id, Metadata, EntityStatus};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct User {
    pub id: Id,
    pub username: String,
    pub email: Option<String>,
    pub display_name: Option<String>,
    pub avatar_url: Option<String>,
    pub preferences: UserPreferences,
    pub status: EntityStatus,
    pub metadata: Metadata,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UserPreferences {
    pub theme: String,
    pub language: String,
    pub timezone: String,
    pub date_format: String,
    pub time_format: String,
    pub notifications_enabled: bool,
    pub auto_save_interval: u32,
}

impl Default for UserPreferences {
    fn default() -> Self {
        Self {
            theme: "system".to_string(),
            language: "zh-CN".to_string(),
            timezone: "Asia/Shanghai".to_string(),
            date_format: "YYYY-MM-DD".to_string(),
            time_format: "24h".to_string(),
            notifications_enabled: true,
            auto_save_interval: 30,
        }
    }
}

impl User {
    pub fn new(username: String) -> Self {
        Self {
            id: crate::shared::utils::IdGenerator::new_id_with_prefix("user"),
            username,
            email: None,
            display_name: None,
            avatar_url: None,
            preferences: UserPreferences::default(),
            status: EntityStatus::Active,
            metadata: Metadata::default(),
        }
    }

    pub fn update_preferences(&mut self, preferences: UserPreferences) {
        self.preferences = preferences;
        self.metadata.updated_at = chrono::Utc::now();
        self.metadata.version += 1;
    }

    pub fn update_profile(&mut self, display_name: Option<String>, email: Option<String>) {
        self.display_name = display_name;
        self.email = email;
        self.metadata.updated_at = chrono::Utc::now();
        self.metadata.version += 1;
    }
}
