// Resource Value Objects - 资源相关值对象

use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub struct ResourceTitle {
    pub value: String,
}

impl ResourceTitle {
    pub fn new(title: String) -> Result<Self, String> {
        if !title.trim().is_empty() && title.len() <= 200 {
            Ok(Self { value: title })
        } else {
            Err("Resource title cannot be empty and must be less than 200 characters".to_string())
        }
    }
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct ResourceContent {
    pub value: String,
}

impl ResourceContent {
    pub fn new(content: String) -> Result<Self, String> {
        if content.len() <= 100000 {
            Ok(Self { value: content })
        } else {
            Err("Resource content must be less than 100000 characters".to_string())
        }
    }
}
