// Project Domain Service - 项目领域服务

use crate::domain::entities::{Project, ProjectStatus};
use crate::shared::errors::Result;

pub struct ProjectDomainService;

impl ProjectDomainService {
    pub fn new() -> Self {
        Self
    }

    pub fn validate_project_name(&self, name: &str) -> Result<()> {
        crate::shared::utils::validate_required(name, "project name")?;
        crate::shared::utils::validate_length(name, "project name", 1, 200)?;
        Ok(())
    }

    pub fn validate_date_range(&self, project: &Project) -> Result<()> {
        if let (Some(start), Some(end)) = (project.start_date, project.due_date) {
            crate::shared::utils::validate_date_range(start, end)?;
        }
        Ok(())
    }

    pub fn can_complete_project(&self, project: &Project) -> bool {
        project.status != ProjectStatus::Completed && project.status != ProjectStatus::Cancelled
    }

    pub fn calculate_progress_from_tasks(&self, _project: &Project, _completed_tasks: u32, _total_tasks: u32) -> f32 {
        // Business logic for calculating project progress based on tasks
        // This would typically involve more complex calculations
        0.0
    }

    pub fn is_at_risk(&self, project: &Project) -> bool {
        if let Some(due_date) = project.due_date {
            let days_remaining = (due_date - chrono::Utc::now()).num_days();
            days_remaining <= 7 && project.progress < 0.8
        } else {
            false
        }
    }
}

impl Default for ProjectDomainService {
    fn default() -> Self {
        Self::new()
    }
}
