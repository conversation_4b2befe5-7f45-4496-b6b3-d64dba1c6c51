// Test Configuration - 测试配置
// 为测试提供通用的配置和工具函数

use crate::infrastructure::config::{AppConfig, DatabaseConfig};
use std::env;

/// 创建测试用的应用配置
pub fn create_test_config() -> AppConfig {
    AppConfig {
        database: DatabaseConfig {
            url: ":memory:".to_string(), // 使用内存数据库进行测试
            max_connections: 5,
            connection_timeout: 30,
        },
        app_name: "BubbleSay Test".to_string(),
        version: "0.1.0-test".to_string(),
        environment: "test".to_string(),
    }
}

/// 测试工具函数
pub struct TestUtils;

impl TestUtils {
    /// 生成测试用的随机字符串
    pub fn random_string(length: usize) -> String {
        use rand::Rng;
        const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ\
                                abcdefghijklmnopqrstuvwxyz\
                                0123456789";
        let mut rng = rand::thread_rng();
        
        (0..length)
            .map(|_| {
                let idx = rng.gen_range(0..CHARSET.len());
                CHARSET[idx] as char
            })
            .collect()
    }

    /// 生成测试用的邮箱地址
    pub fn random_email() -> String {
        format!("test_{}@example.com", Self::random_string(8))
    }

    /// 生成测试用的用户名
    pub fn random_username() -> String {
        format!("user_{}", Self::random_string(6))
    }

    /// 生成测试用的项目名称
    pub fn random_project_name() -> String {
        format!("Project {}", Self::random_string(8))
    }

    /// 生成测试用的任务标题
    pub fn random_task_title() -> String {
        format!("Task {}", Self::random_string(8))
    }

    /// 生成测试用的领域名称
    pub fn random_area_name() -> String {
        format!("Area {}", Self::random_string(8))
    }

    /// 等待异步操作完成的辅助函数
    pub async fn wait_for_condition<F, Fut>(mut condition: F, timeout_ms: u64) -> bool
    where
        F: FnMut() -> Fut,
        Fut: std::future::Future<Output = bool>,
    {
        let start = std::time::Instant::now();
        let timeout = std::time::Duration::from_millis(timeout_ms);

        while start.elapsed() < timeout {
            if condition().await {
                return true;
            }
            tokio::time::sleep(std::time::Duration::from_millis(10)).await;
        }
        false
    }
}

/// 测试断言宏
#[macro_export]
macro_rules! assert_error_type {
    ($result:expr, $error_type:pat) => {
        match $result {
            Err($error_type) => {},
            Ok(_) => panic!("Expected error, but got Ok"),
            Err(e) => panic!("Expected specific error type, but got: {:?}", e),
        }
    };
}

/// 测试数据构建器
pub struct TestDataBuilder;

impl TestDataBuilder {
    /// 创建测试用户数据
    pub fn user() -> UserTestDataBuilder {
        UserTestDataBuilder::new()
    }

    /// 创建测试项目数据
    pub fn project() -> ProjectTestDataBuilder {
        ProjectTestDataBuilder::new()
    }

    /// 创建测试任务数据
    pub fn task() -> TaskTestDataBuilder {
        TaskTestDataBuilder::new()
    }

    /// 创建测试领域数据
    pub fn area() -> AreaTestDataBuilder {
        AreaTestDataBuilder::new()
    }
}

/// 用户测试数据构建器
pub struct UserTestDataBuilder {
    username: Option<String>,
    email: Option<String>,
    display_name: Option<String>,
}

impl UserTestDataBuilder {
    pub fn new() -> Self {
        Self {
            username: None,
            email: None,
            display_name: None,
        }
    }

    pub fn with_username(mut self, username: String) -> Self {
        self.username = Some(username);
        self
    }

    pub fn with_email(mut self, email: String) -> Self {
        self.email = Some(email);
        self
    }

    pub fn with_display_name(mut self, display_name: String) -> Self {
        self.display_name = Some(display_name);
        self
    }

    pub fn build(self) -> (String, Option<String>) {
        let username = self.username.unwrap_or_else(|| TestUtils::random_username());
        let email = self.email.or_else(|| Some(TestUtils::random_email()));
        (username, email)
    }
}

/// 项目测试数据构建器
pub struct ProjectTestDataBuilder {
    name: Option<String>,
    description: Option<String>,
}

impl ProjectTestDataBuilder {
    pub fn new() -> Self {
        Self {
            name: None,
            description: None,
        }
    }

    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }

    pub fn build(self) -> (String, Option<String>) {
        let name = self.name.unwrap_or_else(|| TestUtils::random_project_name());
        let description = self.description;
        (name, description)
    }
}

/// 任务测试数据构建器
pub struct TaskTestDataBuilder {
    title: Option<String>,
    description: Option<String>,
}

impl TaskTestDataBuilder {
    pub fn new() -> Self {
        Self {
            title: None,
            description: None,
        }
    }

    pub fn with_title(mut self, title: String) -> Self {
        self.title = Some(title);
        self
    }

    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }

    pub fn build(self) -> (String, Option<String>) {
        let title = self.title.unwrap_or_else(|| TestUtils::random_task_title());
        let description = self.description;
        (title, description)
    }
}

/// 领域测试数据构建器
pub struct AreaTestDataBuilder {
    name: Option<String>,
    description: Option<String>,
}

impl AreaTestDataBuilder {
    pub fn new() -> Self {
        Self {
            name: None,
            description: None,
        }
    }

    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }

    pub fn build(self) -> (String, Option<String>) {
        let name = self.name.unwrap_or_else(|| TestUtils::random_area_name());
        let description = self.description;
        (name, description)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_random_string_generation() {
        let s1 = TestUtils::random_string(10);
        let s2 = TestUtils::random_string(10);
        
        assert_eq!(s1.len(), 10);
        assert_eq!(s2.len(), 10);
        assert_ne!(s1, s2); // 应该生成不同的字符串
    }

    #[test]
    fn test_random_email_generation() {
        let email = TestUtils::random_email();
        assert!(email.contains("@example.com"));
        assert!(email.starts_with("test_"));
    }

    #[test]
    fn test_data_builders() {
        let (username, email) = TestDataBuilder::user()
            .with_username("testuser".to_string())
            .with_email("<EMAIL>".to_string())
            .build();
        
        assert_eq!(username, "testuser");
        assert_eq!(email, Some("<EMAIL>".to_string()));
    }
}
