/**
 * KPI组件集成测试
 * 用于验证组件功能和集成效果
 */

import { createSignal, onMount, For } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card'
import { <PERSON><PERSON> } from '../../ui/button'
import { Badge } from '../../ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../ui/tabs'
import { 
  Target, 
  CheckCircle, 
  AlertCircle, 
  Play, 
  RotateCcw,
  Settings
} from 'lucide-react'

// 导入KPI组件
import { 
  KPITracker,
  KPIChart,
  KPIInput,
  KPIDialog,
  createKPIDataSource,
  PROJECT_KPI_CONFIG,
  AREA_METRIC_CONFIG,
  type BaseKPI,
  type KPIEventHandlers
} from '../index'

// 模拟数据
const mockProjectKPIs: BaseKPI[] = [
  {
    id: '1',
    name: 'Bug修复数',
    description: '本周修复的bug数量',
    value: 8,
    target: 10,
    unit: '个',
    direction: 'increase',
    frequency: 'weekly',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: '2',
    name: '响应时间',
    description: 'API平均响应时间',
    value: 150,
    target: 200,
    unit: 'ms',
    direction: 'decrease',
    frequency: 'daily',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: '3',
    name: '代码覆盖率',
    description: '单元测试代码覆盖率',
    value: 85,
    target: 80,
    unit: '%',
    direction: 'increase',
    frequency: 'weekly',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  }
]

const mockAreaMetrics: BaseKPI[] = [
  {
    id: '4',
    name: '运动时长',
    description: '每日运动时间',
    value: 45,
    target: 30,
    unit: 'min',
    direction: 'increase',
    frequency: 'daily',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  },
  {
    id: '5',
    name: '体重',
    description: '每日体重记录',
    value: 68,
    target: 65,
    unit: 'kg',
    direction: 'decrease',
    frequency: 'daily',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date()
  }
]

// 测试项目
interface TestCase {
  id: string
  name: string
  description: string
  status: 'pending' | 'running' | 'passed' | 'failed'
  result?: string
}

export function IntegrationTest() {
  // 状态管理
  const [activeTab, setActiveTab] = createSignal('project')
  const [testCases, setTestCases] = createSignal<TestCase[]>([
    {
      id: 'data-loading',
      name: '数据加载测试',
      description: '验证KPI数据能够正确加载和显示',
      status: 'pending'
    },
    {
      id: 'chart-rendering',
      name: '图表渲染测试',
      description: '验证图表组件能够正确渲染KPI数据',
      status: 'pending'
    },
    {
      id: 'input-interaction',
      name: '输入交互测试',
      description: '验证KPI输入组件的交互功能',
      status: 'pending'
    },
    {
      id: 'dialog-functionality',
      name: '对话框功能测试',
      description: '验证创建/编辑对话框的功能',
      status: 'pending'
    },
    {
      id: 'event-handling',
      name: '事件处理测试',
      description: '验证事件处理器的正确执行',
      status: 'pending'
    }
  ])

  const [showCreateDialog, setShowCreateDialog] = createSignal(false)
  const [eventLog, setEventLog] = createSignal<string[]>([])

  // 创建数据源
  const projectDataSource = createKPIDataSource('project')
  const areaDataSource = createKPIDataSource('area')

  // 事件处理器（用于测试）
  const createEventHandlers = (type: string): KPIEventHandlers => ({
    onCreate: (kpi) => {
      addEventLog(`[${type}] KPI Created: ${kpi.name}`)
      updateTestStatus('event-handling', 'passed')
    },
    onUpdate: (kpi, changes) => {
      addEventLog(`[${type}] KPI Updated: ${kpi.name}`)
    },
    onDelete: (kpiId) => {
      addEventLog(`[${type}] KPI Deleted: ${kpiId}`)
    },
    onRecord: (record) => {
      addEventLog(`[${type}] Record Created: ${record.value}`)
    },
    onProgressChange: (kpiId, progress) => {
      addEventLog(`[${type}] Progress Changed: ${progress.progress}%`)
    },
    onStatusChange: (kpiId, oldStatus, newStatus) => {
      addEventLog(`[${type}] Status Changed: ${oldStatus} -> ${newStatus}`)
    },
    onError: (error, context) => {
      addEventLog(`[${type}] Error: ${error.message} (${context})`)
      updateTestStatus('event-handling', 'failed', error.message)
    }
  })

  // 添加事件日志
  const addEventLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setEventLog(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)])
  }

  // 更新测试状态
  const updateTestStatus = (testId: string, status: TestCase['status'], result?: string) => {
    setTestCases(prev => prev.map(test => 
      test.id === testId 
        ? { ...test, status, result }
        : test
    ))
  }

  // 运行测试
  const runTest = async (testId: string) => {
    updateTestStatus(testId, 'running')
    
    try {
      switch (testId) {
        case 'data-loading':
          // 模拟数据加载测试
          await new Promise(resolve => setTimeout(resolve, 1000))
          updateTestStatus(testId, 'passed', 'Mock data loaded successfully')
          break
          
        case 'chart-rendering':
          // 检查图表是否渲染
          const chartElements = document.querySelectorAll('[data-testid="kpi-chart"]')
          if (chartElements.length > 0) {
            updateTestStatus(testId, 'passed', 'Chart rendered successfully')
          } else {
            updateTestStatus(testId, 'failed', 'Chart not found')
          }
          break
          
        case 'input-interaction':
          // 模拟输入交互测试
          await new Promise(resolve => setTimeout(resolve, 500))
          updateTestStatus(testId, 'passed', 'Input interactions working')
          break
          
        case 'dialog-functionality':
          // 测试对话框
          setShowCreateDialog(true)
          await new Promise(resolve => setTimeout(resolve, 100))
          setShowCreateDialog(false)
          updateTestStatus(testId, 'passed', 'Dialog functionality working')
          break
          
        case 'event-handling':
          // 事件处理测试在事件处理器中完成
          updateTestStatus(testId, 'running', 'Waiting for events...')
          break
          
        default:
          updateTestStatus(testId, 'failed', 'Unknown test')
      }
    } catch (error) {
      updateTestStatus(testId, 'failed', error instanceof Error ? error.message : 'Unknown error')
    }
  }

  // 运行所有测试
  const runAllTests = async () => {
    for (const test of testCases()) {
      if (test.id !== 'event-handling') {
        await runTest(test.id)
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }
  }

  // 重置测试
  const resetTests = () => {
    setTestCases(prev => prev.map(test => ({ ...test, status: 'pending', result: undefined })))
    setEventLog([])
  }

  // 获取测试状态图标
  const getStatusIcon = (status: TestCase['status']) => {
    switch (status) {
      case 'passed': return <CheckCircle class="h-4 w-4 text-green-500" />
      case 'failed': return <AlertCircle class="h-4 w-4 text-red-500" />
      case 'running': return <div class="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
      default: return <div class="h-4 w-4 border-2 border-gray-300 rounded-full" />
    }
  }

  return (
    <div class="space-y-6">
      {/* 测试控制面板 */}
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <div>
              <CardTitle class="flex items-center gap-2">
                <Target class="h-5 w-5" />
                KPI组件集成测试
              </CardTitle>
              <CardDescription>
                验证KPI组件的功能和集成效果
              </CardDescription>
            </div>
            <div class="flex gap-2">
              <Button onClick={runAllTests} size="sm">
                <Play class="h-4 w-4 mr-2" />
                运行所有测试
              </Button>
              <Button onClick={resetTests} variant="outline" size="sm">
                <RotateCcw class="h-4 w-4 mr-2" />
                重置
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <For each={testCases()}>
              {(test) => (
                <div class="flex items-center justify-between p-3 border rounded-lg">
                  <div class="flex items-center gap-3">
                    {getStatusIcon(test.status)}
                    <div>
                      <div class="font-medium">{test.name}</div>
                      <div class="text-sm text-muted-foreground">{test.description}</div>
                      {test.result && (
                        <div class="text-xs mt-1">
                          <Badge variant={test.status === 'passed' ? 'default' : 'destructive'}>
                            {test.result}
                          </Badge>
                        </div>
                      )}
                    </div>
                  </div>
                  <Button 
                    onClick={() => runTest(test.id)} 
                    size="sm" 
                    variant="outline"
                    disabled={test.status === 'running'}
                  >
                    运行
                  </Button>
                </div>
              )}
            </For>
          </div>
        </CardContent>
      </Card>

      {/* 组件测试区域 */}
      <Tabs value={activeTab()} onValueChange={setActiveTab}>
        <TabsList class="grid w-full grid-cols-3">
          <TabsTrigger value="project">项目KPI</TabsTrigger>
          <TabsTrigger value="area">领域指标</TabsTrigger>
          <TabsTrigger value="events">事件日志</TabsTrigger>
        </TabsList>

        <TabsContent value="project" class="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>项目KPI测试</CardTitle>
              <CardDescription>测试项目KPI组件的功能</CardDescription>
            </CardHeader>
            <CardContent>
              <KPIChart 
                kpis={mockProjectKPIs} 
                type="bar"
                data-testid="kpi-chart"
              />
            </CardContent>
          </Card>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <For each={mockProjectKPIs}>
              {(kpi) => (
                <KPIInput
                  kpi={kpi}
                  onRecord={(record) => addEventLog(`Record: ${record.value}`)}
                  onEdit={(kpi) => addEventLog(`Edit: ${kpi.name}`)}
                  allowEdit={true}
                  allowDelete={true}
                />
              )}
            </For>
          </div>
        </TabsContent>

        <TabsContent value="area" class="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>领域指标测试</CardTitle>
              <CardDescription>测试领域指标组件的功能</CardDescription>
            </CardHeader>
            <CardContent>
              <KPIChart 
                kpis={mockAreaMetrics} 
                type="donut"
              />
            </CardContent>
          </Card>
          
          <div class="space-y-4">
            <For each={mockAreaMetrics}>
              {(metric) => (
                <KPIInput
                  kpi={metric}
                  onRecord={(record) => addEventLog(`Record: ${record.value}`)}
                  onEdit={(kpi) => addEventLog(`Edit: ${kpi.name}`)}
                  showHistory={true}
                  allowEdit={true}
                />
              )}
            </For>
          </div>
        </TabsContent>

        <TabsContent value="events" class="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>事件日志</CardTitle>
              <CardDescription>查看组件事件的执行情况</CardDescription>
            </CardHeader>
            <CardContent>
              <div class="space-y-2 max-h-96 overflow-y-auto">
                <For each={eventLog()}>
                  {(log) => (
                    <div class="text-sm font-mono p-2 bg-gray-50 rounded">
                      {log}
                    </div>
                  )}
                </For>
                {eventLog().length === 0 && (
                  <div class="text-center text-muted-foreground py-8">
                    暂无事件日志
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 测试对话框 */}
      <KPIDialog
        open={showCreateDialog()}
        mode="create"
        config={PROJECT_KPI_CONFIG}
        onSubmit={async (data) => {
          addEventLog(`Dialog Submit: ${data.name}`)
          setShowCreateDialog(false)
        }}
        onCancel={() => setShowCreateDialog(false)}
      />
    </div>
  )
}

export default IntegrationTest
