import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
// 移除未使用的 shared 组件导入
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs'
import { useLanguage } from '../contexts/LanguageContext'
import { useUIStore } from '../store/uiStore'
import { useProjectStore } from '../store/projectStore'
import { useAreaStore } from '../store/areaStore'
import { databaseApi } from '../lib/api'
import ArchiveItem, {
  type ArchiveItem as ArchiveItemType
} from '../components/features/ArchiveItem'

export function ArchivePage() {
  const [items, setItems] = useState<ArchiveItemType[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState<string>('all')
  const [filterReason, setFilterReason] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('archivedAt')
  const [isLoading, setIsLoading] = useState(true)
  const { t } = useLanguage()
  const { addNotification } = useUIStore()
  const { fetchProjects } = useProjectStore()
  const { fetchAreas } = useAreaStore()
  const navigate = useNavigate()

  // {{ AURA-X: Add - 通用的刷新归档数据函数. Approval: 寸止(ID:归档修复). }}
  const refreshArchivedItems = async () => {
    setIsLoading(true)
    try {
      const [projectsResult, areasResult] = await Promise.all([
        databaseApi.getArchivedProjects(),
        databaseApi.getArchivedAreas()
      ])

      const archivedItems: ArchiveItemType[] = []

      // 处理归档项目
      if (projectsResult.success && projectsResult.data) {
        const projectItems: ArchiveItemType[] = projectsResult.data.map((project: any) => ({
          id: project.id,
          title: project.name,
          description: project.description || '',
          type: 'project' as const,
          originalStatus: project.status || 'Unknown',
          archivedAt: project.updatedAt,
          archivedReason: 'manual' as const,
          tags: [],
          metadata: {
            originalId: project.id,
            lastActivity: project.updatedAt
          }
        }))
        archivedItems.push(...projectItems)
      }

      // 处理归档领域
      if (areasResult.success && areasResult.data) {
        const areaItems: ArchiveItemType[] = areasResult.data.map((area: any) => ({
          id: area.id,
          title: area.name,
          description: area.description || '',
          type: 'area' as const,
          originalStatus: area.status || 'Unknown',
          archivedAt: area.updatedAt,
          archivedReason: 'manual' as const,
          tags: [],
          metadata: {
            originalId: area.id,
            lastActivity: area.updatedAt
          }
        }))
        archivedItems.push(...areaItems)
      }

      // 按归档时间排序
      archivedItems.sort((a, b) => new Date(b.archivedAt).getTime() - new Date(a.archivedAt).getTime())
      setItems(archivedItems)
    } catch (error) {
      console.error('Failed to fetch archived items:', error)
      addNotification({
        type: 'error',
        title: 'Failed to load archived items',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 获取归档数据
  useEffect(() => {
    refreshArchivedItems()
  }, [])

  const handleRestore = async (item: ArchiveItemType) => {
    try {
      if (item.type === 'project') {
        // 先更新 store，避免恢复瞬间列表“丢失”
        await useProjectStore.getState().restoreProject(item.id)
      } else if (item.type === 'area') {
        // 领域也先本地标记再刷新
        useAreaStore.getState().updateArea(item.id, { archived: false, updatedAt: new Date() as any })
        const result = await databaseApi.restoreArea(item.id)
        if (!result.success) {
          throw new Error(result.error || 'Failed to restore area')
        }
      } else {
        console.warn('Resource restoration not implemented yet')
        return
      }

      // 恢复成功后统一刷新归档与主列表
      await refreshArchivedItems()
      if (item.type === 'project') {
        await fetchProjects()
      } else if (item.type === 'area') {
        await fetchAreas()
      }

      addNotification({
        type: 'success',
        title: t('pages.archive.restore'),
        message: `"${item.title}" ${t('pages.archive.restore')} successfully`
      })
      console.log(`${item.type} restored successfully:`, item.title)

      // 体验优化：自动导航到详情页
      if (item.type === 'project') {
        navigate(`/projects/${item.id}`)
      } else if (item.type === 'area') {
        navigate(`/areas/${item.id}`)
      }
    } catch (error) {
      console.error('Error restoring item:', error)
      addNotification({
        type: 'error',
        title: 'Restore Failed',
        message: 'An error occurred while restoring the item'
      })
    }
  }

  const handleDelete = async (itemId: string) => {
    try {
      const item = items.find(i => i.id === itemId)
      if (!item) return

      let result
      if (item.type === 'project') {
        result = await databaseApi.deleteProject(itemId)
      } else if (item.type === 'area') {
        result = await databaseApi.deleteArea(itemId)
      } else {
        console.warn('Resource deletion not implemented yet')
        return
      }

      if (result.success) {
        // {{ AURA-X: Modify - 删除成功后重新获取归档数据. Approval: 寸止(ID:归档修复). }}
        await refreshArchivedItems()

        addNotification({
          type: 'success',
          title: t('pages.archive.deletePermanently'),
          message: `"${item.title}" has been deleted permanently`
        })
        console.log(`${item.type} deleted permanently:`, item.title)
      } else {
        addNotification({
          type: 'error',
          title: `Failed to delete ${item.type}`,
          message: result.error || 'Unknown error occurred'
        })
        console.error(`Failed to delete ${item.type}:`, result.error)
      }
    } catch (error) {
      console.error('Error deleting item:', error)
    }
  }

  const handleViewDetails = (item: ArchiveItemType) => {
    // 导航到对应的详情页面，并传递archived状态
    if (item.type === 'project') {
      navigate(`/projects/${item.id}`, { state: { archived: true } })
    } else if (item.type === 'area') {
      navigate(`/areas/${item.id}`, { state: { archived: true } })
    }
  }

  const handleView = (item: ArchiveItemType) => {
    console.log('View item:', item)
    // In real implementation, this would open a detailed view
  }

  // Filter and sort items
  const filteredItems = items
    .filter((item) => {
      const matchesSearch =
        !searchQuery ||
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))

      const matchesType = filterType === 'all' || item.type === filterType
      const matchesReason = filterReason === 'all' || item.archivedReason === filterReason

      return matchesSearch && matchesType && matchesReason
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'title':
          return a.title.localeCompare(b.title)
        case 'type':
          return a.type.localeCompare(b.type)
        case 'archivedAt':
        default:
          return new Date(b.archivedAt).getTime() - new Date(a.archivedAt).getTime()
      }
    })

  const getItemCounts = () => {
    const projects = items.filter((item) => item.type === 'project').length
    const areas = items.filter((item) => item.type === 'area').length
    const resources = items.filter((item) => item.type === 'resource').length
    return { projects, areas, resources, total: items.length }
  }

  const counts = getItemCounts()

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 统一移除页面大标题与描述，依赖顶部面包屑与局部控件 */}

      {/* Archive Categories */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge className="para-project">P</Badge>
              {t('pages.archive.archivedProjects')}
            </CardTitle>
            <CardDescription>{t('pages.archive.completedOrCancelled')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{counts.projects}</div>
            <p className="text-xs text-muted-foreground">
              {counts.projects > 0
                ? t('pages.archive.lastArchivedRecently')
                : t('pages.archive.noArchivedItems')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge className="para-area">A</Badge>
              {t('pages.archive.archivedAreas')}
            </CardTitle>
            <CardDescription>{t('pages.archive.noLongerMaintained')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{counts.areas}</div>
            <p className="text-xs text-muted-foreground">
              {counts.areas > 0
                ? t('pages.archive.lastArchivedRecently')
                : t('pages.archive.noArchivedItems')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge className="para-resource">R</Badge>
              {t('pages.archive.archivedResources')}
            </CardTitle>
            <CardDescription>{t('pages.archive.noLongerRelevant')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{counts.resources}</div>
            <p className="text-xs text-muted-foreground">
              {counts.resources > 0
                ? t('pages.archive.lastArchivedRecently')
                : t('pages.archive.noArchivedItems')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder={t('pages.archive.searchPlaceholder')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>

        <div className="flex gap-2">
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder={t('pages.archive.filters.type')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('pages.inbox.filters.type.allTypes')}</SelectItem>
              <SelectItem value="project">📋 {t('enums.types.project')}</SelectItem>
              <SelectItem value="area">🏠 {t('enums.types.area')}</SelectItem>
              <SelectItem value="resource">📄 {t('enums.types.resource')}</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filterReason} onValueChange={setFilterReason}>
            <SelectTrigger className="w-36">
              <SelectValue placeholder={t('pages.archive.filters.reason')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('pages.archive.filters.reasonOptions.all')}</SelectItem>
              <SelectItem value="completed">{t('pages.archive.filters.reasonOptions.completed')}</SelectItem>
              <SelectItem value="cancelled">{t('pages.archive.filters.reasonOptions.cancelled')}</SelectItem>
              <SelectItem value="inactive">{t('pages.archive.filters.reasonOptions.inactive')}</SelectItem>
              <SelectItem value="outdated">{t('pages.archive.filters.reasonOptions.outdated')}</SelectItem>
              <SelectItem value="manual">{t('pages.archive.filters.reasonOptions.manual')}</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder={t('pages.archive.filters.sort')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="archivedAt">{t('pages.archive.filters.sortBy.dateArchived')}</SelectItem>
              <SelectItem value="title">{t('pages.archive.filters.sortBy.title')}</SelectItem>
              <SelectItem value="type">{t('pages.archive.filters.sortBy.type')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Archive Items */}
      <Tabs defaultValue="items" className="space-y-4">
        <TabsList>
          <TabsTrigger value="items">{t('pages.archive.tabs.items')}</TabsTrigger>
        </TabsList>

        <TabsContent value="items" className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex items-center gap-2 text-muted-foreground">
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                <span>{t('common.loading')}</span>
              </div>
            </div>
          ) : filteredItems.length === 0 ? (
            <div className="text-center py-12">
              {items.length === 0 ? (
                <div className="text-muted-foreground">
                  <div className="text-4xl mb-2">📦</div>
                  <p className="text-sm">{t('pages.archive.empty.noArchivedItemsYet')}</p>
                  <p className="text-xs mt-1">{t('pages.archive.empty.willAppearWhenArchived')}</p>
                </div>
              ) : (
                <div className="text-muted-foreground">
                  <div className="text-4xl mb-2">🔍</div>
                  <p className="text-sm">{t('components.emptyStates.noItemsFound')}</p>
                  <p className="text-xs mt-1">{t('components.emptyStates.tryAdjustingFilters')}</p>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {filteredItems.map((item) => (
                <ArchiveItem
                  key={item.id}
                  item={item}
                  onRestore={handleRestore}
                  onDelete={handleDelete}
                  onView={handleViewDetails}
                />
              ))}
            </div>
          )}
        </TabsContent>


      </Tabs>
    </div>
  )
}

export default ArchivePage
