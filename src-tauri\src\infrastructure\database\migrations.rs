// Database Migrations - 数据库迁移

use crate::shared::errors::{AppError, Result};
use sqlx::{Pool, Sqlite};

pub struct MigrationManager {
    pool: Pool<Sqlite>,
}

impl MigrationManager {
    pub fn new(pool: Pool<Sqlite>) -> Self {
        Self { pool }
    }

    pub async fn run_migrations(&self) -> Result<()> {
        // Create migrations table if it doesn't exist
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS migrations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                version TEXT NOT NULL UNIQUE,
                applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to create migrations table: {}", e)))?;

        // Run initial schema migration
        self.run_initial_schema().await?;

        Ok(())
    }

    async fn run_initial_schema(&self) -> Result<()> {
        let version = "001_initial_schema";
        
        // Check if migration already applied
        let exists = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM migrations WHERE version = ?",
        )
        .bind(version)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to check migration: {}", e)))?;

        if exists > 0 {
            return Ok(());
        }

        // Create initial tables
        self.create_users_table().await?;
        self.create_projects_table().await?;
        self.create_tasks_table().await?;
        self.create_areas_table().await?;
        self.create_habits_table().await?;
        self.create_resources_table().await?;
        self.create_inbox_table().await?;
        self.create_reviews_table().await?;
        self.create_notifications_table().await?;
        self.create_checklists_table().await?;
        self.create_templates_table().await?;
        self.create_files_table().await?;

        // Mark migration as applied
        sqlx::query("INSERT INTO migrations (version) VALUES (?)")
            .bind(version)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to record migration: {}", e)))?;

        Ok(())
    }

    async fn create_users_table(&self) -> Result<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                username TEXT NOT NULL UNIQUE,
                email TEXT UNIQUE,
                display_name TEXT,
                avatar_url TEXT,
                preferences TEXT NOT NULL DEFAULT '{}',
                status TEXT NOT NULL DEFAULT 'active',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                version INTEGER NOT NULL DEFAULT 1,

                CONSTRAINT chk_users_status CHECK (status IN ('active', 'inactive', 'deleted', 'archived')),
                CONSTRAINT chk_users_username_length CHECK (length(username) >= 3 AND length(username) <= 50),
                CONSTRAINT chk_users_email_format CHECK (email IS NULL OR email LIKE '%@%.%')
            )
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to create users table: {}", e)))?;

        // Create indexes
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create users username index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email) WHERE email IS NOT NULL")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create users email index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create users status index: {}", e)))?;

        Ok(())
    }

    async fn create_projects_table(&self) -> Result<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS projects (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                goals TEXT DEFAULT '[]',
                deliverables TEXT DEFAULT '[]',
                start_date DATETIME,
                due_date DATETIME,
                area_id TEXT,
                status TEXT NOT NULL DEFAULT 'not_started',
                priority TEXT NOT NULL DEFAULT 'medium',
                progress REAL NOT NULL DEFAULT 0.0,
                tags TEXT DEFAULT '[]',
                entity_status TEXT NOT NULL DEFAULT 'active',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                version INTEGER NOT NULL DEFAULT 1,

                CONSTRAINT chk_projects_status CHECK (status IN ('not_started', 'in_progress', 'at_risk', 'on_hold', 'completed', 'cancelled')),
                CONSTRAINT chk_projects_priority CHECK (priority IN ('low', 'medium', 'high', 'critical')),
                CONSTRAINT chk_projects_entity_status CHECK (entity_status IN ('active', 'inactive', 'deleted', 'archived')),
                CONSTRAINT chk_projects_progress CHECK (progress >= 0.0 AND progress <= 1.0),
                CONSTRAINT chk_projects_name_length CHECK (length(name) >= 1 AND length(name) <= 200),
                CONSTRAINT chk_projects_date_order CHECK (start_date IS NULL OR due_date IS NULL OR start_date <= due_date),
                FOREIGN KEY (area_id) REFERENCES areas(id) ON DELETE SET NULL
            )
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to create projects table: {}", e)))?;

        // Create indexes
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_projects_name ON projects(name)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create projects name index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_projects_area_id ON projects(area_id) WHERE area_id IS NOT NULL")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create projects area_id index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create projects status index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_projects_priority ON projects(priority)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create projects priority index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_projects_due_date ON projects(due_date) WHERE due_date IS NOT NULL")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create projects due_date index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_projects_entity_status ON projects(entity_status)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create projects entity_status index: {}", e)))?;

        Ok(())
    }

    async fn create_tasks_table(&self) -> Result<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS tasks (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                description TEXT,
                project_id TEXT,
                parent_task_id TEXT,
                due_date DATETIME,
                status TEXT NOT NULL DEFAULT 'todo',
                priority TEXT NOT NULL DEFAULT 'medium',
                progress REAL NOT NULL DEFAULT 0.0,
                entity_status TEXT NOT NULL DEFAULT 'active',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                version INTEGER NOT NULL DEFAULT 1,

                CONSTRAINT chk_tasks_status CHECK (status IN ('todo', 'in_progress', 'completed', 'cancelled')),
                CONSTRAINT chk_tasks_priority CHECK (priority IN ('low', 'medium', 'high', 'critical')),
                CONSTRAINT chk_tasks_entity_status CHECK (entity_status IN ('active', 'inactive', 'deleted', 'archived')),
                CONSTRAINT chk_tasks_progress CHECK (progress >= 0.0 AND progress <= 1.0),
                CONSTRAINT chk_tasks_title_length CHECK (length(title) >= 1 AND length(title) <= 200),
                FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
                FOREIGN KEY (parent_task_id) REFERENCES tasks(id) ON DELETE CASCADE
            )
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to create tasks table: {}", e)))?;

        // Create indexes
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_tasks_title ON tasks(title)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create tasks title index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON tasks(project_id) WHERE project_id IS NOT NULL")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create tasks project_id index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_tasks_parent_task_id ON tasks(parent_task_id) WHERE parent_task_id IS NOT NULL")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create tasks parent_task_id index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create tasks status index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks(priority)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create tasks priority index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks(due_date) WHERE due_date IS NOT NULL")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create tasks due_date index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_tasks_entity_status ON tasks(entity_status)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create tasks entity_status index: {}", e)))?;

        Ok(())
    }

    async fn create_areas_table(&self) -> Result<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS areas (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                standards TEXT DEFAULT '[]',
                color TEXT,
                icon TEXT,
                status TEXT NOT NULL DEFAULT 'active',
                entity_status TEXT NOT NULL DEFAULT 'active',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                version INTEGER NOT NULL DEFAULT 1,

                CONSTRAINT chk_areas_status CHECK (status IN ('active', 'maintenance', 'dormant')),
                CONSTRAINT chk_areas_entity_status CHECK (entity_status IN ('active', 'inactive', 'deleted', 'archived')),
                CONSTRAINT chk_areas_name_length CHECK (length(name) >= 1 AND length(name) <= 100),
                CONSTRAINT chk_areas_color_format CHECK (color IS NULL OR color LIKE '#______')
            )
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to create areas table: {}", e)))?;

        // Create indexes
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_areas_name ON areas(name)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create areas name index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_areas_status ON areas(status)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create areas status index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_areas_entity_status ON areas(entity_status)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create areas entity_status index: {}", e)))?;

        Ok(())
    }

    async fn create_habits_table(&self) -> Result<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS habits (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                area_id TEXT NOT NULL,
                habit_type TEXT NOT NULL DEFAULT 'boolean',
                frequency TEXT NOT NULL DEFAULT '{"Daily": null}',
                target_value REAL,
                unit TEXT,
                entity_status TEXT NOT NULL DEFAULT 'active',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                version INTEGER NOT NULL DEFAULT 1,

                CONSTRAINT chk_habits_type CHECK (habit_type IN ('boolean', 'numeric', 'duration')),
                CONSTRAINT chk_habits_entity_status CHECK (entity_status IN ('active', 'inactive', 'deleted', 'archived')),
                CONSTRAINT chk_habits_name_length CHECK (length(name) >= 1 AND length(name) <= 100),
                CONSTRAINT chk_habits_target_value CHECK (target_value IS NULL OR target_value > 0),
                FOREIGN KEY (area_id) REFERENCES areas(id) ON DELETE CASCADE
            )
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to create habits table: {}", e)))?;

        // Create indexes
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_habits_name ON habits(name)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create habits name index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_habits_area_id ON habits(area_id)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create habits area_id index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_habits_type ON habits(habit_type)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create habits type index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_habits_entity_status ON habits(entity_status)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create habits entity_status index: {}", e)))?;

        Ok(())
    }

    async fn create_resources_table(&self) -> Result<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS resources (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                content TEXT NOT NULL DEFAULT '',
                resource_type TEXT NOT NULL DEFAULT 'note',
                file_path TEXT,
                tags TEXT DEFAULT '[]',
                links TEXT DEFAULT '[]',
                entity_status TEXT NOT NULL DEFAULT 'active',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                version INTEGER NOT NULL DEFAULT 1,

                CONSTRAINT chk_resources_type CHECK (resource_type IN ('markdown', 'note', 'link', 'file')),
                CONSTRAINT chk_resources_entity_status CHECK (entity_status IN ('active', 'inactive', 'deleted', 'archived')),
                CONSTRAINT chk_resources_title_length CHECK (length(title) >= 1 AND length(title) <= 200),
                CONSTRAINT chk_resources_content_length CHECK (length(content) <= 100000)
            )
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to create resources table: {}", e)))?;

        // Create indexes
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_resources_title ON resources(title)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create resources title index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_resources_type ON resources(resource_type)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create resources type index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_resources_entity_status ON resources(entity_status)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create resources entity_status index: {}", e)))?;

        // Full-text search index for content
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_resources_content_fts ON resources(content)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create resources content index: {}", e)))?;

        Ok(())
    }

    async fn create_inbox_table(&self) -> Result<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS inbox_items (
                id TEXT PRIMARY KEY,
                content TEXT NOT NULL,
                item_type TEXT NOT NULL DEFAULT 'note',
                priority TEXT NOT NULL DEFAULT 'medium',
                tags TEXT DEFAULT '[]',
                processed BOOLEAN NOT NULL DEFAULT FALSE,
                processed_at DATETIME,
                entity_status TEXT NOT NULL DEFAULT 'active',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                version INTEGER NOT NULL DEFAULT 1,

                CONSTRAINT chk_inbox_type CHECK (item_type IN ('note', 'task', 'idea', 'link', 'file')),
                CONSTRAINT chk_inbox_priority CHECK (priority IN ('low', 'medium', 'high', 'critical')),
                CONSTRAINT chk_inbox_entity_status CHECK (entity_status IN ('active', 'inactive', 'deleted', 'archived')),
                CONSTRAINT chk_inbox_content_length CHECK (length(content) >= 1 AND length(content) <= 2000),
                CONSTRAINT chk_inbox_processed_at CHECK (processed = FALSE OR processed_at IS NOT NULL)
            )
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to create inbox table: {}", e)))?;

        // Create indexes
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_inbox_processed ON inbox_items(processed)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create inbox processed index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_inbox_type ON inbox_items(item_type)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create inbox type index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_inbox_priority ON inbox_items(priority)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create inbox priority index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_inbox_created_at ON inbox_items(created_at)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create inbox created_at index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_inbox_entity_status ON inbox_items(entity_status)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create inbox entity_status index: {}", e)))?;

        Ok(())
    }

    async fn create_reviews_table(&self) -> Result<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS reviews (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                content TEXT NOT NULL DEFAULT '',
                review_type TEXT NOT NULL DEFAULT 'weekly',
                period_start DATETIME NOT NULL,
                period_end DATETIME NOT NULL,
                template_id TEXT,
                entity_status TEXT NOT NULL DEFAULT 'active',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                version INTEGER NOT NULL DEFAULT 1,

                CONSTRAINT chk_reviews_type CHECK (review_type IN ('weekly', 'monthly', 'quarterly', 'yearly', 'custom')),
                CONSTRAINT chk_reviews_entity_status CHECK (entity_status IN ('active', 'inactive', 'deleted', 'archived')),
                CONSTRAINT chk_reviews_title_length CHECK (length(title) >= 1 AND length(title) <= 200),
                CONSTRAINT chk_reviews_period_order CHECK (period_start <= period_end),
                FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE SET NULL
            )
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to create reviews table: {}", e)))?;

        // Create indexes
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_reviews_title ON reviews(title)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create reviews title index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_reviews_type ON reviews(review_type)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create reviews type index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_reviews_period_start ON reviews(period_start)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create reviews period_start index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_reviews_period_end ON reviews(period_end)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create reviews period_end index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_reviews_template_id ON reviews(template_id) WHERE template_id IS NOT NULL")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create reviews template_id index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_reviews_entity_status ON reviews(entity_status)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create reviews entity_status index: {}", e)))?;

        Ok(())
    }

    async fn create_notifications_table(&self) -> Result<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS notifications (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                notification_type TEXT NOT NULL DEFAULT 'system',
                priority TEXT NOT NULL DEFAULT 'medium',
                read BOOLEAN NOT NULL DEFAULT FALSE,
                read_at DATETIME,
                scheduled_at DATETIME,
                entity_status TEXT NOT NULL DEFAULT 'active',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                version INTEGER NOT NULL DEFAULT 1,

                CONSTRAINT chk_notifications_type CHECK (notification_type IN ('task_due', 'project_deadline', 'habit_reminder', 'review_reminder', 'system')),
                CONSTRAINT chk_notifications_priority CHECK (priority IN ('low', 'medium', 'high', 'critical')),
                CONSTRAINT chk_notifications_entity_status CHECK (entity_status IN ('active', 'inactive', 'deleted', 'archived')),
                CONSTRAINT chk_notifications_title_length CHECK (length(title) >= 1 AND length(title) <= 100),
                CONSTRAINT chk_notifications_message_length CHECK (length(message) >= 1 AND length(message) <= 500),
                CONSTRAINT chk_notifications_read_at CHECK (read = FALSE OR read_at IS NOT NULL)
            )
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to create notifications table: {}", e)))?;

        // Create indexes
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create notifications read index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(notification_type)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create notifications type index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_notifications_priority ON notifications(priority)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create notifications priority index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_notifications_scheduled_at ON notifications(scheduled_at) WHERE scheduled_at IS NOT NULL")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create notifications scheduled_at index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create notifications created_at index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_notifications_entity_status ON notifications(entity_status)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create notifications entity_status index: {}", e)))?;

        Ok(())
    }

    async fn create_checklists_table(&self) -> Result<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS checklists (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                items TEXT NOT NULL DEFAULT '[]',
                entity_status TEXT NOT NULL DEFAULT 'active',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                version INTEGER NOT NULL DEFAULT 1,

                CONSTRAINT chk_checklists_entity_status CHECK (entity_status IN ('active', 'inactive', 'deleted', 'archived')),
                CONSTRAINT chk_checklists_name_length CHECK (length(name) >= 1 AND length(name) <= 200)
            )
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to create checklists table: {}", e)))?;

        // Create indexes
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_checklists_name ON checklists(name)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create checklists name index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_checklists_entity_status ON checklists(entity_status)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create checklists entity_status index: {}", e)))?;

        Ok(())
    }

    async fn create_templates_table(&self) -> Result<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS templates (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                template_type TEXT NOT NULL DEFAULT 'note',
                content TEXT NOT NULL DEFAULT '',
                variables TEXT NOT NULL DEFAULT '[]',
                entity_status TEXT NOT NULL DEFAULT 'active',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                version INTEGER NOT NULL DEFAULT 1,

                CONSTRAINT chk_templates_type CHECK (template_type IN ('project', 'review', 'checklist', 'note')),
                CONSTRAINT chk_templates_entity_status CHECK (entity_status IN ('active', 'inactive', 'deleted', 'archived')),
                CONSTRAINT chk_templates_name_length CHECK (length(name) >= 1 AND length(name) <= 200)
            )
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to create templates table: {}", e)))?;

        // Create indexes
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_templates_name ON templates(name)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create templates name index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_templates_type ON templates(template_type)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create templates type index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_templates_entity_status ON templates(entity_status)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create templates entity_status index: {}", e)))?;

        Ok(())
    }

    async fn create_files_table(&self) -> Result<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS files (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                path TEXT NOT NULL UNIQUE,
                file_type TEXT NOT NULL DEFAULT 'other',
                size INTEGER NOT NULL DEFAULT 0,
                mime_type TEXT,
                checksum TEXT,
                entity_status TEXT NOT NULL DEFAULT 'active',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                version INTEGER NOT NULL DEFAULT 1,

                CONSTRAINT chk_files_type CHECK (file_type IN ('document', 'image', 'audio', 'video', 'archive', 'other')),
                CONSTRAINT chk_files_entity_status CHECK (entity_status IN ('active', 'inactive', 'deleted', 'archived')),
                CONSTRAINT chk_files_name_length CHECK (length(name) >= 1 AND length(name) <= 255),
                CONSTRAINT chk_files_path_length CHECK (length(path) >= 1 AND length(path) <= 1000),
                CONSTRAINT chk_files_size CHECK (size >= 0)
            )
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to create files table: {}", e)))?;

        // Create indexes
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_files_name ON files(name)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create files name index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_files_path ON files(path)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create files path index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_files_type ON files(file_type)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create files type index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_files_size ON files(size)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create files size index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_files_checksum ON files(checksum) WHERE checksum IS NOT NULL")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create files checksum index: {}", e)))?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_files_entity_status ON files(entity_status)")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create files entity_status index: {}", e)))?;

        Ok(())
    }
}
