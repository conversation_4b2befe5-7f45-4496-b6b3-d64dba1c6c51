import type { Component } from 'solid-js';
import { onMount } from 'solid-js';

interface ArchiveConfirmModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
}

export const ArchiveConfirmModal: Component<ArchiveConfirmModalProps> = (props) => {
    let dialogRef: HTMLDialogElement | undefined;

    onMount(() => {
        if (dialogRef) {
            if (props.isOpen && !dialogRef.open) {
                dialogRef.showModal();
            } else if (!props.isOpen && dialogRef.open) {
                dialogRef.close();
            }
        }
    });

    return (
        <dialog ref={dialogRef} onClose={props.onClose} class="p-0 rounded-lg shadow-xl bg-white dark:bg-gray-800 w-full max-w-md backdrop:bg-black/50 m-auto">
            <div class="p-6 text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 dark:bg-yellow-900/50 mb-4">
                    <svg class="h-6 w-6 text-yellow-600 dark:text-yellow-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                </div>
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100">确认归档</h3>
                <div class="mt-2">
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        归档后的项目将从主视图隐藏，您确定要继续吗？
                    </p>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-800/50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse rounded-b-lg">
                <button
                    type="button"
                    onClick={props.onConfirm}
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-yellow-600 text-base font-medium text-white hover:bg-yellow-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm"
                >
                    确认归档
                </button>
                <button
                    type="button"
                    onClick={props.onClose}
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none sm:mt-0 sm:w-auto sm:text-sm"
                >
                    取消
                </button>
            </div>
        </dialog>
    );
};
