// Common Types - 通用类型定义

import { JSX } from 'solid-js';

// 基础类型
export type Id = string;

// 实体状态
export type EntityStatus = 'active' | 'inactive' | 'deleted' | 'archived';

// 优先级
export type Priority = 'low' | 'medium' | 'high' | 'critical';

// 主题
export type Theme = 'light' | 'dark' | 'system';

// 语言
export type Language = 'zh-CN' | 'en-US';

// 组件基础属性
export interface BaseComponentProps {
  class?: string;
  children?: JSX.Element;
}

// 分页参数
export interface Pagination {
  page: number;
  size: number;
  total?: number;
}

// 排序参数
export interface Sort {
  field: string;
  order: 'asc' | 'desc';
}

// 查询参数
export interface QueryParams {
  pagination?: Pagination;
  sort?: Sort;
  search?: string;
  filters?: Record<string, any>;
}

// API 响应格式
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

// 分页响应格式
export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  size: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 错误类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// 通知类型
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  variant?: 'primary' | 'secondary';
}

// 模态框配置
export interface ModalConfig {
  title?: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'destructive';
  onConfirm?: () => void | Promise<void>;
  onCancel?: () => void;
}

// 表单字段类型
export interface FormField<T = any> {
  name: string;
  label?: string;
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'date' | 'datetime';
  value: T;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  error?: string;
  helperText?: string;
  options?: SelectOption[];
  validation?: ValidationRule[];
}

export interface SelectOption {
  label: string;
  value: string | number;
  disabled?: boolean;
}

export interface ValidationRule {
  type: 'required' | 'email' | 'min' | 'max' | 'pattern' | 'custom';
  value?: any;
  message: string;
  validator?: (value: any) => boolean;
}

// 表格列定义
export interface TableColumn<T = any> {
  key: string;
  title: string;
  width?: string | number;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: T, index: number) => JSX.Element;
  className?: string;
}

// 菜单项
export interface MenuItem {
  id: string;
  label: string;
  icon?: JSX.Element;
  path?: string;
  children?: MenuItem[];
  disabled?: boolean;
  badge?: string | number;
  onClick?: () => void;
}

// 面包屑项
export interface BreadcrumbItem {
  label: string;
  path?: string;
  icon?: JSX.Element;
}

// 标签
export interface Tag {
  id: string;
  name: string;
  color?: string;
  description?: string;
}

// 文件信息
export interface FileInfo {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadedAt: string;
  uploadedBy?: string;
}

// 用户偏好设置
export interface UserPreferences {
  theme: Theme;
  language: Language;
  timezone: string;
  dateFormat: string;
  timeFormat: string;
  notificationsEnabled: boolean;
  autoSaveEnabled: boolean;
  sidebarCollapsed: boolean;
  compactMode: boolean;
}

// 应用配置
export interface AppConfig {
  appName: string;
  version: string;
  environment: 'development' | 'production' | 'test';
  apiBaseUrl?: string;
  features: {
    darkMode: boolean;
    notifications: boolean;
    analytics: boolean;
    backup: boolean;
  };
}

// 统计数据
export interface Statistics {
  total: number;
  active: number;
  completed: number;
  pending: number;
  overdue?: number;
  trend?: {
    period: string;
    change: number;
    direction: 'up' | 'down' | 'stable';
  };
}

// 图表数据
export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
}

// 键盘快捷键
export interface Shortcut {
  key: string;
  description: string;
  action: () => void;
  category?: string;
}

// 搜索结果
export interface SearchResult<T = any> {
  id: string;
  title: string;
  description?: string;
  type: string;
  data: T;
  score?: number;
  highlights?: string[];
}

// 导出/导入配置
export interface ExportConfig {
  format: 'json' | 'csv' | 'xlsx' | 'pdf';
  fields?: string[];
  filters?: Record<string, any>;
  filename?: string;
}

export interface ImportConfig {
  format: 'json' | 'csv' | 'xlsx';
  mapping?: Record<string, string>;
  skipFirstRow?: boolean;
  validateData?: boolean;
}
