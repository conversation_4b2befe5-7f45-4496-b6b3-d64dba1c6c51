/**
 * 习惯日历组件
 * 提供日历视图的习惯追踪界面
 */

import { createSignal, createMemo, For, Show } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { 
  ChevronLeft, 
  ChevronRight, 
  Calendar,
  CheckCircle,
  Circle,
  Target
} from 'lucide-solid'
import { cn } from '../../lib/utils'

import type { HabitCalendarProps, BaseHabit, HabitRecord } from './types'

export function HabitCalendar(props: HabitCalendarProps) {
  const [currentDate, setCurrentDate] = createSignal(props.selectedDate)

  // 获取当前月份的日期范围
  const monthDates = createMemo(() => {
    const date = currentDate()
    const year = date.getFullYear()
    const month = date.getMonth()
    
    // 获取月份第一天和最后一天
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    
    // 获取第一周的开始日期（周日）
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay())
    
    // 获取最后一周的结束日期（周六）
    const endDate = new Date(lastDay)
    endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()))
    
    // 生成所有日期
    const dates: Date[] = []
    const current = new Date(startDate)
    
    while (current <= endDate) {
      dates.push(new Date(current))
      current.setDate(current.getDate() + 1)
    }
    
    return dates
  })

  // 获取指定日期的记录
  const getDateRecords = (date: Date) => {
    const dateString = date.toISOString().split('T')[0]
    return props.records.filter(record => record.date === dateString)
  }

  // 计算日期的完成率
  const getDateCompletionRate = (date: Date) => {
    const records = getDateRecords(date)
    const activeHabits = props.habits.filter(habit => habit.isActive)
    
    if (activeHabits.length === 0) return 0
    
    const completedCount = records.filter(record => record.completed).length
    return Math.round((completedCount / activeHabits.length) * 100)
  }

  // 获取日期的热力图颜色
  const getHeatmapColor = (date: Date) => {
    if (!props.showHeatmap) return ''
    
    const rate = getDateCompletionRate(date)
    
    if (rate === 0) return 'bg-gray-100'
    if (rate <= 25) return 'bg-green-100'
    if (rate <= 50) return 'bg-green-200'
    if (rate <= 75) return 'bg-green-300'
    return 'bg-green-500'
  }

  // 检查日期是否为今天
  const isToday = (date: Date) => {
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  // 检查日期是否为当前月份
  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentDate().getMonth()
  }

  // 检查日期是否被选中
  const isSelected = (date: Date) => {
    return date.toDateString() === props.selectedDate.toDateString()
  }

  // 导航到上个月
  const goToPreviousMonth = () => {
    const newDate = new Date(currentDate())
    newDate.setMonth(newDate.getMonth() - 1)
    setCurrentDate(newDate)
  }

  // 导航到下个月
  const goToNextMonth = () => {
    const newDate = new Date(currentDate())
    newDate.setMonth(newDate.getMonth() + 1)
    setCurrentDate(newDate)
  }

  // 选择日期
  const selectDate = (date: Date) => {
    props.onDateSelect(date)
  }

  // 切换习惯记录
  const toggleHabitRecord = (habitId: string, date: Date, completed: boolean) => {
    const dateString = date.toISOString().split('T')[0]
    props.onRecordToggle?.(habitId, dateString, completed)
  }

  // 格式化月份年份
  const formatMonthYear = () => {
    return currentDate().toLocaleDateString('en-US', { 
      month: 'long', 
      year: 'numeric' 
    })
  }

  // 周日期标题
  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

  return (
    <div class={cn("space-y-6", props.class)}>
      {/* 日历头部 */}
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <div>
              <CardTitle class="flex items-center gap-2">
                <Calendar class="h-5 w-5" />
                Habit Calendar
              </CardTitle>
              <CardDescription>Track your daily habit progress</CardDescription>
            </div>
            
            <div class="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={goToPreviousMonth}>
                <ChevronLeft class="h-4 w-4" />
              </Button>
              <div class="text-lg font-semibold min-w-[150px] text-center">
                {formatMonthYear()}
              </div>
              <Button variant="outline" size="sm" onClick={goToNextMonth}>
                <ChevronRight class="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* 日历网格 */}
          <div class="space-y-4">
            {/* 周标题 */}
            <div class="grid grid-cols-7 gap-1">
              <For each={weekDays}>
                {(day) => (
                  <div class="p-2 text-center text-sm font-medium text-muted-foreground">
                    {day}
                  </div>
                )}
              </For>
            </div>
            
            {/* 日期网格 */}
            <div class="grid grid-cols-7 gap-1">
              <For each={monthDates()}>
                {(date) => {
                  const records = getDateRecords(date)
                  const completionRate = getDateCompletionRate(date)
                  const heatmapColor = getHeatmapColor(date)
                  
                  return (
                    <div
                      class={cn(
                        "relative p-2 min-h-[80px] border rounded-lg cursor-pointer transition-all hover:shadow-md",
                        isCurrentMonth(date) ? "bg-white" : "bg-gray-50",
                        isToday(date) && "ring-2 ring-blue-500",
                        isSelected(date) && "bg-blue-50 border-blue-300",
                        heatmapColor && props.showHeatmap && heatmapColor
                      )}
                      onClick={() => selectDate(date)}
                    >
                      {/* 日期数字 */}
                      <div class={cn(
                        "text-sm font-medium mb-1",
                        isCurrentMonth(date) ? "text-gray-900" : "text-gray-400",
                        isToday(date) && "text-blue-600 font-bold"
                      )}>
                        {date.getDate()}
                      </div>
                      
                      {/* 完成率指示器 */}
                      <Show when={props.habits.length > 0 && isCurrentMonth(date)}>
                        <div class="space-y-1">
                          <Show when={completionRate > 0}>
                            <div class="text-xs text-center">
                              <Badge 
                                variant={completionRate === 100 ? "default" : "secondary"}
                                class="text-xs px-1 py-0"
                              >
                                {completionRate}%
                              </Badge>
                            </div>
                          </Show>
                          
                          {/* 习惯点状指示器 */}
                          <div class="flex flex-wrap gap-0.5 justify-center">
                            <For each={props.habits.slice(0, 6)}>
                              {(habit) => {
                                const record = records.find(r => r.habitId === habit.id)
                                return (
                                  <div
                                    class={cn(
                                      "w-1.5 h-1.5 rounded-full",
                                      record?.completed 
                                        ? "bg-green-500" 
                                        : "bg-gray-300"
                                    )}
                                    style={{ backgroundColor: record?.completed ? habit.color : undefined }}
                                    title={`${habit.name}: ${record?.completed ? 'Completed' : 'Not completed'}`}
                                  />
                                )
                              }}
                            </For>
                            <Show when={props.habits.length > 6}>
                              <div class="w-1.5 h-1.5 rounded-full bg-gray-400" title="More habits..." />
                            </Show>
                          </div>
                        </div>
                      </Show>
                    </div>
                  )
                }}
              </For>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 选中日期的详细信息 */}
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Target class="h-5 w-5" />
            {props.selectedDate.toLocaleDateString('en-US', { 
              weekday: 'long',
              month: 'long', 
              day: 'numeric',
              year: 'numeric'
            })}
            <Show when={isToday(props.selectedDate)}>
              <Badge variant="default" class="text-xs">Today</Badge>
            </Show>
          </CardTitle>
          <CardDescription>
            Habit progress for selected date
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <Show when={props.habits.length === 0}>
            <div class="text-center py-8 text-muted-foreground">
              <Target class="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No habits to track</p>
            </div>
          </Show>
          
          <Show when={props.habits.length > 0}>
            <div class="space-y-3">
              <For each={props.habits}>
                {(habit) => {
                  const dateString = props.selectedDate.toISOString().split('T')[0]
                  const record = props.records.find(r => 
                    r.habitId === habit.id && r.date === dateString
                  )
                  
                  return (
                    <div class="flex items-center gap-3 p-3 border rounded-lg">
                      {/* 完成状态按钮 */}
                      <Button
                        variant="ghost"
                        size="sm"
                        class={cn(
                          "w-8 h-8 p-0 rounded-full",
                          record?.completed 
                            ? "bg-green-100 text-green-600 hover:bg-green-200" 
                            : "bg-gray-100 text-gray-400 hover:bg-gray-200"
                        )}
                        onClick={() => toggleHabitRecord(
                          habit.id, 
                          props.selectedDate, 
                          !record?.completed
                        )}
                      >
                        <Show when={record?.completed} fallback={<Circle class="h-4 w-4" />}>
                          <CheckCircle class="h-4 w-4" />
                        </Show>
                      </Button>

                      {/* 习惯信息 */}
                      <div class="flex-1 min-w-0">
                        <div class="flex items-center gap-2">
                          <div 
                            class="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: habit.color }}
                          />
                          <span class="font-medium">{habit.name}</span>
                          <Badge variant="outline" class="text-xs">
                            {habit.type}
                          </Badge>
                        </div>
                        
                        <Show when={habit.description}>
                          <p class="text-sm text-muted-foreground mt-1">
                            {habit.description}
                          </p>
                        </Show>
                      </div>

                      {/* 数值显示 */}
                      <Show when={habit.type === 'numeric' && record?.value}>
                        <div class="text-right">
                          <div class="font-medium">
                            {record!.value} {habit.unit}
                          </div>
                          <div class="text-xs text-muted-foreground">
                            Target: {habit.target}
                          </div>
                        </div>
                      </Show>
                    </div>
                  )
                }}
              </For>
            </div>
          </Show>
        </CardContent>
      </Card>
    </div>
  )
}

export default HabitCalendar
