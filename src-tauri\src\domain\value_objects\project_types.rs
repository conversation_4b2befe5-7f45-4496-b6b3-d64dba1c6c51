// Project Value Objects - 项目相关值对象

use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Ser<PERSON><PERSON>, Deserialize, PartialEq)]
pub struct ProjectName {
    pub value: String,
}

impl ProjectName {
    pub fn new(name: String) -> Result<Self, String> {
        if !name.trim().is_empty() && name.len() <= 200 {
            Ok(Self { value: name })
        } else {
            Err("Project name cannot be empty and must be less than 200 characters".to_string())
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub struct ProjectDescription {
    pub value: String,
}

impl ProjectDescription {
    pub fn new(description: String) -> Result<Self, String> {
        if description.len() <= 2000 {
            Ok(Self { value: description })
        } else {
            Err("Project description must be less than 2000 characters".to_string())
        }
    }
}
