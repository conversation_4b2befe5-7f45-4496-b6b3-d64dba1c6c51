// Area Repository Implementation - 领域仓储实现

use crate::domain::entities::{Area, AreaStatus};
use crate::domain::repositories::{AreaRepository, AreaStatistics};
use crate::infrastructure::database::{DatabasePool, AreaModel};
use crate::shared::errors::{AppError, Result};
use crate::shared::types::{Id, QueryParams, EntityStatus, Metadata};
use async_trait::async_trait;

pub struct AreaRepositoryImpl {
    pool: DatabasePool,
}

impl AreaRepositoryImpl {
    pub fn new(pool: DatabasePool) -> Self {
        Self { pool }
    }

    /// 将数据库模型转换为领域实体
    fn model_to_entity(&self, model: AreaModel) -> Result<Area> {
        let standards: Vec<String> = serde_json::from_str(&model.standards.unwrap_or_default())
            .map_err(|e| AppError::DatabaseError(format!("Failed to parse area standards: {}", e)))?;

        let status = match model.status.as_str() {
            "active" => AreaStatus::Active,
            "maintenance" => AreaStatus::Maintenance,
            "dormant" => AreaStatus::Dormant,
            _ => AreaStatus::Active,
        };

        let entity_status = match model.entity_status.as_str() {
            "active" => EntityStatus::Active,
            "inactive" => EntityStatus::Inactive,
            "deleted" => EntityStatus::Deleted,
            "archived" => EntityStatus::Archived,
            _ => EntityStatus::Active,
        };

        let metadata = Metadata {
            created_at: model.created_at,
            updated_at: model.updated_at,
            created_by: None,
            updated_by: None,
            version: model.version as u64,
        };

        Ok(Area {
            id: model.id,
            name: model.name,
            description: model.description,
            standards,
            color: model.color,
            icon: model.icon,
            status,
            entity_status,
            metadata,
        })
    }

    /// 将领域实体转换为数据库模型
    fn entity_to_model(&self, area: &Area) -> Result<AreaModel> {
        let standards = serde_json::to_string(&area.standards)
            .map_err(|e| AppError::DatabaseError(format!("Failed to serialize area standards: {}", e)))?;

        let status = match area.status {
            AreaStatus::Active => "active",
            AreaStatus::Maintenance => "maintenance",
            AreaStatus::Dormant => "dormant",
        };

        let entity_status = match area.entity_status {
            EntityStatus::Active => "active",
            EntityStatus::Inactive => "inactive",
            EntityStatus::Deleted => "deleted",
            EntityStatus::Archived => "archived",
        };

        Ok(AreaModel {
            id: area.id.clone(),
            name: area.name.clone(),
            description: area.description.clone(),
            standards: Some(standards),
            color: area.color.clone(),
            icon: area.icon.clone(),
            status: status.to_string(),
            entity_status: entity_status.to_string(),
            created_at: area.metadata.created_at,
            updated_at: area.metadata.updated_at,
            version: area.metadata.version as i64,
        })
    }
}

#[async_trait]
impl AreaRepository for AreaRepositoryImpl {
    async fn find_by_id(&self, id: &Id) -> Result<Option<Area>> {
        let model = sqlx::query_as::<_, AreaModel>(
            "SELECT * FROM areas WHERE id = ? AND entity_status != 'deleted'"
        )
        .bind(id)
        .fetch_optional(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find area by id: {}", e)))?;

        match model {
            Some(m) => Ok(Some(self.model_to_entity(m)?)),
            None => Ok(None),
        }
    }

    async fn find_by_name(&self, name: &str) -> Result<Option<Area>> {
        let model = sqlx::query_as::<_, AreaModel>(
            "SELECT * FROM areas WHERE name = ? AND entity_status != 'deleted'"
        )
        .bind(name)
        .fetch_optional(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find area by name: {}", e)))?;

        match model {
            Some(m) => Ok(Some(self.model_to_entity(m)?)),
            None => Ok(None),
        }
    }

    async fn save(&self, area: &Area) -> Result<()> {
        let model = self.entity_to_model(area)?;
        
        sqlx::query(
            r#"
            INSERT INTO areas (id, name, description, standards, color, icon, status, 
                             entity_status, created_at, updated_at, version)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&model.id)
        .bind(&model.name)
        .bind(&model.description)
        .bind(&model.standards)
        .bind(&model.color)
        .bind(&model.icon)
        .bind(&model.status)
        .bind(&model.entity_status)
        .bind(&model.created_at)
        .bind(&model.updated_at)
        .bind(&model.version)
        .execute(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to save area: {}", e)))?;

        Ok(())
    }

    async fn update(&self, area: &Area) -> Result<()> {
        let model = self.entity_to_model(area)?;
        
        let result = sqlx::query(
            r#"
            UPDATE areas 
            SET name = ?, description = ?, standards = ?, color = ?, icon = ?, 
                status = ?, entity_status = ?, updated_at = ?, version = version + 1
            WHERE id = ? AND version = ?
            "#
        )
        .bind(&model.name)
        .bind(&model.description)
        .bind(&model.standards)
        .bind(&model.color)
        .bind(&model.icon)
        .bind(&model.status)
        .bind(&model.entity_status)
        .bind(chrono::Utc::now())
        .bind(&model.id)
        .bind(&model.version)
        .execute(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to update area: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::DatabaseError("Area not found or version conflict".to_string()));
        }

        Ok(())
    }

    async fn delete(&self, id: &Id) -> Result<()> {
        let result = sqlx::query(
            "UPDATE areas SET entity_status = 'deleted', updated_at = ? WHERE id = ?"
        )
        .bind(chrono::Utc::now())
        .bind(id)
        .execute(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to delete area: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::NotFound("Area not found".to_string()));
        }

        Ok(())
    }

    async fn find_all_active(&self) -> Result<Vec<Area>> {
        let models = sqlx::query_as::<_, AreaModel>(
            "SELECT * FROM areas WHERE entity_status = 'active' ORDER BY created_at DESC"
        )
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find active areas: {}", e)))?;

        let mut areas = Vec::new();
        for model in models {
            areas.push(self.model_to_entity(model)?);
        }

        Ok(areas)
    }

    async fn find_by_status(&self, status: &AreaStatus) -> Result<Vec<Area>> {
        let status_str = match status {
            AreaStatus::Active => "active",
            AreaStatus::Maintenance => "maintenance",
            AreaStatus::Dormant => "dormant",
        };

        let models = sqlx::query_as::<_, AreaModel>(
            "SELECT * FROM areas WHERE status = ? AND entity_status != 'deleted' ORDER BY created_at DESC"
        )
        .bind(status_str)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find areas by status: {}", e)))?;

        let mut areas = Vec::new();
        for model in models {
            areas.push(self.model_to_entity(model)?);
        }

        Ok(areas)
    }

    async fn find_with_pagination(&self, params: &QueryParams) -> Result<(Vec<Area>, u64)> {
        let pagination = params.pagination.as_ref().unwrap_or(&crate::shared::types::Pagination::default());
        let offset = (pagination.page - 1) * pagination.size;

        // 获取总数
        let total = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM areas WHERE entity_status != 'deleted'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count areas: {}", e)))? as u64;

        // 获取分页数据
        let models = sqlx::query_as::<_, AreaModel>(
            "SELECT * FROM areas WHERE entity_status != 'deleted' ORDER BY created_at DESC LIMIT ? OFFSET ?"
        )
        .bind(pagination.size as i64)
        .bind(offset as i64)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to find areas with pagination: {}", e)))?;

        let mut areas = Vec::new();
        for model in models {
            areas.push(self.model_to_entity(model)?);
        }

        Ok((areas, total))
    }

    async fn count_active_areas(&self) -> Result<u64> {
        let count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM areas WHERE entity_status = 'active'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count active areas: {}", e)))?;

        Ok(count as u64)
    }

    async fn search(&self, query: &str) -> Result<Vec<Area>> {
        let search_pattern = format!("%{}%", query);

        let models = sqlx::query_as::<_, AreaModel>(
            r#"
            SELECT * FROM areas 
            WHERE (name LIKE ? OR description LIKE ?)
              AND entity_status != 'deleted'
            ORDER BY 
              CASE WHEN name LIKE ? THEN 1 ELSE 2 END,
              created_at DESC
            "#
        )
        .bind(&search_pattern)
        .bind(&search_pattern)
        .bind(&search_pattern)
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to search areas: {}", e)))?;

        let mut areas = Vec::new();
        for model in models {
            areas.push(self.model_to_entity(model)?);
        }

        Ok(areas)
    }

    async fn name_exists(&self, name: &str) -> Result<bool> {
        let count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM areas WHERE name = ? AND entity_status != 'deleted'"
        )
        .bind(name)
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to check area name existence: {}", e)))?;

        Ok(count > 0)
    }

    async fn get_statistics(&self) -> Result<AreaStatistics> {
        // 获取基础统计
        let total_areas = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM areas WHERE entity_status != 'deleted'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count total areas: {}", e)))? as u64;

        let active_areas = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM areas WHERE entity_status = 'active'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count active areas: {}", e)))? as u64;

        // 按状态统计
        let status_stats = sqlx::query_as::<_, (String, i64)>(
            "SELECT status, COUNT(*) FROM areas WHERE entity_status != 'deleted' GROUP BY status"
        )
        .fetch_all(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to get status statistics: {}", e)))?;

        let areas_by_status = status_stats.into_iter().map(|(status, count)| {
            let area_status = match status.as_str() {
                "active" => AreaStatus::Active,
                "maintenance" => AreaStatus::Maintenance,
                "dormant" => AreaStatus::Dormant,
                _ => AreaStatus::Active,
            };
            (area_status, count as u64)
        }).collect();

        // 统计有项目的领域数量
        let areas_with_projects = sqlx::query_scalar::<_, i64>(
            r#"
            SELECT COUNT(DISTINCT a.id) FROM areas a
            INNER JOIN projects p ON a.id = p.area_id
            WHERE a.entity_status != 'deleted' AND p.entity_status != 'deleted'
            "#
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count areas with projects: {}", e)))? as u64;

        // 统计有习惯的领域数量
        let areas_with_habits = sqlx::query_scalar::<_, i64>(
            r#"
            SELECT COUNT(DISTINCT a.id) FROM areas a
            INNER JOIN habits h ON a.id = h.area_id
            WHERE a.entity_status != 'deleted' AND h.entity_status != 'deleted'
            "#
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count areas with habits: {}", e)))? as u64;

        // 计算平均项目数
        let total_projects = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM projects WHERE entity_status != 'deleted'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count total projects: {}", e)))? as u64;

        let average_projects_per_area = if total_areas > 0 {
            total_projects as f32 / total_areas as f32
        } else {
            0.0
        };

        // 计算平均习惯数
        let total_habits = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM habits WHERE entity_status != 'deleted'"
        )
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count total habits: {}", e)))? as u64;

        let average_habits_per_area = if total_areas > 0 {
            total_habits as f32 / total_areas as f32
        } else {
            0.0
        };

        Ok(AreaStatistics {
            total_areas,
            active_areas,
            areas_by_status,
            areas_with_projects,
            areas_with_habits,
            average_projects_per_area,
            average_habits_per_area,
        })
    }

    async fn get_project_count(&self, area_id: &Id) -> Result<u64> {
        let count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM projects WHERE area_id = ? AND entity_status != 'deleted'"
        )
        .bind(area_id)
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count area projects: {}", e)))?;

        Ok(count as u64)
    }

    async fn get_habit_count(&self, area_id: &Id) -> Result<u64> {
        let count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM habits WHERE area_id = ? AND entity_status != 'deleted'"
        )
        .bind(area_id)
        .fetch_one(&**self.pool)
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to count area habits: {}", e)))?;

        Ok(count as u64)
    }
}
