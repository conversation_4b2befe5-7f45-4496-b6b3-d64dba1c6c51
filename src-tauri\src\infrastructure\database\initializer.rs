// Database Initializer - 数据库初始化器

use crate::infrastructure::database::{DatabaseConnection, MigrationManager};
use crate::infrastructure::config::AppConfig;
use crate::shared::errors::{AppError, Result};
use std::path::Path;

pub struct DatabaseInitializer {
    config: AppConfig,
}

impl DatabaseInitializer {
    pub fn new(config: AppConfig) -> Self {
        Self { config }
    }

    /// 初始化数据库
    pub async fn initialize(&self) -> Result<DatabaseConnection> {
        // 确保数据目录存在
        self.ensure_data_directory().await?;

        // 创建数据库连接
        let connection = DatabaseConnection::new(self.config.database.clone()).await?;

        // 运行数据库迁移
        let migration_manager = MigrationManager::new(connection.pool().as_ref().clone());
        migration_manager.run_migrations().await?;

        // 验证数据库健康状态
        self.verify_database_health(&connection).await?;

        // 插入初始数据（如果需要）
        self.insert_initial_data(&connection).await?;

        Ok(connection)
    }

    /// 确保数据目录存在
    async fn ensure_data_directory(&self) -> Result<()> {
        let db_path = Path::new(&self.config.database.url.replace("sqlite:", ""));
        
        if let Some(parent) = db_path.parent() {
            tokio::fs::create_dir_all(parent)
                .await
                .map_err(|e| AppError::ConfigError(format!("Failed to create data directory: {}", e)))?;
        }

        Ok(())
    }

    /// 验证数据库健康状态
    async fn verify_database_health(&self, connection: &DatabaseConnection) -> Result<()> {
        // 基础连接测试
        connection.health_check().await?;

        // 验证所有表是否存在
        self.verify_tables_exist(connection).await?;

        // 验证索引是否存在
        self.verify_indexes_exist(connection).await?;

        Ok(())
    }

    /// 验证所有表是否存在
    async fn verify_tables_exist(&self, connection: &DatabaseConnection) -> Result<()> {
        let expected_tables = vec![
            "migrations",
            "users",
            "projects", 
            "tasks",
            "areas",
            "habits",
            "resources",
            "inbox_items",
            "reviews",
            "notifications",
            "checklists",
            "templates",
            "files",
        ];

        for table_name in expected_tables {
            let count = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=?"
            )
            .bind(table_name)
            .fetch_one(&**connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to check table {}: {}", table_name, e)))?;

            if count == 0 {
                return Err(AppError::DatabaseError(format!("Table {} does not exist", table_name)));
            }
        }

        Ok(())
    }

    /// 验证关键索引是否存在
    async fn verify_indexes_exist(&self, connection: &DatabaseConnection) -> Result<()> {
        let expected_indexes = vec![
            "idx_users_username",
            "idx_projects_name",
            "idx_tasks_title",
            "idx_areas_name",
            "idx_habits_name",
            "idx_resources_title",
            "idx_inbox_processed",
            "idx_reviews_title",
            "idx_notifications_read",
            "idx_checklists_name",
            "idx_templates_name",
            "idx_files_name",
        ];

        for index_name in expected_indexes {
            let count = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND name=?"
            )
            .bind(index_name)
            .fetch_one(&**connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to check index {}: {}", index_name, e)))?;

            if count == 0 {
                return Err(AppError::DatabaseError(format!("Index {} does not exist", index_name)));
            }
        }

        Ok(())
    }

    /// 插入初始数据
    async fn insert_initial_data(&self, connection: &DatabaseConnection) -> Result<()> {
        // 检查是否已有数据
        let user_count = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM users")
            .fetch_one(&**connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to count users: {}", e)))?;

        // 如果没有用户，插入默认数据
        if user_count == 0 {
            self.insert_default_data(connection).await?;
        }

        Ok(())
    }

    /// 插入默认数据
    async fn insert_default_data(&self, connection: &DatabaseConnection) -> Result<()> {
        // 创建默认用户
        let default_user_id = crate::shared::utils::IdGenerator::new_id_with_prefix("user");
        sqlx::query(
            r#"
            INSERT INTO users (id, username, preferences, status)
            VALUES (?, 'default_user', '{}', 'active')
            "#
        )
        .bind(&default_user_id)
        .execute(&**connection.pool())
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to create default user: {}", e)))?;

        // 创建默认领域
        let default_areas = vec![
            ("个人发展", "Personal Development"),
            ("工作事业", "Career & Work"),
            ("健康生活", "Health & Wellness"),
            ("人际关系", "Relationships"),
        ];

        for (name, description) in default_areas {
            let area_id = crate::shared::utils::IdGenerator::new_id_with_prefix("area");
            sqlx::query(
                r#"
                INSERT INTO areas (id, name, description, status, entity_status)
                VALUES (?, ?, ?, 'active', 'active')
                "#
            )
            .bind(&area_id)
            .bind(name)
            .bind(description)
            .execute(&**connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to create default area {}: {}", name, e)))?;
        }

        // 创建默认模板
        let review_template_id = crate::shared::utils::IdGenerator::new_id_with_prefix("template");
        sqlx::query(
            r#"
            INSERT INTO templates (id, name, description, template_type, content, variables)
            VALUES (?, '周复盘模板', '每周复盘的标准模板', 'review', 
                    '# 本周复盘\n\n## 本周成就\n- \n\n## 遇到的挑战\n- \n\n## 下周计划\n- \n\n## 反思与改进\n- ', 
                    '[]')
            "#
        )
        .bind(&review_template_id)
        .execute(&**connection.pool())
        .await
        .map_err(|e| AppError::DatabaseError(format!("Failed to create default template: {}", e)))?;

        Ok(())
    }

    /// 获取数据库统计信息
    pub async fn get_database_stats(&self, connection: &DatabaseConnection) -> Result<DatabaseStats> {
        let users_count = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM users WHERE entity_status = 'active'")
            .fetch_one(&**connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to count users: {}", e)))?;

        let projects_count = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM projects WHERE entity_status = 'active'")
            .fetch_one(&**connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to count projects: {}", e)))?;

        let tasks_count = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM tasks WHERE entity_status = 'active'")
            .fetch_one(&**connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to count tasks: {}", e)))?;

        let areas_count = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM areas WHERE entity_status = 'active'")
            .fetch_one(&**connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to count areas: {}", e)))?;

        let resources_count = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM resources WHERE entity_status = 'active'")
            .fetch_one(&**connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to count resources: {}", e)))?;

        let inbox_count = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM inbox_items WHERE entity_status = 'active' AND processed = FALSE")
            .fetch_one(&**connection.pool())
            .await
            .map_err(|e| AppError::DatabaseError(format!("Failed to count inbox items: {}", e)))?;

        Ok(DatabaseStats {
            users_count: users_count as u64,
            projects_count: projects_count as u64,
            tasks_count: tasks_count as u64,
            areas_count: areas_count as u64,
            resources_count: resources_count as u64,
            inbox_count: inbox_count as u64,
        })
    }
}

#[derive(Debug, Clone)]
pub struct DatabaseStats {
    pub users_count: u64,
    pub projects_count: u64,
    pub tasks_count: u64,
    pub areas_count: u64,
    pub resources_count: u64,
    pub inbox_count: u64,
}
