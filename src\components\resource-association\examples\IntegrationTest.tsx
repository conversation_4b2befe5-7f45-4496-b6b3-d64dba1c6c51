/**
 * 资源关联组件集成测试
 * 用于验证组件功能和集成效果
 */

import { createSignal, onMount, For } from 'solid-js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card'
import { But<PERSON> } from '../../ui/button'
import { Badge } from '../../ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../ui/tabs'
import { 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  Play, 
  RotateCcw,
  Upload,
  Link as LinkIcon,
  Network
} from 'lucide-solid'

// 导入资源关联组件
import { 
  ResourceAssociation,
  FileUpload,
  LinkCreate,
  MarkdownAssociation,
  BidirectionalLinks,
  createResourceDataSource,
  type UnifiedResource,
  type BidirectionalLink,
  type ResourceEventHandlers
} from '../index'

// 模拟数据
const mockResources: UnifiedResource[] = [
  {
    id: '1',
    title: 'Project Documentation',
    description: 'Main project documentation file',
    type: 'markdown',
    entityType: 'project',
    entityId: 'test-project',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date(),
    accessCount: 15,
    tags: ['documentation', 'project'],
    isActive: true,
    filePath: '/docs/project.md',
    wordCount: 1500,
    linksCount: 8
  },
  {
    id: '2',
    title: 'Design Resources',
    description: 'Collection of design assets',
    type: 'link',
    entityType: 'project',
    entityId: 'test-project',
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date(),
    accessCount: 5,
    tags: ['design', 'resources'],
    isActive: true,
    url: 'https://example.com/design',
    domain: 'example.com',
    isValidUrl: true
  }
]

const mockBidirectionalLinks: BidirectionalLink[] = [
  {
    id: '1',
    sourceDocPath: '/docs/project.md',
    sourceDocTitle: 'Project Documentation',
    targetDocPath: '/docs/api.md',
    targetDocTitle: 'API Documentation',
    linkText: '[[API Documentation]]',
    displayText: 'API Documentation',
    linkType: 'wikilink',
    startPosition: 100,
    endPosition: 120,
    lineNumber: 5,
    columnNumber: 10,
    contextBefore: 'For more details, see ',
    contextAfter: ' for implementation.',
    isValid: true,
    linkStrength: 0.8,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date(),
    lastValidated: new Date()
  }
]

// 测试项目
interface TestCase {
  id: string
  name: string
  description: string
  status: 'pending' | 'running' | 'passed' | 'failed'
  result?: string
}

export function IntegrationTest() {
  // 状态管理
  const [activeTab, setActiveTab] = createSignal('main')
  const [testCases, setTestCases] = createSignal<TestCase[]>([
    {
      id: 'resource-loading',
      name: '资源加载测试',
      description: '验证资源数据能够正确加载和显示',
      status: 'pending'
    },
    {
      id: 'file-upload',
      name: '文件上传测试',
      description: '验证文件上传功能的正确性',
      status: 'pending'
    },
    {
      id: 'link-creation',
      name: '链接创建测试',
      description: '验证外部链接创建功能',
      status: 'pending'
    },
    {
      id: 'markdown-association',
      name: 'Markdown关联测试',
      description: '验证Markdown文档关联功能',
      status: 'pending'
    },
    {
      id: 'bidirectional-links',
      name: '双向链接测试',
      description: '验证双向链接系统的功能',
      status: 'pending'
    },
    {
      id: 'event-handling',
      name: '事件处理测试',
      description: '验证事件处理器的正确执行',
      status: 'pending'
    }
  ])

  const [eventLog, setEventLog] = createSignal<string[]>([])
  const [showUploadDialog, setShowUploadDialog] = createSignal(false)
  const [showLinkDialog, setShowLinkDialog] = createSignal(false)
  const [showMarkdownDialog, setShowMarkdownDialog] = createSignal(false)

  // 创建数据源
  const dataSource = createResourceDataSource('project')

  // 事件处理器（用于测试）
  const eventHandlers: ResourceEventHandlers = {
    onCreate: (resource) => {
      addEventLog(`Resource Created: ${resource.title}`)
      updateTestStatus('event-handling', 'passed')
    },
    onUpdate: (resource, changes) => {
      addEventLog(`Resource Updated: ${resource.title}`)
    },
    onDelete: (resourceId) => {
      addEventLog(`Resource Deleted: ${resourceId}`)
    },
    onAccess: (resource) => {
      addEventLog(`Resource Accessed: ${resource.title}`)
    },
    onUploadStart: (uploadData) => {
      addEventLog(`Upload Started: ${uploadData.file.name}`)
    },
    onUploadComplete: (resource) => {
      addEventLog(`Upload Completed: ${resource.title}`)
      updateTestStatus('file-upload', 'passed')
    },
    onUploadError: (error, uploadData) => {
      addEventLog(`Upload Error: ${error.message}`)
      updateTestStatus('file-upload', 'failed', error.message)
    },
    onLinkCreate: (link) => {
      addEventLog(`Bidirectional Link Created: ${link.linkText}`)
      updateTestStatus('bidirectional-links', 'passed')
    },
    onError: (error, context) => {
      addEventLog(`Error: ${error.message} (${context})`)
    }
  }

  // 添加事件日志
  const addEventLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setEventLog(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)])
  }

  // 更新测试状态
  const updateTestStatus = (testId: string, status: TestCase['status'], result?: string) => {
    setTestCases(prev => prev.map(test => 
      test.id === testId 
        ? { ...test, status, result }
        : test
    ))
  }

  // 运行测试
  const runTest = async (testId: string) => {
    updateTestStatus(testId, 'running')
    
    try {
      switch (testId) {
        case 'resource-loading':
          // 模拟资源加载测试
          await new Promise(resolve => setTimeout(resolve, 1000))
          updateTestStatus(testId, 'passed', 'Mock resources loaded successfully')
          break
          
        case 'file-upload':
          // 测试文件上传对话框
          setShowUploadDialog(true)
          await new Promise(resolve => setTimeout(resolve, 100))
          setShowUploadDialog(false)
          updateTestStatus(testId, 'passed', 'Upload dialog functionality working')
          break
          
        case 'link-creation':
          // 测试链接创建对话框
          setShowLinkDialog(true)
          await new Promise(resolve => setTimeout(resolve, 100))
          setShowLinkDialog(false)
          updateTestStatus(testId, 'passed', 'Link creation dialog working')
          break
          
        case 'markdown-association':
          // 测试Markdown关联对话框
          setShowMarkdownDialog(true)
          await new Promise(resolve => setTimeout(resolve, 100))
          setShowMarkdownDialog(false)
          updateTestStatus(testId, 'passed', 'Markdown association dialog working')
          break
          
        case 'bidirectional-links':
          // 双向链接测试在事件处理器中完成
          updateTestStatus(testId, 'running', 'Testing bidirectional links...')
          break
          
        case 'event-handling':
          // 事件处理测试在事件处理器中完成
          updateTestStatus(testId, 'running', 'Waiting for events...')
          break
          
        default:
          updateTestStatus(testId, 'failed', 'Unknown test')
      }
    } catch (error) {
      updateTestStatus(testId, 'failed', error instanceof Error ? error.message : 'Unknown error')
    }
  }

  // 运行所有测试
  const runAllTests = async () => {
    for (const test of testCases()) {
      if (test.id !== 'event-handling' && test.id !== 'bidirectional-links') {
        await runTest(test.id)
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }
  }

  // 重置测试
  const resetTests = () => {
    setTestCases(prev => prev.map(test => ({ ...test, status: 'pending', result: undefined })))
    setEventLog([])
  }

  // 获取测试状态图标
  const getStatusIcon = (status: TestCase['status']) => {
    switch (status) {
      case 'passed': return <CheckCircle class="h-4 w-4 text-green-500" />
      case 'failed': return <AlertCircle class="h-4 w-4 text-red-500" />
      case 'running': return <div class="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
      default: return <div class="h-4 w-4 border-2 border-gray-300 rounded-full" />
    }
  }

  return (
    <div class="space-y-6">
      {/* 测试控制面板 */}
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <div>
              <CardTitle class="flex items-center gap-2">
                <FileText class="h-5 w-5" />
                资源关联组件集成测试
              </CardTitle>
              <CardDescription>
                验证资源关联组件的功能和集成效果
              </CardDescription>
            </div>
            <div class="flex gap-2">
              <Button onClick={runAllTests} size="sm">
                <Play class="h-4 w-4 mr-2" />
                运行所有测试
              </Button>
              <Button onClick={resetTests} variant="outline" size="sm">
                <RotateCcw class="h-4 w-4 mr-2" />
                重置
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <For each={testCases()}>
              {(test) => (
                <div class="flex items-center justify-between p-3 border rounded-lg">
                  <div class="flex items-center gap-3">
                    {getStatusIcon(test.status)}
                    <div>
                      <div class="font-medium">{test.name}</div>
                      <div class="text-sm text-muted-foreground">{test.description}</div>
                      {test.result && (
                        <div class="text-xs mt-1">
                          <Badge variant={test.status === 'passed' ? 'default' : 'destructive'}>
                            {test.result}
                          </Badge>
                        </div>
                      )}
                    </div>
                  </div>
                  <Button 
                    onClick={() => runTest(test.id)} 
                    size="sm" 
                    variant="outline"
                    disabled={test.status === 'running'}
                  >
                    运行
                  </Button>
                </div>
              )}
            </For>
          </div>
        </CardContent>
      </Card>

      {/* 组件测试区域 */}
      <Tabs value={activeTab()} onValueChange={setActiveTab}>
        <TabsList class="grid w-full grid-cols-3">
          <TabsTrigger value="main">主组件</TabsTrigger>
          <TabsTrigger value="links">双向链接</TabsTrigger>
          <TabsTrigger value="events">事件日志</TabsTrigger>
        </TabsList>

        <TabsContent value="main" class="space-y-6">
          <ResourceAssociation
            entityType="project"
            entityId="test-project"
            dataSource={dataSource}
            initialResources={mockResources}
            eventHandlers={eventHandlers}
          />
        </TabsContent>

        <TabsContent value="links" class="space-y-6">
          <BidirectionalLinks
            documentPath="/docs/project.md"
            links={mockBidirectionalLinks}
            onLinkClick={(link) => addEventLog(`Link clicked: ${link.linkText}`)}
            onLinkCreate={(sourceDoc, targetDoc) => addEventLog(`Create link: ${sourceDoc} -> ${targetDoc}`)}
            showBacklinks={true}
            showOutlinks={true}
          />
        </TabsContent>

        <TabsContent value="events" class="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>事件日志</CardTitle>
              <CardDescription>查看组件事件的执行情况</CardDescription>
            </CardHeader>
            <CardContent>
              <div class="space-y-2 max-h-96 overflow-y-auto">
                <For each={eventLog()}>
                  {(log) => (
                    <div class="text-sm font-mono p-2 bg-gray-50 rounded">
                      {log}
                    </div>
                  )}
                </For>
                {eventLog().length === 0 && (
                  <div class="text-center text-muted-foreground py-8">
                    暂无事件日志
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 测试对话框 */}
      {showUploadDialog() && (
        <FileUpload
          onUpload={async (data) => {
            addEventLog(`Test upload: ${data.file.name}`)
            setShowUploadDialog(false)
          }}
          onCancel={() => setShowUploadDialog(false)}
        />
      )}

      {showLinkDialog() && (
        <LinkCreate
          onCreateLink={async (data) => {
            addEventLog(`Test link: ${data.url}`)
            setShowLinkDialog(false)
          }}
          onCancel={() => setShowLinkDialog(false)}
        />
      )}

      {showMarkdownDialog() && (
        <MarkdownAssociation
          onAssociate={async (data) => {
            addEventLog(`Test markdown: ${data.filePath}`)
            setShowMarkdownDialog(false)
          }}
          onCancel={() => setShowMarkdownDialog(false)}
        />
      )}
    </div>
  )
}

export default IntegrationTest
