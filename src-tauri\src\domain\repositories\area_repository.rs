// Area Repository Interface - 领域仓储接口

use crate::domain::entities::{Area, AreaStatus};
use crate::shared::errors::Result;
use crate::shared::types::{Id, QueryParams};
use async_trait::async_trait;

/// 领域仓储接口
#[async_trait]
pub trait AreaRepository: Send + Sync {
    /// 根据ID查找领域
    async fn find_by_id(&self, id: &Id) -> Result<Option<Area>>;
    
    /// 根据名称查找领域
    async fn find_by_name(&self, name: &str) -> Result<Option<Area>>;
    
    /// 保存领域
    async fn save(&self, area: &Area) -> Result<()>;
    
    /// 更新领域
    async fn update(&self, area: &Area) -> Result<()>;
    
    /// 删除领域（软删除）
    async fn delete(&self, id: &Id) -> Result<()>;
    
    /// 查找所有活跃领域
    async fn find_all_active(&self) -> Result<Vec<Area>>;
    
    /// 根据状态查找领域
    async fn find_by_status(&self, status: &AreaStatus) -> Result<Vec<Area>>;
    
    /// 分页查询领域
    async fn find_with_pagination(&self, params: &QueryParams) -> Result<(Vec<Area>, u64)>;
    
    /// 获取领域总数
    async fn count_active_areas(&self) -> Result<u64>;
    
    /// 搜索领域（按名称和描述）
    async fn search(&self, query: &str) -> Result<Vec<Area>>;
    
    /// 检查领域名称是否存在
    async fn name_exists(&self, name: &str) -> Result<bool>;
    
    /// 获取领域统计信息
    async fn get_statistics(&self) -> Result<AreaStatistics>;
    
    /// 获取领域的项目数量
    async fn get_project_count(&self, area_id: &Id) -> Result<u64>;
    
    /// 获取领域的习惯数量
    async fn get_habit_count(&self, area_id: &Id) -> Result<u64>;
}

/// 领域统计信息
#[derive(Debug, Clone)]
pub struct AreaStatistics {
    pub total_areas: u64,
    pub active_areas: u64,
    pub areas_by_status: Vec<(AreaStatus, u64)>,
    pub areas_with_projects: u64,
    pub areas_with_habits: u64,
    pub average_projects_per_area: f32,
    pub average_habits_per_area: f32,
}
