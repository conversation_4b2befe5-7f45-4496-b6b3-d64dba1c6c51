// Project Repository Interface - 项目仓储接口

use crate::domain::entities::{Project, ProjectStatus};
use crate::shared::errors::Result;
use crate::shared::types::{Id, QueryParams, Priority};
use async_trait::async_trait;

/// 项目仓储接口
#[async_trait]
pub trait ProjectRepository: Send + Sync {
    /// 根据ID查找项目
    async fn find_by_id(&self, id: &Id) -> Result<Option<Project>>;
    
    /// 根据名称查找项目
    async fn find_by_name(&self, name: &str) -> Result<Option<Project>>;
    
    /// 保存项目
    async fn save(&self, project: &Project) -> Result<()>;
    
    /// 更新项目
    async fn update(&self, project: &Project) -> Result<()>;
    
    /// 删除项目（软删除）
    async fn delete(&self, id: &Id) -> Result<()>;
    
    /// 查找所有活跃项目
    async fn find_all_active(&self) -> Result<Vec<Project>>;
    
    /// 根据领域ID查找项目
    async fn find_by_area_id(&self, area_id: &Id) -> Result<Vec<Project>>;
    
    /// 根据状态查找项目
    async fn find_by_status(&self, status: &ProjectStatus) -> Result<Vec<Project>>;
    
    /// 根据优先级查找项目
    async fn find_by_priority(&self, priority: &Priority) -> Result<Vec<Project>>;
    
    /// 查找即将到期的项目
    async fn find_due_soon(&self, days: u32) -> Result<Vec<Project>>;
    
    /// 查找已逾期的项目
    async fn find_overdue(&self) -> Result<Vec<Project>>;
    
    /// 分页查询项目
    async fn find_with_pagination(&self, params: &QueryParams) -> Result<(Vec<Project>, u64)>;
    
    /// 获取项目总数
    async fn count_active_projects(&self) -> Result<u64>;
    
    /// 根据进度范围查找项目
    async fn find_by_progress_range(&self, min_progress: f32, max_progress: f32) -> Result<Vec<Project>>;
    
    /// 搜索项目（按名称和描述）
    async fn search(&self, query: &str) -> Result<Vec<Project>>;
    
    /// 获取项目统计信息
    async fn get_statistics(&self) -> Result<ProjectStatistics>;
}

/// 项目统计信息
#[derive(Debug, Clone)]
pub struct ProjectStatistics {
    pub total_projects: u64,
    pub active_projects: u64,
    pub completed_projects: u64,
    pub overdue_projects: u64,
    pub average_progress: f32,
    pub projects_by_status: Vec<(ProjectStatus, u64)>,
    pub projects_by_priority: Vec<(Priority, u64)>,
}
