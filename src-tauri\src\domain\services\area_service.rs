// Area Domain Service - 领域服务

use crate::domain::entities::{Area, AreaStatus};
use crate::shared::errors::Result;

pub struct AreaDomainService;

impl AreaDomainService {
    pub fn new() -> Self {
        Self
    }

    pub fn validate_area_name(&self, name: &str) -> Result<()> {
        crate::shared::utils::validate_required(name, "area name")?;
        crate::shared::utils::validate_length(name, "area name", 1, 100)?;
        Ok(())
    }

    pub fn can_activate_area(&self, area: &Area) -> bool {
        area.status != AreaStatus::Active
    }

    pub fn calculate_health_score(&self, _area: &Area) -> f32 {
        // Business logic for calculating area health based on:
        // - Habit completion rates
        // - Associated project progress
        // - Standards compliance
        // - Recent activity
        0.0
    }
}

impl Default for AreaDomainService {
    fn default() -> Self {
        Self::new()
    }
}
