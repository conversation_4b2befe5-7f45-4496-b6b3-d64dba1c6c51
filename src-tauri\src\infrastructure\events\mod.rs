// Event Infrastructure - 事件基础设施

use crate::domain::events::DomainEvent;
use crate::shared::errors::Result;
use std::sync::Arc;
use tokio::sync::broadcast;

pub mod event_bus;
pub mod event_store;

// 重新导出事件模块
pub use event_bus::*;
pub use event_store::*;

// 事件发布器接口
#[async_trait::async_trait]
pub trait EventPublisher {
    async fn publish(&self, event: Box<dyn DomainEvent>) -> Result<()>;
}

// 事件订阅器接口
#[async_trait::async_trait]
pub trait EventSubscriber {
    async fn handle(&self, event: Box<dyn DomainEvent>) -> Result<()>;
}

// 内存事件总线实现
pub struct InMemoryEventBus {
    sender: broadcast::Sender<String>,
}

impl InMemoryEventBus {
    pub fn new() -> Self {
        let (sender, _) = broadcast::channel(1000);
        Self { sender }
    }

    pub fn subscribe(&self) -> broadcast::Receiver<String> {
        self.sender.subscribe()
    }
}

#[async_trait::async_trait]
impl EventPublisher for InMemoryEventBus {
    async fn publish(&self, event: Box<dyn DomainEvent>) -> Result<()> {
        let event_json = serde_json::to_string(&format!("{}:{}", event.event_type(), event.aggregate_id()))
            .map_err(|e| crate::shared::errors::AppError::InternalError(format!("Failed to serialize event: {}", e)))?;
        
        let _ = self.sender.send(event_json);
        Ok(())
    }
}

impl Default for InMemoryEventBus {
    fn default() -> Self {
        Self::new()
    }
}
